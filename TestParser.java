LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.

import com.warnomodmaker.parser.*;
import com.warnomodmaker.model.*;
import java.io.*;
import java.util.*;

public class TestParser {
    public static void main(String[] args) {
        try {
            // Test parsing the NEW file format with just first few lines
            String testContent = """
export Descriptor_Unit_2K11_KRUG_DDR is TEntityDescriptor
(
    DescriptorId       = GUID:{c4c83faa-1edf-4382-ad7b-f54860eb49f5}
    ClassNameForDebug  = 'Unit_2K11_KRUG_DDR'
    ModulesDescriptors = [
        TTypeUnitModuleDescriptor
        (
            Coalition                        = ECoalition/PACT
            MotherCountry                    = 'DDR'
            AcknowUnitType                   = ~/TAcknowUnitType_SAM
        ),
        TFormationModuleDescriptor(TypeUnitFormation = 'Artillerie'),
        TankFlagsModuleDescriptor,
        ~/TargetManagerModuleDescriptor,
        $/GFX/UnitConstantes/CriticalEffectModule_Vehicule_ArmeSansTourelle,
        WeaponManager is $/GFX/Weapon/WeaponDescriptor_2K11_KRUG_DDR
,        TBaseDamageModuleDescriptor
        (
            MaxPhysicalDamages = 6
            MaxSuppressionDamages = ~/GroundUnit_MaxSuppressionDamages
        ),
    ]
)
""";

            System.out.println("Testing NEW file format parsing with sample content...");

            try (StringReader reader = new StringReader(testContent)) {
                NDFParser parser = new NDFParser(reader);
                parser.setFileType(NDFValue.NDFFileType.UNITE_DESCRIPTOR);

                List<NDFValue.ObjectValue> objects = parser.parseUniteDescriptor();

                System.out.println("SUCCESS: Parsed " + objects.size() + " objects from NEW format");

                // Print object details
                for (int i = 0; i < objects.size(); i++) {
                    NDFValue.ObjectValue obj = objects.get(i);
                    System.out.println("  Object " + (i+1) + ": " + obj.getInstanceName());
                    System.out.println("    Type: " + obj.getTypeName());
                    System.out.println("    Properties: " + obj.getProperties().size());
                }

            } catch (Exception e) {
                System.err.println("FAILED to parse NEW format: " + e.getMessage());
                e.printStackTrace();
            }

        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
