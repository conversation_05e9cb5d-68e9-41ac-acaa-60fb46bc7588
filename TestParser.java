LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.

import com.warnomodmaker.parser.*;
import com.warnomodmaker.model.*;
import java.io.*;
import java.util.*;

public class TestParser {
    public static void main(String[] args) {
        try {
            // Test parsing the NEW file format
            File newFile = new File("tester files/UniteDescriptorNEW.ndf");
            if (!newFile.exists()) {
                System.err.println("File not found: " + newFile.getAbsolutePath());
                return;
            }
            
            System.out.println("Testing NEW file format parsing...");
            
            try (FileReader reader = new FileReader(newFile)) {
                NDFParser parser = new NDFParser(reader);
                parser.setFileType(NDFValue.NDFFileType.UNITE_DESCRIPTOR);
                
                List<NDFValue.ObjectValue> objects = parser.parseUniteDescriptor();
                
                System.out.println("SUCCESS: Parsed " + objects.size() + " objects from NEW file");
                
                // Print first few object names
                for (int i = 0; i < Math.min(5, objects.size()); i++) {
                    NDFValue.ObjectValue obj = objects.get(i);
                    System.out.println("  Object " + (i+1) + ": " + obj.getInstanceName());
                }
                
            } catch (Exception e) {
                System.err.println("FAILED to parse NEW file: " + e.getMessage());
                e.printStackTrace();
            }
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
