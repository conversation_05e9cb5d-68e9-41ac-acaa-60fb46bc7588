// FICHIER GENERE ! NE PAS MODIFIER !
Parameter_ObjectId is 1
Parameter_AllocationId is 2
Parameter_FromSave is 3
Parameter_ModuleCreationInfo is 4
Parameter_OverrideId is 5
Parameter_ForbiddenModules is 6
Namespace_ModuleDescriptors is 1
Parameter_Position is 7
Parameter_TeamId is 8
Parameter_Rotation is 9
Parameter_VisualPositionOffset is 10
Parameter_VisualRotationOffset is 11
Parameter_Orientation is 12
Parameter_Orientation2D is 13
Parameter_Destination is 14
Parameter_DestinationSpeed is 15
Parameter_ExperienceLevel is 16
Parameter_IsCadavre is 17
Parameter_DepositUnit is 18
Parameter_UnitDescriptor is 19
Parameter_UnitName is 20
Parameter_TireurUnitId is 21
Parameter_TargetUnitId is 22
Parameter_Category is 23
Parameter_Ammunition is 24
Parameter_PorteeMax is 25
Parameter_CanBeControlledWhileMoving is 26
Parameter_InitialSpeed is 27
Parameter_DamageMultiplier is 28
Parameter_TargetIsNotVisible is 29
Parameter_SimultaneousShotsInfo is 30
Parameter_LabelInitialVisibility is 31
Parameter_WayToAddToIAStrat is 32
Parameter_CanSpread is 33
Parameter_FireWasForced is 34
Parameter_ShooterObjectId is 35
Parameter_ShooterTeamId is 36
Parameter_ProductionId is 37
Parameter_ProdMustBeShownAsGhost is 38
Parameter_OverrideBoundingBox is 39
Parameter_OverrideCadavreDuration is 40
Parameter_CauseMort is 41
Parameter_CauseMortEffect is 42
Parameter_CauseMortEffectFnF is 43
Parameter_Ancestor is 44
Parameter_CreatePorteur is 45
Parameter_DistrictIndex is 46
Parameter_BoundingsForScenery is 47
Parameter_BoundingsCenters is 48
Parameter_UnitsPosition is 49
Parameter_GroupOrderToken is 50
Parameter_SourceUnitIds is 51
Parameter_TargetUnitIds is 52
Parameter_TargetDestination is 53
Parameter_PosStartFrontLine is 54
Parameter_PosEndFrontLine is 55
Parameter_OrderModifier is 56
Parameter_CustomOrderData is 57
Parameter_FileLine is 58
Parameter_Owner is 59
Parameter_TeamNum is 60
Parameter_AllianceNum is 61
Parameter_IsNeutre is 62
Parameter_TeamNation is 63
Parameter_SpecialAlliance is 64
Parameter_WingLeader is 65
Parameter_IsGraphicVisible is 66
Parameter_SceneryDescriptorName is 67
Parameter_SelectionPolygon is 68
Parameter_ObserverId is 69
Parameter_MustBeAlignedOnGridAfterSpawn is 70
Parameter_SoldierEquipmentInHand is 71
Parameter_SoldierEquipmentOnBack is 72
Parameter_SoldierIndexInSquad is 73
Parameter_LinkedObjectives is 74
Parameter_WeightPerValueType is 75
Parameter_StartBattleDescriptorType is 76
Parameter_ZoneId is 77
Parameter_ZoneNameTag is 78
Parameter_GroupPool is 79
Parameter_ObjectiveId is 80
Parameter_ForceOnGround is 81
Namespace_AirplaneModule is 2
Namespace_AllianceModuleNamespace is 3
Namespace_ConstructionModule is 4
Namespace_CroissanceModule is 5
Namespace_GroupModule is 6
Namespace_MouvementHandler is 7
Namespace_MouvementHandlerSensor is 8
Namespace_PositionModule is 9
Namespace_StorageModule is 10
Namespace_TeamModuleNamespace is 11
Namespace_UnitsInFormationModule is 12
Namespace_LinkToFormationModule is 13
Parameter_ObjectivesListManagementType is 82
Parameter_TeamAllianceStyle is 83
Parameter_AirplaneLocation is 84
Parameter_OriginalHP is 85
Parameter_ShootingPointList is 86
Parameter_ShootingPointDirectionList is 87
Parameter_GlobalBoxesForDamages is 88
Parameter_GlobalBoundings is 89
Parameter_DynamicTerrainZones is 90

