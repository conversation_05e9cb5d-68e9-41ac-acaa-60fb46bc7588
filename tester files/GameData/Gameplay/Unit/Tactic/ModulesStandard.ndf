OrderDisplayModuleDescriptor is TOrderDisplayModuleDescriptor()

CapturableModuleDescriptor is TCapturableModuleDescriptor
(
    RadiusCaptureGRU            =  353
)

GroupableUnitModuleDescriptor is TGroupableUnitModuleDescriptor()

PlayerMissionModuleDescriptor is TPlayerMissionModuleDescriptor()

TargetCoordinatorModuleDescriptor is TTargetCoordinatorModuleDescriptor()

TargetManagerModuleDescriptor is TTargetManagerModuleDescriptor()

BuildingOrderConfigModuleDescriptor is TOrderableModuleDescriptor
(
    UnlockableOrders = [ 'Stop', 'SupplyUnit', 'AskForSupply', 'AIDefend', 'AIAttack', 'AIStop' ]
)

template AirplaneMovementDescriptor
[
    AltitudeGRU,
    AltitudeMinGRU,
    SpeedInKmph,
    AgilityRadiusGRU,
    PitchAngleDegree,
    RollAngleDegree,
    RollSpeedDegreePerSecond,
    EvacAngleDegree,
    EvacToStartingPoint,
    AttackStrategiesDescriptorsSortedByPriority,
] is TAirplaneMovementModuleDescriptor
(
    AltitudeGRU = <AltitudeGRU>
    AltitudeMaxGRU = ~/MaxAltitudeGRU
    AltitudeMinGRU = <AltitudeMinGRU>
    AltitudeMinForRollGRU = ~/MinAltitudeForRollGRU
    MinRollSpeedDegreePerSecond = ~/MinRollSpeedDegreePerSecond
    SpeedInKmph = <SpeedInKmph>
    AgilityRadiusGRU = <AgilityRadiusGRU>
    PitchAngleDegree = <PitchAngleDegree>
    PitchSpeedDegreePerSecond = ~/PitchSpeedDegreePerSecond
    RollAngleDegree = <RollAngleDegree>
    RollSpeedDegreePerSecond = <RollSpeedDegreePerSecond>
    EvacAngleDegree = <EvacAngleDegree>
    EvacToStartingPoint = <EvacToStartingPoint>
    ElevatorRotationMax = ~/ElevatorRotationMax
    AileronRotationMax = ~/AileronRotationMax
    RudderRotationMax = ~/RudderRotationMax
    AttackStrategiesDescriptorsSortedByPriority = <AttackStrategiesDescriptorsSortedByPriority>
)

template BuildingApparenceModuleDescriptor
[
    BlackHoleIdentifier,
    SymbolName,
] is TApparenceModuleDescriptor
(
    PickableObject                        = True
    BlackHoleIdentifier = <BlackHoleIdentifier>
    Depiction = TTimelyModifyLevelBuildReceiverFactory
    (
        SymbolName = <SymbolName>
    )
)

DistrictApparenceModuleDescriptor is TApparenceModuleDescriptor
(
    PickableObject = True
    Depiction = TTimelyModifyLevelBuildReceiverFactory()
)

template InfantryApparenceModuleDescriptor [
    Depiction,
] is TApparenceModuleDescriptor
(
    UseFollowCam = True
    PickableObject = False
    Depiction = <Depiction>
    ReferenceMesh = $/GFX/DepictionResources/Rien
)

template VehicleApparenceModuleDescriptor [
    Depiction,
    BlackHoleIdentifier,
    GameplayBBoxBoneName = "",
    ReferenceMesh,
] is TApparenceModuleDescriptor
(
    UseFollowCam = True
    PickableObject = True
    Depiction = <Depiction>
    ReferenceMesh = <ReferenceMesh>
    BlackHoleIdentifier = <BlackHoleIdentifier>
    GameplayBBoxBoneName = <GameplayBBoxBoneName>
)

FacingInfosModuleDescriptor is TFacingInfosModuleDescriptor()

HeliApparenceModuleDescriptor is THeliApparenceModuleDescriptor()

UnitStateEngineCompanyModuleDescriptor is TStateEngineCompanyModuleDescriptor
(
)
UnitStateEngineModuleDescriptor is TStateEngineUnitModuleDescriptor
(
)

AirplanePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = True
    ClampInWorld               = False
    ClampOutMap                = True
)

AirplaneCadavrePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = False
    ClampOutMap                = True
)


template HelicopterCadavrePositionModuleDescriptor [
    LowAltitudeFlyingAltitudeGRU,
    NearGroundFlyingAltitudeGRU
] is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = True
    ClampOutMap                = False
    LowAltitudeFlyingAltitudeGRU  = <LowAltitudeFlyingAltitudeGRU>
    NearGroundFlyingAltitudeGRU   = <NearGroundFlyingAltitudeGRU>
)

UnitPositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = True
    ClampInWorld               = True
    ClampOutMap                = False
)

UnitCadavrePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = True
    ClampOutMap                = False
)

DistrictPositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = True
    ClampInWorld               = False
    ClampOutMap                = False
)

DistrictCadavrePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = False
    ClampOutMap                = False
)

BunkerCadavrePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = False
    ClampOutMap                = False
)

BuildingCadavrePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = True
    ClampOutMap                = False
)

SelectionModuleDescriptor is TSelectionModuleDescriptor
(
    IsSelectable        = True
    IsHighlightable     = True
    HasSelectionShader  = False // Disabled by [SDDEUX-1276]
    HasHighlightShader  = True
)

DistrictSelectionModuleDescriptor is TSelectionModuleDescriptor
(
    IsSelectable        = True
    IsHighlightable     = True
    HasSelectionShader  = True
    HasHighlightShader  = True
)

public FXState is TBaseClass
(
    // Doit être synchro avec TActOfRuseBuildingAnimationStates/Type
    // (BuildingAnimationModule_WarGame.h)

    Idle            is 0x00000001
    OnFire          is 0x00000002

    // Merci de mettre à jour ce champ au besoin
    All is Idle + OnFire
)

InflammableModuleDescriptorDistrict  is TInflammableModuleDescriptor
(
    FireFxStateId                   = ~/FXState/OnFire
    IdleFxStateId                   = ~/FXState/Idle
)

template BuildingCadavreModuleDescriptor [
    CadavreDuration,
] is TCadavreModuleDescriptor
(
    KillAsVehicule = False
    KillAsInfanterie = False
    KillAsHelico = False
    KillAsAirplane = False
    KillAsBatiment = True
    CadavreDuration = <CadavreDuration>
    CadavreFireDurationMin = ~/CadavrePeriodeEnFeu_Minimum_Batiment
    CadavreFireDurationMax = ~/CadavrePeriodeEnFeu_Maximum_Batiment
    FadingDuration = 6
    DeathExplosionAmmo = nil
)

template UnitCadavreModuleDescriptor [
    KillAsVehicule : bool = false,
    KillAsInfanterie : bool = false,
    KillAsHelico : bool = false,
    KillAsAirplane : bool = false,
    CadavreDuration,
    DeathExplosionAmmo,
] is TCadavreModuleDescriptor
(
    KillAsVehicule = <KillAsVehicule>
    KillAsInfanterie = <KillAsInfanterie>
    KillAsHelico = <KillAsHelico>
    KillAsAirplane = <KillAsAirplane>
    KillAsBatiment = False
    CadavreDuration = <CadavreDuration>
    CadavreFireDurationMin = ~/CadavrePeriodeEnFeu_Minimum
    CadavreFireDurationMax = ~/CadavrePeriodeEnFeu_Maximum
    FadingDuration = 1
    DeathExplosionAmmo = <DeathExplosionAmmo>
)

FOBBuildingModuleDescriptor is TWargameBuildingModuleDescriptor
(
    BoundingBoxMinInLBU = $/GFX/Constantes/FOBBoxMin
    BoundingBoxMaxInLBU = $/GFX/Constantes/FOBBoxMax
)

BuildingDescriptorTagsModuleDescriptor is TTagsModuleDescriptor
(
    TagSet = [ "FOB" ]
)

DistrictTagsModuleDescriptor is TTagsModuleDescriptor
(
    TagSet = [ "Standard" ]
)

DistrictModuleDescriptor is TDistrictModuleDescriptor()

LineInfantrySquadSlotPositionsModuleDescriptor is TInfantrySquadSlotPositionsModuleDescriptor
(
    SquadMovementDataDescriptor  = ~/SoldierSquadMovementDataDescriptor
    SquadFormationDescriptor = ~/SoldierLineDescriptor
    NoiseDescriptor    = ~/SoldierNoiseDescriptor
)

ColumnInfantrySquadSlotPositionsModuleDescriptor is TInfantrySquadSlotPositionsModuleDescriptor
(
    SquadMovementDataDescriptor  = ~/SoldierSquadMovementDataDescriptor
    SquadFormationDescriptor = ~/SoldierColumnDescriptor
    NoiseDescriptor    = ~/SoldierNoiseDescriptor
)

BuildingScannerConfigurationDescriptor is TScannerConfigurationDescriptor
(
    VisionRangesGRU = MAP [
        ( EVisionRange/Standard, 3533.56890459 ),
        ( EVisionRange/HighAltitude, 3533.56890459 ),
    ]
    OpticalStrengths = MAP [
        ( EOpticalStrength/LowAltitude, 40.0 ),
        ( EOpticalStrength/HighAltitude, 40.0 ),
    ]
)
