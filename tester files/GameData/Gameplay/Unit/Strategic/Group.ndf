

Strategic_GroupModuleDescriptor is TGroupModuleDescriptor
(
)

Strategic_GroupStateEngineDescriptor is TStateEngineGroupModuleDescriptor
(
)

export Strategic_ModularUnitGroupDescriptor is TEntityDescriptor
(
    World = WorldIndices_Groups
    DescriptorId = GUID:{00000000-0000-0000-0000-000113000000}
    ClassNameForDebug = 'StrategicGroup'

    ModulesDescriptors =
    [
        ~/Strategic_GroupModuleDescriptor,
        ~/Strategic_GroupStateEngineDescriptor,
    ]
)




