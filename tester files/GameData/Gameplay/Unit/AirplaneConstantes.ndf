// Autres paramètres avions : DEMANDER AUX DEVS AVANT DE MODIFIER //
PitchSpeedDegreePerSecond is 30
MaxAltitudeGRU is 3534
MinAltitudeForRollGRU is 707
MinRollSpeedDegreePerSecond is 65

//Paramètrage visuels des avions
ElevatorRotationMax is 18
AileronRotationMax is 18
RudderRotationMax is 20

BombAttackStrategyDescriptor is TBombAttackStrategyDescriptor()

DiveAttackStrategyDescriptor is TDiveAttackStrategyDescriptor()

DiveMissileAttackStrategyDescriptor is TDiveMissileAttackStrategyDescriptor()

DiveBombAttackStrategyDescriptor is TDiveBombAttackStrategyDescriptor
(
    DiveBombRecoveryDuration = 10 // Temps en secondes pendant lequel l'avion vol à basse altitude après avoir largué ses bombes
)

DogfightAttackStrategyDescriptor is TDogfightAttackStrategyDescriptor
(
    MinDistanceBetweenFighterAndTargetForAttackOnSlowAirplaneStrategy = 0.20 // en ratio de l'agility radius
    MaxDistanceBetweenFighterAndTargetForLateralMoveOnSlowAirplaneStrategy = 0.27 // en ratio de l'agility radius
    SpeedRatioForAttackOnSlowAirplaneStrategy = 0.85
    SpeedRatioForAttackOnVerySlowAirplaneStrategy = 0.45
    MaxAngleDifferenceWithTargetInDegree = 60
    MaxAngleToConsiderTargetAheadInDegree = 50
    AngleToFakeTargetPositionInDegree = 60
)

StukaNosediveAttackStrategyDescriptor is TStukaNosediveAttackStrategyDescriptor
(
    DistanceNosediveGRU = 477
    WaypointDistanceFromTargetGRU = 283
    AgilityMultiplicator = 2.5
)

AirGroundMissileAttackStrategyDescriptor is TAirGroundMissileAttackStrategyDescriptor()
