DebugModuleDescriptor is TDebugModuleDescriptor()
EffectApplierModuleDescriptor is TEffectApplierModuleDescriptor()
EmptyTagsModuleDescriptor is TTagsModuleDescriptor()
InfluenceDataModuleDescriptor is TInfluenceDataModuleDescriptor()
InfluencePositionModuleDescriptor is TInfluencePositionModuleDescriptor()
LinkTeamModuleDescriptor is TLinkTeamModuleDescriptor()
PackSignauxModuleDescriptor  is TPackSignauxModuleDescriptor()
SimpleTypeUnitModuleDescriptor is TTypeUnitModuleDescriptor()
TargetDataModuleDescriptor is TTargetDataModuleDescriptor()

IfNotCadavreCondition is TModuleSelectorCondition_IsNotCadavre()

BuildingPositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = True
    ClampInWorld               = True
    ClampOutMap                = False
)

ShowroomPositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = False
    ClampOutMap                = False
)

FirePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = True
    ClampOutMap                = False
)

MissilePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = True
    ClampInWorld               = False
    ClampOutMap                = True
)

IAStratZoneIndexModuleDescriptor is TModuleSelector
(
    Default        = TIAStratZoneIndexModuleDescriptor()
    Condition      = ~/IfNotCadavreCondition
)

SmokePositionModuleDescriptor is TPositionModuleDescriptor
(
    InGeoDb                    = False
    ClampInWorld               = True
    ClampOutMap                = False
)

template Descriptor_Unit_MimeticUnit[ DescriptorId, MimeticName ] is TEntityDescriptor
(
    DescriptorId      = <DescriptorId>
    ClassNameForDebug = 'Unit_MimeticUnit'
    World = WorldIndices_Mimetic_Units
    ModulesDescriptors = [
        // Position
            TMimeticTransformModuleDescriptor(),
            TMimeticSteeringModuleDescriptor(),
            TLinkTeamModuleDescriptor(),
            TLinkToOwnerModuleDescriptor(),
            TApparenceModuleDescriptor
            (
                PickableObject = True
                Depiction      = TTimelyDepictionReceiverFactory( MimeticName = <MimeticName> )
            ),
            TSoldierAnimationModuleDescriptor
            (
                SoldierAnimationDataDescriptor = ~/SoldierAnimationDataDescriptor
            ),
            TSoldierStatusModuleDescriptor(),
            TMimeticFXManagerModuleDescriptor(),
    ]
)

MissileFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_ProjectileFlying,
    ]
)

BuildingFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Batiment,
        Flag_Detectable,
        Flag_LdDetectable,
    ]
)

CadavreFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_LdDetectable,
    ]
)

DistrictFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_Batiment,
    ]
)

TankFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_LdDetectable,
        Flag_Tank,
    ]
)

CanonFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_LdDetectable,
    ]
)

HelicoFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_LdDetectable,
        Flag_NearGroundFlying,
    ]
)

AirplaneFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_LdDetectable,
        Flag_Avion,
        Flag_HighAltitudeFlying,
    ]
)

InfantryFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_LdDetectable,
        Flag_Infanterie,
    ]
)

DefaultFlagsModuleDescriptor is TFlagsModuleDescriptor
(
    InitialFlagSet =
    [
        Flag_Detectable,
        Flag_LdDetectable,
    ]
)
