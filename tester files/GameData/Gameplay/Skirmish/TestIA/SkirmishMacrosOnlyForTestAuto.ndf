//-------------------------------------------------------------------------
// Attaques
//-------------------------------------------------------------------------
export Attack_Objectives_OnlyTestAuto is TMacroActionDescriptor_ObjectiveAttack_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(ProdProperties = [TProdProperty_LaunchAsSoonAsPossible] PoolModelList = [Pool_Objective_Attack_TestAuto] DisableReinforcement = true),
        TAttackRatioSettingHolder(RatioMinimumPourAttaquer = 1.0),
    ]
)

//-------------------------------------------------------------------------
export Pool_Objective_Attack_TestAuto is TPoolModel
(
    ModelList = [
        TPoolElement(
            DescriptorId = GUID:{f5493bf9-d4b1-4279-a2bc-3360ebb7c29a}
            Required = false
            IndexPriorityOverPrice = true
            Nb = 1
            PriceComparisonMethod = EPriceComparatorType/Random
            TagsPriority = [
                "Char_Standard",
                "Char"
            ]
        ),
    ]
)

//-------------------------------------------------------------------------
export Attack_Corridor_OnlyTestAuto is TMacroActionDescriptor_CorridorAttack_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(ProdProperties = [TProdProperty_LaunchAsSoonAsPossible] PoolModelList = [Pool_Corridor_Attack_TestAuto] DisableReinforcement = true),
        TAttackRatioSettingHolder(RatioMinimumPourAttaquer = 1.2 RatioPourAttaquerPerduAChaqueTick = 0.00084),
        TCorridorPlacementSettingHolder(),
        TForcedPlacementSettingHolder(ForcedCorridor = [2]), // On force le couloirs pour ne pas dépendre du déterminisme
    ]
)

//-------------------------------------------------------------------------
export Attack_Corridor_MiddleCommandZone_OnlyTestAuto is TMacroActionDescriptor_CorridorAttack_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(ProdProperties = [TProdProperty_LaunchAsSoonAsPossible] PoolModelList = [Pool_Corridor_Attack_TestAuto] DisableReinforcement = true),
        TAttackRatioSettingHolder(RatioMinimumPourAttaquer = 1.2 RatioPourAttaquerPerduAChaqueTick = 0.00084),
        TCorridorPlacementSettingHolder(),
        TForcedPlacementSettingHolder(ForcedCorridor = [5]), // On force le couloirs pour ne pas dépendre du déterminisme
    ]
)

//-------------------------------------------------------------------------
export Pool_Corridor_Attack_TestAuto is TPoolModel
(
    ModelList = [
        TPoolElement(
            DescriptorId = GUID:{a56e6038-4ab2-4291-b2cb-0396e57c2cbb}
            Required = false
            IndexPriorityOverPrice = true
            Nb = 1
            PriceComparisonMethod = EPriceComparatorType/MostExpensive
            TagsPriority = [
                "Char_Standard",
                "Char"
            ]
        ),
        TPoolElement(
            DescriptorId = GUID:{6a1ef1b0-30d4-42d0-9776-099eff45d05c}
            Required = false
            IndexPriorityOverPrice = true
            Nb = 1
            PriceComparisonMethod = EPriceComparatorType/MostExpensive
            TagsPriority = [
                "Char_Standard",
                "Char"
            ]
        ),
        TPoolElement(
            DescriptorId = GUID:{6203262e-7487-4501-b277-fb6ee2bdb723}
            Required = false
            IndexPriorityOverPrice = true
            Nb = 1
            PriceComparisonMethod = EPriceComparatorType/MostExpensive
            TagsPriority = [
                "Char_Standard",
                "Char"
            ]
        ),
    ]
)

//-------------------------------------------------------------------------
export CaptureAllZoneObjectif_OnlyTestAuto is TMacroActionDescriptor_CaptureCommandZone_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_BasedOnNumberOfObjectives NumberLaunchBasedOnGenerationType = 1 ConsiderOnlyAssignedCorridors = true),
        TMacroActionProductionSettingHolder(ProdProperties = [TProdProperty_LaunchAsSoonAsPossible] PoolModelList = [~/Pool_Commandant_TestAuto] DisableReinforcement = true),
        TForcedPlacementSettingHolder(OrderedPrioritaryZoneWeightList = [~/CommandZoneCustomWeighForIAStrat])
    ]
)

//-------------------------------------------------------------------------
export CaptureOneZoneObjectif_OnlyTestAuto is TMacroActionDescriptor_CaptureCommandZone_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(ProdProperties = [TProdProperty_LaunchAsSoonAsPossible] PoolModelList = [~/Pool_Commandant_TestAuto] DisableReinforcement = true),
        TForcedPlacementSettingHolder(OrderedPrioritaryZoneWeightList = [~/CommandZoneCustomWeighForIAStrat])
    ]
)

//-------------------------------------------------------------------------
export Pool_Commandant_TestAuto is TPoolModel
(
    ModelList = [
        TPoolElement(
            DescriptorId = GUID:{cb7672e3-5a81-449c-b6f3-6bb843df285f}
            Required = true
            Nb = 1
            IndexPriorityOverPrice = true
            PriceComparisonMethod = EPriceComparatorType/Cheapest
            TagsPriority = [
                "InfmapCommander",
            ]
        ),
    ]
)

//-------------------------------------------------------------------------
//Defense
//-------------------------------------------------------------------------
export Corridor_Defense_OnlyTestAuto is TMacroActionDescriptor_CorridorDefense_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(PoolModelList = [ ~/Pool_Defense_TestAuto]),
        TForcedPlacementSettingHolder(ForcedCorridor = [2]), // On force le couloirs pour ne pas dépendre du déterminisme
        TDefenseBehaviorSettingHolder( // Pour ce test auto on veut que le point de défense choisit soit le plus en avant possible
            DefensePointScoringFeature =
                TDefensePointScoringFeature(DefensePointScoringValue =  MAP
                [
                    (EDefensePointScoringFeature/AltitudeCoeff, 0.0),                       // Bonus dépendant de l'altitude min et max sur la carte et appliqué sur toute la carte

                    (EDefensePointScoringFeature/DistanceBonus, 1.0),                       // | Entre 0 et DistanceMargin => Bonus de DistanceBonus
                    (EDefensePointScoringFeature/DistanceMarginGRU, 177),                   // | Entre DistanceMargin et DistanceDecayLength => Diminution progressive du bonus jusqu'à 0
                    (EDefensePointScoringFeature/DistanceDecayLengthGRU, 353),              // | Après DistanceDecayLength => Malus progressif
                    (EDefensePointScoringFeature/DistanceDecayCoeff, 1.0),

                    (EDefensePointScoringFeature/DistrictCoeff, 0.0),                       // | Bonus appliqués en fonction de la présence d'un quartier ou d'un terrain
                    (EDefensePointScoringFeature/ForetDenseCoeff, 0.0),                     // | Seul le bonus le plus prioritaire est appliqué si plusieurs bonus sont applicables
                    (EDefensePointScoringFeature/ForetLightCoeff, 0.0),                     // | Ordre de priorité : District > Foret Dense > Foret Legere

                    (EDefensePointScoringFeature/IsObjectifCoeff, 0.0),                     // Bonus si la case contient un objectif
                    (EDefensePointScoringFeature/MalusDefaultDefensePoint, 10.0),           // Malus si la case a été prise à défaut d'autres possibilités
                    (EDefensePointScoringFeature/IsDefenseUnit, 0.0),                       // Bonus si la case contient une tranchée ou une position de tir
                ]
            DefensePointPositioning = EDefensePointPositioningType/Offensive)
        ),
    ]
)

//-------------------------------------------------------------------------
export Pool_Defense_TestAuto is TPoolModel
(
    ModelList = [
        TPoolElement(
            DescriptorId = GUID:{57aed130-f4bf-48b7-a894-b42cf1728c4b}
            Required = false
            Nb = 1
            IndexPriorityOverPrice = true
            TagsPriority = [
                "Vehicule_Appui",
                "Canon_AT_Standard",
                "Char_Standard",
                "Infanterie_Spec_Defense",
                "Char"
            ]
        ),
    ]
)


//-------------------------------------------------------------------------
//Artillery
//-------------------------------------------------------------------------

export Artillery_OnlyTestAuto is TMacroActionDescriptor_Artillery_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TArtilleryPositionningSettingHolder(SupportPointSafetyMargin = 1 ArtilleryPositionOffsetGRU = 353357 ArtilleryUpdateOffsetGRU = 353357),
        TArtilleryThreatPerSplashSettingHolder(MinimumThreatPerSplashForKill = 0  MinimumThreatPerSplashForSupport = 999 ),
        TArtilleryMoveOutThreatSettingHolder(ThreatThresholdValue = 1 MoveOutThreatProbability = 1.0),

        TArtilleryStrikeSettingHolder(
            SupportStrikeRatio = 0.3
            ArtilleryStrikeThreatBonusPerTags =
            [
                (["Canon_AT"], 100.0),
                (["Canon_AA"], 100.0),
                (["Infanterie_Standard"], 100.0),
                (["Infanterie_Spec_Defense"], 100.0),
                (["Infanterie"], 100.0),
                (["Artillerie"], 100.0),
                (["Vehicule_Logistique"], 100.0),
                (["Vehicule"], 100.0),
                (["Char"], 100.0),
                (["Helo"], 100.0),
            ]
        ),

        TMacroActionProductionSettingHolder(PoolModelList = [~/Pool_Artillery_TestAuto] DistanceToOptimumStartDuringDeploymentGRU = 1060),
        TForcedPlacementSettingHolder(ForcedCorridor = [5]),
    ]
)

//-------------------------------------------------------------------------
export Pool_Artillery_TestAuto is TPoolModel
(
    ModelList = [
    TPoolElement(
        DescriptorId = GUID:{9dd0267d-dca0-4633-8c4b-e0cc6c7a3085}
        Required = false
        Nb = 1
        IndexPriorityOverPrice = false
        PriceComparisonMethod = EPriceComparatorType/Random
        TagsPriority = [
            "Artillerie_Longue_Portee",
            "Artillerie_MLRS",
            "Artillerie",
        ]
    ),
    ]
)

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
//-------------------------------------------------------------------------

private template Template_Airstrike_TestAuto
[
    TypesDeRequetesAcceptees = ~/EAirStrikeRequestType/All,
    PoolModel = ~/Pool_Airstrike_AA_TestAuto
]
is TMacroActionDescriptor_AirStrike_Common
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(ProdProperties = [TProdProperty_LaunchAsSoonAsPossible] PoolModelList = [<PoolModel>]),
        TAirStrikeSettingHolder(TypesDeRequetesAcceptees = <TypesDeRequetesAcceptees>),
        TAirStrikeAcceptableThreatSettingHolder(PercentAcceptableDangerousIndicesForAirplaneValidTarget = 0.5),
    ]
)


//-------------------------------------------------------------------------
export Airstrike_AA_TestAutoOnly is Template_Airstrike_TestAuto
(
    TypesDeRequetesAcceptees = ~/EAirStrikeRequestType/Defense
    PoolModel = ~/Pool_Airstrike_AA_TestAuto
)

//-------------------------------------------------------------------------
export Airstrike_OpportunisticSingleTarget_TestAutoOnly is Template_Airstrike_TestAuto
(
    TypesDeRequetesAcceptees = ~/EAirStrikeRequestType/OpportunisticSingleTarget
    PoolModel = ~/Pool_Airstrike_Assault_TestAuto
)

//-------------------------------------------------------------------------
export Airstrike_OpportunisticTargetInGroup_TestAutoOnly is Template_Airstrike_TestAuto
(
    TypesDeRequetesAcceptees = ~/EAirStrikeRequestType/OpportunisticTargetInGroup
    PoolModel = ~/Pool_Airstrike_Offense_TestAuto
)

//-------------------------------------------------------------------------
export Pool_Airstrike_Offense_TestAuto is TPoolModel
(
    ModelList = [
    TPoolElement(
        DescriptorId = GUID:{27acf662-1bf0-4879-ba46-4dabf04d37b5}
        Required = true
        Nb = 1
        IndexPriorityOverPrice = true
        TagsPriority = [
            "Avion_Bombardier",
            "Avion_Chasseur_Bombardier",
        ]
    ),
    ]
)
//-------------------------------------------------------------------------
export Pool_Airstrike_AA_TestAuto is TPoolModel
(
    ModelList = [
    TPoolElement(
        DescriptorId = GUID:{b111a7cc-d9d2-40c7-b0ff-731e76acc509}
        Required = true
        Nb = 1
        IndexPriorityOverPrice = true
        TagsPriority = [
            "Avion_Chasseur",
            "Avion_Chasseur_Bombardier",
        ]
    ),
    ]
)
//-------------------------------------------------------------------------
export Pool_Airstrike_Assault_TestAuto is TPoolModel
(
    ModelList = [
    TPoolElement(
        DescriptorId = GUID:{e94e406b-d711-430c-b3bb-82f28d99d7d5}
        Required = true
        Nb = 1
        IndexPriorityOverPrice = true
        TagsPriority = [
            "Avion_AT",
        ]
    ),
    ]
)

//-------------------------------------------------------------------------
//-------------------------------------------------------------------------
//-------------------------------------------------------------------------

export Supply_TestAutoOnly is TMacroActionDescriptor_Supply_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(PoolModelList = [~/Pool_Supply_Skirmish_TestAuto]),
        TSupplySettingHolder(SupplyableMinimumRatio = MAP[
            (ESupplyableType_Fuel, 0.3),
            (ESupplyableType_Weapons, 0.3),
            (ESupplyableType_Life, 0.5),
        ]),
    ]
)
//-------------------------------------------------------------------------
export Supply_Allied_TestAutoOnly is TMacroActionDescriptor_Supply_Specific
(
    ParametresDeMission =
    [
        TGenerationSettingHolder(GenerationType = TGenerationType_FixedSize NumberLaunchBasedOnGenerationType = 1),
        TMacroActionProductionSettingHolder(PoolModelList = [~/Pool_Supply_Skirmish_TestAuto] DisableReinforcement = true),
        TSupplySettingHolder(SupplyableMinimumRatio = MAP[
            (ESupplyableType_Fuel, 0.2),
            (ESupplyableType_Weapons, 0.2),
            (ESupplyableType_Life, 0.4),
        ]),
        TCanTargetAlliedSettingHolder(),
    ]
)
//-------------------------------------------------------------------------

export Pool_Supply_Skirmish_TestAuto is TPoolModel
(
    ModelList = [
    TPoolElement(
        DescriptorId = GUID:{ebf80b0c-0dd4-4fac-a39b-cabba0a308fa}
        Required = true
        Nb = 1
        TagsPriority = [
            "Vehicule_Logistique",
        ]
    ),
    ]
)
