export Airstrike_OpportunisticTargetInGroup_TestAuto_Strategy is TIAGeneralStrategy
(
    StrategyName = "AIRGROUP"
    FirstGenerator = TestAuto_Airstrike_OpportunisticTargetInGroup
    TransitionList = []
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export TestAuto_Airstrike_OpportunisticTargetInGroup is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        ~/Airstrike_OpportunisticTargetInGroup_TestAutoOnly,
    ]
)
