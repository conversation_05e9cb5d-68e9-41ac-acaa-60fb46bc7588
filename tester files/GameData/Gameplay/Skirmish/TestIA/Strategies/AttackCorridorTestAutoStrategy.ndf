export AttackCorridor_TestAuto_Strategy is TIAGeneralStrategy
(
    StrategyName = "AtkCorIA"
    FirstGenerator = TestAuto_AttackCorridor
    TransitionList = []
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export TestAuto_AttackCorridor is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        ~/Attack_Corridor_OnlyTestAuto,
    ]
)
