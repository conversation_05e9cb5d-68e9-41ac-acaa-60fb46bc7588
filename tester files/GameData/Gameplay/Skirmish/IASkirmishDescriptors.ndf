
private MapTerrestre            is 0
private MapMixte                is 1
private MapMarine               is 2

export EmptyStrategy is TIAGeneralStrategy
(
    StrategyName = "EmptyStrat"
    FirstGenerator = TSequenceGeneratorDescriptor(GeneratorList = [])
)

private DefaultStrategyList is
[
    ~/GenericStrategy,
]

private VeryEasyStrategyList is
[
    ~/VeryEasyStrategy,
]

private EasyStrategyList is
[
    ~/EasyStrategy,
]

private HardStrategyList is
[
    ~/HardStrategy,
]

private VeryHardStrategyList is
[
    ~/VeryHardStrategy,
]

private CampaignStrategyList is
[
]

private BKTDefaultStrategyList is
[
    ~/GenericBreakthroughStrategy
]

private BKTEasyStrategyList is
[
    ~/EasyBreakthroughStrategy
]

private SkirmishList is TIASkirmishList_Specific
(
    // Stratégies disponibles pour l'auto-deploiement
    IAStrategyForAutoDeploy = MAP
    [
        (EDeploymentMode/Breakthrough, ~/AutoDeployBreakthroughStrategy),
        (EDeploymentMode/NotSpecified, ~/AutoDeployStrategy),
    ]

    // Stratégies disponibles pour tous les battlegroups de la campagne solo
    // Ces stratégies doivent pouvoir se jouer avec tous les decks de battlegroups
    IAStrategyListForSoloCampaign =
    [
        ~/EmptyStrategy
    ]

    //Choix de la strategie suivant le mode de jeu et le type de map en skirmish/multi
    IAStrategyConfiguration = MAP
    [
        (IADifficulty/TresFacile, MAP
        [
            (EDeploymentMode/NotSpecified, ~/VeryEasyStrategyList),
        ]),
        (IADifficulty/Facile, MAP
        [
            (EDeploymentMode/NotSpecified, ~/EasyStrategyList),
        ]),
        (IADifficulty/Normal, MAP
        [
            (EDeploymentMode/NotSpecified, ~/DefaultStrategyList),
        ]),
        (IADifficulty/Difficile, MAP
        [
            (EDeploymentMode/NotSpecified, ~/HardStrategyList),
        ]),
        (IADifficulty/TresDifficile, MAP
        [
            (EDeploymentMode/NotSpecified, ~/VeryHardStrategyList),
        ]),
        (IADifficulty/PlusDifficile, MAP
        [
            (EDeploymentMode/NotSpecified, ~/VeryHardStrategyList),
        ]),
        (IADifficulty/AnyDifficulty, MAP
        [
            (EDeploymentMode/NotSpecified, ~/DefaultStrategyList),
        ])
    ]

    IAStrategyOverrideByMap = MAP
    [
        //(GUID:{00000000-0000-0000-0000-000000000000}, MAP[(~/EDeploymentMode/NotSpecified, [~/Strategie_pour_cette_map])]), <-stratégie unique pour cette map
    ]

    //Prioritaire par rapport a l'override
    IAStrategyOverrideForTicketsEconomy = MAP
    [
        (IADifficulty/Facile, MAP
        [
            (EDeploymentMode/Breakthrough, ~/BKTEasyStrategyList),
            (EDeploymentMode/NotSpecified, ~/DefaultStrategyList),
        ]),
        (IADifficulty/AnyDifficulty, MAP
        [
            (EDeploymentMode/Breakthrough, ~/BKTDefaultStrategyList),
            (EDeploymentMode/NotSpecified, ~/DefaultStrategyList),
        ]),
    ]
)
