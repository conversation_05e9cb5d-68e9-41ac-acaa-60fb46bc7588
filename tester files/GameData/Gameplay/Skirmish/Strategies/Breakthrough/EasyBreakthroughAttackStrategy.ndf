//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export EasyBreakthroughAttackStrategy is TIAGeneralStrategy
(
    StrategyName = "IziBTAtkAI"
    FirstGenerator = EasyBTAtk_Choose_Strategy
    TransitionList =
    [
        EasyBTAtk_Transition_StartToPhaseCommand,
        EasyBTAtk_Transition_StartToEasy,
        EasyBTAtk_Transition_DeployToPhaseCommand,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export EasyBTAtk_Condition_GotoEasy is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export EasyBTAtk_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)
//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export EasyBTAtk_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = EasyBTAtk_Choose_Strategy
    Condition = EasyBTAtk_Condition_CommandPhase
    Destination = EasyBTAtk_Phase_Command
)
export EasyBTAtk_Transition_StartToEasy is TIAGeneralStrategyTransition
(
    Origine = EasyBTAtk_Choose_Strategy
    Condition = EasyBTAtk_Condition_GotoEasy
    Destination = EasyBTAtk_PhaseDeploiement
)
//-------------------------------------------------------------------------
export EasyBTAtk_Transition_DeployToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = EasyBTAtk_PhaseDeploiement
    Condition = EasyBTAtk_Condition_CommandPhase
    Destination = EasyBTAtk_Phase_Command
)


//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export EasyBTAtk_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    GeneratorList =
    [
        ~/Supply_Skirmish_WithAllied_OnePerCorridor,
    ]
)
//-------------------------------------------------------------------------
export EasyBTAtk_PhaseDeploiement is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Capture de zones
        ~/CaptureAllZoneObjectif,

        //------------------------------
        // Support & Supply
        //------------------------------

        ~/FOB_Skirmish,

        ~/Supply_Skirmish_WithAllied_OnePerCorridor,

        ~/Reco,

        //------------------------------
        // Attaques principales
        //------------------------------
        // Grosse attaque
        ~/Attack_Main_BKT,
        ~/Attack_Main_Diversify_BKT,

        ~/Artillery_Support,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        ~/Attack_Objectives,

        ~/Artillery_TriggerHappy_1for4Corridors,

    ] + ~/EasyAtk_Defenses_Airstrike_Arti_Shuffle_Deploy

    //------------------------------
    // Scaling
    //------------------------------
    // Dans les difficultés supérieures il faudra surement maintenir
    // ça avec des maintain à la phase d'après pour être sur qu'on
    // produise de nouvelles unités
    ScalingGeneratorList = ~/EasyAtk_Scaling
)
//-------------------------------------------------------------------------
export EasyBTAtk_Phase_Command is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Capture de zones
        ~/CaptureAllZoneObjectif,

        //------------------------------
        // Support & Supply
        //------------------------------

        ~/Supply_Skirmish_WithAllied_OnePerCorridor,
        ~/Reco,

        //------------------------------
        // Attaques principales
        //------------------------------
        ~/Attack_Main_BKT,
        ~/Attack_Main_Diversify_BKT,

        ~/Artillery_Support,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        ~/Attack_Objectives,

        ~/Artillery_TriggerHappy_1for4Corridors,

    ] + ~/Airstrike_Arti_Shuffle // On sort les défenses d'ici pour les mettre plus haut dans la prio

    //------------------------------
    // Scaling
    //------------------------------
    // Dans les difficultés supérieures il faudra surement maintenir
    // ça avec des maintain à la phase d'après pour être sur qu'on
    // produise de nouvelles unités
    ScalingGeneratorList = ~/EasyAtk_Scaling
)

// Le concept de ces blocs de missions
// est d'à la fois défendre ses arrières
// mais aussi de faire usage d'unités diverses
// (artillerie et avions) pour débloquer
// les situations et appuyer le reste des missions
EasyAtk_Defenses_Airstrike_Arti_Shuffle_Deploy is
[
    ~/Airstrike_Assault,
    ~/Defense_Corridor_AA,
    ~/Airstrike_Offense,
    ~/Airstrike_AA,
    ~/Artillery_Expensive,
]

EasyAtk_Scaling is
[
    ~/Attack_Helo_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Attack_Objectives_Autogen,
    ~/Airstrike_AA_Autogen,
    ~/Defense_Corridor_Adaptive_Autogen,
    ~/Artillery_Expensive_Autogen,
    ~/Defense_Corridor_AA_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Airstrike_Offense_Autogen,
    ~/Artillery_TriggerHappy_Autogen,
    ~/Airstrike_Assault_Autogen,
]

