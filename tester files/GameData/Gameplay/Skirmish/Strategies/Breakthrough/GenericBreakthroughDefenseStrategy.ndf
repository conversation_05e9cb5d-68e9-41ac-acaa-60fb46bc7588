//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export GenericBreakthroughDefenseStrategy is TIAGeneralStrategy
(
    StrategyName = "GenBTDefAI"
    FirstGenerator = GenBTDef_Choose_Strategy
    TransitionList =
    [
        GenBTDef_Transition_StartToDeploy,
        GenBTDef_Transition_StartToCommand,
        GenBTDef_Transition_DeployToCommand,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export GenBTDef_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export GenBTDef_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)
//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export GenBTDef_Transition_StartToDeploy is TIAGeneralStrategyTransition
(
    Origine = GenBTDef_Choose_Strategy
    Condition = GenBTDef_Condition_GotoGeneric
    Destination = GenBTDef_Deploiement
)
//-------------------------------------------------------------------------
export GenBTDef_Transition_StartToCommand is TIAGeneralStrategyTransition
(
    Origine = GenBTDef_Choose_Strategy
    Condition = GenBTDef_Condition_CommandPhase
    Destination = GenBTDef_Command
)
//-------------------------------------------------------------------------
export GenBTDef_Transition_DeployToCommand is TIAGeneralStrategyTransition
(
    Origine = GenBTDef_Deploiement
    Condition = GenBTDef_Condition_CommandPhase
    Destination = GenBTDef_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export GenBTDef_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    GeneratorList =
    [
        ~/Supply_Skirmish_WithAllied_OnePerCorridor,
    ]
)
//-------------------------------------------------------------------------
export GenBTDef_Deploiement is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Capture de zones
        ~/CaptureAllZoneObjectif,


        ~/FOB_Skirmish,


        ~/Defense_Static_ATGM_BKT,
        ~/Defense_Static_ATGM_Reco_BKT,
        ~/Defense_Static_Inf_BKT,

        ~/Supply_Skirmish_WithAllied_OnePerCorridor,

        ~/Defense_Objectif_AA_BKT,

        ~/Defense_Objectif_BKT,
        ~/Defense_Objectif_BKT,
        ~/Defense_Objectif_BKT,

        ~/Reco,

        ~/Airstrike_SEAD,
        ~/Artillery_Expensive,
        ~/Airstrike_Assault,
        ~/Airstrike_AA,
        ~/Artillery_TriggerHappy_1for4Corridors,
    ]

    //------------------------------
    // Scaling
    //------------------------------
    ScalingGeneratorList = ~/BKTDef_Scaling
)
//-------------------------------------------------------------------------
export GenBTDef_Command is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Capture de zones
        ~/CaptureAllZoneObjectif,


        ~/Defense_Static_ATGM_BKT,
        ~/Defense_Static_ATGM_Reco_BKT,
        ~/Defense_Static_Inf_BKT,

        ~/Supply_Skirmish_WithAllied_OnePerCorridor,

        ~/Defense_Objectif_AA_BKT,

        ~/Defense_Objectif_BKT,
        ~/Defense_Objectif_BKT,
        ~/Defense_Objectif_BKT,

        ~/Reco,

        ~/Airstrike_SEAD,
        ~/Artillery_Expensive,
        ~/Airstrike_Assault,
        ~/Airstrike_AA,
        ~/Artillery_TriggerHappy_1for4Corridors,
    ]

    //------------------------------
    // Scaling
    //------------------------------
    ScalingGeneratorList = ~/BKTDef_Scaling
)

BKTDef_Scaling is
[
    ~/Defense_Objectif_BKT_Scaling,
    ~/Defense_Objectif_Helo_BKT,
]
