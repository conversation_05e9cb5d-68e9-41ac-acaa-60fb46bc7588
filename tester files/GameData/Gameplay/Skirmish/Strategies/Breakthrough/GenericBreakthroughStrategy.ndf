//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export GenericBreakthroughStrategy is TIAGeneralStrategy
(
    StrategyName = "GenBTAI"
    FirstGenerator = GenBT_Choose_Strategy
    TransitionList =
    [
        GenBT_Transition_DefMod,
        GenBT_Transition_AtkMod,

        GenBTDef_Transition_StartToDeploy,
        GenBTDef_Transition_StartToCommand,
        GenBTDef_Transition_DeployToCommand,

        GenBTAtk_Transition_StartToPhaseCommand,
        GenBTAtk_Transition_StartToGeneric,
        GenBTAtk_Transition_DeployToPhaseCommand,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export GenBT_Condition_GotoDefMod is TSequenceCondition_IsAttackingAlliance
(
    IsAttackingAlliance = false
)

export GenBT_Condition_GotoAtkMod is TSequenceCondition_Not
(
    Condition = ~/GenBT_Condition_GotoDefMod
)
//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export GenBT_Transition_DefMod is TIAGeneralStrategyTransition
(
    Origine = GenBT_Choose_Strategy
    Condition = GenBT_Condition_GotoDefMod
    Destination = GenBTDef_Choose_Strategy
)

export GenBT_Transition_AtkMod is TIAGeneralStrategyTransition
(
    Origine = GenBT_Choose_Strategy
    Condition = GenBT_Condition_GotoAtkMod
    Destination = GenBTAtk_Choose_Strategy
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export GenBT_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    GeneratorList =
    [
    ]
)
