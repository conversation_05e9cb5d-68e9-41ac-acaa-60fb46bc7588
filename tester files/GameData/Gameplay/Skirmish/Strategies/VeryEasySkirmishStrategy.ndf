//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
// Le but de cette strategy est d'être complètement passive pour laisser le joueur apprendre a jouer et expériementer
// Elle ne va donc jamais essayer de prendre plus que la moitié des zones, elle a le strict minimum d'attaques
//-------------------------------------------------------------------------
export VeryEasyStrategy is TIAGeneralStrategy
(
    StrategyName = "VeryEasyAI"
    FirstGenerator = VEasy_Choose_Strategy
    AllowToByPassNbMacroActionAllowedToProduceSimultaneous = true
    TransitionList =
    [
        VEasy_Transition_StartToPhaseCommand,
        VEasy_Transition_StartToGeneric,
        VEasy_Transition_DeployToCommandPhase,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export VEasy_Condition_GotoGeneric is TSequenceCondition_Not
(
    Condition = ~/VEasy_Condition_CommandPhase
)
//-------------------------------------------------------------------------
export VEasy_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)
//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export VEasy_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = VEasy_Choose_Strategy
    Condition = VEasy_Condition_CommandPhase
    Destination = VEasy_Phase_Command
)
export VEasy_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = VEasy_Choose_Strategy
    Condition = VEasy_Condition_GotoGeneric
    Destination = VEasy_Phase_Deploiement
)
//-------------------------------------------------------------------------
export VEasy_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = VEasy_Phase_Deploiement
    Condition = VEasy_Condition_CommandPhase
    Destination = VEasy_Phase_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export VEasy_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    NbMacroActionAllowedToProduceSimultaneous = 9001
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export VEasy_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 4
    GeneratorList =
    [
        ~/CaptureOneObjectif,

        ~/FOB_Skirmish,
        // On garde quand même une attaque objective pour allez prendre les zones de commandement
        ~/Attack_Objectives,

        //------------------------------
        // Support & Supply
        //------------------------------
        ~/Support_Transports,

        ~/Supply_Skirmish,

        ~/CaptureHalfZoneObjectif_Floor,

        ~/Reco,            // 40 par reco

        ~/Defense_Objectif,

        ~/Artillery_Support,        // Artillerie pas chère                 //  80

        ~/Defense_Corridor_Adaptive,          // ~100
        ~/Defense_Filler,            // budget 40

        //------------------------------
        // Défenses Avions Arti
        //------------------------------
        ~/Supply_Skirmish_Allied,

        ~/Airstrike_AirReco,

    ] + ~/Defenses_Airstrike_Arti_Shuffle_Deploy

    //------------------------------
    // Pas de Scaling pour la VEasy
    //------------------------------
)
//-------------------------------------------------------------------------
export VEasy_Phase_Command is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 3
    GeneratorList =
    [
        ~/CaptureOneObjectif,

        ~/Attack_Objectives,
        //------------------------------
        // Support & Supply
        //------------------------------
        ~/Support_Transports,

        ~/Supply_Skirmish,

        ~/Reco,

        ~/CaptureHalfZoneObjectif_Floor,

        //------------------------------
        // Défenses prioritaires
        //------------------------------
        // Statiques pour tenir le terrain
        ~/Defense_Corridor_Adaptive, // Canon AT - Chasseur de char - ATInf - HMG - Flamer // 85
        ~/Defense_Corridor_AA,

        ~/Defense_Objectif,

        //------------------------------
        // VEasy encore des defenses
        //------------------------------
        ~/Artillery_Support,        // Artillerie pas chère                 //  80

        ~/Defense_Corridor_Adaptive,  // Faire du code pour adapater le budget en fonction de l'income
        ~/Defense_Corridor_Adaptive,

        ~/Defense_Corridor_Adaptive,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        //------------------------------
        // Défenses Avions Arti
        //------------------------------
        ~/Supply_Skirmish_Allied,

        ~/Airstrike_AirReco,

    ] + ~/Airstrike_Arti_Shuffle // On sort les défenses d'ici pour les mettre plus haut dans la prio

    //------------------------------
    // Pas de Scaling pour la VEasy
    //------------------------------
)

