//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export VeryHardStrategy is TIAGeneralStrategy
(
    StrategyName = "GenericAI"
    FirstGenerator = VHard_Choose_Strategy
    TransitionList =
    [
        VHard_Transition_StartToPhaseCommand,
        VHard_Transition_StartToGeneric,
        VHard_Transition_DeployToCommandPhase,
        VHard_Transition_CommandPhaseToLastHope,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export VHard_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export VHard_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)
//-------------------------------------------------------------------------
export VHard_Condition_OpponentWinning is Template_SequenceConditionOpponentHasScoreHigher
(
    ScorePointType = ~/EScorePointType/ZonePoint
    RatioValue = 0.7
)
//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export VHard_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = VHard_Choose_Strategy
    Condition = VHard_Condition_CommandPhase
    Destination = VHard_Phase_Command
)
export VHard_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = VHard_Choose_Strategy
    Condition = VHard_Condition_GotoGeneric
    Destination = VHard_Phase_Deploiement
)
//-------------------------------------------------------------------------
export VHard_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = VHard_Phase_Deploiement
    Condition = VHard_Condition_CommandPhase
    Destination = VHard_Phase_Command
)
//-------------------------------------------------------------------------
export VHard_Transition_CommandPhaseToLastHope is TIAGeneralStrategyTransition
(
    Origine = VHard_Phase_Command
    Condition = VHard_Condition_OpponentWinning
    Destination = VHard_Phase_LastHope
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export VHard_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    GeneratorList =
    [
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export VHard_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [

        //------------------------------
        // Support & Supply
        //------------------------------

        ~/Supply_Skirmish,

        ~/CaptureOneObjectif,

        ~/FOB_Skirmish,

        ~/Attack_Objectives,
        ~/Defense_Corridor_AA,

        ~/Airstrike_AA,

        ~/Reco,            // 40 par reco

        //------------------------------
        // Attaques principales
        //------------------------------
        // Grosse attaque
        ~/Attack_Main,
        ~/Attack_Helo,

        ~/Defense_Corridor_AA,
        ~/Defense_Corridor_AA,

        ~/Airstrike_AirReco,

        ~/Artillery_TriggerHappy,        // Artillerie pas chère                 //  80

        // Reste du front
        ~/Attack_Adaptive,          // ~100
        ~/Attack_Adaptive,          // ~100

        ~/Attack_Adaptive,          // ~100
        ~/Attack_Filler,            // budget 40

        ~/Defense_Corridor_AA,
        ~/CaptureHalfZoneObjectif_Ceil,

        //------------------------------
        // Défenses Avions Arti
        //------------------------------
        ~/Airstrike_Assault,
        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/Defense_Corridor_Adaptive,
        ~/Defense_Corridor_Adaptive,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        ~/Artillery_Sniper,
        ~/Artillery_Expensive,

        ~/CaptureAllZoneObjectif,
    ] + ~/Defenses_Airstrike_Arti_Shuffle_Deploy

    //------------------------------
    // Scaling
    //------------------------------
    ScalingGeneratorList = ~/VeryHardScaling
)
//-------------------------------------------------------------------------
export VHard_Phase_Command is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Même chose que la phase de déploiement mais avec les Défenses
        // prioritaires sur les attaques. L'idée est de lancer ses attaques dès
        // le début, mais de renforcer les backlines avant de récupérer de nos pertes
        // Capture de zones

        //------------------------------
        // Support & Supply
        //------------------------------

        ~/Supply_Skirmish,

        ~/CaptureOneObjectif,

        ~/Attack_Objectives,
        ~/Defense_Corridor_AA,

        ~/Airstrike_AA,

        ~/Reco,

        //------------------------------
        // Attaques principales
        //------------------------------
        ~/Attack_Main,              // Plusieurs tanks pour un budget donné // 200
        ~/Attack_Helo,

        //------------------------------
        // Défenses prioritaires
        //------------------------------
        // Statiques pour tenir le terrain
        ~/Defense_Corridor_Adaptive, // Canon AT - Chasseur de char - ATInf - HMG - Flamer // 85
        ~/Defense_Corridor_AA,
        ~/Defense_Corridor_AA,

        ~/Airstrike_AirReco,

        ~/Defense_Corridor_Adaptive,
        ~/Defense_Corridor_Adaptive,

        //Cet mission lance les objectifs restant
        ~/Defense_Objectif,

        ~/Artillery_TriggerHappy,        // Artillerie pas chère                 //  80

        ~/Attack_Adaptive,  // Faire du code pour adapater le budget en fonction de l'income
        ~/Attack_Adaptive,
        ~/Attack_Filler,

        ~/Defense_Corridor_AA,
        ~/CaptureHalfZoneObjectif_Ceil,

        ~/Airstrike_Offense,

        //Low prio mais peut servir si on n'a pas de zone dans nos couloirs

        ~/Attack_Helo,

        //------------------------------
        // Défenses Avions Arti
        //------------------------------
        ~/Airstrike_Assault,

        ~/Defense_Corridor_AA,
        ~/Defense_Corridor_AA,

        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/Defense_Corridor_Adaptive,
        ~/Defense_Corridor_Adaptive,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        ~/Artillery_Sniper,
        ~/Artillery_Expensive,

        ~/CaptureAllZoneObjectif,
        ~/CaptureOneObjectifForAlliance,
        ~/Supply_Skirmish_Allied,

    ] + ~/Airstrike_Arti_Shuffle // On sort les défenses d'ici pour les mettre plus haut dans la prio

    //------------------------------
    // Scaling
    //------------------------------
    ScalingGeneratorList = ~/VeryHardScaling
)

//-------------------------------------------------------------------------
export VHard_Phase_LastHope is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Même chose que la phase de déploiement mais avec les Défenses
        // prioritaires sur les attaques. L'idée est de lancer ses attaques dès
        // le début, mais de renforcer les backlines avant de récupérer de nos pertes
        // Capture de zones
        ~/CaptureAllZoneObjectif,

        ~/Attack_Objectives,

        //------------------------------
        // Support & Supply
        //------------------------------

        ~/Supply_Skirmish,
        ~/Reco,


        ~/Attack_Filler,
        //------------------------------
        // Défenses prioritaires
        //------------------------------
        // Statiques pour tenir le terrain
        ~/Defense_Corridor_AA,

        ~/Airstrike_AirReco,

        //Cet mission lance les objectifs restant
        ~/Defense_Objectif,

        //------------------------------
        // Attaques principales
        //------------------------------
        ~/Attack_Main,              // Plusieurs tanks pour un budget donné // 200
        ~/Attack_Helo,
        ~/Artillery_TriggerHappy,        // Artillerie pas chère                 //  80

        ~/Attack_Adaptive,  // Faire du code pour adapater le budget en fonction de l'income
        ~/Attack_Adaptive,

        ~/Attack_Adaptive,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        //Low prio mais peut servir si on n'a pas de zone dans nos couloirs
        ~/CaptureOneObjectifForAlliance,

        ~/Attack_Helo,

        //------------------------------
        // Défenses Avions Arti
        //------------------------------
        ~/Airstrike_Assault,

        ~/Defense_Corridor_AA,
        ~/Defense_Corridor_AA,

        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        ~/Artillery_Sniper,
        ~/Artillery_Expensive,

        ~/Supply_Skirmish_Allied,

    ] + ~/Airstrike_Arti_Shuffle // On sort les défenses d'ici pour les mettre plus haut dans la prio

    //------------------------------
    // Scaling
    //------------------------------
    ScalingGeneratorList = ~/VeryHardScaling_Attack
)

VeryHardScaling is
[
    ~/Supply_Skirmish_Autogen,
    ~/Reco_Autogen,
    ~/Attack_Objectives_Autogen,

    ~/Attack_Main_Autogen,
    ~/Attack_Helo_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Attack_Adaptive_Autogen,

    ~/Airstrike_AirReco_Autogen,

    ~/Defense_Corridor_Adaptive_Autogen,
    ~/Defense_Corridor_Adaptive_Autogen,
    ~/Defense_Corridor_AA_Autogen,

    ~/Airstrike_Assault_Autogen,
    ~/Airstrike_Offense_Autogen,
    ~/Airstrike_AA_Autogen,

    ~/Artillery_TriggerHappy_Autogen,

    ~/Attack_Objectives_Allied_Autogen,
]

VeryHardScaling_Attack is
[
    ~/Reco_Autogen,
    ~/Attack_Objectives_Autogen,

    ~/Attack_Main_Autogen,
    ~/Attack_Helo_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Attack_Adaptive_Autogen,

    ~/Airstrike_Assault_Autogen,
    ~/Airstrike_Offense_Autogen,

    ~/Airstrike_AirReco_Autogen,
    ~/Airstrike_AA_Autogen,

    ~/Artillery_TriggerHappy_Autogen,
]
