//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export GenericStrategy is TIAGeneralStrategy
(
    StrategyName = "GenericAI"
    FirstGenerator = Gen_Choose_Strategy
    TransitionList =
    [
        Gen_Transition_StartToPhaseCommand,
        Gen_Transition_StartToGeneric,
        Gen_Transition_DeployToCommandPhase,
        Gen_Transition_CommandPhaseToLastHope,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export Gen_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export Gen_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)
//-------------------------------------------------------------------------
export Gen_Condition_OpponentWinning is Template_SequenceConditionOpponentHasScoreHigher
(
    ScorePointType = ~/EScorePointType/ZonePoint
    RatioValue = 0.7
)

//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export Gen_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = Gen_Choose_Strategy
    Condition = Gen_Condition_CommandPhase
    Destination = Gen_Phase_Command
)
export Gen_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = Gen_Choose_Strategy
    Condition = Gen_Condition_GotoGeneric
    Destination = Gen_Phase_Deploiement
)
//-------------------------------------------------------------------------
export Gen_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = Gen_Phase_Deploiement
    Condition = Gen_Condition_CommandPhase
    Destination = Gen_Phase_Command
)
//-------------------------------------------------------------------------
export Gen_Transition_CommandPhaseToLastHope is TIAGeneralStrategyTransition
(
    Origine = Gen_Phase_Command
    Condition = Gen_Condition_OpponentWinning
    Destination = Gen_Phase_LastHope
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export Gen_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    GeneratorList =
    [
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export Gen_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        //------------------------------
        // Support & Supply
        //------------------------------

        ~/Supply_Skirmish,

        ~/CaptureOneObjectif,

        ~/FOB_Skirmish,
        // Capture de zones
        ~/Attack_Objectives,

        ~/Defense_Corridor_AA,
        ~/Airstrike_AA,

        ~/Reco,            // 40 par reco

        //------------------------------
        // Attaques principales
        //------------------------------
        // Grosse attaque
        ~/Attack_Main,              // Plusieurs tanks pour un budget donné // 200 => un cmd pas cher, une reco pas cher, et le reste de tanks pour fill le budget en random.

        ~/Defense_Corridor_AA,
        ~/Defense_Corridor_AA,

        ~/Airstrike_AirReco,

        ~/Attack_Helo,
        ~/Defense_Corridor_AA,

        ~/Artillery_TriggerHappy,        // Artillerie pas chère                 //  80

        // Reste du front
        ~/Attack_Adaptive,          // ~100
        ~/Attack_Adaptive,          // ~100


        ~/Attack_Adaptive,          // ~100
        ~/Attack_Filler,            // budget 40

        //------------------------------
        // Défenses Avions Arti
        //------------------------------
        ~/Supply_Skirmish_Allied,
        ~/CaptureHalfZoneObjectif_Ceil,

    ] + ~/Defenses_Airstrike_Arti_Shuffle_Deploy

    //------------------------------
    // Scaling
    //------------------------------
    // Dans les difficultés supérieures il faudra surement maintenir
    // ça avec des maintain à la phase d'après pour être sur qu'on
    // produise de nouvelles unités
    ScalingGeneratorList = ~/Medium_Scaling
)
//-------------------------------------------------------------------------
export Gen_Phase_Command is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Même chose que la phase de déploiement mais avec les Défenses
        // prioritaires sur les attaques. L'idée est de lancer ses attaques dès
        // le début, mais de renforcer les backlines avant de récupérer de nos pertes
        // Capture de zones
        //------------------------------
        // Support & Supply
        //------------------------------

        ~/Supply_Skirmish,

        ~/CaptureOneObjectif,

        ~/Attack_Objectives,

        ~/Defense_Corridor_AA,
        ~/Airstrike_AA,

        ~/Reco,

        //------------------------------
        // Attaques principales
        //------------------------------
        ~/Attack_Main,              // Plusieurs tanks pour un budget donné // 200

        ~/Defense_Objectif,

        //------------------------------
        // Défenses prioritaires
        //------------------------------
        // Statiques pour tenir le terrain
        ~/Defense_Corridor_Adaptive, // Canon AT - Chasseur de char - ATInf - HMG - Flamer // 85

        ~/Defense_Corridor_Adaptive,
        ~/Defense_Corridor_Adaptive,

        ~/Defense_Corridor_AA,
        ~/Defense_Corridor_AA,

        ~/Airstrike_AirReco,

        ~/Attack_Helo,
        ~/Defense_Corridor_AA,

        ~/CaptureHalfZoneObjectif_Ceil,

        ~/Attack_Filler,

        ~/Artillery_TriggerHappy,        // Artillerie pas chère                 //  80

        ~/Attack_Adaptive,  // Faire du code pour adapater le budget en fonction de l'income
        ~/Attack_Adaptive,

        //Cet mission lance les objectifs restant
        ~/CaptureAllZoneObjectif,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        //Low prio mais peut servir si on n'a pas de zone dans nos couloirs
        ~/CaptureOneObjectifForAlliance,

        //------------------------------
        // Défenses Avions Arti
        //------------------------------
        ~/Supply_Skirmish_Allied,

    ] + ~/Airstrike_Arti_Shuffle // On sort les défenses d'ici pour les mettre plus haut dans la prio

    //------------------------------
    // Scaling
    //------------------------------
    // Dans les difficultés supérieures il faudra surement maintenir
    // ça avec des maintain à la phase d'après pour être sur qu'on
    // produise de nouvelles unités
    ScalingGeneratorList = ~/Medium_Scaling
)

//-------------------------------------------------------------------------
export Gen_Phase_LastHope is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        // Même chose que la phase de déploiement mais avec les Défenses
        // prioritaires sur les attaques. L'idée est de lancer ses attaques dès
        // le début, mais de renforcer les backlines avant de récupérer de nos pertes
        // Capture de zones
        ~/CaptureAllZoneObjectif,

        ~/Attack_Objectives,

        //------------------------------
        // Support & Supply
        //------------------------------
        ~/Supply_Skirmish,

        ~/Reco,

        //------------------------------
        // Défenses prioritaires
        //------------------------------
        ~/Defense_Corridor_AA,
        ~/Defense_Corridor_AA,

        ~/Airstrike_AirReco,

        ~/Attack_Filler,

        //------------------------------
        // Attaques principales
        //------------------------------
        ~/Attack_Main,
        ~/Attack_Main,
        ~/Attack_Main,
        ~/Attack_Helo,
        ~/Artillery_TriggerHappy,        // Artillerie pas chère                 //  80

        ~/Attack_Adaptive,  // Faire du code pour adapater le budget en fonction de l'income
        ~/Attack_Adaptive,

        ~/Airstrike_Offense,
        ~/Airstrike_AA,

        //------------------------------
        // Défenses Avions Arti
        //------------------------------
        ~/Supply_Skirmish_Allied,

    ] + ~/Airstrike_Arti_Shuffle // On sort les défenses d'ici pour les mettre plus haut dans la prio

    //------------------------------
    // Scaling
    //------------------------------
    // Dans les difficultés supérieures il faudra surement maintenir
    // ça avec des maintain à la phase d'après pour être sur qu'on
    // produise de nouvelles unités
    ScalingGeneratorList = ~/Medium_Scaling_Attack
)

// Le concept de ces blocs de missions
// est d'à la fois défendre ses arrières
// mais aussi de faire usage d'unités diverses
// (artillerie et avions) pour débloquer
// les situations et appuyer le reste des missions
Defenses_Airstrike_Arti_Shuffle_Deploy is
[
    ~/Defense_Corridor_Adaptive,
    ~/Airstrike_Assault,
    ~/Artillery_Expensive,
    ~/Defense_Corridor_Adaptive,
    ~/Defense_Corridor_AA,
    ~/Defense_Corridor_AA,
    ~/Defense_Corridor_Adaptive,
    ~/Airstrike_Offense,
    ~/Airstrike_AA,
    ~/Artillery_TriggerHappy_1for4Corridors,

]

Airstrike_Arti_Shuffle is
[
    ~/Artillery_TriggerHappy_1for4Corridors,
    ~/Airstrike_Assault,
    ~/Artillery_Support,
    ~/Airstrike_SEAD,
    ~/Artillery_Expensive,
]

Medium_Scaling is
[
    ~/Supply_Skirmish_Autogen,
    ~/Reco_Autogen,
    ~/Attack_Main_Autogen,
    ~/Attack_Helo_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Defense_Corridor_Adaptive_Autogen,
    ~/Defense_Corridor_AA_Autogen,
    ~/Defense_Corridor_Adaptive_Autogen,
    ~/Airstrike_AirReco_Autogen,
    ~/Airstrike_AA_Autogen,
    ~/Defense_Corridor_Adaptive_Autogen,
    ~/Defense_Corridor_AA_Autogen,
    ~/Artillery_TriggerHappy_Autogen,
    ~/Airstrike_Offense_Autogen,
    ~/Artillery_TriggerHappy_Autogen,
    ~/Attack_Objectives_Autogen,
    ~/Artillery_Expensive_Autogen,
    ~/Airstrike_Assault_Autogen,
    ~/Attack_Objectives_Allied_Autogen,
]

Medium_Scaling_Attack is
[
    ~/Reco_Autogen,
    ~/Attack_Main_Autogen,
    ~/Attack_Helo_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Attack_Adaptive_Autogen,
    ~/Airstrike_AirReco_Autogen,
    ~/Airstrike_AA_Autogen,
    ~/Defense_Corridor_AA_Autogen,
    ~/Artillery_TriggerHappy_Autogen,
    ~/Airstrike_Offense_Autogen,
    ~/Artillery_TriggerHappy_Autogen,
    ~/Attack_Objectives_Autogen,
    ~/Artillery_Expensive_Autogen,
    ~/Airstrike_Assault_Autogen,
]

