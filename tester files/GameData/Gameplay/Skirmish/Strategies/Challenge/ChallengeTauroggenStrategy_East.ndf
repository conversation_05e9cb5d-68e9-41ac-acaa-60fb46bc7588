//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export ChallengeTauroggenStrategy_East is TIAGeneralStrategy
(
    StrategyName = "TaurEAstAI"
    FirstGenerator = ChalTauroEast_Choose_Strategy
    AuthorizedCorridorList = [2]
    TransitionList =
    [
        ChalTauroEast_Transition_StartToPhaseCommand,
        ChalTauroEast_Transition_StartToGeneric,
        ChalTauroEast_Transition_DeployToCommandPhase,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalTauroEast_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export ChalTauroEast_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)

//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalTauroEast_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = ChalTauroEast_Choose_Strategy
    Condition = ChalTauroEast_Condition_CommandPhase
    Destination = ChalTauroEast_Phase_Command
)
export ChalTauroEast_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = ChalTauroEast_Choose_Strategy
    Condition = ChalTauroEast_Condition_GotoGeneric
    Destination = ChalTauroEast_Phase_Deploiement
)
//-------------------------------------------------------------------------
export ChalTauroEast_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = ChalTauroEast_Phase_Deploiement
    Condition = ChalTauroEast_Condition_CommandPhase
    Destination = ChalTauroEast_Phase_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export ChalTauroEast_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    GeneratorList =
    [
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export ChalTauroEast_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        ~/Challenge_Main_Attack_All,
    ]
)
//-------------------------------------------------------------------------
export ChalTauroEast_Phase_Command is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        ~/Challenge_Main_Attack_All,
        ~/CaptureAllZoneObjectif,
    ]
)
