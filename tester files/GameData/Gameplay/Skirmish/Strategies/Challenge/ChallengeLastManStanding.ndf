//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export ChallengeTheDieburgSalientStrategy is TIAGeneralStrategy
(
    StrategyName = "ChalLMSAI"
    FirstGenerator = ChalTheDieburgSalient_Choose_Strategy
    AllowToByPassNbMacroActionAllowedToProduceSimultaneous = true
    TransitionList =
    [
        ChalTheDieburgSalient_Transition_StartToPhaseCommand,
        ChalTheDieburgSalient_Transition_StartToGeneric,
        ChalTheDieburgSalient_Transition_DeployToCommandPhase,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalTheDieburgSalient_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export ChalTheDieburgSalient_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)

//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalTheDieburgSalient_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = ChalTheDieburgSalient_Choose_Strategy
    Condition = ChalTheDieburgSalient_Condition_CommandPhase
    Destination = ChalTheDieburgSalient_Phase_Command
)
export ChalTheDieburgSalient_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = ChalTheDieburgSalient_Choose_Strategy
    Condition = ChalTheDieburgSalient_Condition_GotoGeneric
    Destination = ChalTheDieburgSalient_Phase_Deploiement
)
//-------------------------------------------------------------------------
export ChalTheDieburgSalient_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = ChalTheDieburgSalient_Phase_Deploiement
    Condition = ChalTheDieburgSalient_Condition_CommandPhase
    Destination = ChalTheDieburgSalient_Phase_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export ChalTheDieburgSalient_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    NbMacroActionAllowedToProduceSimultaneous = 9001
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export ChalTheDieburgSalient_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 4
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,

        ~/Challenge_CaptureOneObjectif_LowCost, //Dependant du deck estimation 125

        ~/Attack_Main, //Environ 400

        ~/Airstrike_Assault,
        ~/Airstrike_Offense,

        ~/Attack_Helo, //Environ 250

        ~/Defense_Objectif, //Chere mais dépendant du nombre d'objectif et de la capture

        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/Challenge_CaptureAllZoneObjectif,

        ~/Artillery_Expensive,
    ]
    ScalingGeneratorList = ~/Challenge_TheDieburgSalient_Scaling
)
//-------------------------------------------------------------------------
export ChalTheDieburgSalient_Phase_Command is TSequenceGeneratorDescriptor
(
    NbMacroActionAllowedToProduceSimultaneous = 3
    GeneratorList =
    [
        ~/Support_Transports,
        ~/Supply_Skirmish,

        ~/Challenge_CaptureOneObjectif_LowCost, //Dependant du deck estimation 125

        ~/Attack_Main, //Environ 400

        ~/Airstrike_Assault,
        ~/Airstrike_Offense,

        ~/Attack_Helo, //Environ 250

        ~/Defense_Objectif, //Chere mais dépendant du nombre d'objectif et de la capture

        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/Challenge_CaptureAllZoneObjectif,

        ~/Artillery_Expensive,
        ~/Attack_Adaptive,          // Environ 200
    ]
    ScalingGeneratorList = ~/Challenge_TheDieburgSalient_Scaling
)

Challenge_TheDieburgSalient_Scaling is
[
    ~/Attack_Main_Autogen,
    ~/Attack_Helo_Autogen,

    ~/Attack_Adaptive_Autogen,

    ~/Airstrike_Assault_Autogen,
    ~/Airstrike_Offense_Autogen,
    ~/Airstrike_AA_Autogen,

    ~/Artillery_TriggerHappy_Autogen,
]
