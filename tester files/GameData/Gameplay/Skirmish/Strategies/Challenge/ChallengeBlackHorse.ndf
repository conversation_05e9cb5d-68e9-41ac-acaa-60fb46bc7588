//-------------------------------------------------------------------------
//----------------------------- STRATEGY ----------------------------------
//-------------------------------------------------------------------------
export ChallengeBlackHorse is TIAGeneralStrategy
(
    StrategyName = "BlaHorseAI"
    FirstGenerator = ChalBlackHorse_Choose_Strategy
    TransitionList =
    [
        ChalBlackHorse_Transition_StartToPhaseCommand,
        ChalBlackHorse_Transition_StartToGeneric,
        ChalBlackHorse_Transition_DeployToCommandPhase,
    ]
)

//-------------------------------------------------------------------------
//---------------------------- CONDITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalBlackHorse_Condition_GotoGeneric is TSequenceCondition_True
(
)
//-------------------------------------------------------------------------
export ChalBlackHorse_Condition_CommandPhase is TSequenceCondition_Phase
(
    PhaseType = CommandPhase
)

//-------------------------------------------------------------------------
//--------------------------- TRANSITIONS ---------------------------------
//-------------------------------------------------------------------------
export ChalBlackHorse_Transition_StartToPhaseCommand is TIAGeneralStrategyTransition
(
    Origine = ChalBlackHorse_Choose_Strategy
    Condition = ChalBlackHorse_Condition_CommandPhase
    Destination = ChalBlackHorse_Phase_Command
)
export ChalBlackHorse_Transition_StartToGeneric is TIAGeneralStrategyTransition
(
    Origine = ChalBlackHorse_Choose_Strategy
    Condition = ChalBlackHorse_Condition_GotoGeneric
    Destination = ChalBlackHorse_Phase_Deploiement
)
//-------------------------------------------------------------------------
export ChalBlackHorse_Transition_DeployToCommandPhase is TIAGeneralStrategyTransition
(
    Origine = ChalBlackHorse_Phase_Deploiement
    Condition = ChalBlackHorse_Condition_CommandPhase
    Destination = ChalBlackHorse_Phase_Command
)

//-------------------------------------------------------------------------
//------------------------------ PHASES -----------------------------------
//-------------------------------------------------------------------------
export ChalBlackHorse_Choose_Strategy is TSequenceGeneratorDescriptor
(
    // Fake phase qui permet juste d'orienter la strategie dans la direction voulue
    GeneratorList =
    [
        ~/Supply_Skirmish,
    ]
)
//-------------------------------------------------------------------------
export ChalBlackHorse_Phase_Deploiement is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        ~/CaptureOneObjectif, //Dependant du deck estimation 125

        ~/Defense_Objectif, //Chere mais dépendant du nombre d'objectif et de la capture

        ~/Attack_Objectives, //Environ 300

        ~/Attack_Main, //Environ 400

        ~/Reco_Autogen,
        ~/Blackhorse_Side_Attack,          // Environ 200

        ~/Airstrike_Assault,
        ~/Airstrike_Offense,

        ~/Attack_Helo, //Environ 250

        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/CaptureAllZoneObjectif,

        ~/Artillery_Expensive,

        ~/Airstrike_AA,
    ]
)
//-------------------------------------------------------------------------
export ChalBlackHorse_Phase_Command is TSequenceGeneratorDescriptor
(
    GeneratorList =
    [
        ~/CaptureOneObjectif, //Dependant du deck estimation 125

        ~/Defense_Objectif, //Chere mais dépendant du nombre d'objectif et de la capture

        ~/Attack_Objectives, //Environ 300

        ~/Attack_Main, //Environ 400

        ~/Reco_Autogen,
        ~/Blackhorse_Side_Attack,          // Environ 200

        ~/Airstrike_Assault,
        ~/Airstrike_Offense,

        ~/Attack_Helo, //Environ 250

        ~/Artillery_TriggerHappy_1for2Corridors,

        ~/CaptureAllZoneObjectif,

        ~/Artillery_Expensive,
        ~/Blackhorse_Side_Attack,          // Environ 200

        ~/Airstrike_AA,
    ]
)
