
template InterfaceSound
[
    FileName  : string
] is TSoundDescriptor
(
    SoundSettings = $/SoundSettings/HUD_SoundSettings
    TheSoundStream = TSoundStream ( FileName = <FileName> )
)

// changer ces deux valeurs pour activer ou non les sons des boutons du cube action
CubeActionButtonHasClickSound is true
CubeActionButtonHasHoverSound is false

CubeActionButtonClickSound is InterfaceSound( FileName = "GameData:/Assets/Sounds/Hud/ClicBoutonCube.wav" )
CubeActionButtonHoverSound is InterfaceSound( FileName = "GameData:/Assets/Sounds/Hud/SurvolBoutonCube.wav" )

export CubeActionButtonHoverSound_IfEnabled is TEugBPBaseClass(Value = CubeActionButtonHasHoverSound ? CubeActionButtonHoverSound : nil)
export CubeActionButtonClickSound_IfEnabled is TEugBPBaseClass(Value = CubeActionButtonHasClickSound ? CubeActionButtonClickSound : nil)

template Template_CubeActionOrderButtonInformationsDescriptor_WithSound
[
    HoverSound : TSoundDescriptor = CubeActionButtonHoverSound,
    LeftClickSound : TSoundDescriptor = CubeActionButtonClickSound,

    HoverSoundFileName = nil,
    ClickSoundFileName = nil,

    CubeActionName,
    DebugName,
    CubeActionFunctionType,

    SpotlightUniqueName = "",

    ButtonTextToken,
    HintTitleToken,
    HintBodyToken,
    HintExtendedToken,
    HintDico,

    OrderToken,
    ActionType,
    ValidationTargetToken = '',

    Mapping,
]
is TCubeActionOrderButtonInformationsDescriptor
(
    ButtonTextToken = <ButtonTextToken>
    HintDico = <HintDico>
    HintTitleToken = <HintTitleToken>
    HintBodyToken = <HintBodyToken>
    HintExtendedToken = <HintExtendedToken>

    BackgroundTexture        = '' //Plus branche cote code, on peut le remettre facilement a la place des textes si necessaire

    HoverSound               = (<HoverSoundFileName> == nil) ? <HoverSound> : InterfaceSound( FileName = <HoverSoundFileName> )
    ClickSound               = (<ClickSoundFileName> == nil) ? <LeftClickSound> : InterfaceSound( FileName = <ClickSoundFileName> )

    Mapping                  = <Mapping>

    CubeActionName           = <CubeActionName>
    DebugName                = <DebugName>
    CubeActionFunctionType   = <CubeActionFunctionType>

    SpotlightUniqueName      = <SpotlightUniqueName>

    OrderToken               = <OrderToken>
    ActionType               = <ActionType>
    ValidationTargetToken    = <ValidationTargetToken>
)

template Template_CubeActionFunctionalityButtonInformationsDescriptor_WithSound
[
    HoverSound : TSoundDescriptor = CubeActionButtonHasHoverSound ? CubeActionButtonHoverSound : nil,
    LeftClickSound : TSoundDescriptor = CubeActionButtonHasClickSound ? CubeActionButtonClickSound : nil,

    HoverSoundFileName = nil,
    ClickSoundFileName = nil,

    CubeActionName,
    DebugName,
    CubeActionFunctionType,

    ButtonTextToken,
    HintTitleToken,
    HintBodyToken,
    HintExtendedToken,
    HintDico,
    HintElements = [],

    FunctionalityType,

    Mapping,
]
is TCubeActionFunctionalityButtonInformationsDescriptor
(
    ButtonTextToken = <ButtonTextToken>
    HintDico = <HintDico>
    HintTitleToken = <HintTitleToken>
    HintBodyToken = <HintBodyToken>
    HintExtendedToken = <HintExtendedToken>
    HintElements = <HintElements>

    BackgroundTexture        = '' //Plus branche cote code, on peut le remettre facilement a la place des textes si necessaire

    HoverSound               = (<HoverSoundFileName> == nil) ? <HoverSound> : InterfaceSound( FileName = <HoverSoundFileName> )
    ClickSound               = (<ClickSoundFileName> == nil) ? <LeftClickSound> : InterfaceSound( FileName = <ClickSoundFileName> )

    Mapping                  = <Mapping>

    CubeActionName           = <CubeActionName>
    DebugName                = <DebugName>
    CubeActionFunctionType   = <CubeActionFunctionType>

    FunctionalityType        = <FunctionalityType>
)

export CubeAction_Order_Attack is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Attack"
    DebugName                = "InGameCubeAction_ButtonAttack"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_ATTACK'
    HintTitleToken           = 'ORD_ATTACK'
    HintBodyToken            = 'HNT_ATTACK'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "MoveAndAttack"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALTAR'
    Mapping                  = $/KeyboardOption/Mapping_Attack
)

export CubeAction_Order_FastMoveAndAttack is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_FastMoveAndAttack"
    DebugName                = "InGameCubeAction_ButtonFastMoveAndAttack"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_FSTATK'
    HintTitleToken           = 'ORD_FSTATK'
    HintBodyToken            = 'HNT_FSTATK'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "FastMoveAndAttack"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALTAR'
    Mapping                  = $/KeyboardOption/Mapping_FastMoveAndAttack
)

export CubeAction_Order_Stop is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Stop"
    DebugName                = "InGameCubeAction_ButtonStop"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_STOP'
    HintTitleToken           = 'ORD_STOP'
    HintBodyToken            = 'HNT_STOP'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "Stop"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_Stop
)

export CubeAction_Order_AIDefend is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_AIDefend"
    DebugName                = "InGameCubeAction_ButtonAIDefend"

    CubeActionFunctionType   = TacticCubeActionFunctionType/SmartOrders

    ButtonTextToken          = 'ORD_AIDEF'
    HintTitleToken           = 'ORD_AIDEF'
    HintBodyToken            = 'HNT_AIDEF'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "AIDefend"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_AIDefense
)

export CubeAction_Order_AIManageArtillery_Focus is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_AIManageArtillery_Focus"
    DebugName                = "InGameCubeAction_AIManageArtillery_Focus"

    CubeActionFunctionType   = TacticCubeActionFunctionType/SmartOrders

    ButtonTextToken          = 'ORD_AIARF'
    HintTitleToken           = 'ORD_AIARF'
    HintBodyToken            = 'HNT_AIARF'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "AIManageArtillery_Focus"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_AIManageArtillery_Focus
)

export CubeAction_Order_AIManageArtillery_CounterBattery is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_AIManageArtillery_CounterBattery"
    DebugName                = "InGameCubeAction_AIManageArtillery_CounterBattery"

    CubeActionFunctionType   = TacticCubeActionFunctionType/SmartOrders

    ButtonTextToken          = 'ORD_AIARC'
    HintTitleToken           = 'ORD_AIARC'
    HintBodyToken            = 'HNT_AIARC'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    SpotlightUniqueName      = "Spotlight_CB"

    OrderToken               = "AIManageArtillery_CounterBattery"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_AIManageArtillery_CounterBattery
)

export CubeAction_Order_AIManageArtillery_Auto is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_AIManageArtillery_Auto"
    DebugName                = "InGameCubeAction_AIManageArtillery_Auto"

    CubeActionFunctionType   = TacticCubeActionFunctionType/SmartOrders

    ButtonTextToken          = 'ORD_AIARA'
    HintTitleToken           = 'ORD_AIARA'
    HintBodyToken            = 'HNT_AIARA'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "AIManageArtillery_Auto"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_AIManageArtillery_Auto
)

export CubeAction_Order_AIAirplaneAutoManage is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_AIAirplaneAutoManage"
    DebugName                = "InGameCubeAction_AIAirplaneAutoManage"

    CubeActionFunctionType   = TacticCubeActionFunctionType/SmartOrders

    ButtonTextToken          = 'ORD_AIAAA'
    HintTitleToken           = 'ORD_AIAAA'
    HintBodyToken            = 'HNT_AIAAA'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "AIAirplaneAutoManage"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_AIAirplaneAutoManage
)

export CubeAction_Order_AISupply is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_AISupply"
    DebugName                = "InGameCubeAction_AISupply"

    CubeActionFunctionType   = TacticCubeActionFunctionType/SmartOrders

    ButtonTextToken          = 'ORD_AISUP'
    HintTitleToken           = 'ORD_AISUP'
    HintBodyToken            = 'HNT_AISUP'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "AISupply"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_AISupply
)

export CubeAction_Order_AIStop is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_AIStop"
    DebugName                = "InGameCubeAction_AIStop"

    CubeActionFunctionType   = TacticCubeActionFunctionType/SmartOrders

    ButtonTextToken          = 'ORD_AISTO'
    HintTitleToken           = 'ORD_AISTO'
    HintBodyToken            = 'HNT_AISTO'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "AIStop"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_AIStop
)

export CubeAction_Order_AIAttack is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_AIAttack"
    DebugName                = "InGameCubeAction_ButtonAIAttack"

    CubeActionFunctionType   = TacticCubeActionFunctionType/SmartOrders

    ButtonTextToken          = 'ORD_AIATT'
    HintTitleToken           = 'ORD_AIATT'
    HintBodyToken            = 'HNT_AIATT'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "AIAttack"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALTAR'
    Mapping                  = $/KeyboardOption/Mapping_AIAttack
)

export CubeAction_Order_ShootOnPosition is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Shoot"
    DebugName                = "InGameCubeAction_ButtonShoot"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_SHOPOS'
    HintTitleToken           = 'ORD_SHOPOS'
    HintBodyToken            = 'HNT_SHOPOS'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    SpotlightUniqueName      = "Spotlight_FirePos"

    OrderToken               = "ShootOnPosition"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALPOS'
    Mapping                  = $/KeyboardOption/Mapping_ShootOnPosition
)

export CubeAction_Order_ShootOnPositionSmoke is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Smoke"
    DebugName                = "InGameCubeAction_ButtonSmoke"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_SMOKE'
    HintTitleToken           = 'ORD_SMOKE'
    HintBodyToken            = 'HNT_SMOKE'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    SpotlightUniqueName      = "Spotlight_SmokePos"

    OrderToken               = "ShootOnPositionSmoke"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALPOS'
    Mapping                  = $/KeyboardOption/Mapping_Smoke
)

export CubeAction_Order_ShootDefensiveSmoke is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_DefensiveSmoke"
    DebugName                = "InGameCubeAction_ButtonDefensiveSmoke"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_SMOKD'
    HintTitleToken           = 'ORD_SMOKD'
    HintBodyToken            = 'HNT_SMOKD'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "ShootDefensiveSmoke"
    ActionType               = OrderInstant
    ValidationTargetToken    = 'CA_VALPOS'
    Mapping                  = $/KeyboardOption/Mapping_Smoke
)

export CubeAction_Order_Move is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Move"
    DebugName                = "InGameCubeAction_ButtonMove"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_MOVE'
    HintTitleToken           = 'ORD_MOVE'
    HintBodyToken            = 'HNT_MOVE'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    SpotlightUniqueName      = "Spotlight_move"

    OrderToken               = "Move"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALPOS'
    Mapping                  = $/KeyboardOption/Mapping_Move
)

export CubeAction_Order_QuickMove is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_QuickMove"
    DebugName                = "InGameCubeAction_ButtonQMove"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_QKMOVE'
    HintTitleToken           = 'ORD_QKMOVE'
    HintBodyToken            = 'HNT_QKMOVE'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    SpotlightUniqueName      = "Spotlight_movefast"

    OrderToken               = "QuickMove"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALPOS'
    Mapping                  = $/KeyboardOption/Mapping_QuickMove
)

export CubeAction_Order_SmartMove is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_SmartMove"
    DebugName                = "InGameCubeAction_ButtonSmartMove"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_SMMOVE'
    HintTitleToken           = 'ORD_SMMOVE'
    HintBodyToken            = 'HNT_SMMOVE'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "SmartMove"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALPOS'
    Mapping                  = $/KeyboardOption/Mapping_SmartMove
)

export CubeAction_Order_UnloadFromTransport is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_UnloadFromTransport"
    DebugName                = "InGameCubeAction_ButtonUnloadFromTransport"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_UNLDFT'
    HintTitleToken           = 'ORD_UNLDFT'
    HintBodyToken            = 'HNT_UNLDFT'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "UnloadFromTransport"
    ActionType               = OrderInstant
    ValidationTargetToken    = 'CA_VALBOT'
    Mapping                  = $/KeyboardOption/Mapping_UnloadFromTransport
)

export CubeAction_Order_UnloadAtPosition is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_UnloadAtPosition"
    DebugName                = "InGameCubeAction_ButtonUnloadAtPosition"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_UNLDAT'
    HintTitleToken           = 'ORD_UNLDAT'
    HintBodyToken            = 'HNT_UNLDAT'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "UnloadAtPosition"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALBOT'
    Mapping                  = $/KeyboardOption/Mapping_UnloadAtPosition
)

export CubeAction_Order_Evacuate is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Evac"
    DebugName                = "InGameCubeAction_ButtonEvac"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_EVAC'
    HintTitleToken           = 'ORD_EVAC'
    HintBodyToken            = 'HNT_EVAC'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "AirplaneEvacuate"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_Evacuate
)

export CubeAction_Order_Land is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Land"
    DebugName                = "InGameCubeAction_ButtonLand"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_LAND'
    HintTitleToken           = 'ORD_LAND'
    HintBodyToken            = 'HNT_LAND'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "Land"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_Land
)

export CubeAction_Order_ChangeAltitude is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_ChangeAltitude"
    DebugName                = "InGameCubeAction_ButtonChangeAltitude"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_CHGALT'
    HintTitleToken           = 'ORD_CHGALT'
    HintBodyToken            = 'HNT_CHGALT'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "ChangeAltitude"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_ChangeAltitude
)

export CubeAction_Order_Sell is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Sell"
    DebugName                = "InGameCubeAction_ButtonSell"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_SELL'
    HintTitleToken           = 'ORD_SELL'
    HintBodyToken            = 'HNT_SELL'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    SpotlightUniqueName      = "Spotlight_sellButton"

    OrderToken               = "Sell"
    ActionType               = OrderInstant
    Mapping                  = $/KeyboardOption/Mapping_Sell
)

export CubeAction_Order_Reverse is Template_CubeActionOrderButtonInformationsDescriptor_WithSound
(
    CubeActionName           = "CubeAction_Order_Reverse"
    DebugName                = "InGameCubeAction_ButtonReverse"

    CubeActionFunctionType   = TacticCubeActionFunctionType/Orders

    ButtonTextToken          = 'ORD_RVRSE'
    HintTitleToken           = 'ORD_RVRSE'
    HintBodyToken            = 'HNT_RVRSE'
    HintExtendedToken        = ''
    HintDico                 = ~/LocalisationConstantes/dico_interface_ingame

    OrderToken               = "Reverse"
    ActionType               = OrderWithClickValidation
    ValidationTargetToken    = 'CA_VALPOS'
    Mapping                  = $/KeyboardOption/Mapping_Reverse
)


// Synchro avec enum Functionalities cf CubeActionModuleDescriptor.h
Functionality_RiposteStance    is 9
Functionality_AutoCover        is 10

export CubeAction_Functionality_RiposteStance is Template_CubeActionFunctionalityButtonInformationsDescriptor_WithSound
(
    CubeActionName          = "CubeAction_Order_RiposteStance"
    DebugName               = "InGameCubeAction_ButtonRiposteStance"

    CubeActionFunctionType = ~/TacticCubeActionFunctionType/TogglableOrders

    ButtonTextToken         = 'ORD_RIPOS'
    HintTitleToken          = 'ORD_RIPOS'
    HintBodyToken           = 'HNT_RIPOS'
    HintExtendedToken       = ''
    HintDico                = ~/LocalisationConstantes/dico_interface_ingame

    FunctionalityType       = Functionality_RiposteStance
    Mapping                  = $/KeyboardOption/Mapping_RiposteStance
)

//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------

export CubeAction_Menu_Ghost is TCubeActionMenuDescriptor
(
    CubeActionName    = "Menu_Ghost"
    CubeActionContent = MAP [
        (0, [
            ~/CubeAction_Order_Stop,
        ])
    ]
)
