export influenceMapsTactic is TInfluenceMapDescriptors
(
    ZoneInfluenceMapDescriptor = TInfluenceMapConstantesDescriptor
    (
        TailleDeCaseGRU = 177 // Attention impacte lourdement sur les performances

        DefaultDecay = 0.001
        DefaultMomentum = 0.3
        MinValueInfluenceForOwnership = 0.4

        UseMinValueForEmptyOwnerCell = true
    )
)

export influenceMapsStrategic is TInfluenceMapDescriptors
(
    InfluenceMapDescriptor = TInfluenceMapConstantesDescriptor
    (
        TailleDeCaseGRU = 71

        // pour plus d'info --> https://confluence.eugennet.com/pages/viewpage.action?pageId=29364229
        DefaultDecay = 0.2
        DefaultMomentum = 0.9
        MinValueInfluenceForOwnership = 0.3

        UseMinValueForEmptyOwnerCell = false
    )
)
