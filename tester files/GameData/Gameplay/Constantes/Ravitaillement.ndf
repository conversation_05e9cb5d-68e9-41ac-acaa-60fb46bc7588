export StandardSupply is TSupplyDescriptor
(
    DefaultSupplyRangeGRU = 442

    // Tous les ravitaillement sont découpé en deux variables
    // 1 -> SupplyBySecond => Nombre de point de la ressource concerné ravitaillé par seconde
    // 2 -> SupplyCostBySecond => Le nombre de point de ravitaillement que coûte le ravitaillement ci dessus.
    //
    // Exemple :
    // FuelSupplyBySecond = 1
    // FuelSupplyCostBySecond = 10
    //
    // Une unité ravitaillée en essence recevra un point d'essence par seconde, et ce ravitaillement
    // coûtera 10 points de ravitaillement a la FOB

    // Point d'essence ravitaillés par seconde
    FuelSupplyBySecond = 30
    // Cout du ravitaillement d'essence par seconde
    FuelSupplyCostBySecond =  1.5

    // Point de vie ravitaillés par seconde
    HealthSupplyBySecond = 0.10 //0.00001
    // Cout du ravitaillement de point de vie par seconde
    HealthSupplyCostBySecond = 3 // 0.00001

    // Point de ravitaillement ravitaillés par seconde
    SupplySupplyBySecond = 20 //30
    // Cout du ravitaillement de point de ravitaillement par seconde
    SupplySupplyCostBySecond = 20 //30

    // Point de munition ravitaillés par seconde
    AmmunitionSupplyBySecond = 60

    // Points de "critique" réparés par seconde
    CriticsSupplyBySecond = 10
    // Coût de la réparation de "critique" par seconde
    CriticsSupplyCostBySecond = 20

    FeedbackDrawer = ~/SupplyFeedbackDrawer
)

SupplyFeedbackDrawer is TSupplyFeedbackDrawer
(
    LineColor = RGBA[244, 187, 50, 255]
    ZOffset = 250.0
    LineThickness = 600.0
    FeedbackScreenResilienceDuration = 3.0
    FeedbackFadeOutTime = 1.0
)
