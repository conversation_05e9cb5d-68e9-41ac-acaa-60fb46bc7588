private template ImpactWrapper
[
    Action
]
is TSequencingActionHappening
(
    ActionDescriptor = TSimultaneousAction
    (
        LocalVariables =
        [
            parPosition is TPinnableValue(ExpectedType: float3),
            parDirection is TPinnableValue(ExpectedType: float3),
            parRadiusPhysical is TPinnableValue(ExpectedType: float InitialValue = 0),
            parRadiusSuppress is TPinnableValue(ExpectedType: float InitialValue = 0),
            parTerrain is TPinnableValue(ExpectedType: float),
        ]
        Mobile = TMobileWithLocalRepereMatrixFactory
        (
            Position = parPosition
            Orientation = -parDirection
            InitialScale = 1
            MaxScale = 1
            UsePreviousMobileAsCenterOfScale = false
            ForceIntroduceCentroid = true
            CentroidRelative = false
            UseMobileScale = true
        )
        Actions =
        [
            TActionDescriptorScaleMobile_PixelConstant
            (
                Camera = $/M3D/Misc/CameraPrincipale
                ScaleFactor = ~/PixelConstantValuesSpecific/ImpactFactor
                ScaleMinReferenceAltitude = ~/PixelConstantValuesSpecific/ImpactMinReferenceAltitude
                ScaleMax = ~/PixelConstantValuesSpecific/ImpactScaleMax
                ScaleOption = $/GUIOption/UnitScalingEnabled

                PeriodInFrames = 1
                DurationInSec = 20
            ),
            TActionCall
            (
                Action = <Action>

                NamedParams = MAP[
                    ('parPosition', float3[0, 0, 0]),
                    ('parDirection', float3[1,0,0]),
                    ('parRadiusPhysical', parRadiusPhysical),
                    ('parRadiusSuppress', parRadiusSuppress),
                    ('parTerrain', parTerrain),
                ]
            )
        ]
    )
)
