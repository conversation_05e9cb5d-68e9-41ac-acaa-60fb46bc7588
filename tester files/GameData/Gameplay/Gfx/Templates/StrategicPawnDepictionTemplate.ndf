// Utiliser ce template pour les pions des unités stratégiques dans Steelman
template StrategicPawnDepictionDesc
[
    MeshSocle,
    MeshTige,
    MeshDescriptorPawn,
    SubDepictionGenerators = []
]
is TDepictionDescriptor
(
    Scaler = TConstantScaler( Scale = 6)
    Selector = OnlyHighDepictionSelector
    Operators = [DepictionOperator_Shader_Selection, DepictionOperator_Shader_GhostPawn, StrategicMoveSoundOperator ]
    DepictionAlternatives = [ DepictionVisual_LOD_High( MeshDescriptor = <MeshSocle> ) ]

    SubDepictions = [ TSubDepiction
    (
        Selectable = True
        Depiction = TDepictionDescriptor
        (
            Selector = OnlyHighDepictionSelector
            DepictionAlternatives = [ DepictionVisual_LOD_High( MeshDescriptor = <MeshTige> ) ]

            SubDepictions = [ TSubDepiction
            (
                Selectable = True
                Anchors = ["sommet"]
                Depiction = TDepictionDescriptor
                (
                    Selector = OnlyHighDepictionSelector
                    DepictionAlternatives = [
                        DepictionVisual_LOD_High( MeshDescriptor = <MeshDescriptorPawn> )
                    ]
                    SubDepictionGenerators = <SubDepictionGenerators>
                )
            ) ]
        )
    ) ]
)

StrategicInfantryPoseOperator is TCosmeticFreezeSkeletalAnimationOperatorDesc
(
    Animation = $/GFX/DepictionResources/FusilierRun
    Cursor = 0.5
)

template StrategicInfantryPawnDepictionDesc
[
    MeshSocle,
    MeshDescriptorPawn,
]
is TDepictionDescriptor
(
    Scaler = TConstantScaler( Scale = 7)
    Selector = OnlyHighDepictionSelector
    Operators = [DepictionOperator_Shader_Selection, DepictionOperator_Shader_GhostPawn]
    DepictionAlternatives = [ DepictionVisual_LOD_High( MeshDescriptor = <MeshSocle> ) ]

    SubDepictions = [ TSubDepiction
    (
        Selectable = True
        Depiction = TDepictionDescriptor
        (
            Selector = OnlyHighDepictionSelector
            DepictionAlternatives = [ DepictionVisual_LOD_High( MeshDescriptor = $/GFX/DepictionResources/MeshModele_Tige_courte ) ]

            SubDepictions = [ TSubDepiction
            (
                Selectable = True
                Anchors = ["sommet"]
                Depiction = TDepictionDescriptor
                (
                    Scaler = TConstantScaler( Scale = 2 )
                    Selector = OnlyHighDepictionSelector
                    Operators = [ StrategicInfantryPoseOperator ]
                    DepictionAlternatives = [ DepictionVisual_LOD_High( MeshDescriptor = <MeshDescriptorPawn> ) ]
                )
            ) ]
        )
    ) ]
)

template StrategicAirplanePawnDepictionDesc
[
    MeshSocle,
    MeshDescriptorPawn,
]
is StrategicPawnDepictionDesc
(
    MeshSocle = <MeshSocle>
    MeshDescriptorPawn = <MeshDescriptorPawn>
    MeshTige = $/GFX/DepictionResources/MeshModele_Tige_moyenne
)

template StrategicHelicopterPawnDepictionDesc
[
    MeshSocle,
    MeshDescriptorPawn,
]
is StrategicPawnDepictionDesc
(
    MeshSocle = <MeshSocle>
    MeshDescriptorPawn = <MeshDescriptorPawn>
    MeshTige = $/GFX/DepictionResources/MeshModele_Tige_moyenne
)

template StrategicGroundVehiclePawnDepictionDesc
[
    MeshSocle,
    MeshDescriptorPawn,
    SubDepictionGenerators = []
]
is StrategicPawnDepictionDesc
(
    MeshSocle = <MeshSocle>
    MeshDescriptorPawn = <MeshDescriptorPawn>
    MeshTige = $/GFX/DepictionResources/MeshModele_Tige_courte
    SubDepictionGenerators = <SubDepictionGenerators>
)

template StrategicMoveSound
[
    WavPath,
]
is TSoundDescriptor
(
    SoundSettings = $/SoundSettings/SoundSettings_Strategic_Move
    TheSoundStream = TSoundStream(FileName = <WavPath>)
)

StrategicMoveSoundOperator is TCosmeticStrategicMoveSoundOperatorDesc
(
    StartSounds =
    [
        //StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/Debug/beep_880.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_1.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_2.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_3.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_4.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_5.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_6.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_7.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_8.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_9.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_9.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_9.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_9.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_10.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_10.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_10.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_10.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_10.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Move_10.wav"),
    ]
    StopSounds =
    [
        // StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/Debug/beep_554.wav"),
        // StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/Debug/beep_660.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_1.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_2.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_3.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_4.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_5.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_6.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_7.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_8.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_9.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_10.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_11.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_12.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_13.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_14.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_15.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_16.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_17.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_18.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_19.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_20.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_20.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_20.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_20.wav"),
        StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/SFX/Steelman/Steelman_Pawn_Stop_20.wav"),
    ]
    RunSounds =
    [
        // StrategicMoveSound(WavPath = "GameData:/Assets/Sounds/Debug/beep_330_loop_1.wav"),
    ]
    OperatorId = "driveSound"
)
