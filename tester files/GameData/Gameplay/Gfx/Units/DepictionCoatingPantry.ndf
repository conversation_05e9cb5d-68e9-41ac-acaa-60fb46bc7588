OH58A_Gatling_CAN_tourelle_coating_ingredient is TCoatingIngredientOperator
(
    Descriptor = TCosmeticLandingGearOperatorDesc
    (
        AnimationList = [
            // TBoneProceduralAnimation
            // (
            //     BoneName = 'tourelle_base'
            //     Axis = 2
            //     LimitValue = -1
            //     Delay = 4
            //     Duration = 1.5
            //     IsTranslation = true
            // ),
            TBoneProceduralAnimation
            (
                BoneName = 'tourelle_base'
                LimitValue = -90
                Delay = 4
                Duration = 1.5
            ),
        ]
    )
)

F117_Nighthawk_trapdoor_coating_ingredient is TCoatingIngredientOperator
(
    Descriptor = TCosmeticTrapdoorOperatorDesc
    (
        TrapdoorList = [
            TTrapdoor
            (
                TrapdoorBoneName = 'trappe_g'
                LimitAngleInDegree = 70
            ),
            TTrapdoor
            (
                TrapdoorBoneName = 'trappe_d'
                LimitAngleInDegree = -70
            )
        ]
        LoadSupportBoneName = 'support_missiles'
        LimitTranslationInMeter = 0.7
        TranslationDurationInSec = 5
        TrapdoorOpeningDurationInSec = 5
    )
)

Faun_Kraka_20mm_RFA_offset_coating_ingredient is TCoatingIngredientOperator
(
    Descriptor = TCosmeticOffsetTurretWhileMovingOperatorDesc
    (
        AngleInDegree = 20
    )
)

Mi_24V_SOV_landing_gears_coating_ingredient is TCoatingIngredientOperator
(
    Descriptor = TCosmeticLandingGearOperatorDesc
    (
        AnimationList = [
            TBoneProceduralAnimation
            (
                BoneName = 'roue_avant'
                Axis = 1
                LimitValue = 75
                Delay = 4
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'trappe_droite'
                LimitValue = -95
                Delay = 6
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'trappe_gauche'
                LimitValue = 95
                Delay = 6
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_gauche'
                Axis = 1
                LimitValue = 1.5
                Delay = 4
                Duration = 2
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_gauche'
                LimitValue = 25
                Delay = 4
                Duration = 2
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_droite'
                Axis = 1
                LimitValue = -1.5
                Delay = 4
                Duration = 2
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_droite'
                LimitValue = -25
                Delay = 4
                Duration = 2
            )
        ]
    )
)

Mi_24P_landing_gears_coating_ingredient is TCoatingIngredientOperator
(
    Descriptor = TCosmeticLandingGearOperatorDesc
    (
        AnimationList = [
            TBoneProceduralAnimation
            (
                BoneName = 'roue_avant'
                Axis = 1
                LimitValue = 97
                Delay = 4
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_avant2'
                Axis = 1
                LimitValue = 70
                Delay = 5
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'trappe_avant'
                Axis = 1
                LimitValue = 135
                Delay = 5
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'trappe_droite'
                LimitValue = -80
                Delay = 5
                Duration = 0.5
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'trappe_gauche'
                LimitValue = 80
                Delay = 5
                Duration = 0.5
            ),

            TBoneProceduralAnimation
            (
                BoneName = 'roue_gauche'
                Axis = 1
                LimitValue = 1.5
                Delay = 4
                Duration = 2
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_gauche'
                LimitValue = 25
                Delay = 4
                Duration = 2
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_droite'
                Axis = 1
                LimitValue = -1.5
                Delay = 4
                Duration = 2
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_droite'
                LimitValue = -25
                Delay = 4
                Duration = 2
            )
        ]
    )
)

Mi_14PL_landing_gears_coating_ingredient is TCoatingIngredientOperator
(
    Descriptor = TCosmeticLandingGearOperatorDesc
    (
        AnimationList = [
            TBoneProceduralAnimation
            (
                BoneName = 'roue_avant'
                Axis = 1
                LimitValue = -120
                Delay = 1
                Duration = 2
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_arriere'
                Axis = 1
                LimitValue = 65
                Delay = 1
                Duration = 2
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'bras_arriere'
                Axis = 1
                LimitValue = -20
                Delay = 1
                Duration = 2
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'trappe'
                Axis = 1
                LimitValue = 85
                Delay = 1
                Duration = 2
            ),
        ]
    )
)

Ka_50_SOV_landing_gears_coating_ingredient is TCoatingIngredientOperator
(
    Descriptor = TCosmeticLandingGearOperatorDesc
    (
        AnimationList = [
            TBoneProceduralAnimation
            (
                BoneName = 'roue_avant'
                Axis = 1
                LimitValue = 70
                Delay = 1
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_avant'
                Axis = 2
                LimitValue = 0.6
                Delay = 1
                Duration = 1
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_droite'
                Axis = 1
                LimitValue = 50
                Delay = 1
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_droite'
                LimitValue = -0.6
                Delay = 1
                Duration = 1
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_droite'
                Axis = 1
                LimitValue = -0.7
                Delay = 1
                Duration = 1
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_droite'
                Axis = 2
                LimitValue = 0.3
                Delay = 2
                Duration = 1
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_gauche'
                Axis = 1
                LimitValue = 50
                Delay = 1
                Duration = 1
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_gauche'
                LimitValue = -0.6
                Delay = 1
                Duration = 1
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_gauche'
                Axis = 1
                LimitValue = 0.7
                Delay = 1
                Duration = 1
                IsTranslation = true
            ),
            TBoneProceduralAnimation
            (
                BoneName = 'roue_gauche'
                Axis = 2
                LimitValue = 0.3
                Delay = 2
                Duration = 1
                IsTranslation = true
            )
        ]
    )
)
