export LodHighNoneLimit_Pylon is 100

export LodHighMidLimit_Vehicle is 100
export LodMidLowLimit_Vehicle  is 200

export LodHighMidLimit_Airplane is 100
export LodMidLowLimit_Airplane  is 200

export LodHighMidLimit_Helico is 100
export LodMidLowLimit_Helico  is 200

export LodHighNoneLimit_SoldierWeapon is 40

export MaterialTagGhost is 'GhostTag'

export SpecificPylonDepictionSelector is TDepictionPylonSelector
(
    HighLimitInMeter = LodHighNoneLimit_Pylon
    Camera = $/M3D/Misc/CameraPrincipale
)

export SpecificVehicleDepictionSelector is TLodSelector
(
    HighMidLimitInMeter = LodHighMidLimit_Vehicle
    MidLowLimitInMeter = LodMidLowLimit_Vehicle
    CameraMoverManager = $/Camera/CameraMoverManager
    OptionalLimit = $/GraphicOption/ModelQuality
)

export SpecificAirplaneDepictionSelector is TLodSelector
(
    HighMidLimitInMeter = LodHighMidLimit_Airplane
    MidLowLimitInMeter = LodMidLowLimit_Airplane
    CameraMoverManager = $/Camera/CameraMoverManager
    OptionalLimit = $/GraphicOption/ModelQuality
)

export SpecificFlyingScaler is TCameraScaler
(
    ScaleFactor = PixelConstantValuesSpecific/AirplaneFactor
    ScaleMax = PixelConstantValuesSpecific/AirplaneScaleMax
    ScaleMinReferenceAltitude = PixelConstantValuesSpecific/AirplaneMinReferenceAltitude
    Camera = $/M3D/Misc/CameraPrincipale
    ScaleOption = $/GUIOption/UnitScalingEnabled
)

export SpecificHelicoDepictionSelector is TLodSelector
(
    HighMidLimitInMeter = LodHighMidLimit_Helico
    MidLowLimitInMeter = LodMidLowLimit_Helico
    CameraMoverManager = $/Camera/CameraMoverManager
    OptionalLimit = $/GraphicOption/ModelQuality
)

template HumanSelectorTemplate
[
    DisplayCondition,
]
is THumanWithConditionSelector
(
    HighLowLimitInMeter = LodHighLowLimit_Infantry
    LowNoneLimitInMeter = LodLowNoneLimit_Infantry
    CameraMoverManager = $/Camera/CameraMoverManager
    OptionalLimit = $/GraphicOption/ModelQuality
    DisplayCondition = <DisplayCondition>
)

export HumanAlwaysVisibleSelector is HumanSelectorTemplate
(
    DisplayCondition = nil
)

export HumanVisibleWhenAliveSelector is HumanSelectorTemplate
(
    DisplayCondition = TVisibleWhenAliveCondition()
)

export HumanVisibleWhenDeployedSelector is HumanSelectorTemplate
(
    DisplayCondition = TVisibleWhenAliveAndDeployedCondition()
)

export HumanVisibleWhenUndeployedSelector is HumanSelectorTemplate
(
    DisplayCondition = TVisibleWhenDeadOrUndeployedCondition()
)

export TowedUnitSubDepictionSelector is TTowedUnitSelector
(
    HighMidLimitInMeter = LodHighMidLimit_Vehicle
    MidLowLimitInMeter = LodMidLowLimit_Vehicle
    CameraMoverManager = $/Camera/CameraMoverManager
    OptionalLimit = $/GraphicOption/ModelQuality
)

export SoldierDynamicWeaponSubDepictionSelector is TInfantryWeaponSelector
(
    NoneLimitInMeter = LodHighNoneLimit_SoldierWeapon
    CameraMoverManager = $/Camera/CameraMoverManager
    MeshAlternativeKey = 'mesh_alternative'
    PrincipalDepiction = ['MeshAlternative_1'] // On verra bien si y'a besoin de gérer un truc ici si ça crash parce que y'a pas d'arme 1
)

export SoldierDynamicBackpackWeaponSubDepictionSelector is TInfantryBackpackWeaponSelector
(
    NoneLimitInMeter = LodHighNoneLimit_SoldierWeapon
    CameraMoverManager = $/Camera/CameraMoverManager
    MeshAlternativeKey = 'mesh_alternative_backpack'
    PrincipalDepiction = ['MeshAlternative_1'] // On verra bien si y'a besoin de gérer un truc ici si ça crash parce que y'a pas d'arme 1
)
