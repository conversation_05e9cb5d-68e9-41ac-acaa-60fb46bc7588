DummyDepiction is TDepictionDescriptor
(
    Scaler = TIdentityScaler()
    Selector = TConstantAlternativeSelector(Key = [LOD_High])
    DepictionAlternatives = [ DepictionVisual_LOD_High(MeshDescriptor = $/GFX/DepictionResources/Rien) ]
)

FumigeneDepiction is TDepictionDescriptor
(
    Scaler = TIdentityScaler()
    Selector = OnlyHighDepictionSelector
    DepictionAlternatives = [
        DepictionVisual_LOD_High(
            MeshDescriptor = $/GFX/DepictionResources/Rien
        ),
    ]

    Operators = [ TCosmeticFumigeneOperatorDesc() ]

    Actions = MAP
    [
        (
            "fumigene",
            TIntroduceMobileHappening( Happening = TSequencingActionHappening( ActionDescriptor = FxFumigeneGhostWithParams ))
        )
    ]
)

export InfantryDepictionSquadShowroom is TTimelyDepictionReceiverFactory
(
  DepictionDescriptor = TDepictionDescriptor
  (
    Selector = OnlyHighDepictionSelector
    DepictionAlternatives = [
      DepictionVisual_LOD_High(MeshDescriptor = $/GFX/DepictionResources/Rien),
    ]
  )
)

unnamed TMimeticGhostRegistration
(
    GfxProperties = ~/GfxProperties
    MimeticGhost = MAP
    [
        ('dummy', DummyDepiction),
        ('fumigene', FumigeneDepiction),
    ]
)

export GfxProperties is TGfxProperties
(
    BuildingMimeticGhost = ~/BuildingMimeticGhost
    BuildingULBVDescriptor = ~/DefaultUnitLevelBuildViewDescriptor
    DistrictULBVDescriptor = ~/MapBuildingUnitLevelBuildViewDescriptor
    GhostBuildingULBVDescriptor = ~/GhostUnitLevelBuildViewDescriptor
)

DefaultRenderLayerSelector is TRenderLayerSelector(
    RenderLayerNames = [
        'Calque_Opaque',
        'Calque_Opaque_Transparency',
        'Calque_Opaque_Depiction',
        'Calque_Opaque_Depiction_SelectionMask',
    ]
)

export DepictionProperties is TDepictionProperties
(
    Descriptors = MAP
    [
        ('fumigene', FumigeneDepiction),
    ]
    World = $/M3D/Scene/DefaultWorld
    DepictionMaterialPack = $/M3D/Shader/MultiRenderTypeMaterialPack_Depiction
    DistrictMaterialPack = $/M3D/Shader/MultiRenderTypeMaterialPack_District
    RenderLayerSelector = ~/DefaultRenderLayerSelector
)
