export Modele_DCA_76T2_20mm_CPA_FR is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm/DCA_76T2_20mm.fbx"
)

export Modele_DCA_76T2_20mm_CPA_FR_MID is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm_MID/DCA_76T2_20mm_MID.fbx"
)

export Modele_DCA_76T2_20mm_CPA_FR_LOW is TResourceMesh
(
    Mesh="GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm_LOW/DCA_76T2_20mm_LOW.fbx"
)

export DeployAnimation_DCA_76T2_20mm_CPA_FR is TResourceMatrixArrayAnimation
(
    FileName   = 'GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm/DCA_76T2_20mm_Deploy.fbx'
    PlayInLoop     = false
)

export DepictionOperator_Deployable_DCA_76T2_20mm_CPA_FR is DepictionOperator_AnimationDeployable
(
    DeployAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm/DCA_76T2_20mm_Deploy.fbx'
        PlayInLoop     = false
    )

    FoldAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm/DCA_76T2_20mm_Fold.fbx'
        PlayInLoop     = false
    )

    IdleAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm/DCA_76T2_20mm_Idle.fbx'
        PlayInLoop     = true
    )

    MoveFrontAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm/DCA_76T2_20mm_Move.fbx'
        PlayInLoop     = true
    )

    MoveBackAnimation = TResourceMatrixArrayAnimation
    (
        FileName   = 'GameData:/Assets/3D/Units/FR/Canon/DCA_76T2_20mm/DCA_76T2_20mm_Move_Back.fbx'
        PlayInLoop     = true
    )

    DeployFromMoveTime  = 1.5
    DeployFromTurnTime  = 1.5
    FoldForMoveTime  = 1.5
    FoldForTurnTime  = 1.5
)
