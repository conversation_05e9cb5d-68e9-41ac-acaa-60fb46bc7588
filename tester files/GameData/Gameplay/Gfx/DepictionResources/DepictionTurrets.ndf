export ModelFile_DShK_127mm_t1 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/SOV/DShK_127mm/DShK_127mm_t1.fbx'
export ModelFile_DShK_127mm_t2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/SOV/DShK_127mm/DShK_127mm_t2.fbx'
export ModelFile_PKT_t1 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/SOV/PKT/PKT_t1.fbx'
export ModelFile_TOW_T1 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/BGM71E_TOW_2A/TOW_t1.fbx'
export ModelFile_TOW_T2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/BGM71E_TOW_2A/TOW_t2.fbx'
export ModelFile_M2HB_12_7mm_t1 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/M2HB_12_7mm/M2HB_12_7mm_t1.fbx'
export ModelFile_M2HB_12_7mm_t2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/M2HB_12_7mm/M2HB_12_7mm_t2.fbx'
export ModelFile_M47_Dragon_II_t2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/M47_Dragon_II/M47_Dragon_II_t2.fbx'
export ModelFile_M60_t1 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/M60/M60_t1.fbx'
export ModelFile_M60_t2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/M60/M60_t2.fbx'
export ModelFile_MG3_t1 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/RFA/MG3/MG3_t1.fbx'
export ModelFile_MG3_t2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/RFA/MG3/MG3_t2.fbx'
export ModelFile_MG3_excentree is 'GameData:/Assets/3D/Units/Ammo/Tourelles/RFA/MG3/MG3_excentree.fbx'
export ModelFile_Milan_F1_t1 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/FR/Milan_F1/Milan_F1_t1.fbx'
export ModelFile_Milan_F1_t2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/FR/Milan_F1/Milan_F1_t2.fbx'
// cas particulier, support pour l'affichage correct des drones dans la showroom
export ModelFile_Support_Drones is 'GameData:/Assets/3D/Units/Ammo/Drones/support_drones.fbx'
export ModelFile_MAG_t2 is 'GameData:/Assets/3D/Units/Ammo/Armes/HMG_trepied/MAG_t2.fbx'
export ModelFile_MAG_t4 is 'GameData:/Assets/3D/Units/Ammo/Armes/HMG_trepied/MAG_t4.fbx'
export ModelFile_Javelin_LML_t1 is 'GameData:/Assets/3D/Units/UK/Canon/Javelin_LML/Javelin_LML_t1.fbx'
export ModelFile_Mk19_t1 is 'GameData:/Assets/3D/Units/Ammo/Armes/HMG_trepied/Mk19_t1.fbx'
export ModelFile_M60D_t2 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/M60D/M60D_t2.fbx'
export ModelFile_M60D_t3 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/USA/M60D/M60D_t3.fbx'
export ModelFile_LAG40_t1 is 'GameData:/Assets/3D/Units/Ammo/Tourelles/ESP/LAG40/LAG40_t1.fbx'