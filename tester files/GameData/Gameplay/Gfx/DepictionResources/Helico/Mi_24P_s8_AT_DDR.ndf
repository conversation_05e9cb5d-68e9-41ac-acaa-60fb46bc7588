export ModelFile_Mi_24P_s8_AT_DDR is 'GameData:/Assets/3D/Units/DDR/Helico/Mi_24P/Mi_24P_AT.fbx'
export Modele_Mi_24P_s8_AT_DDR is TResourceMesh
(
    Mesh=ModelFile_Mi_24P_s8_AT_DDR
)

export ModelFile_Mi_24P_s8_AT_DDR_MID is 'GameData:/Assets/3D/Units/DDR/Helico/Mi_24P_MID/Mi_24P_AT_MID.fbx'
export Modele_Mi_24P_s8_AT_DDR_MID is TResourceMesh
(
    Mesh=ModelFile_Mi_24P_s8_AT_DDR_MID
)

export ModelFile_Mi_24P_s8_AT_DDR_LOW is 'GameData:/Assets/3D/Units/DDR/Helico/Mi_24P_LOW/Mi_24P_AT_LOW.fbx'
export Modele_Mi_24P_s8_AT_DDR_LOW is TResourceMesh
(
    Mesh=ModelFile_Mi_24P_s8_AT_DDR_LOW
)
