InterfaceLayers is [
    $/M3D/Scene/ShadingScene/Calque_OpaqueLevelBuild_Depiction_SelectionMask,
    $/M3D/Scene/ShadingScene/Calque_Opaque_Depiction_SelectionMask,
    $/M3D/Scene/ShadingSceneStrategic/Calque_OpaqueLevelBuild_Depiction_SelectionMask,
    $/M3D/Scene/ShadingSceneStrategic/Calque_Opaque_Depiction_SelectionMask,

    $/M3D/Scene/Scene_2D_Interface/Calque_Interface,
    $/M3D/Scene/Scene_2D_Interface/Calque_Interface_Text,
    $/M3D/Scene/Scene_2D_Interface/Calque_FX_First,
    $/M3D/Scene/Scene_2D_Interface/Calque_FX_Billboard,
    $/M3D/Scene/Scene_2D_Interface/Calque_FX,

    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_FX_AfterPostProcess,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_InfluenceMap,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Pictograph_Solid,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Pictograph_Circle,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Pictograph_Line,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Pictograph_Disc_AlwaysOnTop,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Pictograph_Solid_AlwaysOnTop,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Pictograph_Circle_AlwaysOnTop,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Pictograph_Line_AlwaysOnTop,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Fleche_GD,
    $/M3D/Scene/Scene3D_AfterPostProcess/Calque_Labels_Array,

    $/M3D/Scene/Scene_2D_Interface/CalqueArray_Objective,
    $/M3D/Scene/Scene_2D_Interface/CalqueArray_Labels,
    $/M3D/Scene/Scene_2D_Interface/CalqueArray_2DInterface_InGame,
    $/M3D/Scene/Scene_2D_Interface/CalqueArray_2DInterface_OutGame,
    $/M3D/Scene/Scene_2D_Interface/CalqueArray_2DInterface_ShowRoom,
    $/M3D/Scene/Scene_2D_Interface/CalqueArray_2DInterfaceLDHint,

    $/M3D/Scene/Scene_2D_TopInterface/CalqueArray_Modal,
    $/M3D/Scene/Scene_2D_TopInterface/CalqueArray_Hint,
]

InterfaceArrayLayersForDebug is [
        $/M3D/Scene/Scene3D_AfterPostProcess/Calque_UIDebug3D_Array,
        $/M3D/Scene/Scene_2D_AlwaysOnTopInterface/LayerArray_UIDebug,
        $/M3D/Scene/Scene_2D_AlwaysOnTopInterface/CalqueArray_2DInterface_debug,
]
