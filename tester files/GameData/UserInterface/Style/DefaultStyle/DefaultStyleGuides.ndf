template TemplateTFS_Flag
[
    TextureToken     : string
] is TTFSCommand_UISymbol
(
    TextureToken = <TextureToken>
    BBMin=[0, -0.85, 0]
    BBMax=[1.7, 0.15, 0]
    ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
)

template TemplateTFS_Division
[
    TextureToken     : string
] is TTFSCommand_UISymbol
(
    TextureToken = <TextureToken>
    BBMin=[0, -1, 0]
    BBMax=[1.5, 0.5, 0]
    ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
)

DefaultStyleGuide is TUIStyleGuide
(
    TextStylesMap = MAP [
        ("ActivationPoint",                                         MAP [ ( ~/ComponentState/Normal, TextStyleActivationPoint ), ]),
        ("ActivationPointTemp",                                     MAP [ ( ~/ComponentState/Normal, TextStyleActivationPointTemp ), ]),
        ("ActionPoint",                                             MAP [ ( ~/ComponentState/Normal, TextStylePointAction ), ]),
        ("Default",                                                 MAP [ ( ~/ComponentState/Normal, TextStyleDefault ), ]),
        ("LabelUnitNameStroke",                                     MAP [ ( ~/ComponentState/Normal, TextStyleStrokeLabelUnit ), ]),
        ("DefaultWithStroke",                                       MAP [ ( ~/ComponentState/Normal, TextStyleWithStroke ), ]),
        ("DefaultWithStroke_labelVille",                            MAP [ ( ~/ComponentState/Normal, TextStyleDefaultWithStroke_labelVille ), ]),
        ("TitreChallenge_strokeBig",                                MAP [ ( ~/ComponentState/Normal, TitreChallenge_strokeBig ), ]),
        ("TextStyleEcranMoniteur",                                  MAP [ ( ~/ComponentState/Normal, TextStyleEcranMoniteur ),
                                                                          ( ~/ComponentState/Grayed, TextStyleDefault ) ]),
        ("TextStyleEcranMoniteur_cyan",                             MAP [
                                                                            ( ~/ComponentState/Normal, TextStyleEcranMoniteur_cyan ),
                                                                            ( ~/ComponentState/Grayed, TextStyleDefault )
                                                                        ]),
        ("TextStyleEcranMoniteur_jaune",                            MAP [ ( ~/ComponentState/Normal, TextStyleEcranMoniteur_jaune ), ]),
        ("TextStyleEcranMoniteur_solo",                             MAP [ ( ~/ComponentState/Normal, TextStyleEcranMoniteur_solo ), ]),
        ("TextStyleFeedbackLabel",                                  MAP [ ( ~/ComponentState/Normal, ~/TextStyleFeedbackLabel ), ]),

        //InGame
        ("ChatLogMessage",                                      MAP [ ( ~/ComponentState/Normal, TextStyleStroke0806 ), ]),
        ("FactoryName",                                         MAP [ ( ~/ComponentState/Normal, TextStyleFactoryName ), ]),
        ("UnlockingPhase",                                      MAP [ ( ~/ComponentState/Normal, TextStyleUnlockingPhase ), ]),
        ("Engagement",                                          MAP [ ( ~/ComponentState/Normal, TextStyleEngagement ), ]),

        ("PlayerName",                                          MAP [ ( ~/ComponentState/Normal, TextStyleStroke080604 ), ]),
        ("NbUnits",                                             MAP [ ( ~/ComponentState/Normal, TextStyleStroke080604 ), ]),
        ("DefaultSubtitle",                                     MAP [ ( ~/ComponentState/Normal, TextStyleStroke0705 ), ]),
        ("OutmapFeedback",                                      MAP [ ( ~/ComponentState/Normal, TextStyleStroke0705 ), ]),
        ("MouseWidget/TextModule",                              MAP [ ( ~/ComponentState/Normal, TextStyleMouseWidgetTextModule ), ]),
        ("VictoryStatus",                                       MAP [ ( ~/ComponentState/Normal, TextStyleDefault ), ]),
        ("StrategicPointsText",                                 MAP [ ( ~/ComponentState/Normal, TextStyleStroke0705 ), ]),
        ("AlertMessage",                                        MAP [ ( ~/ComponentState/Normal, TextStyleStroke0705 ), ]),
        ("NbUnitsInPackList",                                   MAP [ ( ~/ComponentState/Normal, TextStyleStroke070535 ), ]),
        ("NbUnitsInPackText",                                   MAP [ ( ~/ComponentState/Normal, TextStyleStroke0503 ), ]),
        ("Chat",                                                MAP [
                                                                    ( ~/ComponentState/Normal, TextStyleWithStroke ),
                                                                    ( ~/ComponentState/Highlighted, TextStyleDefault ),
                                                                ]),

        ("AutoKick",                                            MAP [ ( ~/ComponentState/Normal, TextStyleStroke0806 ), ]),
        ("EGWin",                                               MAP [ ( ~/ComponentState/Normal, TextStyle_EGWin ), ]),
        ("EGWin2",                                              MAP [ ( ~/ComponentState/Normal, TextStyle_EGWin2 ), ]),
        ("UpperCase",                                           MAP [ ( ~/ComponentState/Normal, TextStyleUpperCase), ]),
    ]

    Typeface = MAP [
        ("UIMainFont",                  TypefaceTitle),              // BOMBARD.ttf

        ("UISecondFont",                TypefaceSecond),             // Mx437_MBytePC230_8x16.ttf
        ("Liberator",                   TypefaceMainOutgame),        // Liberator-Medium.ttf
        ("SevenSegments",               TypefaceSevenSegments),      // alarm clock.ttf
        ("Chat",                        TypefaceHandPen),            // FELTPEN_.TTF
        ("Subtitles",                   Typeface_Subtitles),         // ARIALNB.ttf
        ("HandPen",                     TypefaceHandPen),            // FELTPEN_.TTF
        ("Area",                        TypefaceAreas),              // BOMBARD.ttf
        ("Courrier",                    TypefaceMainIngame),         // CONSOLA.ttf
        ("IBM",                         TypefaceSecond),             // Mx437_MBytePC230_8x16.ttf
        ("Eurostyle",                   TypefaceThird),              // Amazon Ember Regular.ttf
        ("Eurostyle_Heavy",             TypefaceFourth),             // Amazon Ember Regular.ttf
        ("Eurostyle_Italic",            TypefaceFith),               // Amazon Ember Regular.ttf
        ("Eurostyle_Medium",            TypefaceSixth),              // Amazon Ember Medium.ttf
        ("Bombardier",                  TypefaceTitle),              // BOMBARD.ttf
        ("StrategicFeedbacks",          TypefaceStrategicFeedbacks), // FELTPEN_.ttf
    ]


    //Size
    TextSizesMap = MAP [
        // ++ Nouvelles Tailles (SD2)
        // voir textsize.ndf pour les tailles
        ("SD2_Moyen",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("SD2_Petit",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit3 ) ), ]),


        ("AutoResolve/Title",                             MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 48 ) ), ]),
        ("AutoResolve/SubTitle",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 36 ) ), ]),
        ("AutoResolve/Information",                       MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 20 ) ), ]),
        ("AutoResolve/Rapport",                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 24 ) ), ]),


        ("1",                                               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 1 ) ), ]),
        ("7",                                               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 7 ) ), ]),
        ("8",                                               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 8  ) ), ]),
        ("9",                                               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 9 ) ), ]),
        ("10",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 10 ) ), ]),
        ("11",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 11 ) ), ]),
        ("12",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 12 ) ), ]),
        ("13",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 13 ) ), ]),
        ("14",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 14 ) ), ]),
        ("15",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 15 ) ), ]),
        ("16",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 16  ) ), ]),
        ("17",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 17 ) ), ]),
        ("18",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 18 ) ), ]),
        ("19",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 19 ) ), ]),
        ("20",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 20 ) ), ]),
        ("21",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 21 ) ), ]),
        ("22",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 22 ) ), ]),
        ("23",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 23 ) ), ]),
        ("24",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 24 ) ), ]),
        ("25",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 25 ) ), ]),
        ("26",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 26 ) ), ]),
        ("27",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 27 ) ), ]),
        ("28",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 28 ) ), ]),
        ("29",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 29 ) ), ]),
        ("30",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 30 ) ), ]),
        ("31",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 31 ) ), ]),
        ("32",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 32 ) ), ]),
        ("33",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 33 ) ), ]),
        ("34",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 34 ) ), ]),
        ("36",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand4 ) ), ]),
        ("40",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 40 ) ), ]),
        ("41",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 41 ) ), ]),
        ("42",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 42 ) ), ]),
        ("46",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 46 ) ), ]),
        ("50",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = TresTresGrand ) ), ]),
        ("60",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 60 ) ), ]),
        ("65",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 65 ) ), ]),
        ("90",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 90 ) ), ]),
        ("120",                                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 120 ) ), ]),
        ("5000",                                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 5000 ) ), ]),
        ("8000",                                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 8000 ) ), ]),
        ("12000",                                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 12000 ) ), ]),
        ("17000",                                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 17000 ) ), ]),
        ("20000",                                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 20000 ) ), ]),
        ("25000",                                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 25000 ) ), ]),
        ("35000",                                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 35000 ) ), ]),


        ("FlaresPanel/CustomFlare/EditableText",            MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("Jauge/Title",                                     MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit3 ) ), ]), //ancien composant
        ("PawnLabel/ActionPoints",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit2 ) ), ]),
        ("UnitLabel/TextElement",                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit2 ) ), ]), //moyen1
        ("UnitLabel/CriticDescription",                     MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit2 ) ), ]), // Moyen
        ("UnitLabel/CriticTitle",                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit2 ) ), ]), // Moyen
        ("AlertMessage",                                    MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),



        ("CheckBox",                                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen1 ) ), ]), //ancien composant


        ("ObjectiveLabel/Primary/Title",                    MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen2 ) ), ]),
        ("ObjectiveLabel/Bonus/Title",                      MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("ObjectiveLabel/Info/Title",                       MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("ObjectiveLabel/Heading",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen1 ) ), ]),
        ("ObjectiveLabel/CapturePoint/Title",               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("ObjectiveLabel/CapturePoint/Content",             MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit3 ) ), ]),




        ("HUBAccueil/ProfileNameLevel",                     MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit5 ) ), ]),


        ("PanneauPhase/TextePhase",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen1 ) ), ]),
        ("PanneauPhase/TexteTemps",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen1 ) ), ]),
        ("PanelScore/ObjectiveAlliance",                    MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("PanelScore/VictoryStatus",                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit4 ) ), ]),
        ("PanelScore/ScoreJoueur",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit4 ) ), ]),

        ("OutmapFeedback/Text",                             MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("OutmapFeedback/TimerText",                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen2 ) ), ]),


        ("ReplayPanel/WaitingTimeObs",                      MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),

        ("Labels/Ville",                                    MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit3 ) ), ]),   //Moyen2),
        ("AutoKickPanel/Title",                             MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("AutoKickPanel/PlayerName",                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("AutoKickPanel/Time",                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("Subtitle/ChallengeText",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 60 ) ), ]),
        ("Subtitle/StrategicText",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 37 ) ), ]),
        ("MouseWidget/TextModule",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit5 ) ), ]),
        ("MouseWidget/TextModuleBig",                       MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),

        ("Header/Button",                                   MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen2 ) ), ]),
        ("Default",                                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 17 ) ), ]),

        ("Strategic/Signpost/Title",                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand2 ) ), ]),
        ("Strategic/Signpost/Body",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit4 ) ), ]),



        //OutGame
        ("BoutonsDAction/Standard",                     MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("BoutonsDAction/MissingDLCs",                  MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("BoutonHUB",                                   MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = TresGrand ) ), ]),//TresTresGrand2),
        ("BoutonHUBLittle",                             MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand4 ) ), ]),
        ("BoutonURL",                                   MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("ListeExcel/Cartouche",                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit4 ) ), ]),
        ("ListeExcel/Lignes/Defaut",                    MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("InfoSecondaire",                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit4 ) ), ]),


        ("Checkbox",                                    MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),


        ("NumVersion",                                  MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]), // A setter avec la bonne valeur, mis ici pour la compatibilite avec l'ancienne interface
        ("ChatMessage",                                 MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]), // A setter avec la bonne valeur, mis ici pour la compatibilite avec l'ancienne interface
        ("StandardTextInModal",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]), // A setter avec la bonne valeur, mis ici pour la compatibilite avec l'ancienne interface
        ("RightTextInModal",                            MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("FeedbackTextInModal",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("InfoTextInModal",                             MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("CreateAccountModale/TermOfService",           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("Motd",                                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]), // A setter avec la bonne valeur, mis ici pour la compatibilite avec l'ancienne interface
        ("SearchRankedGame/Title",                      MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand2 ) ), ]),
        ("SearchRankedGame/EllapsingTime",              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen2 ) ), ]),
        ("SearchRankedGame/NbPlayersSearching",         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen2 ) ), ]),

        ("Debrief/Title",                               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand3 ) ), ]),
        ("Debrief/UnitName",                            MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("Debrief/SoldierName",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen  ) ), ]),
        ("Debrief/Timer",                               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit4 ) ), ]),
        ("Debrief/Score",                               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen2 ) ), ]),
        ("Debrief/PostIt",                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Minuscule ) ), ]),
        ("Operation/Resume",                            MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit4 ) ), ]),
        ("Mission/Resume",                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen2 ) ), ]),

        ("Timer",                                       MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand3 ) ), ]),


        ("DivisionPanel/DivisionName",                  MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("DivisionPanel/DivisionNickname",              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen1 ) ), ]),
        ("DivisionPanel/PhaseIncomeLabel",              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit2 ) ), ]),
        ("DivisionPanel/PhaseIncome",                   MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand4 ) ), ]),
        ("DeckOverview/Factory",                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Petit5 ) ), ]),
        ("ServeurDedie/Titre",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand3 ) ), ]),
        ("ServeurDedie/Ligne",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("WaitingModale",                               MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("LoginModale/Default",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("Debrief/BattlefieldTitle",                    MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 25 ) ), ]),
        ("Debrief/Battlefield",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 50 ) ), ]),
        ("Debrief/TimeTitle",                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 20 ) ), ]),
        ("Debrief/Time",                                MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 35 ) ), ]),
        ("Debrief/PointsTitle",                         MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 20 ) ), ]),
        ("Debrief/UpdateProfile",                       MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 20 ) ), ]),
        ("Debrief/Points",                              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 50 ) ), ]),
        ("AutoMatchOption",                             MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("Lobby/PlayerLine",                            MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
        ("Lobby/PlayerLevel",                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand2 ) ), ]),

        ("FenetreDLC/Corps",                            MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen1 ) ), ]),

        ("Challenge/WarningTitle",                      MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Grand1 ) ), ]),
        ("Challenge/WarningContent",                    MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = Moyen ) ), ]),
    ]

    //LineSizes
    LineSizesMap = MAP [
        // ++ Nouvelles Tailles (SD2)
        ("1",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = 1 ) ), ]),
        ("2",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = LigneFine ) ), ]),
        ("3",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = LigneCadreFenetreInt ) ), ]),
        ("4",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = LigneMoyenne ) ), ]),
        ("5",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = LigneCadreFenetreExt ) ), ]),
        ("6",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = 6.0 ) ), ]),
        ("7",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = 7.0 ) ), ]),
        ("8",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = 8.0 ) ), ]),
        ("9",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = 9.0 ) ), ]),
        ("10",                                               MAP [ ( ~/ComponentState/Normal,     TFloatRTTI ( Value = 10.0 ) ), ]),




        ("Hint",                                            MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = LigneTresFine ) ), ]),



        ("Small",                                           MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = LigneTresFine ) ), ]),
        ("Normal",                                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = LigneFine ) ), ]),
        ("PanelScore/SoulignageRevenusJoueur",              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = LigneTresFine ) ), ]),


        ("CheckBox",                                        MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 1 ) ), ]),
        ("PawnLabel/ActionPoints",                          MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 1 ) ), ]),
        ("ObjectiveLabel/CapturePoint/Border",              MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = 2 ) ), ]),

        //OutGame
        ("BoutonsDAction/Standard",                     MAP [
                                                            ( ~/ComponentState/Grayed,      TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Normal,      TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Highlighted, TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Clicked,     TFloatRTTI ( Value = LigneFine ) ),
                                                        ]),
        ("BoutonsDAction/MissingDLCs",                  MAP [
                                                            ( ~/ComponentState/Grayed,      TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Normal,      TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Highlighted, TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Clicked,     TFloatRTTI ( Value = LigneFine ) ),
                                                        ]),
        ("BoutonsDAction/Image",                        MAP [
                                                            ( ~/ComponentState/Grayed,      TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Normal,      TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Highlighted, TFloatRTTI ( Value = LigneFine ) ),
                                                            ( ~/ComponentState/Clicked,     TFloatRTTI ( Value = LigneFine ) ),
                                                        ]),

        ("Onglet",                                      MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = LigneFine ) ), ]),

        ("DeckOverview/CaseVide",                       MAP [ ( ~/ComponentState/Normal, TFloatRTTI( Value = LigneTresFine ) ), ]),


    ]

    //MagnifiableMargins
    MagnifiableMarginsMap = MAP [
        //InGame

        ("Hint",                                        MAP [ ( ~/ComponentState/Normal, TFloat4RTTI( Value = MargeHint ) ), ]),
        ("FlaresPanel/CustomFlare/EditableText",        MAP [ ( ~/ComponentState/Normal, TFloat4RTTI( Value = MargePetite ) ), ]),


        ("EditableText",                                MAP [ ( ~/ComponentState/Normal, TFloat4RTTI( Value = MargeScrollbar ) ), ]),



        //OutGame

        ("Moyenne",                         MAP [ ( ~/ComponentState/Normal, TFloat4RTTI( Value = MargeMoyenne ) ), ]),

    ]

//Colors

    //BlockColors
    BlockColorsMap = MAP[
        //InGame


        ("BoutonStandard",                           MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [220,220,220,255]  ) ),
                                                   (~/ComponentState/Highlighted,            TColorRTTI( Color = Blanc2  ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,255] )),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [255,255,255,27]  ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [255,255,255,20]  ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = VertGris ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [50,50,50,100] ) ),
                                           ]),
        ("CyanClairFulda",                           MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [75,205,187,180]  ) ),
                                                   (~/ComponentState/Highlighted,            TColorRTTI( Color = [255,255,255,27]  ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = [255,255,255,40]  ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [255,255,255,27]  ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [255,255,255,20]  ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = VertGris ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = BleuVariable ) ),
                                           ]),

        ("BlancLight",                           MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [255,255,255,20]  ) ),
                                                   (~/ComponentState/Highlighted,            TColorRTTI( Color = [255,255,255,27]  ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = [255,255,255,40]  ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [255,255,255,27]  ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [255,255,255,20]  ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = VertGris ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = BleuVariable ) ),
                                           ]),
        ("Test",                           MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = Glacier  ) ),
                                                   (~/ComponentState/Highlighted,            TColorRTTI( Color = JauneOrange ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = Orange6 ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = VertPomme ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = VertGris ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = BleuVariable ) ),
                                           ]),

        ("BoutonTemps_Background",         MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [122,167,176,102]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BleuVariable ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color =  [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Blanc2 ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [122,167,176,50] ) ),
                                               ]),
        ("CustomFlareText",                    MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [122,167,176,202]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BleuVariable ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color =  [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Blanc2 ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [122,167,176,50] ) ),
                                               ]),

        ("BoutonTemps_pawn",                    MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [19,85,106,102]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BleuVariable ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color =  [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Blanc2 ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [122,167,176,50] ) ),
                                               ]),
         ("BoutonFlares",                    MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [47,119,139,240]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BleuVariable ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color =  [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Blanc2 ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [122,167,176,50] ) ),
                                               ]),

        ("BoutonSelectionMultiple",        MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [122,167,176,102]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BleuVariable ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = VertPomme ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = VertGris ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = BleuGris ) ),
                                               ]),
        // bleu des sous panels
        ("H2_bleu_1",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [37,127,149,176] ) ), ]),
        ("H2_bleu_1_hint",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [116,159,176,236] ) ), ]),
        ("H2_bleu_2_hint",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [58,100,105,255] ) ), ]),
        ("H2_bleu_3_hint",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [84,127,133,255] ) ), ]),
        ("H2_bleu_4_hint",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [84,180,200,95] ) ), ]),

        // bleu standard
        ("H2_bleu_2",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,191] ) ), ]),
        ("H2_bleu_2_235",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,235] ) ), ]),

        ("OffMapUnitButtonName",        MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [19,85,106,255]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BleuSelection ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = BleuSelectionClicked ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = BleuSelection ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BleuSelection ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [19,85,106,191] ) ),
                                               ]),


        ("H2_bleu_2_60p",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,152] ) ), ]),
        ("H2_bleu_2_40p",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,102] ) ), ]),
        ("H2_bleu_2_30p",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,76] ) ), ]),

        // bleu clair
        ("H2_bleu_3",                      MAP [
                                                   ( ~/ComponentState/Normal,                 TColorRTTI( Color = [98,165,210,255] ) ),
                                                   ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [134,185,220,255] ) ),
                                               ]),
        ("H2_bleu_4",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [11,51,63,255] ) ), ]),    //bleu foncé
        // bleu plein des boutons de prod
        ("H2_bleu_5",                      MAP [
                                                   ( ~/ComponentState/Normal,                 TColorRTTI( Color = [20,103,131,255] ) ),
                                                   ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [134,185,220,255] ) ),
                                               ]),

        ("BleuPV",            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [45,112,118,245] ) ), ]),
        ("Fulda2_blanc15",            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,255,255,15] ) ), ]),
        ("Fulda_BleuNoir",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = Fulda_BleuNoir ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [9, 33, 37,210] ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = BlueLightestOutgame ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = [3, 35, 26,210] ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = BlueLightOutgame ) ),
                                        ]),
        ("Fulda_Gris",                MAP [ ( ~/ComponentState/Normal,                 TColorRTTI( Color = Fulda_Gris ) ), ]),
        ("Fulda_Turquoise",           MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = Fulda_Turquoise ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = Fulda_VertBleu ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = Fulda_Turquoise2 ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = Fulda_VertBleu ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = Fulda_Turquoise2 ) ),
                                        ]),
        ("Fulda_VertBleu",            MAP [
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = Fulda_VertBleu ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = Fulda_Turquoise ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = Fulda_Turquoise2 ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = Fulda_Turquoise ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = Fulda_Turquoise2 ) ),
                                        ]),

        ("Fulda2_Orange30",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Fulda2_Orange30 ) ), ]),
        ("Fulda2_Orange100",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Fulda2_Orange100 ) ), ]),
        ("Fulda2_Noir",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Fulda2_Noir ) ), ]),
        ("Fulda2_Jaune100",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Fulda2_Jaune100 ) ), ]),
        ("Fulda2_BoutonCubeAction",         MAP [
                                                    ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                                    ( ~/ComponentState/Normal,                 TColorRTTI( Color = Transparent ) ),
                                                    ( ~/ComponentState/Highlighted,            TColorRTTI( Color = Fulda2_Orange30 ) ),
                                                    ( ~/ComponentState/Clicked,                TColorRTTI( Color = Fulda2_Orange100 ) ),
                                                    ( ~/ComponentState/Toggled,                TColorRTTI( Color = Fulda2_Orange100 ) ),
                                                    ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = Fulda2_Orange30 ) ),
                                                ]),


        ("Vert",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Vert ) ), ]),
        ("Vert50p",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Vert50p ) ), ]),
        ("Vert25p",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [127,255,117,64] ) ), ]),

        ("drapeau_renfort_nogo",          MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [194,85,96,255] ) ), ]),
        ("drapeau_renfort_go",          MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [171,204,185,255] ) ), ]),
        ("BeigeSteelman",          MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSteelman ) ), ]),
        ("GrisSteelman",           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisSteelman ) ), ]),
        ("Noir80",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur80 ) ), ]),
        ("Noir50",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur50 ) ), ]),
        ("load_columnDarkBackground", MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [12,10,20,10] ) ), ]),
        ("Noir30",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur30 ) ), ]),
        ("Noir30Button",           MAP [
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = NoirPur30 ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = Blanc20 ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = Blanc50 ) ),
                                        ]),
        ("Gris123",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gris123 ) ), ]),
        ("SD2_Blanc184",           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc184 ) ), ]),
        ("SD2_Blanc184_70",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [184,184,184,170] ) ), ]),
        ("SD2_Noir",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),
        ("SD2_Hint",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [52,75,93,245] ) ), ]),
        ("SD2_RougeSovietique",    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique_100) ), ]),
        ("SD2_GrisAllemand",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisAllemand_100 ) ), ]),
        ("SD2_BleuGris_phaseA",                MAP [( ~/ComponentState/Normal, TColorRTTI( Color = [52,66,75,250] ) )  ]),
        ("SD2_BleuGris_phaseB",                MAP [( ~/ComponentState/Normal, TColorRTTI( Color = [52,66,75,210] ) )  ]),
        ("SD2_BleuGris_phaseC",                MAP [( ~/ComponentState/Normal, TColorRTTI( Color = [52,66,75,180] ) )  ]),
        ("SD2_BleuGris",           MAP [
                                           ( ~/ComponentState/Normal, TColorRTTI( Color = BleuGris ) ),
                                           ( ~/ComponentState/Grayed, TColorRTTI( Color = NoirTransparent5 ) ),
                                       ]),

        ("SD2_Marron3",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Marron3 ) ), ]),
        ("SD2_VertGris",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertGris ) ), ]),
        ("SD2_Gris39",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gris39 ) ), ]),
        ("SD2_Gris39_avecTransparence200",  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [39,39,39,200] ) ), ]),
        ("SD2_Routed",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,0,0,200] ) ), ]),
        ("SD2_InOrder",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,168,69,200] ) ), ]),
        ("SD2_BleuVariable",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuVariable ) ), ]),

        ("SD2_BleuUnderlined",          MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = BlueOutgame ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = BlueLightestOutgame ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = BlueLightOutgame ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = BlueLightOutgame ) ),
                                        ]),

        ("grisCompagnie",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [80,80,82,245] ) ), ]),
        ("grisCompagnie_pertes",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [80,80,82,210] ) ), ]),
        ("grisClairDivision",           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [107,108,100,255] ) ), ]),
        ("grisFonceDivision",           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [94,95,97,255] ) ), ]),

        ("SD2_BoutonBleuToggle",        MAP [
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = BleuGris ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [52,66,75,200] ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = BleuVariable ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = [52,66,75,200] ) ),
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                        ]),

        ("Noir_lobby",                  MAP [
                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [10,11,23,178] ) ),
                                            ( ~/ComponentState/Grayed,             TColorRTTI( Color = [255,255,255,0] ) ),
                                            ]),

        ("Debrief/UpdateProfile",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),


        ("SD2_Gris80",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gray90 ) ), ]),
        ("SD2_Gris90",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gray90 ) ), ]),
        ("SD2_Pawns_BoutonDesUnitesDansCompagnie",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = [0,0,0,0] ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = NoirPur80 ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = NoirPur50 ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = NoirPur50) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = BleuVariable ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [20,20,20,255] ) ),
                                           // ADD FOCUSED STATE --> LightestGray
                                        ]),
        ("Steelman_Background_BoutonRenfort",                     MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = NoirTransparent5 ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = NoirPur80 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = GrisSteelman ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = GrisSteelman ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = GrisSteelman ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = GrisSteelman ) ),
                                                        ]),
        ("Steelman_Background_BoutonOrdreDeBataille",   MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = Noir ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [150,150,150,125] ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Grullo ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Grullo ) ),
                                                        ]),
        ("Steelman_Objective_Pole",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair10 ) ), ]),
        ("Transparent",                MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleClicked,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleGrayed,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Intermediate,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/IntermediateHighlighted,     TColorRTTI( Color = Transparent ) ),
                                                        ]),
        ("Green",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0,255,0,255] ) ), ]),
        ("DarkestGray",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkestGray ) ), ]),
        ("DarkerGray",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkerGray ) ), ]),
        ("DarkerGray70",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkerGray70 ) ), ]),
        ("DarkerGray80",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkerGray80 ) ), ]),
        ("DarkGray",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkGray ) ), ]),
        ("DarkGray2",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [10,10,10,200] ) ), ]),
        ("DarkGold",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkGold ) ), ]),
        ("Glacier",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Glacier ) ), ]),
        ("Gray",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gray ) ), ]),
        ("GrisLabelStrat",             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [45,45,45,255] ) ), ]),
        ("Gray70",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gray70 ) ), ]),
        ("Gray80",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gray80 ) ), ]),
        ("LightGray",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LightGray ) ), ]),
        ("LightGray80",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LightGray80 ) ), ]),
        ("GrayMineShaft80",            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrayMineShaft80  ) ), ]),
        ("LighterGray",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LighterGray ) ), ]),
        ("GrayGave",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrayGave ) ), ]),
        ("GrayFuscous",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrayFuscous ) ), ]),
        ("PlayerLine/Background/Empty",MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("GrisClair",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair ) ), ]),
        ("LightestGray",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LightestGray ) ), ]),
        ("SeaBlue",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = SeaBlue ) ), ]),
        ("SeaBlue50p",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [ 55, 100, 145, 130] ) ), ]),
        ("Cyan",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Cyan ) ), ]),
        ("Cyan50p",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Cyan50p ) ), ]),
        ("Blanc",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),
        ("Blanc50p",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc30 ) ), ]),
        ("Yellow",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Jaune ) ), ]),
        ("Orange",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange ) ), ]),
        ("Orange3",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Orange3 ) ), ]),
        ("Red",                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Rouge4_strat2 ) ), ]),
        ("VividRed",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VividRed ) ), ]),
        ("White",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),
        ("PureWhite30",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc30 ) ), ]),
        ("PureBlack",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),
        ("AppleGreen",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPomme ) ), ]),
        ("DarkGreen",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertSombre ) ), ]),
        ("Black80",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Black80 ) ), ]),
        ("Black40",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Black40 ) ), ]),
        ("LabelDebrief",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeMoyen ) ), ]),
        ("Menu/SaveLoadLine",          MAP [
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = [107,124,137,35] ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BlueGray ) ),
                                        ]),
        ("Menu/SaveLoad",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [107,124,137,70] ) ), ]),
        ("ButtonHUD/Main",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc184 ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                           // ADD FOCUSED STATE --> LightestGray
                                       ]),
        ("BlancTransparent",           MAP [ ( ~/ComponentState/Normal,              TColorRTTI( Color = BlancTransparent ) ), ]),
        ("BlancTresTransparent",       MAP [ ( ~/ComponentState/Normal,              TColorRTTI( Color = BlancTresTransparent ) ), ]),
        ("DropdownBlanc",              MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc2 ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BlueGray ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlueGray ) ),
                                       ]),
        ("ButtonHUD/Second",           MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = Black80 ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = Black80 ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = GrayMineShaft50 ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = GrayMineShaft50 ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = GrayDusty80 ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = GrayDulver80 ) ),
                                           // ADD FOCUSED STATE --> DarkerGray
                                       ]),
        ("ButtonHUD/Background",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft50 ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = Black80 ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = GrayGave ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = GrayMineShaft ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = Gray ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = GrayGave ) ),
                                           // ADD FOCUSED STATE --> LightestGray
                                       ]),
        ("ButtonHUD/Background2",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft50 ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = Black80 ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = GrayMineShaft80 ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = GrayMineShaft ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc184 ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc7 ) ),
                                           // ADD FOCUSED STATE --> LightestGray
                                       ]),
        ("ButtonChooseUnitDansDeck",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = Black80 ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = GrayMineShaft80 ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = GrayMineShaft ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc184 ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc7 ) ),
                                           // ADD FOCUSED STATE --> LightestGray
                                       ]),
        ("ButtonHUD/Background3",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = NoirPur ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = NoirPur ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = GrayGave ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = GrayMineShaft ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc184 ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc7 ) ),
                                           // ADD FOCUSED STATE --> LightestGray
                                       ]),

        ("ButtonHUD/BigBorder",        MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc750 ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = GrayMineShaft ) ),
                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = DoveGray ) ),
                                        ]),
        ("ButtonHUD/BigBorder2",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc750 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = NoirPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = GrayGave ) ),
                                                        ]),

         ("ButtonHUD/BigBorderBlanc184",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc750 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = GrayGave ) ),
                                                        ]),


        ("ConfirmButton/Background",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = DarkerGray ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = DarkerGray ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = GrayGave ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = GrayGave ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = GrayGave ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = LightGrayGave ) ),
                                           // ADD FOCUSED STATE --> LightestGray
                                       ]),
        ("ConfirmButton/BigBorder",    MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = BlueGrayedOutgame ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = BlueOutgame ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BlueOutgame ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlueLightestOutgame ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlueOutgame ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlueLightOutgame ) ),
                                       ]),

        ("BattlePreparation/TeamButton",                MAP [
                                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlueGray ) ),
                                                            ( ~/ComponentState/Toggled,     TColorRTTI( Color = BlueGray ) ),
                                                        ]),

        ("bouton_selectScenario",                       MAP [
                                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [93,29,28,0] ) ),
                                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [128,128,128,255] ) ),
                                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [180,180,180,180] ) ),
                                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = [255,255,255,80] ) ),
                                                        ]),

        ("DeckOverview/FondCaseGrisee",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkerGray ) ), ]),
        ("DeckOverview/CouleurUniteNonDisponible",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair7 ) ), ]),


        ("overlayPourSaveLoad",                         MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlueGray ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = BlueGray ) ),
                                                            ]),
        ("DivisionBrief/ButtonOverlay",                 MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancTransparent ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = BlancTransparent ) ),
                                                            ]),
        ("HUBOutWelcome/ButtonOverlay",                 MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancTransparent ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = BlancTransparent ) ),
                                                            ]),
        ("HUBOutGameNavBar/ButtonOverlay",                 MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancTransparent ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = BlancTransparent ) ),
                                                            ]),
        ("HUBOutGameSolo/ButtonOverlay",                 MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = Black ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = Black) ),
                                                            ]),
        ("Showroom/Overlay_DarkWhenToggled",             MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Black80 ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = Black60 ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = Transparent ) ),
                                                              ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = [30,30,30,50] ) ),
                                                            ]),
        ("Showroom/Overlay",                             MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = [30,30,30,50] ) ),
                                                              ( ~/ComponentState/Clicked,     TColorRTTI( Color = Black60 ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = Black40 ) ),
                                                              ( ~/ComponentState/ToggleClicked, TColorRTTI( Color = Black60 ) ),
                                                              ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Black60 ) ),
                                                            ]),
        ("DivisionBrief/OddOverlay",                    MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = Black40 ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = Black40 ) ),
                                                            ]),

        ("deck_lobby",              MAP [
                                        ( ~/ComponentState/Normal, TColorRTTI( Color = [41,45,46,0] ) ),
                                        ( ~/ComponentState/Highlighted,  TColorRTTI( Color = [255,255,255,30] ) ),
                                        ( ~/ComponentState/Grayed, TColorRTTI( Color = [255,255,255,0] ) ),
                                        ]),

        ("game_option_lobby",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [33,41,53,255] ) ) ]),
        ("even_option_lobby",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [103,110,117,255] ) ),
                                          ( ~/ComponentState/Highlighted, TColorRTTI( Color = [103,110,117,230] ) ),
                                          ( ~/ComponentState/Grayed, TColorRTTI( Color = [103,110,117,255] ) ),
                                        ]),
        ("odd_option_lobby",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [134,138,139,255] ) ),
                                          ( ~/ComponentState/Highlighted, TColorRTTI( Color = [134,138,139,230] ) ),
                                          ( ~/ComponentState/Grayed, TColorRTTI( Color = [134,138,139,255] ) ),
                                        ]),

        ("OfficialGameBackground",  MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [30, 210 ,210 , 50] ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [35, 225, 225, 105] ) ),
                                            ( ~/ComponentState/Clicked,     TColorRTTI( Color = [40, 250, 250, 70] ) ),
                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = [30, 210, 210, 30] ) ),
                                            ]),

        ("SoloMission/Background",      MAP [
                                                ( ~/ComponentState/Normal,      TColorRTTI  ( Color = Transparent ) ),
                                                ( ~/ComponentState/Highlighted,  TColorRTTI  ( Color = BlueLightOutgame40 ) ),
                                                ( ~/ComponentState/Toggled,      TColorRTTI  ( Color = BleuGris ) ),
                                                ( ~/ComponentState/Grayed,       TColorRTTI  ( Color = Transparent ) ),
                                            ]),
        ("SoloMission/OverBlock",      MAP [
                                                ( ~/ComponentState/Normal,      TColorRTTI  ( Color = Transparent ) ),
                                                ( ~/ComponentState/Grayed,       TColorRTTI  ( Color = Gray90 ) ),
                                            ]),


        ("loginBoutonBackground_vert2", MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [73,215,99,120] ) ),
                                            ( ~/ComponentState/Highlighted,      TColorRTTI( Color = [73,215,99,240] ) ),
                                            ]),
        ("loginBoutonBackground_vert",  MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Highlighted,      TColorRTTI( Color = [124,210,150,240] ) ),
                                            ( ~/ComponentState/ToggleGrayed,      TColorRTTI( Color = [124,210,150,180] ) ),
                                            ]),

        ("loginBoutonBackground_cyan",  MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [30,210,210,120] ) ),
                                            ( ~/ComponentState/Highlighted,      TColorRTTI( Color = [30,210,210,240] ) ),
                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = [30, 30, 30, 120] ) ),
                                            ]),
        ("loginBoutonBackground_highlightedCyan", MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [0, 0, 0, 0] ) ),
                                            ( ~/ComponentState/Highlighted,      TColorRTTI( Color = [30,210,210,240] ) ),
                                            ]),


        ("MultiList/Background",       MAP [
                                           ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                           ( ~/ComponentState/Highlighted, TColorRTTI( Color = Black40 ) ),
                                           ( ~/ComponentState/Toggled,     TColorRTTI( Color = Black40 ) ),
                                       ]),

        ("Onglet/Background",           MAP [ ( ~/ComponentState/Normal,            TColorRTTI ( Color = NoirPur ) ), ]),

        ("Onglet/Underline",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = BlueOutgame ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = BlueLightestOutgame ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = BlueLightOutgame ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = BlueLightOutgame ) ),
                                        ]),

        ("Onglet/AdditionalUnderline",  MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = Transparent ) ),
                                            // ADD FOCUSED COMPONENT STATE -> LighterGray
                                        ]),

        ("UnitButton/Availability",     MAP [
                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = bleuNavy_fonce ) ),
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                        ]),
        ("Underline",            MAP [
                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Clicked,     TColorRTTI( Color = Glacier ) ),
                                            ( ~/ComponentState/Toggled,     TColorRTTI( Color = Glacier ) ),
                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Glacier ) ),
                                        ]),




        ("WeaponButton/APButton",       MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Gray ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = DarkGray ) ),
                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = DarkGray ) ),
                                        ]),

        ("WeaponButton/HEButton",       MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Gray ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = DarkGray ) ),
                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = DarkGray ) ),
                                        ]),

        ("OffMapPanel/Overblock",       MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = DarkerGray70 ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Transparent ) ),
                                        ]),

        ("OffMapPanel/BackgroundHeader",MAP [
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Black80 ) ),
                                        ]),

        ("Scrollbars/Background",  MAP [
                                                ( ~/ComponentState/Normal,      TColorRTTI( Color = GrayMineShaft ) ),
                                                ( ~/ComponentState/Highlighted, TColorRTTI( Color = LightGray ) ),
                                                ( ~/ComponentState/Clicked,     TColorRTTI( Color = GrisMoyen100 ) ),
                                            ]),
        ("Scrollbars/Elevator/Background",  MAP [
                                                ( ~/ComponentState/Normal,      TColorRTTI( Color = Blanc184 ) ),
                                                ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc5 ) ),
                                                ( ~/ComponentState/Clicked,     TColorRTTI( Color = Blanc2 ) ),
                                            ]),

        ("ScrollBars/GoTo",             MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = LightGray ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = LightestGray ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                        ]),

        ("Slider/Elevator",             MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = LightGray ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = LightestGray ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                       ]),
        ("Slider/GoToBeginningOrEnd",             MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = LightGray ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = LightestGray ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc2 ) ),
                                       ]),
        ("Slider/JaugeBackground",             MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = LightGray ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = LightestGray ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc2 ) ),
                                       ]),
        ("InputField/Background",       MAP [
                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = LighterGray40 ) ),
                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = NoirPur ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = NoirPur ) ),
                                            ( ~/ComponentState/Toggled,     TColorRTTI ( Color = NoirPur ) ),
                                                        ]),
        ("InputField/Selection",        MAP [
                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = Gray ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = LighterGray ) ),
                                        ]),
        ("Checkbox/Background",         MAP [
                                            ( ~/ComponentState/Grayed,                           TColorRTTI( Color = LightGray ) ),
                                            ( ~/ComponentState/Normal,                           TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/Highlighted,                      TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/Clicked,                          TColorRTTI( Color = LightestGray ) ),
                                            ( ~/ComponentState/Toggled,                          TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/ToggleHighlighted,                TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/ToggleGrayed,                     TColorRTTI( Color = LightGray ) ),
                                            ( ~/ComponentState/Intermediate,                     TColorRTTI( Color = DarkerGray ) ),
                                            ( ~/ComponentState/IntermediateHighlighted,          TColorRTTI( Color = DarkerGray ) ),
                                            // ADD FOCUSED STATE --> DarkerGray
                                        ]),
        ("SortingCheckbox/Background",         MAP [
                                            ( ~/ComponentState/Grayed,                           TColorRTTI( Color = LightGray ) ),
                                            ( ~/ComponentState/Normal,                           TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/Highlighted,                      TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/Clicked,                          TColorRTTI( Color = LightestGray ) ),
                                            ( ~/ComponentState/Toggled,                          TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/ToggleHighlighted,                TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/ToggleGrayed,                     TColorRTTI( Color = LightGray ) ),
                                            ( ~/ComponentState/Intermediate,                     TColorRTTI( Color = LighterGray ) ),
                                            ( ~/ComponentState/IntermediateHighlighted,          TColorRTTI( Color = LighterGray ) ),
                                            // ADD FOCUSED STATE --> DarkerGray
                                        ]),

        ("BoutonHUB",                   MAP [
                                            ( ~/ComponentState/Normal,                            TColorRTTI ( Color = Transparent ) ),
                                            ( ~/ComponentState/Highlighted,                       TColorRTTI ( Color = BlancPur ) ),
                                        ]),

        ("BoutonURL",                   MAP [ ( ~/ComponentState/Normal,                          TColorRTTI( Color = RougeSombre2 ) ), ]),


        ("Leaderboard/TrendEqual",      MAP [ ( ~/ComponentState/Normal,                          TColorRTTI( Color = [160, 160, 160,200] ) ), ]),
        ("Leaderboard/TrendPlus",       MAP [ ( ~/ComponentState/Normal,                          TColorRTTI( Color = [110,180,120,200] ) ), ]),
        ("Leaderboard/TrendMinus",      MAP [ ( ~/ComponentState/Normal,                          TColorRTTI( Color = [200, 110, 100,200] ) ), ]),
        ("Leaderboard/Unranked",        MAP [ ( ~/ComponentState/Normal,                          TColorRTTI( Color = [0,0,0,200] ) ), ]),

        // Leaderboard


        // -- Nouvelles Couleur (SD2)
        ("NoirPur",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),

        ("FlaresPanel/CustomFlare/EditableText/Fond",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gray70 ) ), ]),
        ("FlaresPanel/CustomFlare/EditableText/Selected",               MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = DarkerGray70 ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = DarkerGray70 ) ),
                                                                        ]),
        ("FlarePanel/FlareButtonBackground",                            MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancPur ) ),
                                                                            ]),
        ("AlertPanel/Gradient0",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("AlertPanel/Gradient1",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,191] ) ), ]),
        ("Label/Gradient0",                                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("Label/Gradient05",                                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0,0,0,70] ) ), ]),
        ("Label/Gradient1",                                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),
        ("MouseWidget/Gradient0",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("MouseWidget/Gradient1",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),
        ("MultiSelection/Background",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur40 ) ), ]),
        ("MultiSelection/EmptyBackground",                              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("PerformanceAlertPanel/Background",                            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur40 ) ), ]),


        ("DeckOverview/CaseGrisee/EditableText/Selected",               MAP [
                                                                                ( ~/ComponentState/Normal, TColorRTTI( Color = Bleu4_strat ) ),
                                                                                ( ~/ComponentState/Highlighted, TColorRTTI( Color = Bleu4_strat ) ),
                                                                            ]),

        ("PanelScore/ScoreBackground",                                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LighterGray ) ), ]),
        ("PanelScore/NoTeamBackground",                                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkGray ) ), ]),
        ("PingDisplay/GoodBar",                                         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Vert3 ) ), ]),
        ("PingDisplay/AverageBar",                                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Jaune ) ), ]),
        ("PingDisplay/BadBar",                                          MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Orange3 ) ), ]),
        ("PingDisplay/AwfulBar",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Rouge3 ) ), ]),
        ("PingDisplay/InactiveBar",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirTransparent1 ) ), ]),

        ("UnitLabel/GaugeBackground",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),
        ("UnitLabel/SupplyGauge",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Or4 ) ), ]),
        ("UnitLabel/ReloadChronoColor0",                                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("UnitLabel/ReloadChronoColor1",                                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Fulda_Turquoise ) ), ]),
        ("UnitLabel/BackgroundColor",                                   MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = BlancPur ) )
                                                                        ]),

        ("OffmapFeedback/Gradient0",                                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("OffmapFeedback/Gradient1",                                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur43 ) ), ]),
        ("ProductionMenu/IncomeChronoColor0",                           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Glacier ) ), ]),
        ("ProductionMenu/IncomeChronoColor1",                           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("ProductionMenu/UnitNameWhenNotAvailable",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair7 ) ), ]),


        ("SliderBasic/ThumbColor",                                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc184 ) ), ]),
        ("SliderBasic/SliderBar",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),

        ("UIPlayerSpecific/Red/Primary",                                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255, 0, 0, 255] ) ), ]),
        ("UIPlayerSpecific/Blue/Primary",                               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0,0,255,255] ) ), ]),
        ("UIPlayerSpecific/Green/Primary",                              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertSombre2 ) ), ]),
        ("UIPlayerSpecific/Orange/Primary",                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Orange5 ) ), ]),
        ("UIPlayerSpecific/Grey/Primary",                               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair10 ) ), ]),
        ("UIPlayerSpecific/Green/Secondary",                            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertSombre2 ) ), ]),
        ("UIPlayerSpecific/Orange/Secondary",                           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Orange5 ) ), ]),
        ("UIPlayerSpecific/Grey/Secondary",                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair10 ) ), ]),


        ("UIPlayerSpecific/Red/LabelIcon",                              MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [255, 0, 0, 255] ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [255, 0, 0, 255] ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = [255, 0, 0, 255] ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = [255, 0, 0, 255] ) ),
                                                                        ]),
        ("UIPlayerSpecific/Blue/LabelIcon",                             MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [0,0,255,200] ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [0,0,255,255] ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = [0,0,255,200] ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = [0,0,255,255] ) ),
                                                                        ]),
        ("UIPlayerSpecific/Green/LabelIcon",                            MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = Vert5 ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = VertPomme ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = VertClair3 ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = VertCitron ) ),
                                                                        ]),
        ("UIPlayerSpecific/Orange/LabelIcon",                           MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = Orange6 ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = OrangeClair ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = OrangeClair2 ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = JauneOrangeClair ) ),
                                                                        ]),
        ("UIPlayerSpecific/Grey/LabelIcon",                             MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancCasse ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = BlancCasse2 ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Blanc184 ) ),
                                                                        ]),
        //-------------------------------------------------------------------------------------

        ("UIPlayerSpecific/StratLabelIcon",                         MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [210,210,210,255] ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = [202,223,229,255] ) ),
                                                                        ]),
        ("UIPlayerSpecific/Red/StratLabelIcon",                         MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Gris ) ),
                                                                        ]),
        ("UIPlayerSpecific/Blue/StratLabelIcon",                        MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Gris ) ),
                                                                        ]),
        ("UIPlayerSpecific/Green/StratLabelIcon",                       MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Gris ) ),
                                                                        ]),
        ("UIPlayerSpecific/Orange/StratLabelIcon",                      MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Gris ) ),
                                                                        ]),
        ("UIPlayerSpecific/Grey/StratLabelIcon",                        MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = Gris ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Gris ) ),
                                                                        ]),

        ("UIPlayerSpecific/Red/StratLabelIconBackground",               MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique_Pion ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = RougeSovietique_Pion_Toggle ) ),
                                                                            ( ~/ComponentState/Highlighted,    TColorRTTI( Color = RougeSovietique_Pion_survol ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = BlancPur ) ),
                                                                        ]),
        ("UIPlayerSpecific/Blue/StratLabelIconBackground",              MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = GrisAllemand_Pion ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = GrisAllemand_Pion_Toggle ) ),
                                                                            ( ~/ComponentState/Highlighted,    TColorRTTI( Color = GrisAllemand_Pion_survol ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = BlancPur ) ),
                                                                        ]),
        ("UIPlayerSpecific/Green/StratLabelIconBackground",             MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = GrisAllemand_Pion ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = GrisAllemand_Pion_Toggle ) ),
                                                                            ( ~/ComponentState/Highlighted,    TColorRTTI( Color = GrisAllemand_Pion_survol ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = BlancPur ) ),
                                                                        ]),
        ("UIPlayerSpecific/Orange/StratLabelIconBackground",            MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique_Pion ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = RougeSovietique_Pion_Toggle ) ),
                                                                            ( ~/ComponentState/Highlighted,    TColorRTTI( Color = RougeSovietique_Pion_survol ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = BlancPur ) ),
                                                                        ]),
        ("UIPlayerSpecific/Grey/StratLabelIconBackground",              MAP [
                                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = GrisBleu ) ),
                                                                            ( ~/ComponentState/Toggled, TColorRTTI( Color = BlancPur ) ),
                                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = GrisBleu ) ),
                                                                        ]),

        ("UIPlayerSpecific/Blue/AllianceScoreColor",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisAllemand ) ), ]),
        ("UIPlayerSpecific/Green/AllianceScoreColor",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuMoyen2 ) ), ]),
        ("UIPlayerSpecific/Red/AllianceScoreColor",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique ) ), ]),
        ("UIPlayerSpecific/Orange/AllianceScoreColor",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique ) ), ]),
        ("UIPlayerSpecific/Grey/AllianceScoreColor",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisBleu ) ), ]),


        ("UIPlayerSpecific/Blue/PawnFriezeColor",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisAllemand_frieze ) ), ]),
        ("UIPlayerSpecific/Red/PawnFriezeColor",                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique_frieze ) ), ]),
        ("UIPlayerSpecific/Green/PawnFriezeColor",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisAllemand_frieze ) ), ]),
        ("UIPlayerSpecific/Orange/PawnFriezeColor",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique_frieze ) ), ]),
        ("UIPlayerSpecific/Grey/PawnFriezeColor",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Bleu_info_unit ) ), ]),


        ("UIPlayerSpecific/Red/LabelActionPoints",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique_100 ) ), ]),
        ("UIPlayerSpecific/Blue/LabelActionPoints",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Otan ) ), ]),
        ("UIPlayerSpecific/Green/LabelActionPoints",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisAllemand_100 ) ), ]),
        ("UIPlayerSpecific/Orange/LabelActionPoints",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique_100 ) ), ]),
        ("UIPlayerSpecific/Grey/LabelActionPoints",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisBleu ) ), ]),

        ("UIPlayerSpecific/Blue/ObjectiveTitle",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2  ) ), ]),
        ("UIPlayerSpecific/Green/ObjectiveTitle",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuMoyen2 ) ), ]),
        ("UIPlayerSpecific/Red/ObjectiveTitle",                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2 ) ), ]),
        ("UIPlayerSpecific/Orange/ObjectiveTitle",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique ) ), ]),
        ("UIPlayerSpecific/Grey/ObjectiveTitle",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisBleu ) ), ]),

        ("UIPlayerSpecific/Blue/ObjectiveContent",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2 ) ), ]),
        ("UIPlayerSpecific/Green/ObjectiveContent",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuMoyen2 ) ), ]),
        ("UIPlayerSpecific/Red/ObjectiveContent",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2 ) ), ]),
        ("UIPlayerSpecific/Orange/ObjectiveContent",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSovietique ) ), ]),
        ("UIPlayerSpecific/Grey/ObjectiveContent",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisBleu ) ), ]),

        ("Strategic/PawnPower/AttackTop",                              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = OrangeClair ) ), ]),
        ("Strategic/PawnPower/DefenseTop",                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),

        ("Strategic/PawnPower/AssaultTop",                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Assault ) ), ]),
        ("Strategic/PawnPower/ArmoredTop",                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Armored ) ), ]),
        ("Strategic/PawnPower/ArtilleryTop",                           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Artillery ) ), ]),
        ("Strategic/PawnPower/AirplaneTop",                            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RedNutmeg ) ), ]),
        ("Strategic/PawnPower/EvasionTop",                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RedNutmeg ) ), ]),

        ("ObjectiveLabel/CapturePoint/Background",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc50_2 ) ), ]),




        ("TimePanel/Background",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Black80 ) ), ]),


        ("BoutonTimePanel",                                            MAP [
                                                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = Black ) ),
                                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = DarkerGray ) ),
                                                                            ( ~/ComponentState/Clicked,     TColorRTTI( Color = DarkestGray ) ),
                                                                            ( ~/ComponentState/Toggled,     TColorRTTI( Color = DarkestGray ) ),
                                                                       ]),

        ("ProdMenu/UnitButton",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Black80 ) ), ]),

        // Pour debug

        //OutGame
        ("BoutonsDAction/Standard",                     MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Clicked,     TColorRTTI ( Color = MarronRose ) ),
                                                        ]),
        ("BoutonsDAction/MissingDLCs",                     MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = MarronBleu ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = MarronBleu ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = MarronBleu ) ),
                                                            ( ~/ComponentState/Clicked,     TColorRTTI ( Color = MarronBleu ) ),
                                                        ]),
        ("BoutonsDAction/Image",                        MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Clicked,     TColorRTTI ( Color = Transparent ) ),
                                                        ]),
        ("ListeExcel/Cartouche",                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen2_100 ) ), ]),
        ("ListeExcel/Fond",                             MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = LighterGray40 ) ),
                                                            ( ~/ComponentState/Clicked,     TColorRTTI ( Color = LighterGray80 ) ),
                                                        ]),
        ("GameDebrief/TeamStats/PlayerLine/Pact",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSombre ) ), ]),
        ("GameDebrief/TeamStats/PlayerLine/Nato",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuSombre ) ), ]),
        ("Debrief/Title",                               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirTransparent1 ) ), ]),
        ("Debrief/ColumnTitle",                         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirTransparent3 ) ), ]),
        ("ModCenter",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen60 ) ), ]),
        ("Timer",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertSombre ) ), ]),

        ("Scrollbar",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Vert ) ), ]),
        ("DivisionPanel/DivisionName",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombrePlus ) ), ]),
        ("DivisionPanel/DivisionNickname",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombrePlus ) ), ]),
        ("DivisionPanel/PhaseIncomeLabel",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),
        ("DivisionPanel/PhaseIncome",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),

        ("TacticalPreparation/Droppable",               MAP [
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = Black40 )),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = [135, 178, 196, 125] ) ), // glacier
                                                            ( ~/ComponentState/Clicked,     TColorRTTI ( Color = Glacier ) ),
                                                            ( ~/ComponentState/Toggled,     TColorRTTI ( Color = Glacier ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI ( Color = Glacier ) ),
                                                        ]),

        ("Friend/AcceptButton",                         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Glacier ) ), ]),
        ("Friend/JoinButton",                           MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = ForestGreen ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = ForestGreenLight ) ),
                                                            ]),
        ("Friend/DeclineButton",                        MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen100 ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc184 ) ),
                                                        ]),

        ("MoraleGauge",                                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [152, 115, 35, 255] ) ), ]),
        ("ShowroomGray",                                MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked, TColorRTTI( Color = BlancPur ) ),
                                                        ]),


        // Pour debug
        ("ScenarioDlc/Fond",                            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur50 ) ), ]),
        ("UIPlayerSpecific/Blanc",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc184 ) ),
                                                        ]),
        ("UIPlayerSpecific/Otan",               MAP [
                                                    ( ~/ComponentState/Normal, TColorRTTI( Color = Otan ) ),
                                                    ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc184 ) ),
                                                    ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                    ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc184 ) ),
                                                    ]),

        ("UIPlayerSpecific/Pact",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Pact ) ), ]),
        ("UIPlayerSpecific/Pact_light",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [81,39,39,80] ) ), ]),

        ////-------------------------------------------------------------------------------------
        // OTAN   -------------------------------------------------------------------------------
        ("DivisionBrief/Background/Allies",             MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = Otan ) ),
                                                              ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Otan_fond ))
                                                            ]),
        ("DivisionBrief/Background/Axis",               MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = Pact ) ),
                                                              ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Pact_fond ))
                                                            ]),
        ("playerHelper/Otan_background",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Otan_fond ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Otan_fond_plus30 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,153] ) ),
                                                        ]),
        ("playerHelper/Otan_background_light",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Otan_fond70 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Otan_fond ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,153] ) ),
                                                        ]),
        ("playerHelper/Otan_line",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Otan ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Otan ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,255] ) ),
                                                        ]),
        ("playerHelper/Cover/Otan_line",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Cyan ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Cyan ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Cyan ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,255] ) ),
                                                        ]),

        ("playerHelper/Otan_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Otan ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                                        ]),

        // VERT
        ("playerHelper/Vert/Otan_background",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Otan_fond_vert ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Otan_fond_plus30_vert ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,153] ) ),
                                                        ]),
        ("playerHelper/Vert/Otan_background_light",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Otan_fond70_vert ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Otan_fond_vert ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,153] ) ),
                                                        ]),
        ("playerHelper/Vert/Otan_line",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Otan_vert ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Otan_vert ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,255] ) ),
                                                        ]),

        ("playerHelper/Vert/Otan_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Otan_vert ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                                        ]),
        //-------------------------------------------------------------------------------------
        // PACT
        ("playerHelper/Pact_background",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Pact_fond ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Pact_fond_plus30 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Pact_blanc_fond ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Pact_blanc_fond ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Pact_blanc_fond ) ),
                                                        ]),
        ("playerHelper/Pact_background_light",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Pact_fond70 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Pact_fond ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,153] ) ),
                                                        ]),
        ("playerHelper/Pact_line",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Pact ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Pact ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,255] ) ),
                                                        ]),
        ("playerHelper/Cover/Pact_line",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [255,0,0,255] ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [255,0,0,255] ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [255,0,0,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,255] ) ),
                                                        ]),

        ("playerHelper/Pact_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Pact ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                                        ]),
        // ORANGE
        ("playerHelper/Orange/Pact_background",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Pact_fond_orange ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Pact_fond_plus30_orange ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Pact_blanc_fond ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Pact_blanc_fond ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Pact_blanc_fond ) ),
                                                        ]),
        ("playerHelper/Orange/Pact_background_light",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Pact_fond70_orange ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Pact_fond_orange ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,153] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,153] ) ),
                                                        ]),
        ("playerHelper/Orange/Pact_line",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Pact_orange ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Pact_orange ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,255] ) ),
                                                        ]),

        ("playerHelper/Orange/Pact_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Pact_orange ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur) ),
                                                        ]),


        ("StrategicButton_highlightable",       MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [64, 77, 64, 210] ) ),
                                                        ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [84, 97, 84, 210] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [44, 57, 44, 100] ) ),
                                                    ]),


        ("StrategicPanel",                      MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [64, 77, 64, 210] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [44, 57, 44, 100] ) ),
                                                    ]),

        ("TacticButton_highlightable",          MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [122, 167, 176, 102]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BleuVariable ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color =  [22,133,173,255] ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Blanc2 ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [122,167,176,50] ) ),
                                               ]),

        ("TacticPanel",                         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,191] ) ), ]),


    ]

    //LineColors
    LineColorsMap = MAP [
        //InGame
        // ++ Nouvelles Couleur (SD2)

        ("CyanFonceFulda",                           MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [30,120,136,190]  ) ),
                                                   (~/ComponentState/Highlighted,            TColorRTTI( Color = [255,255,255,27]  ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = [255,255,255,40]  ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [255,255,255,27]  ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [255,255,255,20]  ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = VertGris ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = BleuVariable ) ),
                                           ]),
        ("Test",                                        MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = LightestGray  ) ),
                                                                        (~/ComponentState/Highlighted,            TColorRTTI( Color = Blanc2 ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = Green ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BleuSombre2 ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Jaune ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = Gris39 ) ),
                                                                    ]),
        ("BoutonTemps_Line",                                   MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [153,190,201,102] ) ),
                                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc2 ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [144,205,227,255] ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BleuSombre2 ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = BleuSombre2 ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [128,128,128,128] ) ),
                                                                   ]),
        ("BoutonBlanc",                                        MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [190,190,190,255]  ) ),
                                                                        (~/ComponentState/Highlighted,            TColorRTTI( Color = Blanc2 ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [128,128,128,128] ) ),
                                                                   ]),
        ("BoutonVignetteAchat",                                MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent  ) ),
                                                                        (~/ComponentState/Highlighted,            TColorRTTI( Color = [19,85,106,191] ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [19,85,106,191] ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [19,85,106,191] ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = [19,85,106,191] ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                                   ]),
        ("NbUnitsInPackContainer_color",        MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [81,134,145,150]  ) ),
                                                   // ( ~/ComponentState/Highlighted,           TColorRTTI( Color = bleuNavy_superclair ) ),
                                                   // ( ~/ComponentState/Clicked,               TColorRTTI( Color = BleuSelectionClicked ) ),
                                                   // ( ~/ComponentState/Toggled,               TColorRTTI( Color = bleuNavy_clair ) ),
                                                   // ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = bleuNavy_superclair ) ),
                                                   // ( ~/ComponentState/Grayed,                TColorRTTI( Color = [25,25,25,150] ) ),
                                               ]),
        ("ArmoryUnitButtonName",        MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = bleuNavy  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = bleuNavy_superclair ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = BleuSelectionClicked ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = bleuNavy_clair ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = bleuNavy_superclair ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [25,25,25,150] ) ),
                                               ]),


        ("bleuNavy",                           MAP [( ~/ComponentState/Normal,                TColorRTTI( Color = bleuNavy  ) ),]),
        ("bleuNavy_clair",                           MAP [( ~/ComponentState/Normal,                TColorRTTI( Color = bleuNavy_clair  ) ),]),
        ("bleuNavy_fonce",                           MAP [( ~/ComponentState/Normal,                TColorRTTI( Color = bleuNavy_fonce  ) ),]),



        ("BoutonVignetteAchatArmory",                           MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent  ) ),
                                                                        (~/ComponentState/Highlighted,            TColorRTTI( Color = bleuNavy_superclair ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = BleuSelectionClicked ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = bleuNavy_clair ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = bleuNavy_superclair ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = BleuSelectionClicked ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                                   ]),
        ("DeckCreator/AddUnitToDeck",                           MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = bleuNavy_clair ) ),
                                                                        (~/ComponentState/Highlighted,            TColorRTTI( Color = bleuNavy_superclair ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = BleuSelectionClicked ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = BleuSelectionGrayed ) ),
                                                                   ]),
        ("DeckCreator/RemoveUnitFromDeck",                      MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = bleuNavy_fonce ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = [80,80,80,240] ) ),
                                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [50,50,50,240] ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [0, 0, 0,20] ) ),
                                                                   ]),
        ("DeckCreator/SlotLibre",                               MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = bleuNavy ) ), ]),
        ("DeckCreator/SlotSelectionne",                         MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = bleuNavy_clair ) ), ]),
        ("Cyan",                                                MAP [
                                                                        ( ~/ComponentState/Normal,      TColorRTTI( Color = [0,255,255,240] ) ),
                                                                        ( ~/ComponentState/Grayed,      TColorRTTI( Color = [80, 80, 80,160] ) ),
                                                                    ]),

        ("H2_bleu_2",                                           MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [83,138,166,255] ) ), ]),
        ("BleuPV",                                              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [45,112,118,245] ) ), ]),
        ("H2_bleu_2_40p",                                       MAP [
                                                                        ( ~/ComponentState/Normal,      TColorRTTI( Color = [59,134,161,102] ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = Noir ) ),
                                                                    ]),
        ("LigneSeparatricePanelRessources",                     MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [67,131,151,255] ) ), ]),
        // bleu clair
        ("H2_bleu_3",                                           MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [139,200,241,255] ) ), ]),
        ("Fulda_LigneVerte",                                    MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [65, 112, 94,255]  ) ),
                                                                        (~/ComponentState/Highlighted,            TColorRTTI( Color = [153, 255, 189,255] ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [65, 112, 94,255] ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [153, 255, 189,255] ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                                    ]),
        ("Fulda2_Orange100",                                    MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = Fulda2_Orange100 ) ), ]),
        ("Fulda2_Noir",                                         MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = Fulda2_Noir ) ), ]),
        ("Black",                                               MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = NoirPur  ) ), ]),

        ("Noir60",                                              MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = Noir60  ) ), ]),
        ("DarkGray",                                            MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = DarkGray ) ), ]),
        ("Green",                                               MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [0,255,0,255] ) ), ]),
        ("Vert",                                                MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [0,255,0,255] ) ), ]),
        ("Blanc",                                               MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [255,255,255,255] ) ), ]),
        ("Gray",                                                MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = Gray ) ), ]),
        ("LightGray",                                           MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = LightGray ) ), ]),
        ("LighterGray",                                         MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = LighterGray ) ), ]),
        ("LightestGray",                                        MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = LightestGray ) ), ]),
        ("DarkGold",                                            MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = DarkGold ) ), ]),
        ("SD2_Blanc184",                                        MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = Blanc184  ) ),
                                                                      (~/ComponentState/Highlighted,            TColorRTTI( Color = BlancPur ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = LightestGray ) ),

            ]),
        ("SM_selectFlag",                                       MAP [
                                                                        ( ~/ComponentState/Normal,                      TColorRTTI( Color = [184,184,184,0] ) ),
                                                                        ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [202,223,229,255] ) ),
                                                                        ( ~/ComponentState/Toggled,                TColorRTTI( Color = [202,223,229,255] ) ),
                                                                    ]),
        ("SD2_Blanc184_2",                                      MAP [
                                                                        ( ~/ComponentState/Normal,      TColorRTTI( Color = Blanc184  ) ),
                                                                        (~/ComponentState/Highlighted,  TColorRTTI( Color = BlancPur ) ),
                                                                    ]),
        ("SD2_BleuVariable",                                    MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = Glacier  ) ), ]),
        ("SD2_Gris39",                                          MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = Gris39 ) ), ]),
        ("SD2_LigneGris",                                       MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [39,32,25,255] ) ),]),
        ("SD2_LigneBleuGris",                                   MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = LigneBleuGris  ) ),
                                                                        (~/ComponentState/Highlighted,            TColorRTTI( Color = BlancPur ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                                    ]),

         ("SD2_StratBoutonCombatGroup",                         MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = Transparent ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = LightestGray ) ),
                                                                    ]),
         ("SD2_testLigne",                                      MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = LightestGray ) ),
                                                                    ]),
        ("Transparent",                             MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleClicked,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleGrayed,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Intermediate,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/IntermediateHighlighted,     TColorRTTI( Color = Transparent ) ),
                                                        ]),
        ("SD2_Stroke_Bouton2a",                                      MAP [
                                                                        ( ~/ComponentState/Grayed,      TColorRTTI( Color = Transparent ) ),
                                                                        ( ~/ComponentState/Normal,      TColorRTTI( Color = [70,70,70,255] ) ),
                                                                        ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Clicked,     TColorRTTI( Color = Blanc2 ) ),
                                                                    ]),
        ("ButtonHUD",                                               MAP [
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Blanc5 ) ),
                                                                        // ADD FOCUSED STATE --> LighterGray
                                                                    ]),
        ("ButtonHUD/Border",                                       MAP [
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc750 ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = GrayMineShaft ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = DoveGray ) ),
                                                                    ]),
        ("ButtonHUD/Border2",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc750 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = NoirPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = GrayGave ) ),
                                                        ]),
        ("ButtonHUD/Border3",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc750 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = NoirPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = GrayGave ) ),
                                                        ]),

        ("Debrief/UpdateProfile",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),

        ("ButtonHUD/ExternalBorder",                                MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                                    ]),

        ("ConfirmButton/Border",                                    MAP [
                                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = BlueGrayedOutgame ) ),
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = BlueOutgame ) ),
                                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BlueOutgame ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlueLightestOutgame ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlueOutgame ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlueLightOutgame ) ),
                                                                        // ADD FOCUSED STATE --> DarkGray
                                                                    ]),
        ("ConfirmButton/ExternalBorder",                            MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                                    ]),

        ("BorderButtonHUD",                                         MAP [
                                                                        ( ~/ComponentState/Grayed,      TColorRTTI( Color = Transparent ) ),
                                                                        ( ~/ComponentState/Normal,      TColorRTTI( Color = Transparent ) ),
                                                                        ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc5 ) ),
                                                                        ( ~/ComponentState/Clicked,     TColorRTTI( Color = Blanc2 ) ),
                                                                    ]),

        ("DarkerGray",                                              MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = DarkerGray ) ), ]),

        ("OffMapPanel/ButtonBorder",                                MAP [
                                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = LightGray ) ),
                                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = LightestGray ) ),
                                                                    ]),

        ("Slider/BackgroundBorders",                                MAP [
                                                                        ( ~/ComponentState/Normal,              TColorRTTI( Color = Transparent ) ),
                                                                        // ADD FOCUSED STATE --> LighterGray
                                                                    ]),

        ("InputField/Line",                                         MAP [
                                                                        ( ~/ComponentState/Grayed,              TColorRTTI( Color = Gray ) ),
                                                                        ( ~/ComponentState/Normal,              TColorRTTI( Color = LightGray ) ),
                                                                        ( ~/ComponentState/Highlighted,         TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/Clicked,             TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = LightestGray ) ),
                                                                    ]),
        ("Checkbox/Frame",                                          MAP [
                                                                        ( ~/ComponentState/Grayed,                       TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/Normal,                       TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/Highlighted,                  TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Clicked,                      TColorRTTI( Color = Blanc2 ) ),
                                                                        ( ~/ComponentState/Toggled,                      TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,            TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleGrayed,                 TColorRTTI( Color = LighterGray ) ),
                                                                        ( ~/ComponentState/Intermediate,                 TColorRTTI( Color = LighterGray) ),
                                                                        ( ~/ComponentState/IntermediateHighlighted,      TColorRTTI( Color = LightestGray) ),
                                                                        // ADD FOCUSED STATE --> DarkerGray
                                                                    ]),
        ("SortingCheckbox/Frame",                                   MAP [
                                                                        ( ~/ComponentState/Grayed,                       TColorRTTI( Color = DarkerGray ) ),
                                                                        ( ~/ComponentState/Normal,                       TColorRTTI( Color = DarkerGray ) ),
                                                                        ( ~/ComponentState/Highlighted,                  TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/Clicked,                      TColorRTTI( Color = Blanc2 ) ),
                                                                        ( ~/ComponentState/Toggled,                      TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleHighlighted,            TColorRTTI( Color = LightestGray ) ),
                                                                        ( ~/ComponentState/ToggleGrayed,                 TColorRTTI( Color = DarkerGray ) ),
                                                                        ( ~/ComponentState/Intermediate,                 TColorRTTI( Color = DarkerGray) ),
                                                                        ( ~/ComponentState/IntermediateHighlighted,      TColorRTTI( Color = LightestGray) ),
                                                                        // ADD FOCUSED STATE --> DarkerGray
                                                                    ]),
        // -- Nouvelles Couleur (SD2)

        ("ContainerDarkStyle/BordureExterne",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),



        ("FlarePanel/FlareButtonBorder",                            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LightGray ) ), ]),


        ("DeckOverview/CaseVide/Survol",                            MAP [
                                                                          ( ~/ComponentState/Normal, TColorRTTI( Color = GrayMineShaft ) ),
                                                                          ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc2 ) ),
                                                                        ]),
        ("DeckOverview/CaseGrisee",                                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),




        ("CheckBox",                                                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombre ) ), ]), //ancien composant

        ("ScriptedLabelPole",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gris ) ), ]), //ancien composant

        ("PanelScore/SoulignageRevenusJoueur",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen ) ), ]),
        ("TimePanel/ButtonBorder",                                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LightGray ) ), ]),
        ("EditableText",                                            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = MarronDesature3 ) ), ]),


        ("Ingame/GaugeBorder",                                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Black80 ) ), ]),

        ("PawnLabel/ActionPoints",                                  MAP [
                                                                        ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ),
                                                                    ]),
        ("PawnSelection/Unit",                                      MAP [
                                                                        ( ~/ComponentState/Grayed,      TColorRTTI( Color = Transparent ) ),
                                                                        ( ~/ComponentState/Normal,      TColorRTTI( Color = Gray ) ),
                                                                        ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc2 ) ),
                                                                        ( ~/ComponentState/Clicked,     TColorRTTI( Color = Blanc2 ) ),
                                                                    ]),
        ("PawnSelection/UnitUniqueSelection",                       MAP [
                                                                        ( ~/ComponentState/Normal,      TColorRTTI( Color = Blanc2 ) ),
                                                                    ]),


        //OutGame
        ("BoutonsDAction/Standard",                     MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = GrisMoyen50_3 ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = BordeauSombre ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = BordeauSombre ) ),
                                                            ( ~/ComponentState/Clicked,     TColorRTTI ( Color = Blanc2 ) ),
                                                        ]),
        ("BoutonsDAction/MissingDLCs",                     MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = GrisMoyen50_3 ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = BlancCasse2 ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = BlancCasse2 ) ),
                                                            ( ~/ComponentState/Clicked,     TColorRTTI ( Color = Blanc2 ) ),
                                                        ]),
        ("BoutonsDAction/Image",                        MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = GrisMoyen50_3 ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = BordeauSombre ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = Blanc2 ) ),
                                                            ( ~/ComponentState/Clicked,     TColorRTTI ( Color = BlancPur ) ),
                                                        ]),
        ("BoutonHUB",                                   MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI ( Color = RougeVif ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI ( Color = RougeVif ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI ( Color = BlancPur ) ),
                                                        ]),
        ("BoutonURL",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSombre2 ) ), ]),

        ("Onglet",                                      MAP [
                                                            ( ~/ComponentState/Grayed,            TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Normal,            TColorRTTI ( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted,       TColorRTTI ( Color = Blanc2 ) ),
                                                            ( ~/ComponentState/Clicked,           TColorRTTI ( Color = Marron2 ) ),
                                                            ( ~/ComponentState/Toggled,           TColorRTTI ( Color = Marron2 ) ),
                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI ( Color = Marron2 ) ),
                                                        ]),


        ("Debrief/StatsLine",                           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Cyan ) ), ]),
        ("Debrief/LabelContour",                         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombre ) ), ]),



        ("Resume/CadreDivisions",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),

        ("UIPlayerSpecific/Otan",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Otan ) ), ]),
        ("UIPlayerSpecific/Pact",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Pact ) ), ]),
        ("UIPlayerSpecific/Orange/Primary",             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Orange5 ) ), ]),
        ("UIPlayerSpecific/Blanc",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [120,120,120,128] ) ), ]),
        ("UIPlayerSpecific/Green/Primary",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertSombre2 ) ), ]),


        ("MultiListLineBeige",                          MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc20 ) ), ]),

        ("PureBlack",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),

        ("Objective/CheckboxBorder",        MAP [
                                                ( ~/ComponentState/Grayed,                           TColorRTTI( Color = LighterGray ) ),
                                                ( ~/ComponentState/Normal,                           TColorRTTI( Color = LighterGray ) ),
                                                ( ~/ComponentState/ToggleGrayed,                     TColorRTTI( Color = Vert2 ) ),
                                            ]),
        ("UnitLabel/SupplyGaugeBorder",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Or2 ) ), ]),

        ("StrategicButton_highlightable",       MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = Grullo ) ),
                                                        ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [210,216,240,255] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [210,216,240,50] ) ),
                                                    ]),

        ("StrategicPanel",                      MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = Grullo ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [210,216,240,50] ) ),
                                                    ]),

        ("TacticButton_highlightable",          MAP [
                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [153,190,201,102] ) ),
                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc2 ) ),
                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [144,205,227,255] ) ),
                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BleuSombre2 ) ),
                                                        ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = BleuSombre2 ) ),
                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [128,128,128,128] ) ),
                                                   ]),

        ("TacticPanel",                         MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [83,138,166,255] ) ), ]),
    ]

    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------
    //-------------------------------------------------------------------------------------

    //TextColors
    TextColorsMap = MAP [

        ("Debrief/UpdateProfile",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Noir ) ), ]),


        ("SovDivision1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [134,37,4,180] ) ), ]),
        ("SovDivision2",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [116,90,35,180] ) ), ]),
        ("SovDivision3",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [201,59,12,180] ) ), ]),
        ("SovDivision4",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0,201,155,180] ) ), ]),


        ("AutoResolve/Title",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),
        ("AutoResolve/ATQ",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = OrangeClair ) ), ]),
        ("AutoResolve/DEF",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertClair3 ) ), ]),
        ("AutoResolve/Information",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair10 ) ), ]),
        ("AutoResolve/Rapport",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc184 ) ), ]),


        //UI Helper
        ("grisLabel",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [90,90,90,250] ) ), ]),
        ("grisLabel2",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [90,90,90,128] ) ), ]),
        ("ColorCode/bleu",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [67,255,255,255] ) ), ]),
        ("ColorCode/vert",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [127,255,117,255] ) ), ]),
        ("ColorCode/orange",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,190,0,255]) ), ]),
        ("ColorCode/rouge",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [172,0,0,255] ) ), ]),
        ("UIPlayerSpecific/Blanc",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc184 ) ),
                                                        ]),

        ("LightGrayBlue",                               MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [128,128,128,250] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [19,85,106,191] ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [90,90,90,128] ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [65,116,108,250] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [65,116,108,250] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [90,90,90,128] ) ),
                                                        ]),
        ("UIPlayerSpecific/Red/Primary",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,0,0,255] ) ), ]),
        ("UIPlayerSpecific/Otan",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [153,173,204,255] ) ), ]),
        ("UIPlayerSpecific/Pact",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [200,173,204,255] ) ), ]),
        ("UIPlayerSpecific/Green/Primary",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertSombre2 ) ), ]),
        ("UIPlayerSpecific/Orange/Primary",             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Orange5 ) ), ]),
        ("UIPlayerSpecific/Grey/Primary",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair10 ) ), ]),
        ("UIPlayerSpecific/Red/LabelIcon",              MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [255,0,0,200] ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [255,0,0,255] ) ),
                                                        ]),
        ("UIPlayerSpecific/Blue/LabelIcon",             MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [0,0,255,200] ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [0,0,255,255] ) ),
                                                        ]),
        ("UIPlayerSpecific/Green/LabelIcon",            MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = Vert5 ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = VertClair3 ) ),
                                                        ]),
        ("UIPlayerSpecific/Orange/LabelIcon",           MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = Orange6 ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = OrangeClair2 ) ),
                                                        ]),
        ("UIPlayerSpecific/Grey/LabelIcon",             MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancPur ) ),
                                                        ]),
        ("UIPlayerSpecific/Blue/ObjectiveTitle",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2 ) ), ]),
        ("UIPlayerSpecific/Blue/ObjectiveContent",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2 ) ), ]),
        ("UIPlayerSpecific/Green/ObjectiveTitle",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuMoyen2 ) ), ]),
        ("UIPlayerSpecific/Green/ObjectiveContent",     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuMoyen2 ) ), ]),
        ("UIPlayerSpecific/Red/ObjectiveTitle",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2 ) ), ]),
        ("UIPlayerSpecific/Red/ObjectiveContent",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2 ) ), ]),
        ("UIPlayerSpecific/Orange/ObjectiveTitle",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeMoyen4 ) ), ]),
        ("UIPlayerSpecific/Orange/ObjectiveContent",    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeMoyen4 ) ), ]),
        ("UIPlayerSpecific/Grey/ObjectiveTitle",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen5 ) ), ]),
        ("UIPlayerSpecific/Grey/ObjectiveContent",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen5 ) ), ]),
        ("UIPlayerSpecific/Blue/PawnPhaseFighting",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color =  Noir) ), ]),
        ("UIPlayerSpecific/Blue/PawnPhaseNotFighting",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Otan_fond ) ), ]),
        ("UIPlayerSpecific/Red/PawnPhaseFighting",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color =  Noir) ), ]),
        ("UIPlayerSpecific/Red/PawnPhaseNotFighting",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Rouge ) ), ]),
        ("UIPlayerSpecific/Orange/PawnPhaseFighting",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),
        ("UIPlayerSpecific/Orange/PawnPhaseNotFighting",    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Rouge ) ), ]),
        ("UIPlayerSpecific/Grey/PawnPhaseFighting",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen5 ) ), ]),
        ("UIPlayerSpecific/Grey/PawnPhaseNotFighting",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen5 ) ), ]),

        ("UIPlayerSpecific/Blue/ActionPointCantAttack",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI ( Color = [ 128, 125, 255, 128] ) ), ]),
        ("UIPlayerSpecific/Red/ActionPointCantAttack",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI ( Color = [ 255, 128, 128, 128] ) ), ]),
        ("UIPlayerSpecific/Grey/ActionPointCantAttack",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI ( Color = BlancPur ) ), ]),
        //-------------------------------------------------------------------------------------
        // playerHelper - textes
        //-------------------------------------------------------------------------------------
        // OTAN
        ("playerHelper/Otan_line_Endgame",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Otan ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Otan ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [220,220,220,255] ) ),
                                                        ]),

        ("playerHelper/texte/Otan_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [255,255,255,255] ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Otan_fond ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Otan ) ),
                                                        ]),
        ("playerHelper/texte/Otan_line2",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [90+15,150+15,180+35,255] ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [0,0,180,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [0,0,180,255] ) ),
                                                        ]),
        // VERT
        ("playerHelper/texte/Vert/Otan_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Otan_vert ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Otan_vert ) ),
                                                        ]),


        ("GameDebrief/TeamStats/PlayerLine/Pact",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeSombre ) ), ]),
        //-------------------------------------------------------------------------------------
        // PACT
        ("playerHelper/Pact_background",                MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Pact_fond_orange ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Pact_fond_orange ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Pact_fond_orange ) ),
                                                        ]),
        ("playerHelper/Pact_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Pact_orange ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Pact_orange ) ),
                                                        ]),
        ("playerHelper/texte/Pact_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [255,255,255,255] ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Pact_fond ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [18,4,4,255] ) ),
                                                        ]),

        ("playerHelper/Pact_line",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [255,0,0,255] ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [180,0,0,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [180,0,0,255] ) ),
                                                        ]),
        // ORANGE
        ("playerHelper/texte/Orange/Pact_element",                      MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Pact_orange ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Pact_orange ) ),
                                                        ]),
        ("GameDebrief/TeamStats/PlayerLine/Nato",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuSombre ) ), ]),

        //InGame
        // endGame

        ("GrisTexte",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [69,69,69,235] ) ),]),
        ("NatoTexte",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [0,174,239,235] ) ),]),
        ("PacteTexte",                       MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [239,0,0,235] ) ),]),
        ("GrisTexteHighlightable",           MAP [
                                                    ( ~/ComponentState/Normal,                 TColorRTTI( Color = [69, 69, 69, 235] ) ),
                                                    ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [140, 140, 140, 235] ) ),
                                                ]),
        //-------------------------------------------------------------------------------------



         ("WeaponButton/Overblock",      MAP [
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = DarkerGray30 ) ),
                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [77,167,189,76] ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [37,127,149,156] ) ),
                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [30,30,30,120] ) ),
                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color =  [30,30,30,90] ) ),
                                        ]),
        //-------------------------------------------------------------------------------------
        // fin de partie

        ("Cyan_xp",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [  30,   255,   255, 65] ) ),]),
        //-------------------------------------------------------------------------------------
        // chat outgame
        ("CouleurfondChat",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [103,115,100,255] ) ),]),
        //-------------------------------------------------------------------------------------
        // hint
        ("Hint_titre",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [224,193,15,255] ) ),]),
        ("Hint_main",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [222,222,222,255] ) ),]),
        ("Hint_fond",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [23,23,23,255] ) ),]),
        ("Hint_ligne",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [187,187,187,255] ) ),]),


        // challenge
        ("challenge_titre",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [41,49,53,255] ) ),]),
        ("challenge_bordure",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [77,162,165,255] ) ),]),
        ("challenge_fond",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [50,86,100,230] ) ),]),
        //-------------------------------------------------------------------------------------
        ("BoutonTemps_Text",                          MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [156,210,226,255] ) ),
                                                        ]),
        ("BoutonManuelJaune",                    MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [222,255,176,102]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [222,255,176,182] ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [222,255,176,255] ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [222,255,176,255] ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Blanc2 ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [122,167,176,50] ) ),
                                               ]),
        ("BoutonManuelRouge",                    MAP [
                                                   ( ~/ComponentState/Normal,                TColorRTTI( Color = [222,155,136,102]  ) ),
                                                   ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [222,155,136,182] ) ),
                                                   ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc184 ) ),
                                                   ( ~/ComponentState/Toggled,               TColorRTTI( Color = [222,155,136,255] ) ),
                                                   ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [222,155,136,255] ) ),
                                                   ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = Blanc2 ) ),
                                                   ( ~/ComponentState/Grayed,                TColorRTTI( Color = [122,167,176,50] ) ),
                                               ]),
        ("BoutonUnitPattern",                        MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Gris39 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [156,210,226,255] ) ),
                                                        ]),
        ("BoutonUnitPatternFixe",                    MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Gris39 ) ),
                                                        ]),
        ("BoutonUnitPatternClair",                   MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [255,255,255,160] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [156,210,226,255] ) ),
                                                        ]),
        // panel Info
        ("White_50",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [255,255,255,127] ) ),]),
        ("Black_50",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [0,0,0,127] ) ),]),
        ("Gris_pi",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [142,136,126,233] ) ),]),
        ("Gris3_pi",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [99,104,104,200] ) ),]),
        ("GrisBarre_pi",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [109,111,111,255] ) ),]),
        ("Gris2_pi",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [154,158,158,153] ) ),]),
        ("GrisFoncePapier",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [30,33,36,185] ) ),]),
        ("GrisFonceWeaponAttribute",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [45,46,47,183] ) ),]),
        ("GrisFonceJauge",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [63,63,63,205] ) ),]),
        ("MarronPanel_base",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [145,131,104,245] ) ),]),
        ("MarronPanel_base_75",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [110,101,78,100] ) ),]),

        ("MarronPanel_baseLight",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [145,131,104,195] ) ),]),
        ("MarronPanel_fonce",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [110,101,78,255] ) ),]),
        ("MarronPanel_Tresfonce",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [82,75,58,235] ) ),]),  //[65,60,46,235]
        ("MarronPanel_BarreTresFonce",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [65,60,46,235] ) ),]),  //[65,60,46,235]
        ("MarronPanel_noir",                        MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [108,108,108,170] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [43,35,36,245] ) ),
                                                            //( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [43+30,35+30,36+30,245] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [43,35,36,245] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [43,35,36,245] ) ),
                                                        ]),
        ("MarronPanel_noir_clicked",                MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [108,108,108,170] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [43,35,36,245] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [65, 60, 46, 235] ) ),
                                                            ( ~/ComponentState/Clicked,                             TColorRTTI( Color = [203, 194, 180, 235] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [43,35,36,245] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [43,35,36,245] ) ),
                                                        ]),
        ("MarronPanel_noir2",                       MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [108,108,108,170] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [64,53,54,245] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [22,17,18,245] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [64,53,54,245] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [64,53,54,245] ) ),
                                                        ]),
        ("MarronPanel_noir_toggled",                MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [43,35,36,10] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [43,35,36,80] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [43+30,35+30,36+30,245] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [43,35,36,245] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [43,35,36,245] ) ),
                                                        ]),
        ("MarronPanel_noir_toggled2",               MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [43,35,36,10] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [43+90,35+90,36+90,245] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [43+30,35+30,36+30,245] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [43,35,36,245] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [43,35,36,245] ) ),
                                                        ]),
        ("MarronPanel_blanc",                       MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [203+20,194+20,180+20,255] ) ),]),
        ("AmroryButtonBkg",               MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [43,35,36,10] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [142,132,113,255] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [223+10,214+10,200+10,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [223,214,200,255] ) ),
                                                            ( ~/ComponentState/Toggled,                   TColorRTTI( Color = [223,214,200,255] ) ),
                                                        ]),
        ("AmroryCheckBoxBkg",               MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [43,35,36,10] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [142,132,113,255] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [142+10,132+10,113+10,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [142,132,113,255] ) ),
                                                            ( ~/ComponentState/Toggled,                   TColorRTTI( Color = [142+10,132+10,113+10,255] ) ),
                                                        ]),
        ("AmroryButtonBkg_75",               MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [43,35,36,10] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [142,132,113,191] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [223+10,214+10,200+10,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [223,214,200,255] ) ),
                                                            ( ~/ComponentState/Toggled,                   TColorRTTI( Color = [223,212,205,255] ) ),
                                                        ]),
        ("AmroryButtonBkg2",               MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [43,35,36,10] ) ),
                                                            ( ~/ComponentState/Normal,                             TColorRTTI( Color = [223,214,200,255] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [223+10,214+10,200+10,255] ) ),
                                                            ( ~/ComponentState/Toggled,                              TColorRTTI( Color = [223,214,200,211] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [223+10,214+10,200+10,255] ) ),
                                                        ]),
        ("AmroryButtonTxt",                       MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [108,108,108,170] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [46,39,39,255] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [22,17,18,245] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [64,53,54,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [17,14,14,255] ) ),
                                                        ]),
        ("AmroryFiltreTxt",                       MAP [
                                                            ( ~/ComponentState/Grayed,                              TColorRTTI( Color = [108,108,108,170] ) ),
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [223,214,200,255] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [255,255,255,255] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [243,234,220,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [255,255,255,255] ) ),
                                                        ]),
        ("AmroryButtonBorder",                       MAP [
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [255,255,255,50] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [255,225,255,191] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [255,255,255,255] ) ),
                                                        ]),
        ("AmroryFlagColor",                       MAP [
                                                            ( ~/ComponentState/Normal,                              TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Highlighted,                         TColorRTTI( Color = [255,255,255,255] ) ),
                                                            ( ~/ComponentState/Toggled,                             TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,                   TColorRTTI( Color = [255,255,255,255] ) ),
                                                        ]),

        ("ArmoryFiterToggle",                     MAP [
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent  ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                      ]),


        ("Blanc220",                                MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [220,220,220,255] ) ),]),

        ("HostGame",                                MAP [
                                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [30,210,210,120] ) ),
                                                            ( ~/ComponentState/Highlighted,      TColorRTTI( Color = [30,210,210,240] ) ),
                                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = [30, 30, 30, 120] ) ),
                                                        ]),

        ("filterColor",                                MAP [
                                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [255,255,255,225] ) ),
                                                            ( ~/ComponentState/Highlighted,      TColorRTTI( Color = [255,255,255,255] ) ),
                                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = [118, 115, 109, 255] ) ),
                                                        ]),
        //-------------------------------------------------------------------------------------
        //Steelman
        ("SM_SelectPawn_oob",                       MAP [
                                                        ( ~/ComponentState/Normal,                      TColorRTTI( Color = [184,184,184,255] ) ),
                                                        ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [202,223,229,255] ) ),
                                                        ( ~/ComponentState/Toggled,                TColorRTTI( Color = [202,223,229,255] ) ),

                                                    ]),

        ("Vert_foret",                          MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [164,198,148,235] ) ),]),
        ("autoresolveFond",                     MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [240,240,250,20] ) ),]),
        ("division_test",                       MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [100,240,150,255] ) ),]),
        ("SM_xanadu",                           MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = xanadu ) ),]),
        ("SM_paleSilver",                       MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = paleSilver ) ),
                                                        ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [210,216,240,255] ) ),
                                                        ( ~/ComponentState/Toggled,                TColorRTTI( Color = [204,192,182,255] ) ),
                                                        ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = [210,216,240,255] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [204 - 20,192 - 20,182 - 20,100] ) ),
                                                    ]),
        ("SM_onyx",                             MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = onyx ) ),]),
        ("SM_rawUmber",                         MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = rawUmber ) ),]),

        ("SM_Ebony",                            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = Ebony ) ),]),
        ("SM_Feldgrau",                         MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = Feldgrau ) ),
                                                        ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [93,116,88,255] ) ),
                                                        ( ~/ComponentState/Toggled,                TColorRTTI( Color = [101,128,96,255] ) ),
                                                    ]),
        ("SM_Feldgrau_NoToggle",                  MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [101,128,96,255] ) ),
                                                        ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [93,116,88,255] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Feldgrau ) ),
                                                    ]),
        ("SM_Feldgrau_75",                         MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [73,91,69,220] ) ),
                                                        ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [93,116,88,255] ) ),
                                                        ( ~/ComponentState/Toggled,                TColorRTTI( Color = [101,128,96,255] ) ),
                                                    ]),
        ("SM_RifleGreen",                       MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = RifleGreen ) ),]),
        ("SM_RifleGreen_75",                    MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [64,77,64,210] ) ),
                                                        ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [64+20,77+20,64+20,210] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [64 - 20,77 - 20,64 - 20, 100] ) ),
                                                    ]),
        ("SM_Grullo",                           MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = Grullo ) ),
                                                        ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [210,216,240,255] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [210,216,240,50] ) ),
                                                    ]),
        ("SM_Grullo_NoToggle",                           MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [210,216,240,255] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [210,216,240,50] ) ),
                                                    ]),
        ("SM_Grullo_2",                           MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = Grullo ) ),
                                                        ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [190, 185, 165, 255] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [210,216,240,50] ) ),
                                                    ]),
        ("SM_LDhint_fond",                           MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [46,58,47,210] ) ),
                                                    ]),
        ("SM_LDhint_texte_gen",                           MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [46,58,47,255] ) ),
                                                    ]),
        ("SM_LDhint_texte",                           MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [166,178,167,255] ) ),
                                                    ]),

        ("SM_renfort_fleche", MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = Grullo ) ),]),
        ("SM_DarkLava",                         MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = DarkLava ) ),]),

        ("SM_Noir",                             MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [30,32,33,255] ) ),]),
        ("Transparent_SM_Noir",                             MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [30,32,33, 175] ) ),]),
        ("SM_Noir_80",                             MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [30,32,33,240] ) ),]),
        ("SM_renfort_nonAvailable",MAP [
                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = [30,32,33,255] ) ),
                                        ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [30+40,32+40,33+40,255] ) ),
                                        ( ~/ComponentState/Toggled,                TColorRTTI( Color = [30+30,32+30,33+30,255] ) ),
                                        ( ~/ComponentState/ToggleHighlighted,                TColorRTTI( Color = [30+80,32+80,33+80,255] ) ),
                                        ]),

        ("bouton_cubeAction",              MAP [
                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [128,128,128,25] ) ),
                                         ]),

        ("StrategicButton_highlightable",           MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = paleSilver ) ),
                                                        ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [210, 216, 240, 255] ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [184, 172, 162, 100] ) ),
                                                    ]),

        ("StrategicPanel",                          MAP [
                                                        ( ~/ComponentState/Normal,                 TColorRTTI( Color = paleSilver ) ),
                                                        ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [184, 172, 162, 100] ) ),
                                                    ]),

        ("TacticButton_highlightable",              MAP [
                                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [156,210,226,255] ) ),
                                                    ]),

        ("TacticPanel",                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [129, 238, 242, 255] ) ), ]),

        // couleur des division
        // US
        ("division_US_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [47,203,252,255] ) ),]),
        ("division_US_2",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [114, 126, 76,255] ) ),]),
        ("division_US_3",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [136, 171, 114,255] ) ),]),
        ("division_US_4",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [47,131,252,255] ) ),]),
        ("division_US_5",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [84, 96, 76,255] ) ),]),
        ("division_US_6",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [114,126,106,255] ) ),]),


        ("division_US_8th",MAP [( ~/ComponentState/Normal, TColorRTTI( Color = [125,155,3,255] ) ),]),

        ("division_US_air",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = bleuNavy_clair ) ),]),

        // RFA
        // eviter division_RFA_1 trop clair
        ("division_RFA_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [172,184,184,255] ) ),]),

        ("division_RFA_2",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [86,93,94,255] ) ),]),
        ("division_RFA_3",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [87,100,102,255] ) ),]),
        ("division_RFA_4",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [45,58,60,255] ) ),]),
        ("division_RFA_5",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [2,16,19,255] ) ),]),
        ("division_RFA_violet_sombre",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [0,25,66,255] ) ),]),
        ("division_RFA_kassel_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [28,29,32,215] ) ),]),
        ("division_RFA_kdo", MAP [( ~/ComponentState/Normal, TColorRTTI( Color = [73,71,62,215] ) ),]),
        ("division_RFA_air_kassel_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [157,177,234,210] ) ),]),

        ("division_BE_kassel_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [120,121,65,255] ) ),]),
        ("division_BE_Boar_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [147,148,79,255] ) ),]),
        ("division_BE_Boar_2",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [181,180,97,255] ) ),]),
        ("division_BE_air",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [155,162,129,255] ) ),]),

        ("division_UK_corps",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [112,73,16,180] ) ),]),
        ("division_UK_boar_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [178,131,42,190] ) ),]),
        ("division_UK_air",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [129,162,155,255] ) ),]),

        ("division_NL_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [250,129,0,255] ) ),]),
        ("division_NL_2",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [250,157,58,255] ) ),]),

        ("division_POL_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [213,19,58,255] ) ),]),

        ("division_SOV_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [134,37,4,180] ) ), ]),
        ("division_SOV_2",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [116,90,35,180] ) ), ]),
        ("division_SOV_3",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [201,59,12,180] ) ), ]),
        ("SovDivision_air",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0,201,155,180] ) ), ]),
        ("SovDivision_kassel_2",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [87,28,28,180] ) ), ]),
        ("SovDivision_kassel_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [99,191,172,180] ) ), ]),
        ("SovDivision_kassel_3",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [201,59,12,180] ) ), ]),
        ("SovDivision_boar_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,45,46,180] ) ), ]),
        ("division_SOV_4",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [201,59,12,255] ) ), ]),


        ("canadianDiv_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [120, 40, 30,220] ) ), ]),

        ("CzecDiv_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [60, 75, 50,255] ) ), ]),
        ("CzecDiv_2",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [85, 107, 47,255] ) ), ]),
        ("CzecDiv_3",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [90, 95, 80,255] ) ), ]),
        ("CzecDiv_4",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [70, 85, 90,255] ) ), ]),
        ("CzecDiv_5",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [110, 120, 90,255] ) ), ]),

        // division ESP
        ("division_ESP_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [181, 82, 57,250] ) ), ]),


        // division FR
        ("division_FR_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0, 85, 164,250] ) ), ]),
        ("division_FR_2",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [47, 62, 117,250] ) ), ]),
        ("division_FR_3",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [62, 76, 89,250] ) ), ]),
        ("division_FR_4",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [63, 93, 90, 250] ) ), ]),

        // ("division_DDR_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [139,65,5,220] ) ), ]),
        ("division_DDR_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [15,5,5,210] ) ), ]),
        ("division_DDR_kassel_1",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [15,5,5,210] ) ), ]),
        ("division_DDR_hq",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [91,80,80,210] ) ), ]),
        ("division_DDR_11",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [36,19,15,230] ) ), ]),
        ("division_DDR_7",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [43,36,32,230] ) ), ]),
        ("division_DDR_8",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [49,40,36,255] ) ),]),
        ("division_DDR_9",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [70,37,10,255] ) ),]),
        ("division_DDR_20",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [61,48,20,255] ) ),]),


        // OTAN DIVERS
        ("corps_otan",                            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [19,42,19,255] ) ),]),
        ("bataillon_otan",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [159,105,118,255] ) ),]),

        ("Feedback/DeadUnit",                     MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[255,180,  0,255] ) ), ]),
        ("Feedback/AutomaticBehavior",            MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[244, 50, 50,255] ) ), ]),
        ("Feedback/UnitSold",                     MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[244,187, 50,255] ) ), ]),
        ("Feedback/OnGainXpLevel",                MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[229,218, 92,255] ) ), ]),

        ("Feedback/OnAPLost",                     MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[255,  0,  0,255] ) ), ]),
        ("Feedback/OnAPGained",                   MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[  0,255,  0,  0] ) ), ]),
        ("Feedback/OnPawnEvent",                  MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[255,  0,  0,255] ) ), ]),
        ("Feedback/OnAttackResult",               MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[255,255,255,255] ) ), ]),
        ("Feedback/OnOrderCancelled",             MAP [ ( ~/ComponentState/Normal,          TColorRTTI( Color = RGBA[255,  0,  0,255] ) ), ]),


        ("SM_cyan_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [0,255,255,255] ) ),]),
        ("SM_cyan_2",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [0,200,200,255] ) ),]),
        ("SM_cyan_3",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [0,150,150,255] ) ),]),
        ("SM_gris_1",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [100,100,100,255] ) ),]),
        ("SM_gris_2",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [125,125,125,255] ) ),]),
        ("SM_gris_3",                        MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [150,150,150,255] ) ),]),



        // Steelman panel score
        ("score_01",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [3,108,0,255] ) ),]),
        ("score_02",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [38,158,34,255] ) ),]),
        ("score_03",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [49,207,45,255] ) ),]),
        ("score_04",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [183,172,41,255] ) ),]),
        ("score_05",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [255,74,57,255] ) ),]),
        ("score_06",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [158,34,34,255] ) ),]),
        ("score_07",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [108,24,24,255] ) ),]),


        ("pi0",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [172,0,0,255] ) ),]),
        ("pi1",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [255,190,0,255] ) ),]),
        ("pi2",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [127,255,117,255] ) ),]),
        ("pi3",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [67,255,255,255] ) ),]),

        ("score_05b",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [255,187,181,255] ) ),]),


        ("moral_color_bad_1",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [255,228,0,255] ) ),]),
        ("moral_color_bad_2",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [255,156,0,255] ) ),]),
        ("moral_color_bad_3",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [255,74,57,255] ) ),]),
        ("moral_color_bad_4",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [61,21,17,255] ) ),]),

        // labelVille

        ("labelVille_autoroute",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [56,120,199,255] ) ),]),
        //-------------------------------------------------------------------------------------
        //smart groups

        ("NomGroupe",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [47,48,49,255] ) ),]),
        ("NomChef",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [54,54,54,255] ) ),]),
        ("TexteNomChef",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [198,174,42,255] ) ),]),
        ("FondGroupe",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [243-10,235-10,220-10,255] ) ),]),
        ("FondGroupe_company",            MAP [
                                                ( ~/ComponentState/Normal,               TColorRTTI( Color = [171,151,135,125] ) ),
                                                ( ~/ComponentState/Toggled,                 TColorRTTI( Color = [204,192,182,255] ) ),
                                                ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [204,192,182,255] ) ),
                                                ( ~/ComponentState/ToggleHighlighted,                 TColorRTTI( Color = [204,192,182,255] ) ),
                                                ]),
        ("FondGroupe_company_ligne",            MAP [
                                                ( ~/ComponentState/Normal,                 TColorRTTI( Color = [204,192,182,255] ) ),
                                                ( ~/ComponentState/Toggled,                 TColorRTTI( Color = [204,192,182,255] ) ),
                                                ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [204,192,182,255] ) ),
                                                ( ~/ComponentState/ToggleHighlighted,                 TColorRTTI( Color = [204,192,182,255] ) ),
                                                ]),
        ("FondSmartGroupePanel",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [142,134,123,255] ) ),]),
        ("FondVide",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [180,180,180,255] ) ),]),
        ("ContourFondVide",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [129,129,129,255] ) ),
                                             ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
            ]),
        ("FondSection",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [213,204,193,255] ) ),]),
        ("FondsmartGroupe",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [236,227,216,255] ) ),]),
        ("FondOnglet_new",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [176,178,152,255] ) ),]),

        ("bouton_strategic_choice",                              MAP [
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [255,255,255,255] ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                        ]),

        ("BoutonXP_deck",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = bleuNavy_fonce ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = bleuNavy ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = bleuNavy_clair ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc7 ) ),
                                                        ]),
        ("BoutonXP_deck_border",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc7 ) ),
                                                        ]),
        ("BoutonXP_deck_chevron",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = bleuNavy_fonce ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [14,28,31,255] ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [14,28,31,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [14,28,31,255] ) ),
                                                        ]),

        ("text_couleur_bleunavy_standard",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = bleuNavy_fonce ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [14,28,31,255] ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [14,28,31,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [14,28,31,255] ) ),
                                                        ]),
        ("text_couleur_bleunavy_secondaire",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = bleuNavy_fonce ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [14,28,31,255] ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [14,28,31,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [14,28,31,255] ) ),
                                                        ]),
        ("BoutonTransport_deck",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [20,20,20,20] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [156,210,226,255] ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [156+20,210+20,226+20,255] ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [156,210,226,255] ) ),
                                                        ]),



        //-------------------------------------------------------------------------------------
        // decklist
        ("Gris_Titre_decklist",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [76,84,90,255] ) ),
                                                ( ~/ComponentState/Highlighted,                TColorRTTI( Color = Blanc2 ) ),
                                                ]),
        ("Gris_Colonne_decklist",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [53,56,59,255] ) ),
                                                ]),
        ("Bleu_Ligne_name",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [12,51,96,255] ) ),]),
        ("Vert_bouton",                MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [89,103,93,255] ) ),
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [89,103,93,100]) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [129,140,132,255]  ) ),
                                            ]),
        ("blanc_contour",                MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [222,212,203,255] ) ),
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color =[222,212,203,100]) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [255,255,255,255]  ) ),
                                            ]),
        ("Gris_boutonAction",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [188,181,182,255] ) ),
                                              ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [110,110,110,180] ) ),
                                              ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [210,210,210,255] ) ),
                                            ]),
        //-------------------------------------------------------------------------------------
        // LDHINT
        ("Vert_LDHint",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [55,133,27,230] ) ), ]),
        ("Rouge_LDHint",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [132,85,29,230] ) ), ]),
        //-------------------------------------------------------------------------------------
        // accueil
        ("TexteProfile",               MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [31,35,39,255] ) ),
                                           ]),
        ("TexteProfileTitre",          MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [54,74,88,255] ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [4,82,137,255] ) ),
                                            ( ~/ComponentState/Clicked,     TColorRTTI( Color = [100,115,126,255] ) ),
                                           ]),
        ("TexteProfileGris",           MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [158,155,162,255] ) ),
                                           ]),
        ("VertLogin",                  MAP [
                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = [80, 80, 80,160] ) ),
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [124,210,150,180] ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [124,210,150,240] ) ),
                                           ]),
        ("Vert2Login",                 MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [73,215,99,180] ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [73,215,99,240] ) ),
                                            ]),
        ("JauneLogin",                 MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [230,215,56,240] ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [230,215,53,240] ) ),
                                           ]),
        ("loginBlanc",                 MAP [
                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = [255,255,255,240] ) ),
                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [30,30,30,240] ) ),
                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = [130,130,130,120] ) ),
                                           ]),
        //-------------------------------------------------------------------------------------
        // ARMORY
        ("grisBoutonFilter",            MAP [( ~/ComponentState/Normal,              TColorRTTI( Color = [185,181,173,200] ) ),]),
        ("VertMilitaire",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [26,43,25,255] ) ),]),
        ("StrategicUI/Button",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = [128,128,128,25] ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = [200,200,200,255] ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [255,255,255,255] ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = Blanc2 ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = Noir ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [128,128,128,255] ) ),
                                       ]),
        ("Bouton_puissance",             MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = [128,128,128,25] ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = [210,210,210,255] ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [230,230,230,255] ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = [160,160,160,255] ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = [180,180,180,255] ) ),
                                           ( ~/ComponentState/ToggleClicked,         TColorRTTI( Color = [160,160,160,255] ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [180,180,180,200] ) ),
                                       ]),
        ("blancCasseFiltre",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [242,234,220,200] ) ), ]),
        ("GrisBouton_armory",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [65,65,65,255] ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [170,168,168,255] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [210,210,210,255]  ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = Blanc2 ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = Blanc2 ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color =  [210,210,210,255] ) ),
                                ]),
        ("GrisBois_armory",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [65,65,65,255] ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [195,184,177,255] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [210,210,210,255] ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = Blanc2 ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = Blanc2 ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color =  [210,210,210,255] ) ),
                                ]),
        ("GrisOnglet_armory",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [128,128,128,25] ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [92,86,81,125] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [55,50,45,255] ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = [55,50,45,255] ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = [75,70,65,255] ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color =  [55,50,45,255] ) ),
                                ]),
        ("Marron_texte",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [117,102,96,255] ) ),]),
        ("BlancTexte",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [226,220,218,255] ) ),]),

        ("BlancListeUnite",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [241,242,247,255] ) ),]),
        ("Gris_unitConfig_titre",                 MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [72,72,74,255] ) ),]),
        ("Gris_unitConfig_Experience",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [67,67,70,255] ) ),]),
        ("Noir_unitConfig",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [31,25,26,255] ) ),
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [31,25,26,100]) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [41,35,36,255]  ) ),
                                            ]),

        ("Vert_boutonDeck",                MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [140,136,100,255] ) ),
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [140,136,100,100]) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [164,160,131,255]  ) ),
                                            ]),
        ("blanc_unitConfig",                MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [206,205,206,255] ) ),
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [206,205,206,100]) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [226,225,226,255]  ) ),
                                            ]),
        //-------------------------------------------------------------------------------------
        // LEADERBOARD

        ("SelfPlayer",            MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [177, 38, 38, 255] ) ),]),
        ("FriendPlayer",              MAP [( ~/ComponentState/Normal,                 TColorRTTI( Color = [175, 121, 81, 255] ) ),]),

        //-------------------------------------------------------------------------------------
        ("OTAN",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [65,65,65,255] ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [3,73,126,255] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [4,102,171,255] ) ),
                                ]),
        ("PACT",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [65,65,65,255] ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [93,29,28,255] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [130,42,39,255] ) ),
                                ]),

        ("menu_titre_solo",            MAP [
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [68,72,80,255] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [239,232,230,255] ) ),
                                        ]),
        ("menu_solo_liste_mission",            MAP [
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [176,171,173,255] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [255,255,255,255] ) ),
                                        ]),
        ("noir_listeMission",            MAP [
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [0,0,0,250] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [41,45,46,128] ) ),
                                        ]),
        ("noir_liste_Carte",            MAP [
                                            ( ~/ComponentState/Normal,            TColorRTTI( Color = [41,45,46,128] ) ),
                                            ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [0,0,0,250] ) ),
                                        ]),
        ("gris_loupe_scenarioListe",            MAP [
                                            ( ~/ComponentState/Normal,            TColorRTTI( Color = [120,120,120,255] ) ),
                                            ( ~/ComponentState/Highlighted,                 TColorRTTI( Color = [160,160,160,255] ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = [255,255,255,255] ) ),

                                        ]),
        ("fond_gris_liste_carte",            MAP [
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = [41,45,46,25] ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = [41,45,46,40] ) ),
                                             ( ~/ComponentState/Toggled,                TColorRTTI( Color = [41,45,46,60] ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color =  [41,45,46,50] ) ),
                                        ]),

        ("VertConfirm",                                MAP [
                                                            ( ~/ComponentState/Normal,            TColorRTTI( Color = [125,178,120,210] ) ),
                                                            ( ~/ComponentState/Highlighted,       TColorRTTI( Color = [125+10,178+10,120+10,210] ) ),
                                                            ( ~/ComponentState/Clicked,           TColorRTTI( Color = [159,225,153,255] ) ),

                                                        ]),
        ("tampon_vert",                                MAP [
                                                            ( ~/ComponentState/Normal,            TColorRTTI( Color = [1,166,108,210] ) ),
                                                            ( ~/ComponentState/Highlighted,       TColorRTTI( Color = [1+15,166+15,108+15,210] ) ),
                                                            ( ~/ComponentState/Clicked,           TColorRTTI( Color = [159,225,153,255] ) ),
                                                            ( ~/ComponentState/Grayed,            TColorRTTI( Color = [94, 179, 149, 125] ) ),
                                                        ]),
        ("tampon_gris",              MAP [
                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [20,20,20,128] ) ),
                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [128,128,128,250] ) ),
                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                        ( ~/ComponentState/Clicked,               TColorRTTI( Color = [160,160,160,250] ) ),
                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [255,255,255,245] ) ),
                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [255,255,255,128] ) ),
                                        ]),
        ("boutonBack",                 MAP [
                                                              ( ~/ComponentState/Normal,      TColorRTTI( Color = BlancTransparent2 ) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancTransparent ) ),
                                                              ( ~/ComponentState/Toggled,     TColorRTTI( Color = BlancTransparent ) ),
                                                            ]),
        //-------------------------------------------------------------------------------------
        // multi
        ("Blanc_leaderboard",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [229,219,214,255] ) ), ]),
        ("Gris_leaderboard",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [158,148,146,255] ) ), ]),
        ("Gris_QuickServeur",       MAP [
                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [190,185,187,255] ) ),
                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [255,255,255,255] ) ),
                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,65] ) ),
                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [255,255,255,245] ) ),
                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [255,255,255,128] ) ),
                                        ]),
        ("Gris_Friends",            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [116,110,110,255] ) ), ]),
        ("Jaune_NbPlayers",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [254,253,24,255] ) ), ]),
        ("boutonFiltre",            MAP [
                                            ( ~/ComponentState/Normal,                  TColorRTTI( Color = [60,60,60,128] ) ),
                                            ( ~/ComponentState/Highlighted,             TColorRTTI( Color = [128,128,128,250] ) ),
                                            ( ~/ComponentState/Grayed,                  TColorRTTI( Color = [120,120,120,128] ) ),
                                            ( ~/ComponentState/Toggled,                 TColorRTTI( Color = [255,255,255,245] ) ),
                                            ( ~/ComponentState/ToggleHighlighted,       TColorRTTI( Color = [255,255,255,128] ) ),
                                            ( ~/ComponentState/Intermediate,            TColorRTTI( Color = [255,255,255,245] ) ),
                                            ( ~/ComponentState/IntermediateHighlighted, TColorRTTI( Color = [255,255,255,128] ) ),
                                        ]),
        //-------------------------------------------------------------------------------------
        // load
        ("gris_load",              MAP [
                                        ( ~/ComponentState/Normal, TColorRTTI( Color = [88,93,93,255] ) ),
                                        ( ~/ComponentState/Highlighted, TColorRTTI( Color = [88-40,93-40,93-40,255] ) ),
                                        ]),
        ("Vert_loadOnglet",              MAP [
                                                ( ~/ComponentState/Normal, TColorRTTI( Color = [212,207,183,255] ) ),
                                               ( ~/ComponentState/Toggled, TColorRTTI( Color = [33,48,17,255] ) ),
                                               ( ~/ComponentState/Highlighted,             TColorRTTI( Color = [255,255,255,255] ) ),
                                            ]),
        ("blanc_load",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [189,184,179,215] ) ),
                                          ( ~/ComponentState/Highlighted, TColorRTTI( Color = [209,204,199,215] ) ), ]),
        ("loadTexte",              MAP [
                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [60,60,60,180] ) ),
                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [35,35,35,245] ) ),
                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [0,0,0,255] ) ),
                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [0,0,0,255] ) ),
                                        ]),
        //-------------------------------------------------------------------------------------
        ("gris_combatUrbain",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [46,43,36,255] ) ),]),
        ("gris_battlegroup",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [144,156,142,255] ) ),]),
        ("gris_ligne_battlegroup",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [148,142,142,255] ) ),]),
        ("gris_filtre_battlegroup",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [201,192,188,255] ) ),]),
        //-------------------------------------------------------------------------------------
        ("noir_SM_company",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [48,52,56,255] ) ), ]),
        ("noir_stats",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [71,71,71,255] ) ), ]),
        ("noir_onlget_stats",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [48,48,48,255] ) ),
                                                ( ~/ComponentState/Toggled, TColorRTTI( Color = [217,211,210,255] ) ),
                                               ( ~/ComponentState/Highlighted,             TColorRTTI( Color = [255,255,255,255] ) ),
                                                ]),

        ("noir_deckSmartGroups_onglets_selectable",   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [60, 60, 60, 255] ) ),
                                                 ( ~/ComponentState/Highlighted, TColorRTTI( Color = [210, 210, 210, 255] ) ),
                                                ]),
        ("noir_deckSmartGroups_onglets",   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [48, 48, 48, 255] ) ), ]),
        ("gris_stats",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [107,108,108,255] ) ), ]),
        //-------------------------------------------------------------------------------------

        ("noir_param",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [30,37,49,255] ) ), ]),
        //-------------------------------------------------------------------------------------
        // modding
        ("blanc_modding",              MAP [
                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [255,255,255,255] ) ),
                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [110,207,160,255] ) ),
                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [167,25,17,245] ) ),
                                        ]),
        ("blanc_modding_line",              MAP [
                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [211,255,211,200] ) ),
                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [110,207,160,255] ) ),
                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [167,25,17,245] ) ),
                                        ]),
        //-------------------------------------------------------------------------------------
        // LDHINT
        ("LDHintSolo_fond",              MAP [
                                                ( ~/ComponentState/Normal, TColorRTTI( Color = [20,52,67,200] ) ),
                                                ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [20+5,52+5,67+5,200] ) ),
                                                ]),
        ("LDHintSolo_texte",              MAP [
                                                ( ~/ComponentState/Normal, TColorRTTI( Color = [188,217,202,200] ) ),
                                                ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [188+20,217+20,202+20,200] ) ),
                                                ]),
        ("LDHintSolo_texte_or",              MAP [
                                                ( ~/ComponentState/Normal, TColorRTTI( Color = [223,217,202,200] ) ),
                                                ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [188+20,217+20,202+20,200] ) ),
                                                ]),
        //-------------------------------------------------------------------------------------
        // hint
        ("hint_fond",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,235,153,210] ) ), ]),
        ("hint_fond_meilleureLecture",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [16,58,63,255] ) ), ]),
        ("hint_titre_meilleureLecture",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [16,59,64,255] ) ), ]),
        //-------------------------------------------------------------------------------------
        // options
        ("bleu_navigation",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [95,109,128,255] ) ), ]),
        ("Noir_option",              MAP [
                                        ( ~/ComponentState/Normal,                TColorRTTI( Color = [41,45,46,255] ) ),
                                        ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [0,0,0,255] ) ),
                                        ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                        ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                        ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [167,25,17,245] ) ),
                                        ]),
        ("Noir_option_ligne",              MAP [
                                        ( ~/ComponentState/Normal, TColorRTTI( Color = [41,45,46,128] ) ),
                                        ( ~/ComponentState/Highlighted,             TColorRTTI( Color = [0,0,0,128] ) ),
                                        ]),

        ("Noir_multi",              MAP [
                                        ( ~/ComponentState/Normal, TColorRTTI( Color = [41,45,46,255] ) ),
                                        ( ~/ComponentState/Highlighted,             TColorRTTI( Color = [255,255,255,255] ) ),
                                        ( ~/ComponentState/Toggled,             TColorRTTI( Color = [240,228,225,255] ) ),
                                        ( ~/ComponentState/Grayed,             TColorRTTI( Color = [41, 45, 46, 70] ) ),
                                        ]),
        ("Noir_multi2",              MAP [
                                        ( ~/ComponentState/Normal,                  TColorRTTI( Color = [37,48,54,255] ) ),
                                        ( ~/ComponentState/Highlighted,             TColorRTTI( Color = [255,255,255,255] ) ),
                                        ( ~/ComponentState/Grayed,                  TColorRTTI( Color = [120,120,120,128] ) ),
                                    ]),
        ("Blanc_multi",              MAP [
                                    ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [128,128,128,128] ) ),
                                    ( ~/ComponentState/Highlighted,                  TColorRTTI( Color = [255,255,255,255] ) ),
                                    ( ~/ComponentState/Normal,             TColorRTTI( Color = [200,200,200,255] ) ),
                                    ]),
        ("Blanc_Home",              MAP [
                                    ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [128,128,128,128] ) ),
                                    ( ~/ComponentState/Highlighted,                  TColorRTTI( Color = [200,200,200,120] ) ),
                                    ( ~/ComponentState/Normal,             TColorRTTI( Color = [240,240,240,160] ) ),
                                    ]),
        ("Blanc_Home_hidden",              MAP [
                                    ( ~/ComponentState/Grayed,                 TColorRTTI( Color = [128,128,128,128] ) ),
                                    ( ~/ComponentState/Highlighted,                  TColorRTTI( Color = [240,240,240,240] ) ),
                                    ( ~/ComponentState/Normal,             TColorRTTI( Color = [200,200,200,0] ) ),
                                    ]),
        //-------------------------------------------------------------------------------------
        ("LigneB",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [83,138,166,76] ) ), ]),  //ligne bleu un peu plus foncée
        ("LigneA",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [94,210,251,191] ) ), ]), //ligne quasi cyan

        ("TypeE",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [14,63,78,250] ) ), ]),    // couleur foncé des boutons
        ("TypeD",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [99,163,207,250] ) ), ]),    // couleur claire des armes  et nom

        ("TypeC",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [20,103,131,171] ) ), ]),    // bouton prod et bas à gauche
        ("TypeB_alpha",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,102] ) ), ]),// barre des ressources
        ("TypeB",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [19,85,106,191] ) ), ]),    //classique bleu
        ("TypeA",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [37,127,149,76] ) ), ]),    // panel du haut très pale + alph

        ("CyanChrono_180",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [129,238,242,180] ) ), ]),
        ("CyanChrono_120",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [129,238,242,120] ) ), ]),
        ("CyanChrono",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [129,238,242,255] ) ), ]),
        ("VertSecteur",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [184,239,154,255] ) ), ]),
        ("BlancEquipe_alpha",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [202,223,229,180] ) ), ]),
        ("BlancEquipe_30",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [202,223,229,50] ) ), ]),
        ("BlancEquipe",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [202,223,229,255] ) ), ]),
        ("WIP_Vert",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [89,210,112,255] ) ), ]),

        // equivaut à un Blanc2 bleu
        ("H2_blanc",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0,0,0,255] ) ), ]),
        ("H2_bleu_1",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [100,164,210,255] ) ), ]),
        //-------------------------------------------------------------------------------------
        // shortcuts
        ("CouleurTexture_boutonShortcuts", MAP [
                                    ( ~/ComponentState/Grayed,            TColorRTTI( Color = [255,255,255,80] ) ),
                                    ( ~/ComponentState/Normal,            TColorRTTI( Color = [183,211,219,245] ) ),
                                    ( ~/ComponentState/Highlighted,       TColorRTTI( Color = Blanc2 ) ),
                                    ( ~/ComponentState/Clicked,           TColorRTTI( Color = Blanc184 ) ),
                                    ( ~/ComponentState/Toggled,           TColorRTTI( Color = [234,243,245,255] ) ),
                                    ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Blanc2 ) ),
                                    ( ~/ComponentState/ToggleClicked,     TColorRTTI( Color = Blanc184 ) ),
                                ]),
        ("CouleurTexture_boutonShortcutsStrategic", MAP [
                                    ( ~/ComponentState/Grayed,            TColorRTTI( Color = [255,255,255,80] ) ),
                                    ( ~/ComponentState/Normal,            TColorRTTI( Color = [144,157,153,245] ) ),
                                    ( ~/ComponentState/Highlighted,       TColorRTTI( Color = Blanc2 ) ),
                                    ( ~/ComponentState/Clicked,           TColorRTTI( Color = Blanc184 ) ),
                                    ( ~/ComponentState/Toggled,           TColorRTTI( Color = [234,243,245,255] ) ),
                                    ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = Blanc2 ) ),
                                    ( ~/ComponentState/ToggleClicked,     TColorRTTI( Color = Blanc184 ) ),
                                ]),
        ("CouleurTexture_boutonShortcutsStrategic_NoToggle", MAP [
                                    ( ~/ComponentState/Grayed,            TColorRTTI( Color = [255,255,255,80] ) ),
                                    ( ~/ComponentState/Normal,           TColorRTTI( Color = [234,243,245,255] ) ),
                                    ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc2 ) ),
                                    ( ~/ComponentState/Clicked,     TColorRTTI( Color = Blanc184 ) ),
                                ]),
        ("CouleurFond_boutonShortcuts", MAP [

                                    ( ~/ComponentState/Normal,            TColorRTTI( Color = [22,133,173,245] ) ),
                                    ( ~/ComponentState/Highlighted,       TColorRTTI( Color = Blanc2 ) ),
                                    ( ~/ComponentState/Clicked,           TColorRTTI( Color = Blanc184 ) ),
                                    ( ~/ComponentState/Toggled,           TColorRTTI( Color = [20,103,131,170] ) ),
                                    ( ~/ComponentState/ToggleHighlighted,           TColorRTTI( Color = [84,127,134,220] ) ),

                                ]),
        ("CouleurBordure_boutonShortcuts", MAP [

                                    ( ~/ComponentState/Normal,            TColorRTTI( Color = [140,140,140,170] ) ),
                                    ( ~/ComponentState/Toggled,           TColorRTTI( Color = [59,134,161,170] ) ),
                                ]),
        //-------------------------------------------------------------------------------------
        ("Fulda_BleuNoir",             MAP [ ( ~/ComponentState/Normal,                TColorRTTI( Color = Fulda_BleuNoir ) ), ]),
        ("Fulda_Gris",                 MAP [ ( ~/ComponentState/Normal,                TColorRTTI( Color = Fulda_Gris ) ), ]),
        ("Fulda_Turquoise",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = Fulda_Turquoise ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = Fulda_VertBleu ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = Fulda_Turquoise2 ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = Fulda_VertBleu ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = Fulda_Turquoise2 ) ),
                                        ]),
        ("panel_losses",            MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = NoirPur80 ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = NoirPur50 ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = Blanc30 ) ),
                                        ]),


        ("Fulda2_Orange100",              MAP [
                                            ( ~/ComponentState/Grayed,                 TColorRTTI( Color = Transparent ) ),
                                            ( ~/ComponentState/Normal,                 TColorRTTI( Color = Fulda2_Orange100 ) ),
                                            ( ~/ComponentState/Highlighted,            TColorRTTI( Color = BlueOutgame ) ),
                                            ( ~/ComponentState/Clicked,                TColorRTTI( Color = BlueLightestOutgame ) ),
                                            ( ~/ComponentState/Toggled,                TColorRTTI( Color = Fulda2_Noir ) ),
                                            ( ~/ComponentState/ToggleHighlighted,      TColorRTTI( Color = BlueLightOutgame ) ),
                                        ]),

        ("veteranceValueBonus",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,234,192,150] ) ), ]),
        ("Fulda2_Noir",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Fulda2_Noir ) ), ]),
        ("SD2_Transparent",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0,0,0,0] ) ), ]),
        ("SD2_NoirPur",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),
        ("SD2_Noir",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),
        ("SD2_BleuVariable",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuVariable ) ), ]),
        ("SD2_BlancPur",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),

        ("SD2_Blanc184",                                MAP [
                                                            ( ~/ComponentState/Normal,            TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,       TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Clicked,           TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Toggled,           TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleClicked,     TColorRTTI( Color = BlancPur ) ),
                                                        ]),
        ("SD2_Blanc30",                                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc30 ) ), ]),
        ("SD2_Blanc2",                                  MAP [
                                                            ( ~/ComponentState/Normal,            TColorRTTI( Color = [204, 204, 204, 255] ) ),
                                                            ( ~/ComponentState/Highlighted,       TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Clicked,           TColorRTTI( Color = [204, 204, 204, 255] ) ),
                                                            ( ~/ComponentState/Toggled,           TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleClicked,     TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Grayed,            TColorRTTI( Color = GrayMineShaft ) ),
                                                        ]),

        ("ConfirmButton/BigBorder",    MAP [
                                           ( ~/ComponentState/Grayed,                TColorRTTI( Color = BlueGrayedOutgame ) ),
                                           ( ~/ComponentState/Normal,                TColorRTTI( Color = BlueOutgame ) ),
                                           ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BlueOutgame ) ),
                                           ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlueLightestOutgame ) ),
                                           ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlueOutgame ) ),
                                           ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlueLightOutgame ) ),
                                       ]),
        ("ButtonHUD",                                   MAP [
                                                            ( ~/ComponentState/Grayed,            TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,            TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,       TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Clicked,           TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,           TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted, TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleClicked,     TColorRTTI( Color = BlancPur ) ),
                                                        ]),
        ("ButtonHUD/Text",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = DoveGray ) ),
                                                        ]),

        ("ButtonHUD/Text2",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [156,210,226,255] ) ),
                                                        ]),
        ("ButtonHUD/Text2_pawn",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = [120,120,120,128] ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = [220,220,220,255] ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = [156,210,226,255] ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = [156,210,226,255] ) ),
                                                        ]),
        ("ButtonHUD/Text3",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc184 ) ),
                                                        ]),
        ("ButtonSimple/TextBleuGris",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = BleuGrisClair ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur) ),
                                                        ]),
        ("ButtonSimple/TextMarron",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Marron4 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                                        ]),
        ("ButtonHUD/Text4",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Blanc7 ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Blanc7 ) ),
                                                        ]),
        ("ButtonHUD/ButtonSoloHUB",                              MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Noir ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Noir ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = BlancPur ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = BlancPur ) ),
                                                        ]),

        ("HUBActionButton",                             MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = GrayMineShaft ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = VividRed ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = JauneOrange ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Orange3 ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Orange3 ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Orange3 ) ),
                                                        ]),
        ("ButtonHUD/Dropdown",                              MAP [
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Gris123 ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = BleuGrisClair ) ),
                                                        ]),

        ("FriendState/InGame",                          MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = OrangeTangerine ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = OrangeCalifornia ) ),
                                                            ]),
        ("FriendState/InShowRoom",                      MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = ForestGreen ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = ForestGreenLight ) ),
                                                            ]),
        ("FriendState/IntoSession",                     MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = OrangeTangerine ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = OrangeCalifornia ) ),
                                                            ]),
        ("FriendState/Offline",                         MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen100 ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc184 ) ),
                                                            ]),
        ("FriendState/ReceivedFriendInvitation",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Glacier ) ), ]),
        ("FriendState/SentFriendInvitation",            MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = OrangeTangerine ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = OrangeCalifornia ) ),
                                                            ]),
        ("FriendState/WatchingGame",                    MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = OrangeTangerine ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = OrangeCalifornia ) ),
                                                            ]),
        ("FriendState/InAnotherGame",                   MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen100 ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc184 ) ),
                                                            ]),

        ("Lobby/ShowHideButton",                        MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [60, 50, 35, 255] ) ),
                                                            ( ~/ComponentState/Grayed, TColorRTTI( Color = [60, 50, 35, 60] ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = [120, 105, 65, 255] ) ),
                                                            ]),

        ("Lobby/PlayerNeedSwitchSideColorSlotColor",    MAP [
                                                            ( ~/ComponentState/Normal, TColorRTTI( Color = [255, 0, 0, 50] ) ),
                                                            ]),


        ("Cyan",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Cyan ) ), ]),
        ("Turquoise",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Fulda_Turquoise ) ), ]),
        ("Jaune",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Jaune ) ), ]),
        ("Magenta",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,0,255,255] ) ), ]),
        ("Transparent",                MAP [
                                                            ( ~/ComponentState/Grayed,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Normal,                TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Clicked,               TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Toggled,               TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleHighlighted,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleClicked,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/ToggleGrayed,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/Intermediate,     TColorRTTI( Color = Transparent ) ),
                                                            ( ~/ComponentState/IntermediateHighlighted,     TColorRTTI( Color = Transparent ) ),
                                                        ]),
        ("Blanc",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,255,255,255] ) ), ]),
        ("VividRed",                                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VividRed ) ), ]),
        ("PureBlack",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),
        ("DarkerGray",                                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkerGray ) ), ]),
        ("DarkGray",                                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkGray ) ), ]),
        ("Gray",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Gray ) ), ]),

        ("LightGray",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LightGray ) ), ]),
        ("LighterGray",                                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LighterGray ) ), ]),
        ("Noir",                                        MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [0,0,0,255]  ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [50,50,50,255] ) ),
                                                            ]),
        ("Noir_noHL",                                        MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [0,0,0,255]  ) ), ]),
        ("NoirEndgameButton",                           MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [30,30,30,255]  ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [150,150,150,255] ) ),
                                                            ( ~/ComponentState/Grayed,           TColorRTTI( Color = [150,150,150,150] ) ),
                                                            ]),
        ("Noir_Grise",                                  MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [0,0,0,255]  ) ),
                                                            ( ~/ComponentState/Highlighted,           TColorRTTI( Color = [50,50,50,255] ) ),
                                                            ( ~/ComponentState/Grayed,           TColorRTTI( Color = [80, 80, 80,255] ) ),
                                                            ]),
        ("Gris",                                        MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = [110,110,110,180]  ) ),]),
        ("Glacier",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Glacier ) ),
                                                              ( ~/ComponentState/Highlighted,       TColorRTTI( Color = BlancPur ) ),
                                                            ]),
        ("GrisClair",                                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair ) ), ]),
        ("LightestGray",                                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LightestGray ) ), ]),
        ("LightestestGray",                             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LightestestGray ) ), ]),
        ("ListeExcel/Cartouche",                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc7 ) ), ]),
        ("ListeExcel/Lignes/Defaut",                    MAP [
                                                            ( ~/ComponentState/Grayed,      TColorRTTI( Color = LightGray ) ),
                                                            ( ~/ComponentState/Normal,      TColorRTTI( Color = Blanc184 ) ),
                                                            ( ~/ComponentState/Highlighted, TColorRTTI( Color = Blanc5  ) ),
                                                            ( ~/ComponentState/Clicked,     TColorRTTI( Color = Blanc5  ) ),
                                                        ]),
        ("Orange",                                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange ) ), ]),
        ("Orange3",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Orange3 ) ), ]),
        ("Orange7",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Orange7 ) ), ]),
        ("JauneLeger2",                                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneLeger2 ) ), ]),
        ("Gold",                                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Or4 ) ), ]),
        ("Rouge",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,0,0,255] ) ), ]),
        ("Rouge64",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,0,0,64] ) ), ]),
        ("White",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,255,255,255] ) ), ]),
        ("TransparentWhite",                            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,255,255,200] ) ), ]),
        ("AppleGreen",                                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPomme ) ), ]),
        ("VertPur",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("Vert",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        //("Magenta",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("Green2",                                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Green2 ) ), ]),
        ("Green3",                                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Green3 ) ), ]),
        ("DarkGold",                                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = DarkGold ) ), ]),
        ("HUBAccueil/ProfileId",                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Vert4 ) ), ]),

        ("Jauge/Text01",                                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Rouge ) ), ]), //ancien composant
        ("Jauge/Text04",                                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Vert ) ), ]), //ancien composant
        ("Jauge/Title",                                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombre ) ), ]), //ancien composant
        ("UnitLabel/CriticDescription",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange ) ), ]),
        ("UnitLabel/CriticTitle",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc184 ) ), ]), //

        ("Title",                                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombre ) ), ]), //ancien composant
        ("ObjectiveEtiquetteManager/TextBackground",    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [40, 40, 40, 100] ) ), ]),
        ("ObjectiveEtiquetteManager/FlareText",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc7 ) ), ]),
        ("ObjectiveEtiquetteManager/Text",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombre ) ), ]),
        ("ObjectiveLabel/Primary/Title",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = JauneOrange2 ) ), ]),
        ("ObjectiveLabel/Bonus/Title",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc7 ) ), ]),
        ("ObjectiveLabel/Info/Title",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisMoyen2 ) ), ]),
        ("ScriptedLabel/New",                           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("ObjectiveLabel/Failed",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeVif ) ), ]),
        ("ObjectiveLabel/Completed",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Vert4 ) ), ]),
        ("PanneauPhase/TextePhase",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),
        ("PanneauPhase/TexteTemps",                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeClair ) ), ]),
        ("PanelScore/ObjectiveAlliance",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Glacier ) ), ]),
        ("PanelScore/VictoryStatus",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc184 ) ), ]),
        ("PanelScore/ScoreJoueur",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = LighterGray ) ), ]),
        ("OutmapFeedback/Text",                         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair8 ) ), ]),
        ("ProductionMenu/UnitNameWhenNotAvailable",     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair7 ) ), ]),
        ("ProductionMenu/UnitCostWhenNotAvailable",     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair7 ) ), ]),
        ("ProductionMenu/UnitCostWhenNotAvailable/Glow", MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),
        ("ProductionMenu/UnitCostWhenCannotBeBought",   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeVif ) ), ]),
        ("ReplayPanel/WaitingTimeObs",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombre4 ) ), ]),
        ("WarningPanel/Negative",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeVif ) ), ]),
        ("DeckOverview/CouleurUniteNonDisponible",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = GrisClair7 ) ), ]),
        ("DeckOverview/CouleurCoutUniteNonDisponible",  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur20 ) ), ]),
        ("DeckOverview/CouleurCoutUniteNonDisponible/Glow",  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),
        ("Labels/Ville",                                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc5 ) ), ]),
        ("AutoKickPanel/Title",                         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombre4 ) ), ]),
        ("AutoKickPanel/PlayerName",                    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BeigeSombre4 ) ), ]),
        ("AutoKickPanel/Time",                          MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Rouge ) ), ]),
        ("AlertMessage/Text",                           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougeMoyen2 ) ), ]),
        ("Subtitle/Text",                               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),
        ("MouseWidget/TextModule",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),
        ("WidgetSelector/Bonus",                        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [142,229,99,255] ) ), ]),
        ("WidgetSelector/WithinRange",                  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [0,255,255,255] ) ), ]),
        ("WidgetSelector/OutOfRange",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("WidgetSelector/Unseen",                       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("WidgetSelector/Blocked",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),
        ("WidgetSelector/Invalid",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,0,255,255] ) ), ]),
        ("WidgetSelector/NotEfficient",                 MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255,255,0,255] ) ), ]),

        ("AlertPanel/Line/Rens_EnemyContact",           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [81,184,228,220] ) ), ]), // bleu contact
        ("AlertPanel/Line/Arei_AirplanceContact",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [250,190,50,250] ) ), ]),
        ("AlertPanel/Line/AlertMsg_None",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Transparent ) ), ]),
        ("AlertPanel/Line/Par_ResteMoitieBataille",     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("AlertPanel/Line/Par_ResteQuartBataille",      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("AlertPanel/Line/Rens_AllyLaunchBeacon",       MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("AlertPanel/Line/Rens_UnitUnderAttack",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("AlertPanel/Line/Rens_OnAirplaneOutOfFuel",    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("AlertPanel/Line/Rens_OnAirplaneOutOfAmmo",    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("AlertPanel/Line/Per_UniteDetruite",           MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Per_UniteDetruiteREDReplay",  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Per_UniteDetruiteBLUReplay",  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [81,184,228,220] ) ), ]),
        ("AlertPanel/Line/CommandZoneCapturedForReplay", MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("AlertPanel/Line/CommandZoneLostForReplay",    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),
        ("AlertPanel/Line/Zone_Lost",                   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Zone_SecuredByEnemy",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Per_FOBPerdue",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Per_SupplyPerdu",             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Aeri_AerialCorridorLost",     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Mari_MaritimeCorridorLost",   MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Arei_UnitSurrenderEn",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = RougePur ) ), ]),
        ("AlertPanel/Line/Zone_SecuredByPlayer",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("AlertPanel/Line/Att_FOBPrise",                MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("AlertPanel/Line/Att_SupplyPrise",             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("AlertPanel/Line/Aeri_AerialCorridorSecured",  MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("AlertPanel/Line/Mari_MaritimeCorridorSecured", MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("AlertPanel/Line/Arei_UnitSurrender",          MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("AlertPanel/Line/Arei_EnemyUnitDestroyed",     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [255, 216, 0, 255] ) ), ]),
        ("AlertPanel/Line/Arei_OnObjectiveCaptured",    MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertPur ) ), ]),
        ("AlertPanel/Line/Par_NewPhaseStarted",         MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = VertCitron ) ), ]),
        ("AlertPanel/Line/Arei_NewPhaseStartedForCampaign", MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BleuTurquoise ) ), ]),
        ("AlertPanel/Line/Arei_OnObjectiveLost",        MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = [228, 110, 9, 255] ) ), ]),

        ("Strategic/PawnPower/AttackTop",               MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = OrangeClair ) ), ]),
        ("Strategic/PawnPower/DefenseTop",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Blanc2 ) ), ]),

        ("Strategic/PawnPower/AssaultTop",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Assault ) ), ]), // BlueSanMarino
        ("Strategic/PawnPower/ArmoredTop",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Armored ) ), ]), // GreenSea
        ("Strategic/PawnPower/ArtilleryTop",            MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Artillery ) ), ]), //RedNutmeg
        ("Strategic/PawnPower/AirplaneTop",             MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Airplane ) ), ]),
        ("Strategic/PawnPower/EvasionTop",              MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = Airplane ) ), ]),
        ("PawnLabel/ActionPoints",                      MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = BlancPur ) ), ]),

        ("NoirPur",                                     MAP [ ( ~/ComponentState/Normal, TColorRTTI( Color = NoirPur ) ), ]),

        ("Objective/Sucess",                            MAP [   ( ~/ComponentState/Normal, TColorRTTI( Color = Vert2 ) ),
                                                                ( ~/ComponentState/Grayed,  TColorRTTI( Color = Vert2 ) ),
                                                            ]),
        ("Objective/Failed",                            MAP [   ( ~/ComponentState/Normal,  TColorRTTI( Color = Rouge4_strat2 ) ),
                                                                ( ~/ComponentState/Grayed,  TColorRTTI( Color = Rouge4_strat2 ) ),
                                     ]),
        ("DeploymentPhase/CancelTimer",                 MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = BlancCasse) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = BlancPur) ),
                                                              ( ~/ComponentState/Clicked,     TColorRTTI( Color = BlancPur) ),
                                                            ]),
        ("Multiliste_division_text",                 MAP [ ( ~/ComponentState/Normal,      TColorRTTI( Color = BlancPur) ),
                                                              ( ~/ComponentState/Highlighted, TColorRTTI( Color = [17,42,70,255]) ),
                                                              ( ~/ComponentState/Clicked,     TColorRTTI( Color = [6,16,26,255]) ),
                                                              ( ~/ComponentState/Toggled,               TColorRTTI( Color = [0,0,0,255] ) ),

                                                            ]),

    ]

    TextureDrawerMap = MAP [
        ("ColorMultiply",                               MAP [
                                                            ( ~/ComponentState/Grayed, $/UserInterface/UITextureDrawer_ColorMultiply_Grayscale ),
                                                            ( ~/ComponentState/Normal, $/UserInterface/UITextureDrawer_ColorMultiply ),
                                                        ]),
        ("ColorMultiply_NoGrayscale",                   MAP [ ( ~/ComponentState/Normal, $/UserInterface/UITextureDrawer_ColorMultiply ), ]),
        ("ColorMultiply_Additive",                      MAP [ ( ~/ComponentState/Normal, $/UserInterface/UITextureDrawer_ColorMultiply_Additive ), ]),
        ("ColorMultiply_Grayscale",                     MAP [ ( ~/ComponentState/Normal, $/UserInterface/UITextureDrawer_ColorMultiply_Grayscale ), ]),
        ("ColorMultiply_Highlight",                     MAP [ ( ~/ComponentState/Normal, $/UserInterface/UITextureDrawer_ColorMultiply_Highlight ), ]),

        ("MonochromeMonitorEffect",                     MAP [ ( ~/ComponentState/Normal, TUITextureDrawer( ShaderDescriptor = $/M3D/Shader/ShaderMonochromeMonitorEffect ) ), ]),
    ]
)
