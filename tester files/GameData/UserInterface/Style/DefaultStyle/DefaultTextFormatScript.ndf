private trait_BBMin is [0.5, -0.5, 0]
private trait_BBMax is [2, 0.5, 0]

DefaultTextFormatScript is TTextFormatScriptRTTI
(
    Commands = MAP
    [
        //-------------------------------------------------------------------------------------
        // infos, victoire / defaite

        (
            "score_01",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [3,108,0,255]
                    ColorUp     = [38,158,34,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "score_02",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [38,158,34,255]
                    ColorUp     = [38,158,34,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "score_03",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [49,207,45,255]
                    ColorUp     = [49,207,45,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "score_04",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [183,172,41,255]
                    ColorUp     = [183,172,41,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "score_05",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255,74,57,255]
                    ColorUp     = [255,74,57,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "moral_color_bad_1",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255,228,0,255]
                    ColorUp     = [255,228,0,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "moral_color_bad_2",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255,156,0,255]
                    ColorUp     = [255,156,0,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "moral_color_bad_3",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255,74,57,255]
                    ColorUp     = [255,74,57,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "moral_color_bad_4",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [61,21,17,255]
                    ColorUp     = [61,21,17,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "pi0",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [172,0,0,255]
                    ColorUp     = [172,0,0,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "pi1",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255,190,0,255]
                    ColorUp     = [255,190,0,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "pi2",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [127,255,117,255]
                    ColorUp     = [127,255,117,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "pi3",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [67,255,255,255]
                    ColorUp     = [67,255,255,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "win75",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255,168,69,255]
                    ColorUp     = [255,168,69,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "win90",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255,168,0,255]
                    ColorUp     = [255,168,0,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "Ewin50",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [175, 121, 81, 255]
                    ColorUp     = [175, 121, 81, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "Ewin75",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [175, 88, 23, 255]
                    ColorUp     = [175, 88, 23, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "Ewin90",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [175, 39, 23, 255]
                    ColorUp     = [175, 39, 23, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),

        //-------------------------------------------------------------------------------------
        (
            "shint",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.4
                    ColorBottom = [0, 255, 255, 255]
                    ColorUp     = [0, 255, 255, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "poor",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [243, 84, 66, 255]
                    ColorUp     = [243, 84, 66, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "SemiTransp",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 0.55
                    ColorBottom = [109, 111, 111, 180]
                    ColorUp     = [109, 111, 111, 180]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "SemiTransp2",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 0.4
                    ColorBottom = [125, 125, 125, 180]
                    ColorUp     = [125, 125, 125, 180]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "U1",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [135, 178, 196, 255]
                    ColorUp     = [135, 178, 196, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "default",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
        (
            "small",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 0.5
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
        (
            "large",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.4
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
        (
            "large2",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
        (
            "dateSM",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 0.65
                    ColorBottom = [0, 0, 0, 255]
                    ColorUp     = [0, 0, 0, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
         (
            "medium",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 0.7
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
         (
            "up",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 0.7
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                    UpperCase = true
                )
            )
        ),
         (
            "transparent",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 0.1
                    ColorBottom = [255,  35,  35, 0]
                    ColorUp     = [255,  35,  35, 0]
                    ColorStroke = [  0,   0,   0, 0]
                    Stroke = false
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
        (
            "or", // BLANC PUR
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    =1
                    ColorBottom = JauneOrange
                    ColorUp     = JauneOrange
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "coloredtext1", // VERT FLUO
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [127, 255, 117, 255] // BlockColor14
                    ColorUp     = [127, 255, 117, 255] // BlockColor14
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "coloredtext2", // GRIS
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [192, 192, 192, 255]
                    ColorUp     = [192, 192, 192, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "coloredtext3", // ORANGE
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [255, 180,   0, 255]
                    ColorUp     = [255, 180,   0, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = true
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.2
                )
            )
        ),
        (
            "coloredtext4", // BLEU CIEL
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [130, 235, 255, 200]
                    ColorUp     = [130, 235, 255, 200]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = true
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
        (
            "coloredtext5", // ORANGE - doublon
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [255, 180,  10, 220]
                    ColorUp     = [255, 180,  10, 220]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = true
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
         (
            "blue", // ROUGE
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [135,178,196,255]
                    ColorUp     = [135,178,196,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
         (
            "green", // ROUGE
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [0,200,0,255]
                    ColorUp     = [0,200,0,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
         (
            "lightgreen",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [90,220,90,255]
                    ColorUp     = [0,220,0,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
         (
            "magenta", // ROUGE
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [200,0,255,255]
                    ColorUp     = [200,0,255,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
        (
            "red", // ROUGE
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [255,  35,  35, 220]
                    ColorUp     = [255,  35,  35, 220]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
        (
            "Red", // ROUGE
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [255,  35,  35, 220]
                    ColorUp     = [255,  35,  35, 220]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
        (
            "black", // ROUGE
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [20,  20,  20, 220]
                    ColorUp     = [20,  20,  20, 220]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
        (
            "coloredtext6", // ROUGE
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [255,  35,  35, 220]
                    ColorUp     = [255,  35,  35, 220]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = true
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
        (
            "coloredtext7", // VertPomme
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = VertPomme
                    ColorUp     = VertPomme
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = true
                    StrokeSize = 0.6
                    StrokeHardness = 0.5
                    TextThickness = 0.45
                )
            )
        ),
        (
            "pricetext",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255, 168,  69, 255]
                    ColorUp     = [255, 168,  69, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "conquestincometext",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [0,   255,   0, 255]
                    ColorUp     = [0,   255,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "smallertitle",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 0.8
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )

        ),
        (
            "hint",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [225, 148, 80, 255]
                    ColorUp     = [225, 148, 80, 255]
                    Stroke = false
                )
            )
        ),
        (
            "stroked",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = true
                )
            )
        ),
        //-------------------------------------------------------------------------------------//
        (
            "gras",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
        (
            "bold",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.1
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
        (
            "gras1",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.3
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
        (
            "style1",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
            )
        ),
        (
            "styleGreen",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [127, 255, 117, 255]
                    ColorUp     = [127, 255, 117, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "grayed",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [214, 214, 214, 255] // = TextColor3
                    ColorUp     = [214, 214, 214, 255] // = TextColor3
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "grayed2",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [117, 117, 117, 255] // = TextColor10
                    ColorUp     = [117, 117, 117, 255] // = TextColor10
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "LightestGray",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [190, 190, 190, 255] // = LightestGray
                    ColorUp     = [190, 190, 190, 255] // = LightestGray
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "arme",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.5//1.3
                    ColorBottom = [135,178,196,255] // = bleu variable
                    ColorUp     = [135,178,196,255] // = bleu variable
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
                Typeface = $/M3D/Typefaces/Typeface_Main_OutGame
            )
        ),
        (
            "bleuvariable",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1//1.3
                    ColorBottom = [135,178,196,255] // = bleu variable
                    ColorUp     = [135,178,196,255] // = bleu variable
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
                Typeface = $/M3D/Typefaces/Typeface_Main_OutGame
            )
        ),
        (
            "date",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.5//1.3
                    ColorBottom = [135,178,196,255] // = bleu variable
                    ColorUp     = [135,178,196,255] // = bleu variable
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
                Typeface = $/M3D/Typefaces/Typeface_Main_OutGame
            )
        ),
        (
            "Arialbold",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.2
                    ColorBottom = [0, 0, 0, 0]
                    ColorUp     = [0, 0, 0, 0]
                    ColorStroke = [0, 0, 0, 0]
                    Stroke = false
                )
                Typeface = $/M3D/Typefaces/Typeface_Main_OutGame
            )
        ),
        (
            "styleYellow",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [251, 202,  30, 255]
                    ColorUp     = [251, 202,  30, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "exposant",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    Offset      = [0,-5]
                    FontSize    = 0.6
                    ColorBottom = [117, 255, 255, 255] // TextColor1
                    ColorUp     = [117, 255, 255, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "turquoise",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [117, 255, 255, 255] // TextColor1
                    ColorUp     = [117, 255, 255, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "cyan",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [117, 255, 255, 255] // TextColor1
                    ColorUp     = [117, 255, 255, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "boldcyan",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.15
                    ColorBottom = [180, 255, 255, 255]
                    ColorUp     = [180, 255, 255, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "style_armored",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [216,151,60,255] // TextColor1
                    ColorUp     = [216,151,60,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "style_assault",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [200,195,177,255] // TextColor1
                    ColorUp     = [200,195,177,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "style_artillery",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [115,132,127,255] // TextColor1
                    ColorUp     = [115,132,127,255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "styleTurquoise",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [117, 255, 255, 255] // TextColor1
                    ColorUp     = [117, 255, 255, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "styleGlacier",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = Glacier // TextColor1
                    ColorUp     = Glacier
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "styleOTAN",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [ 81, 122, 140, 255]
                    ColorUp     = [ 81, 122, 140, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "stylePACT",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [255,  84,  84, 255]
                    ColorUp     = [255,  84,  84, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "blueNATO",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [0,  86, 177, 255]
                    ColorUp     = [0,  86, 177, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "redPACT",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [177,  38,  38, 255]
                    ColorUp     = [177,  38,  38, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "stylered",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [240,  10,  10, 255]
                    ColorUp     = [240,  10,  100, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        (
            "styleblue",
            TTFSCommand_StyleChange
            (
                Style = TTextStyle
                (
                    FontSize    = 1.0
                    ColorBottom = [0, 177, 255, 255]
                    ColorUp     = [0, 177, 255, 255]
                    ColorStroke = [  0,   0,   0, 255]
                    Stroke = false
                )
            )
        ),
        //-------------------------------------------------------------------------------------
        //-------------------------------------------------------------------------------------
        //-------------------------------------------------------------------------------------
        // Fulda icones TFS
        //-------------------------------------------------------------------------------------
        // pour scenarioListe
        (
            "size_s",
            TTFSCommand_UISymbol
            (
                TextureToken = "size_s"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "size_m",
            TTFSCommand_UISymbol
            (
                TextureToken = "size_m"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "size_l",
            TTFSCommand_UISymbol
            (
                TextureToken = "size_l"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        // traits
        (
            "trait_SML",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_TraitUni_Icon_half"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "trait_CMD",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_TraitUni_Icon_cmd"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "trait_SF",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_TraitUni_Icon_sf"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "trait_IFV",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_TraitUni_Icon_ifv"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "trait_PARA",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_TraitUni_Icon_para"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "trait_SHOCK",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_TraitUni_Icon_shock"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "trait_MP",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_TraitUni_Icon_mp"
                BBMin=[0.0, -0.80, 0]
                BBMax=[1.00, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),

        (
            "HEL",
            TTFSCommand_UISymbol
            (
                TextureToken = "trait_HEL"
                BBMin= trait_BBMin
                BBMax= trait_BBMax
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "SUP",
            TTFSCommand_UISymbol
            (
                TextureToken = "trait_SUP"
                BBMin= trait_BBMin
                BBMax= trait_BBMax
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HE",
            TTFSCommand_UISymbol
            (
                TextureToken = "trait_HE"
                BBMin= trait_BBMin
                BBMax= trait_BBMax
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HEAT",
            TTFSCommand_UISymbol
            (
                TextureToken = "trait_HEAT"
                BBMin= trait_BBMin
                BBMax= trait_BBMax
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),

        (
            "MVT",
            TTFSCommand_UISymbol
            (
                TextureToken = "trait_MVT"
                BBMin= trait_BBMin
                BBMax= trait_BBMax
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        //-------------------------------------------------------------------------------------
        //-------------------------------------------------------------------------------------
        // bombes avions
        (
            "mk82",
            TTFSCommand_UISymbol
            (
                TextureToken = "Icone_mk82"
                BBMin=[0, -1.0, 0]
                BBMax=[3, 0.43, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "mk20",
            TTFSCommand_UISymbol
            (
                TextureToken = "Icone_mk20"
                BBMin=[0, -1.0, 0]
                BBMax=[3, 0.43, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "mk77",
            TTFSCommand_UISymbol
            (
                TextureToken = "Icone_mk77"
                BBMin=[0, -1.0, 0]
                BBMax=[3, 0.43, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        //-------------------------------------------------------------------------------------
        (
            "conquestArea", //sector
            TTFSCommand_UISymbol
            (
                TextureToken = "Icon_ConquestArea"
                BBMin=[0.0, -0.76, 0.0]
                BBMax=[0.9, 0.1, 0.0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "commandArea",
            TTFSCommand_UISymbol
            (
                TextureToken = "Icon_CommandArea"
                BBMin=[0.0, -0.8, 0.0]
                BBMax=[1.0, 0.15, 0.0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "command",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_CommandAreaResize"
                BBMin=[0.0, -0.96, 0.0]
                BBMax=[1.547, 0.299, 0.0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "Enemy_unit_contact",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_Enemy_unit_contact"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
         ),
        (
            "Enemy_unit_destroyed",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_Enemy_unit_destroyed"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
         ),
        (
            "New_phase_started",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_New_phase_started"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
         ),
        (
            "Unit_under_attack",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_Unit_under_attack"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
         ),
        (
            "Player_unit_destroyed",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_Player_unit_destroyed"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
         ),
        (
            "Player_unit_captured",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_Player_unit_captured"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
         ),
        (
            "Flare_Launched",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_Flare_Launched"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
         ),
        (
            "Enemy_airplane_contact",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_Enemy_airplane_contact"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
         ),
        (
            "Enemy_unit_captured",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_SituationAwareness_Enemy_unit_captured"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "1vs1",
            TTFSCommand_UISymbol
            (
                TextureToken = "TextureMapTaille1v1"
                BBMin=[0.2, -1.3, 0]
                BBMax=[3.75, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "2vs2",
            TTFSCommand_UISymbol
            (
                TextureToken = "TextureMapTaille2v2"
                BBMin=[0.2, -1.3, 0]
                BBMax=[3.75, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "3vs3",
            TTFSCommand_UISymbol
            (
                TextureToken = "TextureMapTaille3v3"
                BBMin=[0.2, -1.3, 0]
                BBMax=[3.75, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "4vs4",
            TTFSCommand_UISymbol
            (
                TextureToken = "TextureMapTaille4v4"
                BBMin=[0.2, -1.3, 0]
                BBMax=[3.75, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "10vs10",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutGameTexture_10vs10"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "TerrainForet",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_Labels_Terrain_TerrainForet"
                BBMin=[0, -1, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "CombatRuleDestruction",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutgameTexture_DestructionTypeTexture2"
                BBMin=[0.0, -1.3, 0]
                BBMax=[3.55, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "CombatRuleConquest",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutgameTexture_ConquestTypeTexture2"
                BBMin=[0.0, -1.3, 0]
                BBMax=[3.55, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "ModeConquest",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutgameTexture_ConquestModeTexture2"
                BBMin=[0.0, -1.3, 0]
                BBMax=[3.55, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "ModeDestruction",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutgameTexture_DestructionModeTexture2"
                BBMin=[0.0, -1.3, 0]
                BBMax=[3.55, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "ModeEncounter",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutgameTexture_Mode_EncounterBatte"
                BBMin=[0.0, -1.3, 0]
                BBMax=[3.55, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "ModeBreakthrough",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutgameTexture_Mode_Breakthrough2"
                BBMin=[0.0, -1.3, 0]
                BBMax=[3.55, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "RedAttack",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutgameTexture_RedAttack"
                BBMin=[0.0, -1.3, 0]
                BBMax=[3.55, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "BlueAttack",
            TTFSCommand_UISymbol
            (
                TextureToken = "OutgameTexture_BlueAttack"
                BBMin=[0.0, -1.3, 0]
                BBMax=[3.55, 0.45, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        //-------------------------------------------------------------------------------------
        (
            "stealth",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_hidden"
                BBMin=[0.0, -1.10, 0]
                BBMax=[1.487, 0.41, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        //-------------------------------------------------------------------------------------
        (
            "RECO1",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_Recon1"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "RECO2",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_Recon2"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "RECO3",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_Recon3"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "CMD",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_cmd"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "IFV",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_ifv"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "ARMOR",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_armor"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "APC",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_apc"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "AIR",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_air"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "AA",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_AA"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "INF",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_Infantry"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "HOWZ",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_howitzer"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "ASSAUT",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_assault"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "AT",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_at"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        (
            "HELO",
            TTFSCommand_UISymbol
            (
                TextureToken = "Texture_Icon_helo"
                BBMin=[0.0, -0.718, 0]
                BBMax=[1.68, 0.128, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),
        //-------------------------------------------------------------------------------------
        (
            "renfort_flag",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SpawnPoint"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HQ", // à DEPREC
            TTFSCommand_UISymbol
            (
                TextureToken = "texture_HQ_Regiment"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HQ_Div", // à DEPREC
            TTFSCommand_UISymbol
            (
                TextureToken = "texture_HQ_Division"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HQ_Regiment",
            TTFSCommand_UISymbol
            (
                TextureToken = "texture_HQ_Regiment"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HQ_Division",
            TTFSCommand_UISymbol
            (
                TextureToken = "texture_HQ_Division"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HQ_Corps",
            TTFSCommand_UISymbol
            (
                TextureToken = "texture_HQ_Corps"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HQ_Army",
            TTFSCommand_UISymbol
            (
                TextureToken = "texture_HQ_Army"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "HQ_ArmyGroup",
            TTFSCommand_UISymbol
            (
                TextureToken = "texture_HQ_ArmyGroup"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "supply2",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_Labels_NoSupplyZerosDay"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "supply1",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_Labels_NoSupplyHalfDay"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "supply0",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_Labels_NoSupply"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "Steelman_terrain_urbain",
            TTFSCommand_UISymbol
            (
                TextureToken = "Steelman_terrain_urbain"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "Steelman_terrain_plaine",
            TTFSCommand_UISymbol
            (
                TextureToken = "Steelman_terrain_plaine"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "Steelman_terrain_foret",
            TTFSCommand_UISymbol
            (
                TextureToken = "Steelman_terrain_foret"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        //-------------------------------------------------------------------------------------//
        (
            "complex",
            TTFSCommand_UISymbol
            (
                TextureToken = "TextureComplexiteCampagne"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        //-------------------------------------------------------------------------------------
        // icones pour drapeaux
        (
            "sov",
            TTFSCommand_UISymbol
            (
                TextureToken = "CommonTexture_MotherCountryFlag_tfs_SOV"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),

        //-------------------------------------------------------------------------------------
        // icones pour AutoResolve
        (
            "assault",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SelectionPanel_Assault"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "armored",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SelectionPanel_Armored"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "artillery",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SelectionPanel_Artillery"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "launchAttack",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_Labels_CanLaunchAttack"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "bombard",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SelectionPawnPanel_BMB"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "attack",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SelectionPanel_Attack"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "defense",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SelectionPanel_Defense"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        //-------------------------------------------------------------------------------------

        (
            "US_1AD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_1AD")
        ),
        (
            "US_2AD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_2AD")
        ),
        (
            "US_3AD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_3AD")
        ),
        (
            "US_3ID",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_3ID")
        ),
        (
            "US_8ID",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_8ID")
        ),
        (
            "US_17AF",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_17AF")
        ),
        (
            "US_11ACR",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_11ACR")
        ),
        (
            "US_4ATAF",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_4ATAF")
        ),
        (
            "US_2ATAF",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_US_2ATAF")
        ),
        (
            "UK_3armoured",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_UK_3armoured")
        ),
        (
            "UK_1AD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_UK_1AD")
        ),
        (
            "UK_2ID",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_UK_2ID")
        ),
        (
            "UK_4AD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_UK_4AD")
        ),
        (
            "BEL_16mec",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_BEL_16mec")
        ),
        (
            "BEL_1corps",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_BEL_1corps")
        ),
        (
            "BEL_1_INFDIV",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_BEL_1_INFDIV")
        ),
        (
            "CAN_1",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_CAN_1")
        ),
        (
            "RFA_26llb",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_26llb")
        ),
        (
            "RFA_5PZ",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_5PZ")
        ),
        (
            "RFA_2PZ",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_2PZ")
        ),
        (
            "RFA_3PZ",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_3PZ")
        ),
        (
            "RFA_10PZ",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_10PZ")
        ),
        (
            "RFA_12PZ",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_12PZ")
        ),
        (
            "RFA_1PZ",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_1PZ")
        ),
        (
            "RFA_terrkdo",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_terrkdo")
        ),
        (
            "RFA_2Luft",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_2Luft")
        ),
        (
            "RFA_Luft",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_luft")
        ),
        (
            "RFA_4PzGren",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_4PzGren")
        ),
        (
            "RFA_default",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_RFA_default")
        ),
        (
            "NL_1D",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_NL_1D")
        ),
        (
            "NL_4D",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_NL_4D")
        ),
        (
            "NL_KLu",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_NL_KLu")
        ),

        (
            "FR_4",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_FR_4")
        ),
        (
            "FR_5db",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_FR_5db")
        ),
        (
            "FR_6dbl",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_FR_6dbl")
        ),
        (
            "FR_11para",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_FR_11para")
        ),
        (
            "ES_brunete",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_ES_brunete")
        ),


        // pacte
        (
            "CZ_1tk",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_CZ_1tk")
        ),
        (
            "CZ_9tk",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_CZ_9tk")
        ),
        (
            "CZ_2mot",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_CZ_2mot")
        ),
        (
            "CZ_3mot",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_CZ_3mot")
        ),
        (
            "CZ_20mot",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_CZ_20mot")
        ),
        (
            "CZ_19mot",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_CZ_19mot")
        ),
        (
            "DDR_7PZD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_7PZD")
        ),
        (
            "DDR_4MSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_4MSD")
        ),
        (
            "DDR_11MSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_11MSD")
        ),
        (
            "DDR_17MSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_17MSD")
        ),
        (
            "DDR_19MSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_19MSD")
        ),
        (
            "DDR_20MSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_20MSD")
        ),
        (
            "DDR_8MSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_8MSD")
        ),
        (
            "DDR_9PZD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_9PZD")
        ),
        (
            "DDR_Luft",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_DDR_Luft")
        ),
        (
            "SOV_18_ZRB",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_18_ZRB")
        ),
        (
            "SOV_53_ZRB",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_53_ZRB")
        ),
        (
            "SOV_27_GVMSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_27_GVMSD")
        ),
        (
            "SOV_39_GVMSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_39_GVMSD")
        ),
        (
            "SOV_57_GVMSD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_57_GVMSD")
        ),
        (
            "SOV_16_VA",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_16_VA")
        ),
        (
            "SOV_35_GvDShB",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_35_GvDShB")
        ),
        (
            "SOV_20_GVMD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_20_GVMD")
        ),
        (
            "SOV_79_GVTD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_79_GVTD")
        ),
        (
            "SOV_11_GVTD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_11_GVTD")
        ),
        (
            "SOV_9_TD",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_9_TD")
        ),
        (
            "SOV_default",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_default")
        ),
        (
            "SOV_guard",
            TemplateTFS_Division(TextureToken = "CommonTexture_division_SOV_guard")
        ),
        //-------------------------------------------------------------------------------------


        (
            "NATO",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_NATO_small")
        ),
        (
            "PACT",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_PACT_small")
        ),
        (
            "TCH",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_TCH")
        ),
        (
            "CZ",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_TCH")
        ),
        (
            "FR",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_FR")
        ),
        (
            "FRA",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_FR")
        ),
        (
            "POL",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_POL")
        ),
        (
            "RFA",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_RFA")
        ),
        (
            "UK_RFA",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_UK_RFA")
        ),
        (
            "DDR",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_DDR")
        ),
        (
            "UK",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_UK")
        ),
        (
            "SOV",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_SOV")
        ),
        (
            "US",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_US")
        ),
        (
            "CAN",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_CAN")
        ),
        (
            "DAN",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_DAN")
        ),
        (
            "SWE",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_SWE")
        ),
        (
            "NOR",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_NOR")
        ),
        (
            "NK",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_NK")
        ),
        (
            "ROK",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_ROK")
        ),
        (
            "CHI",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_CHI")
        ),
        (
            "JAP",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_JAP")
        ),
        (
            "ANZ",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_ANZ")
        ),
        (
            "GER",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_GERW")
        ),
        (
            "BEL",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_BEL")
        ),
        (
            "NL",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_NL")
        ),
        (
            "ESP",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_ESP")
        ),
        (
            "ES",
            TemplateTFS_Flag(TextureToken = "CommonTexture_MotherCountryFlag_ESP")
        ),
        //-------------------------------------------------------------------------------------
        (
            "ammo",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_Texture_OffMap_Ammo"
                BBMin=[0, -0.8, 0]
                BBMax=[1.2, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
                UseTextColor = true
            )
        ),



        //TUTORIALS------------------------------------------------------------------------------------------------
        (
            "mouseleftclick",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_TutorialsIcons_SizeX12_MouseLeftClick"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.3, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
       (
            "mouserightclick",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_TutorialsIcons_SizeX12_MouseRightClick"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.3, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
       (
            "mousemiddle",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_TutorialsIcons_SizeX12_MouseMiddle"
                BBMin=[0, -1.18, 0]
                BBMax=[1.75, 0.65, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "mousescrollwheel",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_TutorialsIcons_SizeX12_MouseScrollWheel"
                BBMin=[0, -1.18, 0]
                BBMax=[1.75, 0.65, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "mouseleftclickdraganddrop",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseInGame_TutorialsIcons_SizeX12_MouseLeftClickDragAndDrop"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.3, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        //-------------------------------------------------------------------------------------

        (
            "tuto_LOS",
            TTFSCommand_UISymbol
            (
                TextureToken = "icone_los"
                BBMin=[0, -1, 0]
                BBMax=[2, 0.4, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "tuto_hq",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SelectionPawnPanel_HQ"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "tuto_motorised",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_SelectionPawnPanel_Motorised"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "objectif",
            TTFSCommand_UISymbol
            (
                TextureToken = "UseStrategic_BoutonMission"
                BBMin=[0, -1, 0]
                BBMax=[1.5, 0.5, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "locked",
            TTFSCommand_UISymbol
            (
                TextureToken = "Icon_Locked"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
        (
            "contentDLC",
            TTFSCommand_UISymbol
            (
                TextureToken = "Icon_ContentDLC"
                BBMin=[0, -0.8, 0]
                BBMax=[1, 0.2, 0]
                ShaderDescriptor = $/M3D/Shader/MaterialInterface2D_Blend
            )
        ),
    ]
)
