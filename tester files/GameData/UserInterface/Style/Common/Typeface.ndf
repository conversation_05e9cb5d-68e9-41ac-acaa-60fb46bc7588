
// Alias de polices destinés à être utilisés dans le code sans namespace dans les champs Typeface,
// ou préférentiellement via les 'TypefaceToken' et la map Typeface de DefaultStyleGuides.ndf

TypefaceTitle           is $/M3D/Typefaces/Typeface_Title
TypefaceSecond          is $/M3D/Typefaces/Typeface_Second
TypefaceThird           is $/M3D/Typefaces/Typeface_Third
TypefaceFourth           is $/M3D/Typefaces/Typeface_Fourth
TypefaceFith            is $/M3D/Typefaces/Typeface_Fith
TypefaceSixth            is $/M3D/Typefaces/Typeface_Sixth
TypefaceHandPen         is $/M3D/Typefaces/Typeface_HandPen
TypefaceSevenSegments   is $/M3D/Typefaces/Typeface_SevenSegmentsFont
TypefaceMainOutgame     is $/M3D/Typefaces/Typeface_Main_OutGame
Typeface_Subtitles      is $/M3D/Typefaces/Typeface_Subtitles
TypefaceMainIngame      is $/M3D/Typefaces/Typeface_Main_InGame
TypefaceAreas           is $/M3D/Typefaces/Typeface3D_Title_CompensatePostProcess
TypefaceStrategicFeedbacks  is $/M3D/Typefaces/Typeface3D_Default
