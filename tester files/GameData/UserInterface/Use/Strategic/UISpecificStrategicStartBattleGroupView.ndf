
//------------------------------------------------------------------------------
PawnSlot is BUCKListDescriptor
(
    ElementName = "PawnSlotContent"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKSpecificDropdownDescriptor
            (
                ElementName = "PawnSlotBox"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [80.0, 80.0]
                )

                HasBorder = false

                MainTextColorToken = 'Transparent'
                ShowArrow = false
                HidePointerEvents = true
                ItemsTextColorToken = 'Noir_option'
                AlignFloatingPanelRight = true
                ItemMagnifiableHeight = 50.0
                OpenSound = ~/SoundEvent_SteelmanAddUnitOpenList

                ItemsTextPadding = TRTTILength4( Magnifiable = [50.0, 0.0, 0.0, 0.0] )
                ItemComponents =
                [
                    BUCKTextureDescriptor
                    (
                        ElementName = "PawnTexture"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [40.0, 40.0]
                            AlignementToAnchor = [0.0, 0.5]
                            AlignementToFather = [0.0, 0.5]
                            MagnifiableOffset = [5.0, 0.0]
                        )

                        ClipTextureToComponent = true
                        TextureColorToken = "SM_RifleGreen"
                    )
                ]

                Components =
                [
                    PanelRoundedCorner
                    (
                        BackgroundBlockColorToken = 'SM_Grullo_2'
                        BorderLineColorToken = 'SM_Noir'
                    ),
                    BUCKSpecificStrategicHintableArea
                    (
                        ElementName = "PawnSlotHintableArea"
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    ),
                    BUCKTextureDescriptor
                    (
                        ElementName = "PawnSlotAddTexture"
                        ClipTextureToComponent = true
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )
                        TextureToken = "UseStrategic_PrepareBattle_AddPawn"
                        TextureColorToken = "BoutonStandard"
                    ),
                    BUCKContainerDescriptor
                    (
                        ElementName = "PawnSlotAvailablePawnsContainer"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [24.0, 24.0]
                            MagnifiableOffset = [10.0, 10.0]
                            AlignementToAnchor = [1.0, 1.0]
                            AlignementToFather = [1.0, 1.0]
                        )

                        Components =
                        [
                            PanelRoundedCorner
                            (
                                Radius = 12.0
                                BorderThicknessToken = "2"
                                BackgroundBlockColorToken = "SM_RifleGreen"
                                BorderLineColorToken = "SM_Grullo"
                            ),
                            BUCKTextDescriptor
                            (
                                ElementName = "PawnSlotAvailablePawnsText"
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 1.0]
                                )

                                ParagraphStyle = TParagraphStyle
                                (
                                    Alignment = UIText_Center
                                    VerticalAlignment = UIText_VerticalCenter
                                )

                                TextStyle = "Default"

                                HorizontalFitStyle = ~/FitStyle/UserDefined
                                VerticalFitStyle = ~/FitStyle/UserDefined

                                TextToken = "DES_VALUE"
                                TypefaceToken = "Eurostyle"

                                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                                TextColor = "SM_paleSilver"
                                TextSize = "16"
                            )
                        ]
                    )
                ]
            )
        )
    ]
)



//------------------------------------------------------------------------------

BUCKSpecificStrategicStartBattleGroupMainComponentDescriptor is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        PawnSlot
    ]
)

//------------------------------------------------------------------------------

template UISpecificStrategicStartBattleGroupViewDescriptor
[
    UniqueNameForInsertion : string = "",
] is TUISpecificStrategicStartBattleGroupViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificStrategicStartBattleGroupMainComponentDescriptor
    MainComponentContainerUniqueName = <UniqueNameForInsertion> // Permet d'indiquer l'endroit ou le composant doit s'insérer

    PawnBriefDescriptor = ~/UISpecificStrategicStartBattlePawnBriefViewDescriptor

    // à garder d'équerre avec StrategicBattleRole.ndf
    // la position dans le tableau correspond au role défini dans StrategicBattleRole.ndf (PanelListeGroupCombatRack pour les fighter donc en position 0 par exemple)
    HintTitleByRole =
    [
        "PrC_stan", // Fighter
        "PrC_aux", // AuxiliarySupport
        "PrC_as",  // GroundSupport
        "PrC_arts", // AirSupport
    ]

    // à garder d'équerre avec StrategicBattleRole.ndf
    // la position dans le tableau correspond au role défini dans StrategicBattleRole.ndf (PanelListeGroupCombatRack pour les fighter donc en position 0 par exemple)
    HintBodyByRole =
    [
        "PrC_stanb", // Fighter
        "PrC_auxb", // AuxiliarySupport
        "sm_pre6",  // GroundSupport
        "sm_pre5", // AirSupport
    ]

    TextDico = ~/LocalisationConstantes/dico_interface_ingame
    PawnItemTextToken = "BTL_pwnpwr"

    AddUnitToBattleSound = ~/SoundEvent_SteelmanAttackPrepareAddUnit
    RemoveUnitFromBattleSound = ~/SoundEvent_SteelmanAttackPrepareRemoveUnit
)
