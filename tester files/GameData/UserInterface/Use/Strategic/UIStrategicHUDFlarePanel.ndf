
//-------------------------------------------------------------------------------------
// Conteneur pour les flares
//-------------------------------------------------------------------------------------
UIStrategicFlarePanelViewMainComponent is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    FirstMargin = TRTTILength (Magnifiable = 5.0)
    InterItemMargin = TRTTILength(Magnifiable = 11.0)
    LastMargin = TRTTILength(Magnifiable = 5.0)
    Axis = ~/ListAxis/Vertical

    HidePointerEvents = true

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                    MagnifiableWidthHeight = [0.0, 27.0]
                )

                FirstMargin = TRTTILength (Magnifiable = 5.0)
                InterItemMargin = TRTTILength(Magnifiable = 11.0)
                LastMargin = TRTTILength(Magnifiable = 5.0)
                Axis = ~/ListAxis/Horizontal

                Elements =
                [
                    BUCKListElementDescriptor(ComponentDescriptor = FlareButtonNeedHelp(IsFromStrategic = true)),
                    BUCKListElementDescriptor(ComponentDescriptor = FlareButtonStrategyAttack(IsFromStrategic = true)),
                    BUCKListElementDescriptor(ComponentDescriptor = FlareButtonStrategyDefend(IsFromStrategic = true)),
                    BUCKListElementDescriptor(ComponentDescriptor = FlareButtonSupport(IsFromStrategic = true)),
                    BUCKListElementDescriptor(ComponentDescriptor = FlareButtonEnemySpotted(IsFromStrategic = true)),
                    BUCKListElementDescriptor(ComponentDescriptor = FlareButtonCustom(IsFromStrategic = true))
                ]
            )
        )
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            BackgroundBlockColorToken = "SM_RifleGreen_75"
            BorderLineColorToken = "SM_Grullo"
        )
    ]

    ForegroundComponents =
    [
        FlareCustomEditableText(IsFromStrategic = true)
    ]
)

//-------------------------------------------------------------------------------------

UIStrategicFlarePanelViewDescriptor is TUIInGameFlarePanelViewDescriptor
(
    MainComponentDescriptor = ~/UIStrategicFlarePanelViewMainComponent
    MainComponentContainerUniqueName = "UICommonFlarePanelViewMainContainer"
)
