BUCKSpecificStrategicPlayerNameDisplayMainComponentDescriptor is BUCKContainerDescriptor
(
    ElementName = "StrategicPlayerNameDisplayMainComponent"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 40.0]
    )

    HasBackground = true
    BackgroundBlockColorToken = 'SM_Feldgrau_75'

    Components =
    [
        OmbrePanel(),

        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather = [0.5, 0.0]
                AlignementToAnchor = [0.5, 0.0]
            )

            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
            Axis = ~/ListAxis/Horizontal

            HidePointerEvents = true
            PointerEventsToAllow = ~/EAllowablePointerEventType/Scroll

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        UniqueName = "StrategicGlobalObjectivesScoreContainer"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.0, 1.0]
                            AlignementToAnchor = [0.0, 1.0]
                            MagnifiableOffset = [0,-3]
                        )

                        FitStyle = ~/ContainerFitStyle/FitToContent
                        HasBackground = true
                        BackgroundBlockColorToken = 'SM_RifleGreen_75'
                        // envoie BUCKSpecificStrategicObjectiveScoreMainComponentDescriptor
                    )
                ),

                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        UniqueName = "StrategicDateContainer"
                        ComponentFrame = TUIFramePropertyRTTI()

                        FitStyle = ~/ContainerFitStyle/FitToContent
                        // envoie BUCKSpecificStrategicDateMainComponentDescriptor
                    )
                ),

            ]
        ),
        BUCKTextDescriptor
        (
            ElementName = "PlayerName"

            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [224.0, 40.0]
                AlignementToAnchor = [0.5, 0.0]
                AlignementToFather = [0.5, 0.0]
                MagnifiableOffset = [145.0, 0.0]
            )


            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined
            ParagraphStyle = ~/paragraphStyleTextCenter
            TextStyle = "Default"
            TypefaceToken = "Eurostyle_Italic"
            TextDico = ~/LocalisationConstantes/dico_interface_ingame
            TextSize = "22"
            TextColor = "BlancEquipe"
        )
    ]
)

UISpecificStrategicPlayerNameDisplayViewDescriptor is TUISpecificStrategicPlayerNameDisplayViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificStrategicPlayerNameDisplayMainComponentDescriptor
)
