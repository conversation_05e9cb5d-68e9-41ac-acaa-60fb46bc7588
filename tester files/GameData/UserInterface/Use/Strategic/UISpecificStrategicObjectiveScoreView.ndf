BUCKSpecificStrategicObjectiveScoreMainComponentDescriptor is BUCKRackDescriptor
(
    ElementName = 'TextScoreRack'

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 24.0]
        AlignementToFather = [0.0, 0.5]
        AlignementToAnchor = [0.0, 0.5]
    )
    HidePointerEvents = true
    Axis = ~/ListAxis/Horizontal

    BladeDescriptor = BUCKListDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI
        (
            MagnifiableWidthHeight = [0.0, 24.0]

        )
        Axis = ~/ListAxis/Horizontal

        Elements =
        [
            BUCKListElementDescriptor
            (
            MinSize = TRTTILength( Magnifiable = 4.0 )
                ComponentDescriptor = BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI()
                )
            ),
            BUCKListElementDescriptor
            (
                ComponentDescriptor = BUCKTextureDescriptor
                (
                    ElementName = 'Flag'
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [42, 22]
                        AlignementToFather = [0.0, 0.5]
                        AlignementToAnchor = [0.0, 0.5]

                    )

                    TextureFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [0.85, 0.85]
                        AlignementToFather = [0.5, 0.5]
                        AlignementToAnchor = [0.5, 0.5]
                    )
                )
            ),
            BUCKListElementDescriptor
            (
                ComponentDescriptor = BUCKTextDescriptor
                (
                    ElementName = 'Score'
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [66.0, 20.0]
                        AlignementToFather = [0.0, 0.5]
                        AlignementToAnchor = [0.0, 0.5]
                    )
                    ParagraphStyle = ~/LDHintParagraphStyleCentered
                    TextStyle = "Default"

                    HorizontalFitStyle = ~/FitStyle/UserDefined
                    VerticalFitStyle = ~/FitStyle/UserDefined

                    TextDico = ~/LocalisationConstantes/dico_interface_ingame
                    TypefaceToken = "Eurostyle_Italic"
                    TextColor = "SM_paleSilver"
                    TextSize = "15"
                )
            )
        ]
        ForegroundComponents =
        [
            BUCKSpecificStrategicHintableArea
            (
                HintTitleToken = 'SM_scoret'
                HintBodyToken = 'SM_scoreb'
                HintExtendedToken = 'SM_scoree'
                DicoToken = ~/LocalisationConstantes/dico_interface_ingame
            )
        ]
    )
)
//-------------------------------------------------------------------------------------
UISpecificStrategicObjectiveScoreViewDescriptor is TUISpecificStrategicObjectiveScoreViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificStrategicObjectiveScoreMainComponentDescriptor
    MainComponentContainerUniqueName = "StrategicGlobalObjectivesScoreContainer" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    CoalitionTextureTokens = MAP
    [
        (ECoalition/NATO, "CommonTexture_MotherCountryFlag_NATO_small"),
        (ECoalition/PACT, "CommonTexture_MotherCountryFlag_PACT_small"),
    ]
)
