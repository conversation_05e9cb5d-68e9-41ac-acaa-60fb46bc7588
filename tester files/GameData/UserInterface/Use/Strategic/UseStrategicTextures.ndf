// !!!!!! ATTENTION
// Toutes les textures listées ici doivent forcement se trouver dans GameData:/Assets/2D/Interface
// !!!!!! ATTENTION

//Ex : InGameTexture_Blablabla is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/InGame/Blablabla.png")

UseStrategic_AdditionalTextureBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP
    [
        ("UseStrategic_Labels_NoSupplyZerosDay",                     MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_NoSupplyZerosDay)] ),
        ("UseStrategic_Labels_NoSupplyHalfDay",                      MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_NoSupplyHalfDay)] ),
        ("UseStrategic_Labels_NoSupply",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_NoSupply)] ),

        ("UseStrategic_Labels_Fatigue_0",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Fatigue_0)] ),
        ("UseStrategic_Labels_Fatigue_1",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Fatigue_1)] ),
        ("UseStrategic_Labels_Fatigue_2",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Fatigue_2)] ),
        ("UseStrategic_Labels_Fatigue_3",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Fatigue_3)] ),
        ("UseStrategic_Labels_Fatigue_4",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Fatigue_4)] ),
        ("UseStrategic_Labels_Fatigue_5",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Fatigue_5)] ),
        ("UseStrategic_Labels_Fatigue_6",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Fatigue_6)] ),
        ("UseStrategic_Labels_Fatigue_7",                             MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Fatigue_7)] ),

        ("UseStrategic_Labels_FortificationAntiAirEnCours",          MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_FortificationAntiAirEnCours)] ),
        ("UseStrategic_Labels_FortificationAntiAir",                 MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_FortificationAntiAir)] ),
        ("UseStrategic_Labels_Arrow",                                MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Arrow)] ),
        ("UseStrategic_MapScaleIcon",                                MAP [(~/ComponentState/Normal, ~/UseStrategic_MapScaleIcon)] ),
        ("UseStrategic_SelectionPawnPanel_Infantry",                 MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_Infantry)] ),
        ("UseStrategic_SelectionPawnPanel_Cannon",                   MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_Cannon)] ),
        ("UseStrategic_SelectionPawnPanel_Tank",                     MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_Tank)] ),
        ("UseStrategic_SelectionPawnPanel_Vehicle",                  MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_Vehicle)] ),
        ("UseStrategic_SelectionPawnPanel_Plane",                    MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_Plane)] ),
        ("UseStrategic_SelectionPawnPanel_Artillery",                MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_Artillery)] ),

        ("UseStrategic_SelectionPawnPanel_BMB",                      MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_BMB)] ),
        ("UseStrategic_SelectionPawnPanel_DCA",                      MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_DCA)] ),
        ("UseStrategic_SelectionPawnPanel_HQ",                       MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_HQ)] ),
        ("UseStrategic_SelectionPawnPanel_Motorised",                MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPawnPanel_Motor)] ),

        ("UseStrategic_PrepareBattle_AddPawn",                       MAP [(~/ComponentState/Normal, ~/UseStrategic_PrepareBattle_AddPawn)] ),

        ("UseStrategic_Labels_Terrain_TerrainUrbain",                MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/terrain_urbain.png"))] ),
        ("UseStrategic_Labels_Terrain_TerrainForet",                 MAP [(~/ComponentState/Normal, ~/UseStrategic_Labels_Terrain_TerrainForet)] ),
        ("UseStrategic_Labels_Terrain_TerrainPlaine",                MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/terrain_plaine.png"))] ),

        ("UseStrategic_Labels_NoCmd",                                MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/no_cmd.png"))] ),
        ("UseStrategic_Labels_NoInfantry",                           MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/no_infantry.png"))] ),


        ("renfort_end", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Renfort/renfort_end.png"))] ),
        ("renfort_mid", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Renfort/renfort_mid.png"))] ),
        ("renfort_start", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Renfort/renfort_start.png"))] ),
        ("renfort_date", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Renfort/renfort_date.png"))] ),

        ("SelectPawn_oob", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/SelectPawn_oob.png"))] ),

        ("icone_nextTurn", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Feedback/icone_nextTurn.png'))] ),
        ("wait_until_next_turn", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Feedback/icone_nextTurn.png')),
                                      (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Feedback/icone_wakeup.png' )),
                                      (~/ComponentState/ToggleHighlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Feedback/icone_wakeup.png' )), ] ),

        ("UseStrategic_SelectionPanel_Deploy",                       MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Deploy)] ),
        ("UseStrategic_SelectionPanel_Command",                      MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Command)] ),
        ("UseStrategic_SelectionPanel_Assault",                      MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Assault)] ),
        ("UseStrategic_SelectionPanel_Armored",                      MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Armored)] ),
        ("UseStrategic_SelectionPanel_Artillery",                    MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Artillery)] ),
        ("UseStrategic_SelectionPanel_Airplane",                     MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Airplane)] ),
        ("UseStrategic_SelectionPanel_Evasion",                      MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Evasion)] ),
        ("UseStrategic_SelectionPanel_Attack",                       MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Attack)] ),
        ("UseStrategic_SelectionPanel_Defense",                      MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Defense)] ),
        ("UseStrategic_SelectionPanel_PA",                           MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/PA.png"))] ),
        ("UseStrategic_SelectionPanel_fatigue",                      MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/fatigue.png"))] ),


        ("UseStrategic_BoutonMission",                               MAP [
                                                                          (~/ComponentState/Normal,      TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/objectif3.png")),

                                                                     ]),

        ("UseStrategic_BoutonOrganisation",                               MAP [
                                                                          (~/ComponentState/Normal,      TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/organisation2.png")),

                                                                     ]),

        ("UseStrategic_BoutonPertes",                               MAP [
                                                                          (~/ComponentState/Normal,      TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Kills2.png")),

                                                                     ]),



        ("vitesse03Strat", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseStrategic/Icons/TimeSpeed2.png' )),
                            ] ),
        ("vitesse04Strat", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/vitesse04t.png' )),
                            ] ),
        ("vitesse05Strat", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/GameSpeed/vitesse05t.png' )),
                            ] ),

        ("UseStrategic_Commandement",                               MAP [(~/ComponentState/Normal,      TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/commandement.png"))]),

        ("UseStrategic_SelectionPanel_Wrench",                       MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Wrench)]),
        ("UseStrategic_SelectionPanel_Skull",                        MAP [(~/ComponentState/Normal, ~/UseStrategic_SelectionPanel_Skull)]),
        ("UseStrategic_ProductionMenu_Star",                         MAP [(~/ComponentState/Normal, ~/UseStrategic_ProductionMenu_Star)] ),
        ("UseStrategic_SpawnPoint",                                  MAP [(~/ComponentState/Normal, ~/UseStrategic_SpawnPoint)] ),
        ("UseStrategic_SpawnPointMain_Pole",                         MAP [(~/ComponentState/Normal, ~/UseStrategic_SpawnPointMain_Pole)] ),
        ("UseStrategic_SpawnPointMain_HorizontalBorder",             MAP [(~/ComponentState/Normal, ~/UseStrategic_SpawnPointMain_HorizontalBorder)] ),
        ("UseStrategic_SpawnPointMain_Fill",                         MAP [(~/ComponentState/Normal, ~/UseStrategic_SpawnPointMain_Fill)] ),

        ("UseStrategic_Lock_Company",                                MAP [(~/ComponentState/Normal, ~/UseStrategic_Lock_Company)] ),

        ("UseStrategic_oob_L",  MAP [(~/ComponentState/Normal, TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/oob_L.png"))] ),
    ]
)

UseStrategic_Labels_NoSupplyZerosDay                     is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/NoSupplyingZeroDays.png")
UseStrategic_Labels_NoSupplyHalfDay                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/NoSupplyingHalfDay.png")
UseStrategic_Labels_NoSupply                             is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/NoSupplying.png")

UseStrategic_Labels_Fatigue_0                            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Fatigue_0.png")
UseStrategic_Labels_Fatigue_1                            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Fatigue_1.png")
UseStrategic_Labels_Fatigue_2                            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Fatigue_2.png")
UseStrategic_Labels_Fatigue_3                            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Fatigue_3.png")
UseStrategic_Labels_Fatigue_4                            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Fatigue_4.png")
UseStrategic_Labels_Fatigue_5                            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Fatigue_5.png")
UseStrategic_Labels_Fatigue_6                            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Fatigue_6.png")
UseStrategic_Labels_Fatigue_7                            is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Fatigue_7.png")


UseStrategic_Labels_FortificationAntiAirEnCours          is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/DCAModeEnCours.png")
UseStrategic_Labels_FortificationAntiAir                 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/DCA.png")

UseStrategic_Labels_Terrain_TerrainForet                 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/TerrainForet.png")
//UseStrategic_Labels_HQ                                   is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisation_HQ.png")

UseStrategic_Labels_Arrow                                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/fleche.png")

UseStrategic_MapScaleIcon                                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/ScaleIcon.png")
UseStrategic_SelectionPawnPanel_Infantry                 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Infantry.png")
UseStrategic_SelectionPawnPanel_Cannon                   is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Cannon.png")
UseStrategic_SelectionPawnPanel_Tank                     is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Tank.png")
UseStrategic_SelectionPawnPanel_Vehicle                  is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Vehicle.png")
UseStrategic_SelectionPawnPanel_Plane                    is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Plane.png")
UseStrategic_SelectionPawnPanel_Artillery                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Artillery.png")

UseStrategic_SelectionPawnPanel_BMB                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Bomber.png")
UseStrategic_SelectionPawnPanel_DCA                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/DCA.png")
UseStrategic_SelectionPawnPanel_HQ                       is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/mobilisation_HQn.png")
UseStrategic_SelectionPawnPanel_Motor                    is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Motorised.png")

UseStrategic_SelectionPanel_Deploy                       is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Deploy.png")
UseStrategic_SelectionPanel_Command                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Command.png")
UseStrategic_SelectionPanel_Assault                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Assault.png")
UseStrategic_SelectionPanel_Armored                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Armored.png")
UseStrategic_SelectionPanel_Artillery                    is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Artillery.png")
UseStrategic_SelectionPanel_Airplane                     is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Airplane.png")
UseStrategic_SelectionPanel_Evasion                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Evasion.png")
UseStrategic_SelectionPanel_Attack                       is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Attack.png")
UseStrategic_SelectionPanel_Defense                      is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/Defense.png")

UseStrategic_PrepareBattle_AddPawn                       is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/AddPawnToBattle.png")


UseStrategic_SelectionPanel_Wrench                       is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SelectionPanel/icone_molette.png")
UseStrategic_SelectionPanel_Skull                        is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/Kills_normal.png")

UseStrategic_ProductionMenu_Star                         is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/StarProduction.png")
UseStrategic_SpawnPoint                                  is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SpawnPoint.png")
UseStrategic_SpawnPointMain_Pole                         is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SpawnPointMain_Pole.png")
UseStrategic_SpawnPointMain_HorizontalBorder             is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SpawnPointMain_HorizontalBorder.png")
UseStrategic_SpawnPointMain_Fill                         is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/SpawnPointMain_Fill.png")

UseStrategic_Lock_Company                                is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseStrategic/Icons/lock.png")

