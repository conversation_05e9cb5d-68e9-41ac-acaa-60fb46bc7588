private StrategicBattlePlanningButtons is StrategicSecondaryCubeActionPanel
(
    ComponentFrame = TUIFramePropertyRTTI
    (

        MagnifiableOffset = [0.0, 200.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )
    ContentComponents = MAP
    [
        ([0, 0],
            AttackPreparation
        ),

    ]
)

AttackPreparation is PanelRoundedCorner
(
    ElementName = "AttackPreparation"
    HidePointerEvents = true

    BackgroundBlockColorToken = 'SM_RifleGreen_75'
    BorderLineColorToken = 'SM_Grullo'
    BorderThicknessToken = '2'
    Radius = 10

    ComponentFrame = TUIFramePropertyRTTI ( MagnifiableWidthHeight = [600,0])
    FitStyle = ~/ContainerFitStyle/FitToContentVertically
    Components =
    [
       BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            FirstMargin = TRTTILength (Magnifiable = 15.0)
            InterItemMargin = TRTTILength( Magnifiable = 5.0 )
            LastMargin = TRTTILength( Magnifiable = 15.0 )
            Axis = ~/ListAxis/Vertical

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.5, 0.0]
                            AlignementToAnchor = [0.5, 0.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_VerticalCenter
                            InterLine = 0.3
                        )
                        TextStyle = "Default"

                        HorizontalFitStyle = ~/FitStyle/FitToContent
                        VerticalFitStyle = ~/FitStyle/FitToContent

                        TypefaceToken = "Eurostyle_Heavy"
                        BigLineAction = ~/BigLineAction/MultiLine

                        TextToken = 'CUR_ASSAUL'
                        TextDico = ~/LocalisationConstantes/dico_interface_ingame

                        TextColor = "BlancEquipe"
                        TextSize = "24"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 0.0]
                            AlignementToFather = [0.5, 0.0]
                            AlignementToAnchor = [0.5, 0.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_VerticalCenter
                            InterLine = 0.3
                        )

                        TextStyle = "Default"
                        TextPadding =  TRTTILength4( Magnifiable = [20.0, 2.0, 20.0, 2.0] )
                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        VerticalFitStyle = ~/FitStyle/FitToContent

                        TypefaceToken = "Eurostyle_Italic"
                        BigLineAction = ~/BigLineAction/MultiLine

                        TextToken = 'CUR_Atth'
                        TextDico = ~/LocalisationConstantes/dico_interface_ingame

                        TextColor = "BlancEquipe"
                        TextSize = "14"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKListDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.5, 0.0]
                            AlignementToAnchor = [0.5, 0.0]
                        )

                        BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                        Axis = ~/ListAxis/Horizontal
                        InterItemMargin = TRTTILength (Magnifiable = 10.0)

                        Elements =
                        [
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = BoutonStartBattle
                                (
                                    MagnifiableWidthHeight =  [160.0, 40.0]
                                    ElementName = 'ValidateButton'
                                    TextToken = "BtnValPlan"
                                    HintBodyToken = "BtnValPlaH"
                                )
                            ),
                            BUCKListElementDescriptor
                            (
                                ComponentDescriptor = BoutonStartBattle
                                (
                                    MagnifiableWidthHeight =  [160.0, 40.0]
                                    ElementName = 'CancelButton'
                                    TextToken = "BTL_SHOCAN"
                                    HintBodyToken = "BtnCanPreH"
                                    Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ESCAPE ) )
                                )
                            ),
                         ]
                    )
                ),
            ]
        ),
    ]
)
StrategicBattlePlanningResources is TStrategicBattlePlanningResources
(
    MainComponent = ~/StrategicBattlePlanningButtons
)

