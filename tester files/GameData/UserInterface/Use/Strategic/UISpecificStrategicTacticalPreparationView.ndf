
private CombatGroupPhaseWidth is 30.0

//------------------------------------------------------------------------------

private DroppableBackground is BUCKContainerDescriptor
(
    ElementName = "DroppableBackground"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    HasBorder = true
    BorderThicknessToken = "2"
    BorderLineColorToken = "SD2_BleuVariable"
)

//------------------------------------------------------------------------------

template AvatarInDropDownDescriptor
[
    MagnifiableOffset : float2 = [0.0, 0.0]
] is BUCKSpecificAvatarDescriptor
(
    ElementName = "AvatarDropDown"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset = <MagnifiableOffset>
        MagnifiableWidthHeight = [24.0, 24.0]
        AlignementToAnchor = [0.0, 0.5]
        AlignementToFather = [0.0, 0.5]
    )
)

//------------------------------------------------------------------------------

TeamButton is BUCKButtonDescriptor
(
    ElementName = "TeamButton"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [150.0, 24.0]
    )

    IsTogglable = true
    CannotDeselect = true
    LeftClickSound = ~/SoundEvent_SteelmanTacticPrepChangeTeam

    Components =
    [
        PanelRoundedCorner
        (
            BackgroundBlockColorToken = "BattlePreparation/TeamButton"
            BorderLineColorToken = "SM_Grullo"
        ),
        BUCKListDescriptor
        (
            ElementName = "AvatarAndList"

            ComponentFrame = TUIFramePropertyRTTI()

            Axis = ~/ListAxis/Horizontal
            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = AvatarInDropDownDescriptor()
                ),
                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "TeamName"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 0.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_Center
                        )

                        VerticalFitStyle = ~/FitStyle/FitToContent

                        TextSize = "SD2_Moyen"
                        TextColor = "loginBlanc"

                        TextStyle = "Default"
                        TypefaceToken = "UIMainFont"

                        TextPadding = TRTTILength4(Magnifiable=[5.0, 0.0, 0.0, 0.0])

                        TextDico = ~/LocalisationConstantes/dico_interface_ingame
                    )
                ),
            ]
        )
    ]
)

//------------------------------------------------------------------------------

TeamButtonGrid is BUCKGridDescriptor
(
    ElementName = "TeamButtonGrid"

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    FirstElementMargin = TRTTILength2(Magnifiable = [10.0, 0.0])
    InterElementMargin = TRTTILength2(Magnifiable = [10.0, 10.0])
    LastElementMargin = TRTTILength2(Magnifiable = [5.0, 0.0])

    MaxElementsPerDimension = [2, 0]

    // Remplit via cpp avec ~/TeamButton
)

//------------------------------------------------------------------------------

private PawnContainer is BUCKListDescriptor
(
    ElementName = "PawnList"

    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Vertical
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    FirstMargin = TRTTILength(Magnifiable = 5.0)
    InterItemMargin = TRTTILength(Magnifiable = 15.0)

    ForegroundComponents =
    [
        ~/DroppableBackground
    ]

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_Center
                )

                VerticalFitStyle = ~/FitStyle/FitToContent

                TextSize = "SD2_Moyen"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle"

                TextToken = "Tac_IA"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                TextPadding = TRTTILength4(Magnifiable=[10.0, 0.0, 0.0, 0.0])
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/TeamButtonGrid
        ),
        BUCKListElementDescriptor
        (
            MinSize = TRTTILength( Magnifiable = 250.0 )
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ElementName = "PawnContainer"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )
                HasBackground = true
                BackgroundBlockColorToken = "SM_Grullo"
                FitStyle = ~/ContainerFitStyle/FitToContent
            )
        ),
    ]
    BackgroundComponents =
    [
        PanelRoundedCorner
        (
            BackgroundBlockColorToken = 'SM_RifleGreen_75'
            BorderLineColorToken = 'SM_Noir'
        )
    ]
)

//------------------------------------------------------------------------------

PawnMainList is BUCKListDescriptor
(
    ElementName = "PawnMainList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Horizontal

    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    InterItemMargin = TRTTILength(Magnifiable = 10.0)

    Elements =
    [
        // Remplit avec des PawnContainerDescriptor par le cpp
    ]
)

//------------------------------------------------------------------------------

SmartGroupDisplayerContainer is BUCKContainerDescriptor
(
    ElementName = "SmartGroupDisplayerContainerForTacticalPreparation"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 220.0]
    )
    HasBackground = true
    BackgroundBlockColorToken = 'SM_Ebony'
    FitStyle = ~/ContainerFitStyle/FitToLargestBetweenChildAndParentVertically
)

//------------------------------------------------------------------------------

TacticalPreparationCenterContainer is BUCKListDescriptor
(
    ElementName = "CenterContainerList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            MinSize = TRTTILength( Magnifiable = 305.0 )
            ComponentDescriptor = ~/SmartGroupDisplayerContainer
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/Title_bataillonDetails
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKSpecificScrollingContainerDescriptor
            (
                ElementName = "PawnsScrollingContainer"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 381.0]
                )

                ExternalScrollbar = true
                HasVerticalScrollbar = true

                VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [10.0, 0.0]
                    MagnifiableOffset = [2.0, 0.0]
                    AlignementToAnchor = [1.0, 0.0]
                    AlignementToFather = [1.0, 0.0]
                )

                Components =
                [
                    ~/PawnMainList
                ]
            )
        ),
        // Filler
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI()
            )
        ),
    ]
)

//------------------------------------------------------------------------------
TitleList is BUCKListDescriptor
(
    ElementName = "TitleList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    FirstMargin = TRTTILength( Magnifiable = 30.0)
    InterItemMargin = TRTTILength( Magnifiable = 5.0)
    LastMargin = TRTTILength( Magnifiable = 10.0)

    HasBackground = true
    BackgroundBlockColorToken = 'SM_Noir_80'

    Elements =
    [
        // titre
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                VerticalFitStyle = ~/FitStyle/FitToContent

                TextSize = "42"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle_Heavy"

                TextToken = "sm_ttitre"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )

                VerticalFitStyle = ~/FitStyle/FitToContent

                TextSize = "SD2_Moyen"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle"

                TextToken = "sm_tpara"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        )
    ]
)

//------------------------------------------------------------------------------
Title_companieDetails is BUCKTextDescriptor
(
    ElementName = "Title_companieDetails"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 32.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
    )

    HasBackground = true
    BackgroundBlockColorToken = 'SM_Noir_80'

    TextSize = "SD2_Moyen"
    TextColor = "SD2_Blanc184"

    TextStyle = "Default"
    TypefaceToken = "Eurostyle"

    TextToken = "sm_tcom"
    TextDico = ~/LocalisationConstantes/dico_interface_ingame
)

//------------------------------------------------------------------------------
Title_bataillonDetails is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 32.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    HasBackground = true
    BackgroundBlockColorToken = 'SM_Noir_80'

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "Title_bataillonDetails"

            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [500.0, 0.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            HorizontalFitStyle = ~/FitStyle/UserDefined

            TextSize = "SD2_Moyen"
            TextColor = "SD2_Blanc184"

            TextStyle = "Default"
            TypefaceToken = "Eurostyle"

            TextToken = "SC_TP_Tit"
            TextDico = ~/LocalisationConstantes/dico_interface_ingame

            Components =
            [
                BUCKTextDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        AlignementToFather = [1.0, 0.5]
                        AlignementToAnchor = [0.0, 0.5]
                    )

                    ParagraphStyle = TParagraphStyle
                    (
                        Alignment = UIText_Left
                        VerticalAlignment = UIText_VerticalCenter
                    )
                    HorizontalFitStyle = ~/FitStyle/FitToContent
                    VerticalFitStyle = ~/FitStyle/FitToContent

                    TextSize = "SD2_Petit"
                    TextColor = "SD2_Blanc184"

                    TextStyle = "Default"
                    TypefaceToken = "Eurostyle"

                    TextToken = "SC_TP_Sub"
                    TextDico = ~/LocalisationConstantes/dico_interface_ingame
                    Hint = BUCKSpecificStrategicHintableArea
                    (
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                        HintBodyToken = 'sm_tdrag'
                    )
                )
            ]
        )
    ]
)

//------------------------------------------------------------------------------

template StrategicTacticalPreparationInfosLine
[
    ElementName :string = "",
    TitleToken : string,
    ValueToken : string,
] is BUCKListDescriptor
(
    ElementName = <ElementName>

    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode= ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    FirstMargin = TRTTILength(Magnifiable = 15.0)
    LastMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = <ElementName> + "Title"

                ComponentFrame = TUIFramePropertyRTTI()

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                )

                BigLineAction = ~/BigLineAction/MultiLine
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TextSize = "14"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle"

                TextToken = <TitleToken>
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
        BUCKListElementDescriptor
         (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI()
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = <ElementName> + "Value"

                ComponentFrame = TUIFramePropertyRTTI()

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Right
                    VerticalAlignment = UIText_VerticalCenter
                )

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TextSize = "19"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle_Heavy"

                TextToken = <ValueToken>
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
    ]
)

//------------------------------------------------------------------------------

template TacticalPreparationCategoryLine
[
    TextToken : string,
    HintExtendedToken : string = '',
    HintTitleToken : string = '',
    HintBodyToken : string = '',
] is BUCKTextDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    HasBorder = true
    BorderThicknessToken = "1"
    BorderLineColorToken = "SD2_LigneBleuGris"
    BordersToDraw =  ~/TBorderSide/Bottom

    ParagraphStyle = ~/paragraphStyleTextLeftAlign

    VerticalFitStyle = ~/FitStyle/FitToContent

    TextSize = "18"
    TextColor = "SD2_Blanc184"

    TextStyle = "Default"
    TypefaceToken = "Eurostyle_Medium"

    TextToken = <TextToken>
    TextDico = ~/LocalisationConstantes/dico_interface_ingame

    TextPadding = TRTTILength4(Magnifiable = [5.0, 20.0, 0.0, 7.0])
    Hint = BUCKSpecificStrategicHintableArea
    (
        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
        HintTitleToken = <HintTitleToken>
        HintBodyToken = <HintBodyToken>
        HintExtendedToken = <HintExtendedToken>
    )
)

//------------------------------------------------------------------------------

TacticalPreparationIncomeAndMoralInfos is BUCKListDescriptor
(
    ElementName = "IncomeAndMoralInfos"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [500, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    InterItemMargin = TRTTILength(Magnifiable = 4.0)
    LastMargin = TRTTILength(Magnifiable = 5.0)

    BorderThicknessToken = "1"
    BorderLineColorToken = "SD2_LigneBleuGris"

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = TacticalPreparationCategoryLine
            (
                TextToken = "LR_cmd"
                HintTitleToken = "LR_cmd"
                HintBodyToken = "sm_pre7"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = StrategicTacticalPreparationInfosLine
            (
                ElementName = "Deployment"
                TitleToken = "PrC_in12"
                ValueToken = "depl_pts"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = StrategicTacticalPreparationInfosLine
            (
                ElementName = "Income"
                TitleToken = "PrC_in22"
                ValueToken = "income"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = TacticalPreparationCategoryLine
            (
                TextToken = "moral"
                HintTitleToken = "moral"
                HintBodyToken = "sm_tcas"
                HintExtendedToken = "sm_tcase"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = StrategicTacticalPreparationInfosLine
            (
                ElementName = "Moral"
                TitleToken = "morTitl"
                ValueToken = "morValu"
            )
        ),
    ]
)

//------------------------------------------------------------------------------
SliderWidth is 250.0

SliderTitles is BUCKListDescriptor
(
    ElementName = "SliderTitles"

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
    InterItemMargin = TRTTILength(Magnifiable = 2.0)
    LastMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "LeftPlayerSliderTitle"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [100.0, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Right
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextSize = "17"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle_Heavy"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
        // Filler
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.5
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI()

                Axis = ~/ListAxis/Vertical
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                InterItemMargin = TRTTILength( Magnifiable = 4.0 )

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor  = BUCKTextDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                                MagnifiableWidthHeight = [0.0, 20.0]
                            )

                            ParagraphStyle = TParagraphStyle
                            (
                                Alignment = UIText_Center
                                VerticalAlignment = UIText_VerticalCenter
                            )

                            BigLineAction = ~/BigLineAction/MultiLine

                            TextSize = "14"
                            TextColor = "SD2_Blanc184"

                            TextStyle = "Default"
                            TypefaceToken = "Eurostyle"

                            TextToken = 'PrC_in1'
                            TextDico = ~/LocalisationConstantes/dico_interface_ingame
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = TitleAndSlider
                        (
                            ElementName = "DeploymentConfig"
                            ValueToken = "depl_pts"
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor  = BUCKTextDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                                MagnifiableWidthHeight = [0.0, 20.0]
                            )

                            ParagraphStyle = TParagraphStyle
                            (
                                Alignment = UIText_Center
                                VerticalAlignment = UIText_VerticalCenter
                            )

                            BigLineAction = ~/BigLineAction/MultiLine

                            TextSize = "14"
                            TextColor = "SD2_Blanc184"

                            TextStyle = "Default"
                            TypefaceToken = "Eurostyle"

                            TextToken = 'PrC_in2'
                            TextDico = ~/LocalisationConstantes/dico_interface_ingame
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = TitleAndSlider
                        (
                            ElementName = "IncomeConfig"
                            ValueToken = "income"
                        )
                    ),
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.25

            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "RightPlayerSliderTitle"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [100.0, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                )

                HorizontalFitStyle = ~/FitStyle/MaxBetweenUserDefinedAndContent

                TextSize = "17"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle_Heavy"

                TextToken = "DSI_IA"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
    ]
)

//------------------------------------------------------------------------------

template TitleAndSlider
[
    ElementName : string,
    ValueToken : string,
] is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 30.0]
    )

    Axis = ~/ListAxis/Horizontal

    FirstMargin = TRTTILength(Magnifiable = 5.0)
    InterItemMargin = TRTTILength(Magnifiable = 2.0)
    LastMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = <ElementName> + "LeftValue"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [30.0, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Right
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextSize = "16"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle"

                TextToken = <ValueToken>
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [~/SliderWidth, 30.0]
                )

                Components =
                [
                    BUCKSpecificSliderDescriptor
                    (
                        ElementName = <ElementName> + "EditorSlider"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [0.0, 30.0]
                            AlignementToAnchor = [0.0, 0.5]
                            AlignementToFather = [0.0, 0.5]
                        )
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = <ElementName> + "RightValue"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [30.0, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                )

                TextSize = "16"
                TextColor = "SD2_Blanc184"

                TextStyle = "Default"
                TypefaceToken = "Eurostyle"

                TextToken = <ValueToken>
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
    ]
)

//------------------------------------------------------------------------------

TeamCommandPointsConfiguration is BUCKListDescriptor
(
    ElementName = "TeamCommandPointsConfiguration"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [500.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    InterItemMargin = TRTTILength(Magnifiable = 10.0)

    BorderThicknessToken = "1"
    BorderLineColorToken = "SD2_LigneBleuGris"

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = TacticalPreparationCategoryLine
            (
                TextToken = "LR_cmdAI"
                HintTitleToken = "LR_cmdAI"
                HintBodyToken = "sm_tdiste"
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/SliderTitles
        ),
    ]
)

//------------------------------------------------------------------------------

TacticalPreparationMainContainer is BUCKListDescriptor
(
    ElementName = "MainContainer"

    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    InterItemMargin = TRTTILength(Magnifiable = 100.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.5
            ComponentDescriptor = ~/TacticalPreparationIncomeAndMoralInfos
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.5
            ComponentDescriptor = ~/TeamCommandPointsConfiguration
        ),
    ]
)

//------------------------------------------------------------------------------

BUCKSpecificStrategicTacticalPreparationMainComponentDescriptor is BUCKListDescriptor
(
    ElementName = "BUCKSpecificStrategicTacticalPreparationMainComponent"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    HasBackground = true
    BackgroundBlockColorToken = "SM_Ebony"

    ForegroundComponents =
    [
        BUCKContainerDescriptor
        (
            UniqueName = "UnitPanelInfoContainerForTacticalPreparation"
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather = [1.0, 0.5]
                AlignementToAnchor = [1.0, 0.5]
                MagnifiableOffset = [-4,0]
            )
            FitStyle = ~/ContainerFitStyle/FitToContent
        )
    ]

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/TitleList
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 180.0]
                )

                HasBackground = true
                BackgroundBlockColorToken = 'SM_RifleGreen'

                HasBorder = true
                BorderThicknessToken = '1'
                BorderLineColorToken = "SM_Noir"
                BordersToDraw = ~/TBorderSide/Bottom

                Components =
                [
                    ~/TacticalPreparationMainContainer
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = Title_companieDetails
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = ~/TacticalPreparationCenterContainer
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 16.0]
                )
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
                Axis = ~/ListAxis/Horizontal

                FirstMargin = TRTTILength( Magnifiable = 5.0)
                InterItemMargin = TRTTILength( Magnifiable = 5.0)
                LastMargin = TRTTILength( Magnifiable = 5.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = ConfirmButton
                        (
                            TextToken = 'BtnBattle'
                            ElementName = 'StartButton'
                            Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ENTER ) )
                            ButtonMagnifiableWidthHeight = [175.0, 35.0]
                            ButtonAlignementToAnchor = [0.0, 0.0]
                            ButtonAlignementToFather = [0.0, 0.0]
                            ButtonMagnifiableOffset = [0.0, -10.0]
                            TextTypefaceToken = "Liberator"
                            TextColorToken = "tampon_vert"
                            BorderLineColorToken = "tampon_vert"
                            HasBackground = false
                            BackgroundBlockColorToken = "loginBoutonBackground_cyan"
                            TextSizeToken = "32"
                            BorderThicknessToken = "3"
                            BigLineAction = ~/BigLineAction/ResizeFont
                            TextPadding = TRTTILength4( Magnifiable = [5.0, 0.0, 5.0, 0.0] )
                            TextDico = ~/LocalisationConstantes/dico_interface_ingame
                            LeftClickSound = ~/SoundEvent_SteelmanTacticPrepStart
                        )
                    ),
                ]
            )
        ),
    ]
)

//------------------------------------------------------------------------------

TacticalPreparationTopContainer is BUCKSensibleAreaDescriptor
(
    ElementName = "TacticalPreparationTopContainer"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    HidePointerEvents = true

    Components =
    [
        ~/BUCKSpecificStrategicTacticalPreparationMainComponentDescriptor,
    ]
)

//------------------------------------------------------------------------------

UISpecificStrategicTacticalPreparationViewDescriptor is TUISpecificStrategicTacticalPreparationViewDescriptor
(
    MainComponentDescriptor = ~/TacticalPreparationTopContainer
    MainComponentContainerUniqueName = "BUCKMainComponentScreenWideContainer" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    PawnSelectionDescriptor = UISpecificSelectionPanelPawnViewDescriptorTemplate
    (
        SmartGroupsDisplayerAlignment = [0.5, 0.0]
        ContainsCubeAction = false
        HasSpotlight = false
        HQBackgroundBlockColorToken = "SM_onyx"

        EmptyBattlegroupDicoToken = ~/LocalisationConstantes/dico_interface_ingame
        EmptyBattlegroupNameToken = "HUD_PRDDCK"
        EmptyBattlegroupTextureName = ""
    )

    PawnContainerDescriptor = ~/PawnContainer
    UnitInfoPanelViewDescriptor = UISpecificUnitInfoPanelViewDescriptor(MainComponentContainerUniqueName = "UnitPanelInfoContainerForTacticalPreparation")

    ConfigurationMinMaxPercentage = [0.2, 0.8]

    TeamButtonDescriptor = ~/TeamButton
)
