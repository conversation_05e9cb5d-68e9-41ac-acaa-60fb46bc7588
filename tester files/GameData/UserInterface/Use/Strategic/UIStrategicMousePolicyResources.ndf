
private MouseCurseur_AssaultPossible is TMouseCursorAnimation
(
    HotSpotX           = 16
    HotSpotY           = 16
    AnimationCycleTime = 1
    TextureList = TResourceDescriptorTextureAnimation
    (
        FileNameList =
        [
            "GameData:/Assets/2D/Interface/Common/Cursors/OrderCursorGreen.png",
        ]
    )
)

private MouseCurseur_AssaultImpossible is TMouseCursorAnimation
(
    HotSpotX           = 16
    HotSpotY           = 16
    AnimationCycleTime = 1
    TextureList = TResourceDescriptorTextureAnimation
    (
        FileNameList =
        [
            "GameData:/Assets/2D/Interface/Common/Cursors/OrderCursorRed.png",
        ]
    )
)

private MouseCurseur_MultiselectionStrategic is TMouseCursorAnimation
(
    HotSpotX           = 0
    HotSpotY           = 0
    AnimationCycleTime = 1
    TextureList = TResourceDescriptorTextureAnimation
    (
        FileNameList =
        [
            "GameData:/Assets/2D/Interface/Common/Cursors/CursorDefault.png",
        ]
    )
)

private MouseWidget_AssaultPossible is Template_HardwareMouseWidgetResource
(
    HardwareMouseCursorAnimation    = ~/MouseCurseur_AssaultPossible
)

private MouseWidget_AssaultImpossible is Template_HardwareMouseWidgetResource
(
    HardwareMouseCursorAnimation    = ~/MouseCurseur_AssaultImpossible
)

private MouseWidget_MultiSelectionStrategic is Template_HardwareMouseWidgetResource
(
    HardwareMouseCursorAnimation    = ~/MouseCurseur_MultiselectionStrategic
)

StrategicTerrainTypeToMouseCursorAnimation is MAP
[
    (ETerrainType/None,                 ~/MouseCursorAnimationDefault),
    (ETerrainType/Default,              ~/MouseCursorAnimationDefault),
    (ETerrainType/StrategicForest,      ~/MouseCurseur_strategic_foret),
    (ETerrainType/StrategicPlain,       ~/MouseCurseur_strategic_plaine),
    (ETerrainType/StrategicSemiUrban,   ~/MouseCurseur_strategic_semiurbain),
    (ETerrainType/StrategicUrban,       ~/MouseCurseur_strategic_urbain),
]

MouseWidget_StrategicSelectNeutralLand is Template_HardwareMouseWidgetResource_WithTerrainFeedback
(
    StillTimeBeforeHintAppearance = 2

    TerrainTypeToHintContent = MAP
    [
        (
            ~/ETerrainType/None, //
            THintContentHolder
            (
                TitleToken = ""
                BodyToken = ""
                ExtendedToken = ""
            )
        ),



        (
            ~/ETerrainType/StrategicUrban, // semi urbain pour l'instant
            THintContentHolder
            (
                // TitleToken = ""
                BodyToken = "smTer_u"
                ExtendedToken = ""
            )
        ),
        (
            ~/ETerrainType/StrategicForest,
            THintContentHolder
            (
                BodyToken = "smTer_f"
                ExtendedToken = ""
            )
        ),

        (
            ~/ETerrainType/StrategicSemiUrban,
            THintContentHolder
            (
                BodyToken = "smTer_s"
                ExtendedToken = ""
            )
        ),
        (
            ~/ETerrainType/StrategicPlain,
            THintContentHolder
            (
                BodyToken = "smTer_p"
                ExtendedToken = ""
            )
        ),

    ]

    TerrainTypeToMouseCursorAnimation = ~/StrategicTerrainTypeToMouseCursorAnimation
)

private PANumberMouseCursor is BUCKTextDescriptor
(
    ElementName = 'ActionPoints'
    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 0.5]
        AlignementToAnchor = [0.5, 0.5]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
    )

    HasBackground = false

    HasBorder = false
    BorderLineColorToken = "PawnLabel/ActionPoints"
    BorderThicknessToken = "PawnLabel/ActionPoints"

    TextStyle = "ActionPoint"
    // TextPadding = TRTTILength4(Pixel = [1.0, 1.0, 1.0, 1.0] Magnifiable = [2.0, 2.0, 2.0, 2.0])

    HorizontalFitStyle = ~/FitStyle/FitToContent
    VerticalFitStyle = ~/FitStyle/FitToContent

    TypefaceToken = "UIMainFont"
    BigLineAction = ~/BigLineAction/CutByDots

    TextDico = ~/LocalisationConstantes/dico_interface_ingame

    TextColor = "PawnLabel/ActionPoints"
    TextSize = "18"
)


PAComponentMouseCursor is BUCKGaugeDescriptor
(
    ElementName = 'ActionPointsGauge'

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 0]
        AlignementToAnchor = [0.5, 0]
        MagnifiableWidthHeight = [20.0, 20.0] // Width de l'etiquette
    )

    HasBackground = true
    HasBorder = true
    BorderLineColorToken = "PawnLabel/ActionPoints"
    BorderThicknessToken = "PawnLabel/ActionPoints"
    BackgroundBlockColorToken = "GrisLabelStrat"

    DurationForGaugeFullChange = 0.20
    GaugeMax = 8

    GaugeComponentNames = ['ActionPointGaugeComponent']

    Components =
    [
        BUCKGaugeValueDescriptor
        (
            ElementName = 'ActionPointGaugeComponent'
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight    = [0.0, 1.0]
                AlignementToFather     = [0.0, 0.5]
                AlignementToAnchor     = [0.0, 0.5]
            )

            HasBackground = true
            BackgroundBlockColorToken = "LightGray80"

            HasBorder = false
            BorderLineColorToken = "PawnLabel/ActionPoints"
            BorderThicknessToken = "PawnLabel/ActionPoints"
        ),
        PANumberMouseCursor,
    ]
)

//--------------------------------------------------------

template Template_HardwareMouseWidgetResource_WithBUCK
[
    BUCKDescriptor : TBUCKContainerDescriptor = nil,
    PointerFollowingProperties : TPointerFollowingPropertiesRTTI = nil,
    TerrainTypeToMouseCursorAnimation,
]
is TUIMouseWidgetResource
(
    ModuleResourceList =
    MAP
    [
        (

            "CursorSetter",
            TUIMouseWidgetTerrainCursorSetterModuleResource
            (
                TerrainTypeToMouseCursorAnimation = <TerrainTypeToMouseCursorAnimation>
            )
        ),

        (
            "BUCKModule",
            TUIMouseWidgetBUCKModuleResource
            (
                BUCKDescriptor = <BUCKDescriptor>
                PointerFollowingProperties = <PointerFollowingProperties>
                UILayer = $/UserInterface/UILayer_2DInterface_InGame
            )
        ),
    ]
    UILayer = $/UserInterface/UILayer_2DInterface_InGame
)

MouseWidget_StrategicOrderNeutralLand is Template_HardwareMouseWidgetResource_WithBUCK
(
    TerrainTypeToMouseCursorAnimation = ~/StrategicTerrainTypeToMouseCursorAnimation

    BUCKDescriptor = BUCKContainerDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI
        (
            MagnifiableWidthHeight = [100, 75]
        )

        Components =
        [
            BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    AlignementToAnchor = [0.5, 1.0]
                    AlignementToFather = [0.0, 1.0]
                )

                Axis = ~/ListAxis/Vertical

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = ~/PAComponentMouseCursor
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = 'Kilometers'

                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            ParagraphStyle = TParagraphStyle
                            (
                                Alignment = UIText_Center
                                VerticalAlignment = UIText_VerticalCenter
                            )

                            TextStyle = "Default"
                            TextPadding = TRTTILength4(Magnifiable = [1.0, 3.0, 1.0, 1.0])

                            VerticalFitStyle = ~/FitStyle/FitToContent

                            TypefaceToken = "Eurostyle"
                            BigLineAction = ~/BigLineAction/CutByDots

                            TextDico = ~/LocalisationConstantes/dico_interface_ingame

                            TextColor = "SD2_Noir"
                            TextSize = "14"
                        )
                    ),
                ]
            ),
        ]
    )

    PointerFollowingProperties = TPointerFollowingPropertiesRTTI
    (
        MagnifiableOffset = [0.0, 0.0]
        HorizontalPlacement = HorizontalPlacement/Right
        VerticalPlacement = VerticalPlacement/Down
        OutOfBorderHorizontalAction = OutOfBorderAction/StickToBorder
        OutOfBorderVerticalAction = OutOfBorderAction/StickToBorder
    )
)

//--------------------------------------------------------
// Strategic Orders
//--------------------------------------------------------
StrategicMouseWidgetSelector_Default is TUIStrategicMouseWidgetSelectorDefault
(
    MouseWidgetList = MAP
    [
        (Cursor_Default          , ~/DefaultMouseWidget),
        (Cursor_None             , ~/MouseWidget_Blank),
        (Cursor_OrderAlly        , ~/MouseWidget_StrategicOrderNeutralLand),
        (Cursor_OrderPlayer      , ~/MouseWidget_MultiSelectionStrategic),
        (Cursor_OrderEnemy       , ~/MouseWidget_AssaultPossible),
        (Cursor_OrderInvalid     , ~/DefaultMouseWidget), // assaut impossible
        (Cursor_OrderNeutralLand , ~/MouseWidget_StrategicOrderNeutralLand),
        (Cursor_SelectNeutralLand, ~/MouseWidget_StrategicSelectNeutralLand),
    ]

    TextColorMap = MAP
    [
        (TextStyle_Bonus        , "WidgetSelector/Bonus"),
        (TextStyle_WithinRange  , "WidgetSelector/WithinRange"),
        (TextStyle_OutOfRange   , "WidgetSelector/OutOfRange"),
        (TextStyle_Unseen       , "WidgetSelector/Unseen"),
        (TextStyle_Blocked      , "WidgetSelector/Blocked"),
        (TextStyle_Invalid      , "WidgetSelector/Invalid"),
    ]

    KilometersToken = "RANGE_KM"
    AttackAvailableToken = "CUR_ASSAUL"
)

private StrategicMousePolicyManagerResources is TUIStrategicMousePolicyManagerResources
(
    KeyBoardPresetUpdater   = $/KeyboardPreset/KeyboardPresetUpdater
    Camera                  = $/M3D/Misc/CameraPrincipale
    World3D                 = $/M3D/Scene/DefaultWorld
    Scene3D                 = $/M3D/Scene/Scene_2D_Interface
    WorldFloorProxy = $/M3D/Scene/WorldFloorOutMapProxy

    DefaultMouseWidgetSelectorName = "StrategicMouseWidgetSelector_Default"
    MouseWidgetSelectorByName = MAP
    [
        (
            "StrategicMouseWidgetSelector_Default",
            ~/StrategicMouseWidgetSelector_Default
        ),
        (
            "MouseWidgetSelector_DefaultOrder",
            ~/MouseWidgetSelector_DefaultOrder //Nico : Ca me parait bizarre de pas utiliser celui du strategic ici mais je porte le comportement tel qu'il était
        ),
    ]
)
