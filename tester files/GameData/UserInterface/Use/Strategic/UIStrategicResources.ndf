
StrategicResource is TUISpecificStrategicResources
(
    Handler = $/UI/ResourcesHandler/StrategicResourcesHandler

    UILayer                     = $/UserInterface/UILayer_2DInterface_InGame
    UILayerLDHint               = $/UserInterface/UILayer_2DInterfaceLDHint
    CommonResources             = ~/UICommonResource

    MainStyleGuide              = ~/DefaultStyleGuide

    ViewDescriptors = MAP
    [
        ("UISpecificIngameChatViewDescriptor", UISpecificIngameChatViewDescriptor(PanelColorStyle = "StrategicPanel" ButtonColorStyle = "StrategicButton_highlightable")),
        ("UISpecificInGameFlarePanelViewDescriptor", ~/UIStrategicFlarePanelViewDescriptor),
        ("UISpecificLossesStateViewDescriptor", ~/UISpecificLossesStateViewDescriptor),
        ("UISpecificUnitInfoPanelViewDescriptor", UISpecificUnitInfoPanelViewDescriptor(MainComponentContainerUniqueName = "BUCKSpecificUnitInfoPanelMainComponentContainer")),
        ("UISpecificStrategicDateViewDescriptor", ~/UISpecificStrategicDateViewDescriptor),
        ("UISpecificSelectionPanelPawnViewDescriptor", UISpecificSelectionPanelPawnViewDescriptorTemplate(MainComponentContainerUniqueName = "BUCKSpecificSelectionPanelPawnMainComponentContainer" ContainsCubeAction = true HasSpotlight = true)),
        ("UISpecificStrategicSpawnPointViewDescriptor", ~/UISpecificStrategicSpawnPointViewDescriptor),
        ("ScorePanelDescriptor", ~/UISpecificStrategicScorePanelViewDescriptor),
        ("UISpecificStrategicCubeActionViewDescriptor", ~/UISpecificStrategicCubeActionViewDescriptor),
        ("UISpecificStrategicBattleOrderPanelViewDescriptor", ~/UISpecificStrategicBattleOrderPanelViewDescriptor),
        ("StrategicProductionMenu", ~/UIStrategicProductionMenuViewDescriptor),
        ("StrategicPawnLabel", ~/UISpecificStrategicPawnLabelViewDescriptor),
        ("StrategicTacticalPreparation", ~/UISpecificStrategicTacticalPreparationViewDescriptor),
        ("StrategicConnectedPlayersDescriptor", ~/UISpecificStrategicConnectedPlayersViewDescriptor),
        ("UICommonHUDPingDisplayViewDescriptor", ~/UICommonHUDPingDisplayViewDescriptor),
        ("UISpecificStrategicHUDGameSpeedViewDescriptor", ~/UISpecificStrategicHUDGameSpeedViewDescriptor),
        ("UISpecificStrategicObjectiveScoreViewDescriptor", ~/UISpecificStrategicObjectiveScoreViewDescriptor),
        ("ObjectiveViewDescriptor", ~/UISpecificStrategicObjectiveBriefViewDescriptor),
        ("LDVideoViewDescriptor", ~/UIStrategicLDVideoViewDescriptor),
        ("UIStrategicHUDHeaderViewDescriptor", ~/SpecificStrategicHUDHeaderViewDescriptor),
        ("UIStrategicChatButtonDescriptor", ~/UIStrategicChatButtonDescriptor),
        ("UIStrategicUnusedUnitButtonDescriptor", ~/UIStrategicUnusedUnitButtonDescriptor),
        ("UIWaitUntilNextTurnViewDescriptor", ~/UIWaitUntilNextTurnViewDescriptor),
    ]

    MainContainerResource       = ~/StrategicMainContainerResource

    CommonHUDResources          = ~/InGameHUDResource
    LabelResource               = ~/CommonLabelResource
    GabaritResources            = ~/StrategicGabaritResources
    ZoCResources                = ~/StrategicZoCResources
    WarningPanelResources       = ~/WarningPanelStrategicResources
    LabelsOnMapResources        = ~/StrategicLabelsOnMapResources
    ScriptedLabelResources      = ~/StrategicScriptedLabelResources
    FeedbackManagerDescriptor   = ~/StrategicFeedbackMessageManagerDescriptor
    UnitLabelResource           = ~/SpecificStrategicUnitLabelResources
    FlareLabelResources         = ~/CommonFlareLabelResources

    SelectionHandlerInGameResources = ~/SelectionResources
    GroupSelectionReminderResources = ~/GroupSelectionReminder
    PositionEvents              = ~/MainPositionEvents
    MousePolicyManagerResources = ~/StrategicMousePolicyManagerResources
    OptionManager               = $/GameOptionsInterface/GameOptionsInterfaceWrapper

    ShortcutUserInputLayer      = $/M3D/Input/UserInputLayerHandler/InputLayer_InGameShortcuts
    LDScriptUserInputLayer      =  $/M3D/Input/UserInputLayerHandler/InputLayer_LDScriptInterceptor
    DisplayAllOrdersMapping     = $/KeyboardOption/Mapping_DisplayAllOrders

    StrategicOrderDisplayResources = ~/StrategicOrderDisplayResources

    EndTurnResources            = ~/StrategicEndTurnResources
    AutoResolveResources      = ~/UISpecificStrategicAutoResolveViewDescriptor
    StartBattleResources        = ~/UISpecificStrategicStartBattleViewDescriptor
    BattlePlanningResources     = ~/StrategicBattlePlanningResources
    ConnectedPlayersResources   = ~/UISpecificStrategicConnectedPlayersViewDescriptor

    PlayerNameDisplayDescriptor = ~/UISpecificStrategicPlayerNameDisplayViewDescriptor
    StrategicBattleResources    = ~/StrategicBattleResources

    PlayerTurnCheckerResources = ~/StrategicPlayerTurnCheckerResources

    StrategicWorldFloorProxy = $/M3D/Scene/WorldFloorOutMapProxy
)

//------------------------------------------------------------------------------------

private StrategicBottomScreen is BUCKListDescriptor
(
    ElementName = 'StrategicBottomScreen'
    UniqueName = 'StrategicBottomScreen'

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 1.0]
    )

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    InterItemMargin = TRTTILength(Magnifiable =  0.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ElementName = 'PanneauInfoPion'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [0.0, 1.0]
                    AlignementToFather = [0.0, 1.0]
                )

                Axis = ~/ListAxis/Vertical
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
                InterItemMargin = TRTTILength( Magnifiable = 20.0 )

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "BUCKSpecificSelectionPanelPawnMainComponentContainer"
                            ComponentFrame = TUIFramePropertyRTTI()
                            FitStyle = ~/ContainerFitStyle/FitToContent

                            // envoie SelectionPanelPawnMainComponent
                        )
                    ),


                ]
            )
        ),
        // affichage groupé du panel info unité et préparation combat pour des raisons de placement
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                UniqueName = "BUCKSpecificUnitInfoPanelMainComponentContainer"
                ComponentFrame = TUIFramePropertyRTTI()
                FitStyle = ~/ContainerFitStyle/FitToContent
            )
        ),
    ]

)

//------------------------------------------------------------------------------------

private StrategicMainContainerResource is TUICommonMainContainerResource
(
    ContentRefSize = [ 2134 , 1440 ]
    SafeBoxMin     = [  107 ,   80 ]
    SafeBoxSize    = ~/FullScreenSafeBoxSize

    ForegroundComponents = BUCKContainerDescriptor
    (
        ElementName = "StrategicMainContainerForeground"

        ComponentFrame = TUIFramePropertyRTTI
        (
            RelativeWidthHeight = [1.0, 1.0]
        )

        Components =
        [
            BUCKListDescriptor
            (
                UniqueName = "StrategicInfoPlayerAndScore"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToFather = [1.0, 0.0]
                    AlignementToAnchor = [1.0, 0.0]
                    MagnifiableOffset = [0.0, 44.0]
                )

                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
                InterItemMargin = TRTTILength( Magnifiable = 2.0 )
                Axis = ~/ListAxis/Vertical
                HidePointerEvents = true
                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "StrategicGlobalObjectivesContainer"

                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                AlignementToAnchor = [1.0, 0.0]
                                AlignementToFather = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContent
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = FlarePanelChatContainer is BUCKContainerDescriptor
                        (
                            UniqueName = "UICommonFlarePanelViewMainContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                                AlignementToAnchor = [1.0, 0.0]
                                AlignementToFather = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    )
                ]
            ),

            BUCKContainerDescriptor
            (
                UniqueName = "BUCKSpecificConnectedPlayersPanel"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [1.0, 0.0]
                    AlignementToFather = [1.0, 0.0]
                    MagnifiableOffset = [-5.0, 125.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContent
            ),
            BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                Axis = ~/ListAxis/Vertical
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromFrameProperty
                FirstMargin = TRTTILength( Magnifiable = 44.0 )
                InterItemMargin = TRTTILength( Magnifiable = 0.0 )

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "SpecificStrategicHUDHeaderViewMainContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                                MagnifiableWidthHeight = [0,120]
                            )

                            //FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),

                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "StrategicProductionUnitContainerListScrollingContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )
                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                        ComponentFrame = TUIFramePropertyRTTI
                            (
                                MagnifiableWidthHeight = [10.0, 3.0]
                            )
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "StrategicProductionBattalionRackScrollingContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                MagnifiableOffset = [10.0, 0.0]
                            )

                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ExtendWeight = 1.0
                        ComponentDescriptor = BUCKContainerDescriptor(ComponentFrame = TUIFramePropertyRTTI())
                    ),
                ]
            ),

            //-------------------------------------------------------------------------------------

            BUCKContainerDescriptor
            (
                UniqueName = "BUCKMainComponentScreenWideContainer"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )
            ),

            BUCKContainerDescriptor
            (
                UniqueName = "BUCKSpecificAutoresolveUnitSelectorViewContainer"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToAnchor = [1.0, 0.0]
                    AlignementToFather = [1.0, 0.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContent
            ),
            BUCKContainerDescriptor
            (
                UniqueName = "PourPlacerPanneauPreparationCombat"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )
            ),

            //-------------------------------------------------------------------------------------

            ~/StrategicBottomScreen,
            ~/StrategicBoutonsMenus,

            //-------------------------------------------------------------------------------------

            BUCKContainerDescriptor
            (
                UniqueName = "SpecificAutoresolveViewDescriptor"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    AlignementToAnchor = [0.5, 0.5]
                    AlignementToFather = [0.5, 0.5]
                )
            ),
        ]
    )
)

//-------------------------------------------------------------------------------------

private StrategicOrderDisplayResources is TStrategicOrderDisplayResources
(
    Camera                      = $/M3D/Misc/CameraPrincipale
    OrderDisplayUserInputLayer  = $/M3D/Input/UserInputLayerHandler/InputLayer_OrderDisplay
    OrderDisplayDrawer          = ~/StrategicOrderDisplayDrawer

    PhaseLabelDescriptor = ~/StrategicLabelDescriptor
)

//-------------------------------------------------------------------------------------

private StrategicLabelDescriptor is BUCKTextDescriptor
(
    ElementName = 'PhaseText'

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
    )

    HasBorder = true
    BorderLineColorToken = "PawnLabel/ActionPoints"
    BorderThicknessToken = "PawnLabel/ActionPoints"

    TextStyle = "Default"
    TextPadding = TRTTILength4(Magnifiable = [5.0, 5.0, 5.0, 5.0])

    HorizontalFitStyle = ~/FitStyle/FitToContent
    VerticalFitStyle = ~/FitStyle/FitToContent

    TypefaceToken = "UIMainFont"
    BigLineAction = ~/BigLineAction/CutByDots

    TextDico = ~/LocalisationConstantes/dico_interface_ingame

    TextColor = "PawnLabel/ActionPoints"
    TextSize = "PawnLabel/ActionPoints"
)

                    //-------------------------------------------------------------------------------------

private StrategicOrderDisplayDrawer is TStrategicOrderDisplayDrawer
(
    OrderDisplayDrawInfo = TStrategicOrderDisplayDrawInfo
    (
        LineThickness = 10000
        ArrowLength = 5000
        ArrowWidth = 3000*2
        ArrowLineWidth = 1200*2
        ArrowFadeLength = 10000*2
    )
)

                    //-------------------------------------------------------------------------------------

private UISpecificStrategicCubeActionViewDescriptor is TUISpecificCubeActionViewDescriptor
(
    MainComponentDescriptor = ~/StrategicCubeAction
    MainComponentContainerUniqueName = "SpecificStrategicCubeActionViewMainContainer"

    ButtonTemplateDescriptorMap       = MAP
    [
        (~/StrategicCubeActionFunctionType/Orders,
            CubeActionButton
            (
                BackgroundBlockColorToken = 'SM_Noir'
                BorderLineColorToken = 'SM_Noir'
                RoundedVertexes = [false, true, true, false]
                BigLineAction = ~/BigLineAction/ResizeFont

                HintableAreaComponent = BUCKSpecificStrategicHintableArea
                (
                    ElementName = "CubeActionHintableArea"
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                )
            )
        ),
        (~/StrategicCubeActionFunctionType/TogglableOrders, CubeActionToggleButton(BigLineAction = ~/BigLineAction/ResizeFont)),
    ]
    EmptyButtonTemplateDescriptor  = ~/CubeActionEmptyButton

    SpotlightDescriptor = BUCKSpotlightDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI
        (
            RelativeWidthHeight = [1.0, 1.0]
        )
    )
)
