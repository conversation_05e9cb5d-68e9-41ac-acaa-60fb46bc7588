private LeaderboardAddFriendButton is HUBActionButton
(
    TextToken = 'LDB_FRNDS'
    ElementName = 'LeaderBoardAddFriendButton'
    TextSize = '16'
    TextColor = 'Blanc_multi'
    ButtonAlignementToAnchor = [0.5,0.5]
    ButtonAlignementToFather = [0.5,0.5]
    ButtonMagnifiableWidthHeight = [100,15]
    LeftClickSound = SoundEvent_LeaderboardAddFriend
)

//-------------------------------------------------------------------------------------

private LeaderboardViewProfileButton is HUBActionButton
(
    TextToken = 'LDB_PFL'
    ElementName = 'LeaderBoardViewProfileButton'
    TextSize = '16'
    ButtonAlignementToAnchor = [0.5,0.5]
    ButtonAlignementToFather = [0.5,0.5]
    ButtonMagnifiableWidthHeight = [100,15]
    TextColor = 'Noir_multi2'
    LeftClickSound = SoundEvent_LeaderboardViewProfile
)

//-------------------------------------------------------------------------------------

// Par simplicité, la somme de ces weight doit rester à 1
private LeaderboardRankColumnWeight is 0.05
private LeaderboardPlayerNameColumnWeight is 0.07
private LeaderboardGradeColumnWeight is 0.05
private LeaderboardTrendColumnWeight is 0.12

//-------------------------------------------------------------------------------------

template LeaderboardTitleTextComponent
[
    TextToken : string,
    Alignment : int = UIText_Center,
    TextPadding : TRTTILength4 = TRTTILength4(),
]
is BUCKTextDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )
    ParagraphStyle = TParagraphStyle
    (
        Alignment = <Alignment>
        VerticalAlignment = ~/UIText_VerticalCenter
        BigWordAction = ~/BigWordAction/BigWordNewLine
    )
    TextStyle = 'Default'
    TypefaceToken = "Liberator"
    TextSize = '14'
    TextColor = 'Gris_leaderboard'
    TextToken = <TextToken>
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextPadding = <TextPadding>
)

//-------------------------------------------------------------------------------------

private LeaderboardMultiListTitle is
[
    // Rank
    BUCKListElementDescriptor
    (

        ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
        (
            ElementName = 'LdbRankMultiList'
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
                MagnifiableWidthHeight = [57,0]
            )

            SortingType = ~/MultiListSorting/String

            HidePointerEvents = true
            ShowCheckbox = false
            Components =
            [
                LeaderboardTitleTextComponent(TextToken = 'LDB_RANK'),
            ]
        )
    ),
    // Name
    BUCKListElementDescriptor
    (

        ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
        (
            ElementName = 'LdbNameMultiList'
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [175,0]
                RelativeWidthHeight = [0.0, 1.0]
            )

            SortingType = ~/MultiListSorting/String
            HidePointerEvents = true
            ShowCheckbox = false
            Components =
            [
                LeaderboardTitleTextComponent
                (
                    Alignment = UIText_Left
                    TextToken = 'LDB_NAME'
                    TextPadding = TRTTILength4( Magnifiable = [10.0, 0.0, 0.0, 0.0])
                ),
            ]
        )
    ),
    // Grade
    BUCKListElementDescriptor
    (

        ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
        (
            ElementName = 'LdbGradeMultiList'
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [55,0]
                RelativeWidthHeight = [0.0, 1.0]
            )

            SortingType = ~/MultiListSorting/String

            HidePointerEvents = true
            ShowCheckbox = false
            Components =
            [
                LeaderboardTitleTextComponent(TextToken = 'LDB_GRADE'),
            ]
        )
    ),

]

//-------------------------------------------------------------------------------------

private template LeaderboardMultiSimplifiedListContent
[
    ElementNamePrefix : string = ""
] is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 18.0]
    )
    Axis = ~/ListAxis/Horizontal

    FirstMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        // Rank
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = <ElementNamePrefix> + "LdbRankMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [36,0]
                )
                Components =
                [

                    BUCKTextDescriptor
                    (
                        ElementName = "LdbRank"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        BigLineAction = ~/BigLineAction/CutByDots

                        TextStyle = "Default"
                        TypefaceToken = "Courrier"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame

                        TextColor = 'noir_param'
                        TextSize = "12"
                    )
                ]
            )
        ),
        // Trend
        BUCKListElementDescriptor
        (

            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = <ElementNamePrefix> + "LdbTrendMultiList"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [16,16]
                )
                Components =
                [
                    BUCKTextureDescriptor
                    (
                        ElementName = "LdbTrend"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.9, 1.0]
                            AlignementToFather = [1.0, 0.0]
                            AlignementToAnchor = [1.0, 0.0]
                        )
                    )
                ]
            )
        ),
        // Name
        BUCKListElementDescriptor
        (

            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = <ElementNamePrefix> + "LdbNameMultiList"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [169,0]
                    RelativeWidthHeight = [0.0, 1.0]
                )

                Components =
                [
                    BUCKTextDescriptor
                    (
                        ElementName = "LdbName"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_VerticalCenter
                        )

                        BigLineAction = ~/BigLineAction/CutByDots

                        TextStyle = "Default"
                        TypefaceToken = "Courrier"

                        TextDico = ~/LocalisationConstantes/dico_interface_outgame

                        TextColor = "noir_param"
                        TextSize = "12"

                        TextPadding = TRTTILength4(Magnifiable = [10.0, 0.0, 0.0, 0.0])

                        TextFormatScript = nil
                    )
                ]
            )
        ),
        BUCKListElementSpacer(Magnifiable = 10.0),
        // Grade
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = <ElementNamePrefix> + "LdbGradeMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [45,0]
                    RelativeWidthHeight = [0.0, 1.0]
                )

                Components =
                [
                    BUCKSpecificRankDescriptor
                    (
                        ElementName = "LdbGrade"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.9, 1.0]
                            AlignementToFather = [1.0, 0.0]
                            AlignementToAnchor = [1.0, 0.0]
                        )
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

LeaderboardButtonRadioManager is TBUCKRadioButtonManager()

//-------------------------------------------------------------------------------------

private template LeaderboardButton
[
    ElementNamePrefix : string = "",
    BackgroundBlockColorToken : string = "DivisionBrief/ButtonOverlay"
] is BUCKButtonDescriptor
(
    ElementName = <ElementNamePrefix> + "LeaderboardButton"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )
    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    IsTogglable = true
    RadioButtonManager = ~/LeaderboardButtonRadioManager
    HasBackground = true
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    HidePointerEvents = false

    Components =
    [
        LeaderboardMultiSimplifiedListContent(ElementNamePrefix = <ElementNamePrefix>)
    ]
)

//-------------------------------------------------------------------------------------
private LeaderboardContentContainer is BUCKContainerDescriptor
(

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 400.0]

    )
    HasBackground = false
    BackgroundBlockColorToken = 'Orange'
    ClipContent = true
    Components =
    [
        BUCKRackDescriptor
        (
            ElementName = "LeaderboardRack"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )
            InterItemMargin = TRTTILength(Magnifiable = 2.0)
            BladeDescriptor = LeaderboardButton()
        ),
        BUCKSensibleAreaDescriptor
        (
            ElementName = "LeaderboardContentContainer"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------
private LeaderboardTitle is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 25.0]
    )
    HasBackground = false
    BackgroundBlockColorToken = "ListeExcel/Cartouche"
    Axis = ~/ListAxis/Horizontal
    Elements =  ~/LeaderboardMultiListTitle
)

//-------------------------------------------------------------------------------------

private  LeaderboardMultiList is BUCKMultiListDescriptor
(
    ElementName = "LeaderboardMultiList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight =  [1.0, 0.0]
        MagnifiableWidthHeight = [0,-82]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    ColumnNames =
    [
        "LdbRankMultiList",
        "LdbTrendMultiList",
        "LdbNameMultiList",
        "LdbGradeMultiList",
    ]

    SortableColumnNames = []

    RackName = "LeaderboardRack"

    TitleDescriptor = ~/LeaderboardTitle
    ContentDescriptor = ~/LeaderboardContentContainer
)

//-------------------------------------------------------------------------------------

private LeaderboardListHeader is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 20.0]
        AlignementToFather = [1.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Horizontal

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "PlayerNumber"

                ComponentFrame = TUIFramePropertyRTTI
                (
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = ~/UIText_Center
                    VerticalAlignment = ~/UIText_VerticalCenter
                    BigWordAction = ~/BigWordAction/BigWordNewLine
                )

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TextStyle = 'Default'
                TypefaceToken = "UIMainFont"
                TextSize = '14'
                TextColor = 'Orange'
                TextToken = 'LDB_NBPLYR'
                TextDico = ~/LocalisationConstantes/dico_interface_outgame

                TextPadding = TRTTILength4(Magnifiable = [5.0, 5.0, 5.0, 5.0])
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

private BUCKSpecificLeaderboardMainComponentDescriptor is WindowFrame
(
    ContentExtendWeight = 1.0
    ContentRelativeWidthHeight = [1.0, 0.73]

    HasTitle = false
    HasBackground =  false

    ContentComponents =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical

            InterItemMargin = TRTTILength(Magnifiable = 2.0)
            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/Titre_Leaderboard
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/LeaderboardMultiList
                ),
            ]
        ),
        // affichage du morceau de chaise par dessus
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [ 21.0, 410.0 ]
                MagnifiableOffset = [191,0]
                AlignementToAnchor = [0,0]
                AlignementToFather = [0,0]
            )
            TextureFrame   = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])
            TextureToken   = "outgame_cable_pour_multi"
        ),
    ]
    ButtonList =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [300,80]
                )
                HasBackground = false
                BackgroundBlockColorToken = 'Noir'
                Components =
                [
                    BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [130,45]
                            MagnifiableOffset = [24,28]
                        )

                        Components =
                        [
                            ~/LeaderboardAddFriendButton,
                        ]
                    ),
                    BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [130,45]
                            MagnifiableOffset = [50,82]
                        )
                        HasBackground = false
                        BackgroundBlockColorToken = 'Orange'
                        Components =
                        [
                            ~/LeaderboardViewProfileButton,
                        ]
                    ),

                ]
            )
        ),
    ]
)
//-------------------------------------------------------------------------------------
private Titre_Leaderboard is BUCKTextDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0,17]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Left
        VerticalAlignment = UIText_VerticalCenter
    )

    BigLineAction = ~/BigLineAction/CutByDots

    HorizontalFitStyle = ~/FitStyle/UserDefined
    VerticalFitStyle = ~/FitStyle/UserDefined

    TextStyle = "Default"
    TypefaceToken = "Liberator"
    TextPadding = TRTTILength4(Magnifiable = [10.0, 0.0, 0.0, 0.0])
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextToken = 'T_LDB'
    TextColor = 'Blanc_leaderboard'
    TextSize = "14"
)
//-------------------------------------------------------------------------------------

template UISpecificLeaderboardViewDescriptor
[
    ComponentContainerUniqueName : string = "",
] is TUISpecificLeaderboardViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificLeaderboardMainComponentDescriptor
    MainComponentContainerUniqueName = <ComponentContainerUniqueName> // Permet d'indiquer l'endroit ou le composant doit s'insérer

    TrendEqualTexture = "OutgameTexture_TrendEqual"
    TrendMinusTexture = "OutgameTexture_TrendMinus"
    TrendPlusTexture = "OutgameTexture_TrendPlus"
    TrendUnrankedTexture = "OutgameTexture_TrendUnranked"

    TrendEqualColorToken = "Leaderboard/TrendEqual"
    TrendMinusColorToken = "Leaderboard/TrendMinus"
    TrendPlusColorToken = "Leaderboard/TrendPlus"
    TrendUnrankedColorToken = "Leaderboard/Unranked"

    PlayerNameColorToken = "noir_param"
    FriendNameColorToken = "FriendPlayer"
    SelfNameColorToken = "SelfPlayer"

    ShortcutsLayer = $/M3D/Input/UserInputLayerHandler/InputLayer_2DInterfaceAboveBlur_OutGame

    MaxElementsInRack = 17
)
