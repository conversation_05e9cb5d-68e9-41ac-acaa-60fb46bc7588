template MainBackButtonContainer
[
    ButtonDefaultToken  : string,
    BackMapping : TEugBMutablePBaseClass,
    ButtonLeftClickSound : TSoundDescriptor = SoundEvent_BackToMainMenu,
    BackgroundBlockColorToken : string = "HUBOutGameNavBar/ButtonOverlay",

    AlignementToAnchor : float2  = [0.0, 1.0],
    AlignementToFather : float2 = [0.0, 1.0],
    MagnifiableOffset : float2 = [0.0, 0.0],
]
 is BUCKButtonDescriptor
(
    ElementName = "GlobalBackButton"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset = <MagnifiableOffset>
        MagnifiableWidthHeight = [200.0, 60.0]
        AlignementToAnchor = <AlignementToAnchor>
        AlignementToFather = <AlignementToFather>
    )
    HasBackground = true
    BackgroundBlockColorToken = <BackgroundBlockColorToken>
    LeftClickSound = <ButtonLeftClickSound>

    Mapping = <BackMapping>
    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "GlobalBackButtonMainText"
            ParagraphStyle = TParagraphStyle
            (
                VerticalAlignment = ~/UIText_VerticalCenter
                Alignment = ~/UIText_Center
            )
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            TextColor = "ListeExcel/Cartouche"
            TextSize  = "28"
            TextStyle = "Default"
            TypefaceToken = "UIMainFont"
            TextDico = ~/LocalisationConstantes/dico_interface_outgame
            TextToken = <ButtonDefaultToken>
        ),
    ]
)

