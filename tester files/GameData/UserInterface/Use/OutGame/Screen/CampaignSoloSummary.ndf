//-------------------------------------------------------------------------------------

private template MissionDescriptionText
[
    ElementName : string = "",
    TextToken : string = "",
    TextColor : string = 'Noir_noHL',
    TextSize = "13",
    TextStyle : string = "Default",
    TypefaceToken = "Courrier",
    Alignment = UIText_Left,
    MagnifiableWidthHeight : float2 = [0.0, 0.0],
    RelativeWidthHeight : float2 = [1.0, 0.0],
    MagnifiableOffset : float2 = [0.0, 0.0],
    CouleurDuTrait : string = "Transparent",
    TextDico : string = ~/LocalisationConstantes/dico_maps,
    TextPadding : TRTTILength4 = TRTTILength4(Magnifiable = [20.0, 0.0, 20.0, 0.0])
]
is BUCKTextDescriptor
(
    ElementName = <ElementName>

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = <RelativeWidthHeight>
        MagnifiableWidthHeight = <MagnifiableWidthHeight>
        MagnifiableOffset = <MagnifiableOffset>
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment         = <Alignment>
        VerticalAlignment = UIText_Up
        Balanced          = true
        InterLine         = 0.4
    )

    TextStyle = <TextStyle>
    TypefaceToken = <TypefaceToken>

    TextPadding = <TextPadding>

    TextDico = <TextDico>
    TextToken = <TextToken>

    TextSize = <TextSize>
    TextColor = <TextColor>

    BigLineAction = ~/BigLineAction/MultiLine

    HorizontalFitStyle = ~/FitStyle/UserDefined
    VerticalFitStyle = ~/FitStyle/FitToContent
    //surlignage
    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
                MagnifiableWidthHeight = [0.0, 1.0]
                PixelWidthHeight = [0.0, 1.0]
                AlignementToFather = [0.5, 1.0]
                AlignementToAnchor = [0.5, 1.0]
            )

            HasBackground = true
            BackgroundBlockColorToken = <CouleurDuTrait>
        )
    ]
)

//-------------------------------------------------------------------------------------

private template MissionDescriptionTexture
[
    ElementName : string,
    MagnifiableWidthHeight : float2,
    RelativeWidthHeight : float2 = [0.0, 0.0],
    AlignementToFather : float2 = [0.0, 0.0],
    AlignementToAnchor : float2 = [0.0, 0.0]
]
is BUCKTextureDescriptor
(
    ElementName = <ElementName>

    ComponentFrame = TUIFramePropertyRTTI (
        RelativeWidthHeight = <RelativeWidthHeight>
        MagnifiableWidthHeight = <MagnifiableWidthHeight>
        AlignementToFather = <AlignementToFather>
        AlignementToAnchor = <AlignementToAnchor>
    )
    TextureFrame   = TUIFramePropertyRTTI (
        RelativeWidthHeight = [1.0, 1.0]
    )
    TileTextureInComponent = false
    ClipTextureToComponent = true
)

//-------------------------------------------------------------------------------------

private template OneHint
[
    TextureElementName : string,
    TextElementName : string,
    TextureSize : float2 = [32.0, 32.0],
    TextureContainerSize : float2 = [40.0, 40.0],
    ContainerWidth : float = 524.0
]
is BUCKListElementDescriptor
(
    ComponentDescriptor = BUCKListDescriptor
    (
        Axis = ~/ListAxis/Horizontal
        BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

        Elements =
        [
            BUCKListElementDescriptor
            (
                ComponentDescriptor = BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = <TextureContainerSize>
                    )

                    Components =
                    [
                        MissionDescriptionTexture
                        (
                            ElementName = <TextureElementName>
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                            MagnifiableWidthHeight = <TextureSize>
                        )
                    ]
                )
            ),
            BUCKListElementDescriptor
            (
                ExtendWeight = 1.0

                ComponentDescriptor = MissionDescriptionText
                (
                    ElementName = <TextElementName>
                    MagnifiableWidthHeight = [<ContainerWidth> - <TextureContainerSize>[0], 0.0]
                    RelativeWidthHeight = [0.0, 0.0]
                )
            )
        ]
    )
)

//-------------------------------------------------------------------------------------

private ListSeparator is BUCKListElementDescriptor
(
    ComponentDescriptor = MissionDescriptionText
    (
        TextToken = "TUTO_SEP"
        Alignment = UIText_Center
    )
)

//-------------------------------------------------------------------------------------//
// campagne stratégique
// tuto aussi
ResumeCampagneType3 is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [-40.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    FirstMargin = TRTTILength( Magnifiable = 50.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = MissionDescriptionText
            (
                ElementName = "TextePrincipal"
            )
        )
    ]
)

//-------------------------------------------------------------------------------------

template BlocTextureDivisionHistorique
[
    ElementName : string
]
is BUCKListElementDescriptor
(
    ComponentDescriptor = BUCKContainerDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI( MagnifiableWidthHeight = [60.0, 72.0] )
        Components =
        [
            BUCKTextureDescriptor
            (
                ElementName = <ElementName>
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [55.0, 55.0]
                    AlignementToFather = [0.5,0.5]
                    AlignementToAnchor = [0.5,0.5]
                )

                ClipTextureToComponent = true

                Components =
                [
                    BUCKSpecificHintableArea
                    (
                        ElementName = <ElementName> + "/Hint"
                        DicoToken = ~/LocalisationConstantes/dico_units
                    )
                ]
            )
        ]

        HasBorder = false
        BorderLineColorToken = "Resume/CadreDivisions"
        BorderThicknessToken = "1"
        BordersToDraw = ~/TBorderSide/Left
    )
)

//-------------------------------------------------------------------------------------
// affichage dans l'outgame
ResumeCampagneTypeHistorique is BUCKSpecificScrollingContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    ExternalScrollbar = true
    HasVerticalScrollbar = true

    VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [8.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
        MagnifiableOffset =  [-8.0, 0.0]
    )

    Components =
     [
        BUCKListDescriptor
        (
            ElementName = "ObjectivesList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
                MagnifiableOffset = [15.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical
            FirstMargin = TRTTILength( Magnifiable = 10)
            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = MissionDescriptionText
                    (
                        ElementName = "TitreObjectifs"
                        TextPadding = TRTTILength4(Magnifiable = [20.0, 0.0, 50.0, 10.0])
                        Alignment = UIText_Center
                        TypefaceToken = "Eurostyle"
                        TextColor = "Noir"
                        TextSize = "20"
                        TextStyle = "UpperCase"
                    )
                ),
                // BUCKListElementSpacer(Magnifiable = 20.0),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = "MapTexture_AG"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.4, 0.0]
                            AlignementToFather = [0.4, 0.0]
                        )

                        TextureFrame = TUIFramePropertyRTTI()

                        ResizeMode = ~/TextureResizeMode/FitToContent
                        AdaptContainerToTextureSize = true
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = MissionDescriptionText
                    (
                        ElementName = "TexteObjectifs"
                        TextPadding = TRTTILength4(Magnifiable = [20.0, 10.0, 50.0, 0.0])
                        Alignment = UIText_Left
                        TypefaceToken = "Eurostyle"
                        TextSize = '14'

                    )
                ),

            ]
        )
    ]
)
//-------------------------------------------------------------------------------------
template displayArmyGeneral_missionDisplay_template
[
    BackgroundComponents = [],
    ForegroundComponents = [],
]
is BUCKSpecificScrollingContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 800.0]
    )

    ExternalScrollbar = true
    HasVerticalScrollbar = true

    VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [8.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
        MagnifiableOffset =  [-8.0, 0.0]
    )

    HasBackground = true
    BackgroundBlockColorToken = 'Orange'

    Components =
     [
        BUCKListDescriptor
        (
            ElementName = "ObjectivesList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
                MagnifiableOffset = [15.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical
            FirstMargin = TRTTILength( Magnifiable = 10)
            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = MissionDescriptionText
                    (
                        ElementName = "TitreObjectifs"
                        TextPadding = TRTTILength4(Magnifiable = [20,30,50,0])
                        Alignment = UIText_Center
                        TypefaceToken = "Eurostyle"
                        TextColor = "Noir"
                        TextSize = "20"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = MissionDescriptionText
                    (
                        ElementName = "TexteObjectifs"
                        TextPadding = TRTTILength4(Magnifiable = [20,30,50,0])
                        Alignment = UIText_Left
                        TypefaceToken = "Eurostyle"
                        TextSize = '14'
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = "MapTexture_AG"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [200, 200]
                            AlignementToAnchor = [0.5, 0.0]
                            AlignementToFather = [0.5, 0.0]
                            // MagnifiableOffset = [-1,10]

                        )
                    )
                )
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------
//-------------------------------------------------------------------------------------
//-------------------------------------------------------------------------------------
MissionElementsList is BUCKListDescriptor
(
    ElementName = "MissionElementsList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset = [15.0, 0.0]
        RelativeWidthHeight = [0.95, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    InterItemMargin = TRTTILength( Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight = [0.0, 120.0]
                )

                Components =
                [
                    BUCKTextureDescriptor
                    (
                        ElementName = "CornerLogo"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [80.0, 80.0]
                            AlignementToAnchor = [0.0, 0.0]
                            AlignementToFather = [0.0, 0.0]
                            MagnifiableOffset = [20.0, 0.0]
                        )
                    ),
                    MissionDescriptionText
                    (
                        ElementName = "TexteForcesEnPresence"
                        Alignment = UIText_Center
                        TypefaceToken = "Eurostyle"
                        TextSize = '14'
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = MissionDescriptionText
            (
                ElementName = "TextePresentationScenario"
                Alignment = UIText_Left
                TypefaceToken = "Eurostyle"
                TextSize = '13'
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 1.0]
                )

                Components =
                [
                    MissionDescriptionText
                    (
                        ElementName = "TexteHautGauche"
                        TextSize = "SD2_Moyen"
                        Alignment = UIText_Left
                        TextColor = 'SD2_Transparent'
                    ),
                    MissionDescriptionText
                    (
                        ElementName = "TexteHautDroite"
                        TextSize = "SD2_Moyen"
                        TextColor = 'SD2_Transparent'
                        Alignment = UIText_Right
                    ),
                    BUCKListDescriptor
                    (
                        Axis = ~/ListAxis/Horizontal
                        BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                        HasBorder = false
                        BorderLineColorToken = "Resume/CadreDivisions"
                        BorderThicknessToken = "1"
                        BordersToDraw = ~/TBorderSide/All

                        Elements =
                        [
                            BlocTextureDivisionHistorique( ElementName = "TextureDivisionGauche1" ),
                            BlocTextureDivisionHistorique( ElementName = "TextureDivisionGauche2" ),
                            BlocTextureDivisionHistorique( ElementName = "TextureDivisionGauche3" ),

                            BUCKListElementDescriptor
                            (
                                ExtendWeight = 1.0
                                ComponentDescriptor = BUCKTextDescriptor
                                (
                                    ComponentFrame = TUIFramePropertyRTTI
                                    (
                                        RelativeWidthHeight = [1.0, 1.0]
                                    )

                                    ParagraphStyle = TParagraphStyle
                                    (
                                        Alignment         = UIText_Center
                                        VerticalAlignment = UIText_VerticalCenter
                                        Balanced          = true
                                    )

                                    TextStyle = "Default"
                                    TypefaceToken = "Liberator"

                                    TextDico = ~/LocalisationConstantes/dico_maps
                                    TextToken = "HistoForcs"

                                    TextSize = "SD2_Moyen"
                                    TextColor = "SD2_Transparent"

                                    BigLineAction = ~/BigLineAction/MultiLine

                                    HorizontalFitStyle = ~/FitStyle/UserDefined
                                    VerticalFitStyle = ~/FitStyle/UserDefined

                                    HasBorder = false
                                    BorderLineColorToken = "Resume/CadreDivisions"
                                    BorderThicknessToken = "1"
                                    BordersToDraw = ~/TBorderSide/Left
                                )
                            ),

                            BlocTextureDivisionHistorique( ElementName = "TextureDivisionDroite3" ),
                            BlocTextureDivisionHistorique( ElementName = "TextureDivisionDroite2" ),
                            BlocTextureDivisionHistorique( ElementName = "TextureDivisionDroite1" ),
                        ]
                    ),
                    MissionDescriptionText
                    (
                        ElementName = "TexteTitreObjectifs"
                        TextToken = "HB_SCEOB"
                        TextSize = "19"
                        TextColor = 'SD2_Transparent'
                        Alignment = UIText_Left
                    ),
                    MissionDescriptionText
                    (
                        ElementName = "TexteObjectifs"
                        TextSize = "SD2_Moyen"
                        TextColor = 'SD2_Transparent'
                        Alignment = UIText_Left
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

SummaryCampaignShowroom is BUCKSpecificScrollingContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 600.0]
        MagnifiableOffset = [0.0, 50.0]
    )

    ExternalScrollbar = true
    HasVerticalScrollbar = true

    VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [8.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
        MagnifiableOffset =  [-8.0, 0.0]
    )

    Components =
    [
        ~/MissionElementsList
    ]
)

//-------------------------------------------------------------------------------------

MissionMap is BUCKTextureDescriptor
(
    ElementName = "MapTexture"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [537-15, 598-15]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
        MagnifiableOffset = [-1,10]

    )

    Components =
    [
    // BUCKTextureDescriptor
    // (
    //         ComponentFrame = TUIFramePropertyRTTI
    //         (
    //                 MagnifiableWidthHeight = [36.0, 70.0]
    //                 AlignementToAnchor = [0.0, 0.0]
    //                 AlignementToFather = [0.0, 0.0]
    //                 MagnifiableOffset = [70+20,-8]
    //         )
    //         TextureToken = 'paperclip'
    // ),
    MissionDescriptionText
    (
        ElementName = "MapSubText"
        Alignment = UIText_Center
    )
    ]
)

//-------------------------------------------------------------------------------------

ScenarioMapDescription is BUCKSpecificScrollingContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    ExternalScrollbar = true
    HasVerticalScrollbar = true

    VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [8.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
        MagnifiableOffset =  [-8.0, 0.0]
    )
    Components =
    [
        BUCKContainerDescriptor
        (
            ElementName = "CampaignMoreDescriptionContainer"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            Components =
            [
                ~/MissionMap
            ]
        )
    ]
)