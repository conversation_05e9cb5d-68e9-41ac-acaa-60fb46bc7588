
BUCKSpecificOutGameChatMessageMainComponentDescriptor is BUCKTextDescriptor
(
    ElementName = "Text"

    ComponentFrame = TUIFramePropertyRTTI()

    HasBackground = true

    TextSize  = "14"
    TypefaceToken = "UISecondFont"
    TextStyle = "Default"

    BigLineAction = ~/BigLineAction/MultiLine

    HorizontalFitStyle = ~/FitStyle/FitToParent
    VerticalFitStyle = ~/FitStyle/FitToContent

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Left
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
        BigWordAction = ~/BigWordAction/BigWordNewLine
    )

    TextFormatScript = nil

    Components =
    [
        BUCKSensibleAreaDescriptor
        (
            ElementName = "NameSensibleArea"
            ComponentFrame = TUIFramePropertyRTTI
            (
            )
        ),
        BUCKSensibleAreaDescriptor
        (
            ElementName = "MessageSensibleArea"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
        ),
    ]
)

UISpecificOutGameChatMessageViewDescriptor is TUISpecificOutGameChatMessageViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificOutGameChatMessageMainComponentDescriptor
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    BackgroundTextColorByMessageType =
    [
        "Gris", //System
        "Transparent", //Public
        "Transparent", //Private
    ]

    TextColorByMessageType =
    [
        "Cyan", //System
        "Blanc", //Public
        "VertPur", //Private
    ]
)
