
private AutoMatchButtonManager is TBUCKRadioButtonManager()

template AllianceButton
[
    ElementName : string,
    TextToken : string,
    //ColorToken : string,
    ComponentFrame : TUIFramePropertyRTTI,
] is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKButtonDescriptor
        (
    ElementName = <ElementName> + "Button"
    ComponentFrame = <ComponentFrame>

    FitStyle = ~/ContainerFitStyle/FitToContent

    IsTogglable = true
    CannotDeselect = true
    RadioButtonManager = ~/AutoMatchButtonManager

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = <ElementName> + "Text"
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToAnchor = [1.0, 0.5]
                AlignementToFather = [1.0, 0.5]
                MagnifiableWidthHeight = [175, 35]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            BigLineAction = ~/BigLineAction/CutByDots

            HorizontalFitStyle = ~/FitStyle/UserDefined
            VerticalFitStyle = ~/FitStyle/UserDefined


            TextToken = <TextToken>
            TextDico = ~/LocalisationConstantes/dico_interface_outgame



             //ButtonMagnifiableWidthHeight = [175, 35]
            TypefaceToken = "UISecondFont"
            TextStyle = 'TextStyleEcranMoniteur'
            TextColor = 'loginBlanc'
            BorderLineColorToken = 'VertLogin'
            HasBackground = true
            BackgroundBlockColorToken = 'loginBoutonBackground_vert'
            TextSize = '24'
            BorderThicknessToken = '3'
            HasBorder = true
        )
    ]
        )
    ]
)

BUCKSpecificOutGameAutoMatchMainComponentDescriptor is SpecificModalWindow
(
    ContentMagnifiableWidthHeight = [0.0, 350.0]
    TitleToken = "T_RANKED"

    ButtonList =
    [
        BUCKListElementDescriptor(ComponentDescriptor = SpecificModalConfirmButton),
        BUCKListElementDescriptor(ComponentDescriptor = SpecificModalCancelButton)
    ]

    ElementsList =
    [
        //-------------------------------------------------------------------------------------
        // ligne,
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalDottedLine
        ),
        //-------------------------------------------------------------------------------------
        // rank
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ElementName = "RankComponent"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 30.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                Axis = ~/ListAxis/Horizontal

                FirstMargin = TRTTILength(Magnifiable = 20.0)
                InterItemMargin = TRTTILength( Magnifiable = 20.0)
                LastMargin = TRTTILength( Magnifiable = 20.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                AlignementToAnchor = [0.0, 0.5]
                                AlignementToFather = [0.0, 0.5]
                            )

                            ParagraphStyle = TParagraphStyle
                            (
                                Alignment = UIText_Center
                                VerticalAlignment = UIText_VerticalCenter
                            )

                            BigLineAction = ~/BigLineAction/CutByDots

                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent

                            TextStyle = "TextStyleEcranMoniteur"
                            TypefaceToken = "IBM"

                            TextToken = "Auto_Rck"
                            TextDico = ~/LocalisationConstantes/dico_interface_outgame

                            TextColor = "Blanc"
                            TextSize = "24"
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKSpecificRankDescriptor
                        (
                            ElementName = "RankTexture"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                MagnifiableWidthHeight = [100.0, 30.0]
                                AlignementToAnchor = [0.0, 0.5]
                                AlignementToFather = [0.0, 0.5]
                            )
                        )
                    ),
                ]
            )
        ),
        //-------------------------------------------------------------------------------------
        // ligne,
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalDottedLine
        ),
        //-------------------------------------------------------------------------------------
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ElementName = "AllianceComponent"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 50.0]
                    AlignementToAnchor = [0.5, 0.0]
                    AlignementToFather = [0.5, 0.0]
                )

                Axis = ~/ListAxis/Horizontal

                FirstMargin = TRTTILength(Magnifiable = 20.0)
                InterItemMargin = TRTTILength( Magnifiable = 20.0)
                LastMargin = TRTTILength( Magnifiable = 20.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ExtendWeight = 1.0
                        ComponentDescriptor = AllianceButton
                        (
                            ElementName = "Allies"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                AlignementToAnchor = [1.0, 0.5]
                                AlignementToFather = [1.0, 0.5]
                            )
                            TextToken = "NATO"
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ExtendWeight = 1.0
                        ComponentDescriptor = AllianceButton
                        (
                            ElementName = "Axis"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                AlignementToAnchor = [0.0, 0.5]
                                AlignementToFather = [0.0, 0.5]
                            )
                            TextToken = "PACT"
                        )
                    ),
                ]
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalDropdown
            (
                TextToken = "Auto_Dck"
                DropdownElementName = "DeckListDropdown"
                TextMagnifiableWidth = 350.0
                DropdownMagnifiableWidth = 450.0
            )
        ),

        //-------------------------------------------------------------------------------------
        BUCKListElementSpacer( Magnifiable = 20.0 ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalDottedLine
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalText(ElementName = "ModalContent")
        )
    ]
)

UISpecificOutGameAutoMatchViewDescriptor is TUISpecificOutGameAutoMatchViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificOutGameAutoMatchMainComponentDescriptor
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer
)
