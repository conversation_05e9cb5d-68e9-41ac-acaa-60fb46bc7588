DeckListButtonsDims is [ 118.0, 27.0 ]
DeckListButtonsDims2 is [ 106.0, 45.0 ]

template DeckListButton
[
    ElementName : string = '',
    TextToken : string = '',
    HintableAreaElementName : string = "",
    HintTitleToken : string = "",
    HintBodyToken : string = "",
    HintExtendedToken : string = "",
    HintDico : string = ~/LocalisationConstantes/dico_interface_outgame,
    BigLineAction : int = ~/BigLineAction/ResizeFont,
    LeftClickSound : TSoundDescriptor = nil,
] is ConfirmButton
(
    ElementName = <ElementName>
    TextToken = <TextToken>

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        ElementName = <HintableAreaElementName>
        HintTitleToken = <HintTitleToken>
        HintBodyToken = <HintBodyToken>
        HintExtendedToken = <HintExtendedToken>
        DicoToken = <HintDico>
    )

    BigLineAction = <BigLineAction>

    ButtonMagnifiableWidthHeight = ~/DeckListButtonsDims
    BackgroundBlockColorToken = 'Vert_bouton'
    ButtonAlignementToAnchor = [0.0, 0.5]
    ButtonAlignementToFather = [0.0, 0.5]
    BorderLineColorToken = 'blanc_contour'
    HasBackground = true
    BorderThicknessToken = '2'
    TextColorToken = 'blanc_contour'
    TextSizeToken = '18'
    TextTypefaceToken = "Liberator"
    TextPadding = TRTTILength4( Magnifiable = [2.0, 0.0, 2.0, 0.0] )

    LeftClickSound = <LeftClickSound>
)

//--------------------------------------------------------

private DeckListPanelRenameButton is DeckListButton
(
    ElementName = 'RenameButton'
    TextToken = 'AB_DCKRENA'

    HintTitleToken = "ODL_RENABT"
    HintBodyToken = "ODL_RENABB"
    HintExtendedToken = "ODL_RENABE"
    HintDico = ~/LocalisationConstantes/dico_interface_outgame

    LeftClickSound = SoundEvent_RenameDeck
)

//--------------------------------------------------------

private DeckListPanelRemoveButton is DeckListButton
(
    ElementName = 'RemoveButton'
    TextToken = 'AB_DCKREMO'

    HintableAreaElementName = 'HintableAreaRemoveButton'
    HintTitleToken = "ODL_RMVBT"
    HintBodyToken = "ODL_RMVBB"
    HintExtendedToken = "ODL_RMVBE"
    HintDico = ~/LocalisationConstantes/dico_interface_outgame

    LeftClickSound = SoundEvent_RemoveDeck
)

//--------------------------------------------------------

private DeckListPanelCopyButton is DeckListButton
(
    ElementName = 'CopyButton'
    TextToken = 'AB_DCKCOPY'

    HintTitleToken = "ODL_COPYBT"
    HintBodyToken = "ODL_COPYBB"
    HintExtendedToken = "ODL_COPYBE"
    HintDico = ~/LocalisationConstantes/dico_interface_outgame

    LeftClickSound = SoundEvent_CopyDeck
)

//-------------------------------------------------------------------------------------

private DeckListPanelExportButton is DeckListButton
(
    ElementName = 'ExportButton'
    TextToken = 'AB_DCKEXPO'

    HintTitleToken = "ODL_EXPOBT"
    HintBodyToken = "ODL_EXPOBB"
    HintExtendedToken = "ODL_EXPOBE"
    HintDico = ~/LocalisationConstantes/dico_interface_outgame

    LeftClickSound = SoundEvent_ExportDeck
)

//-------------------------------------------------------------------------------------

private DeckListPanelImportButton is ConfirmButton
(
    ElementName = "ImportButton"
    TextToken = "AB_DCKIMPO"
    ButtonMagnifiableWidthHeight = [175.0, 34.0]
    TextTypefaceToken = "Liberator"
    TextColorToken = "boutonFiltre"
    BorderLineColorToken = "boutonFiltre"
    HasBackground = false
    BackgroundBlockColorToken = "loginBoutonBackground_cyan"
    TextSizeToken = "26"
    BorderThicknessToken = "3"

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "ODL_IMPOBT"
        HintBodyToken = "ODL_IMPOBB"
        HintExtendedToken = "ODL_IMPOBE"
        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
    )

    LeftClickSound = SoundEvent_OpenImportDeckModal
)

//-------------------------------------------------------------------------------------

private DeckListPanelEditButton is ConfirmButton
(
    ElementName = "EditButton"
    TextToken = "AB_DCKEDIT"
    ButtonMagnifiableWidthHeight = [175.0, 34.0]
    TextTypefaceToken = "Liberator"
    TextColorToken = "boutonFiltre"
    BorderLineColorToken = "boutonFiltre"
    HasBackground = false
    BackgroundBlockColorToken = "loginBoutonBackground_cyan"
    TextSizeToken = "26"
    BorderThicknessToken = "3"

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "ODL_EDITBT"
        HintBodyToken = "ODL_EDITBB"
        HintExtendedToken = "ODL_EDITBE"
        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
    )
    LeftClickSound = SoundEvent_EditDeck
)

//-------------------------------------------------------------------------------------

private DeckListPanelViewButton is ConfirmButton
(
    ElementName = "ViewButton"
    TextToken = "AB_DCKVIEW"
    ButtonMagnifiableWidthHeight = [175.0, 34.0]
    TextTypefaceToken = "Liberator"
    TextColorToken = "boutonFiltre"
    BorderLineColorToken = "boutonFiltre"
    HasBackground = false
    BackgroundBlockColorToken = "loginBoutonBackground_cyan"
    TextSizeToken = "26"
    BorderThicknessToken = "3"

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "ODL_VIEWBT"
        HintBodyToken = "ODL_VIEWBB"
        HintExtendedToken = "ODL_VIEWBE"
        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
    )
    LeftClickSound = SoundEvent_ViewDeck
)

//-------------------------------------------------------------------------------------

// BattlegroupList
private DeckListPanelCreateButton is ConfirmButton
(
    ElementName = "CreateButton"
    TextToken = "NV_CREATE"
    Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ENTER ) )
    ButtonMagnifiableWidthHeight = [175.0, 34.0]
    TextTypefaceToken = "Liberator"
    TextColorToken = "blanc_contour"
    BorderLineColorToken = "boutonFiltre"
    HasBackground = true
    BackgroundBlockColorToken = "Vert_bouton"
    TextSizeToken = "26"
    BorderThicknessToken = "3"
    BigLineAction = ~/BigLineAction/ResizeFont
    TextPadding = TRTTILength4( Magnifiable = [5.0, 0.0, 5.0, 0.0] )

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "ODL_CREABT"
        HintBodyToken = "ODL_CREABB"
        HintExtendedToken = "ODL_CREABE"
        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
    )
    LeftClickSound = SoundEvent_OpenCreateDeckModal
)

//-------------------------------------------------------------------------------------

private DeckListPanelSelectButton is ConfirmButton
(
    ElementName = "SelectButton"
    TextToken = "NV_SELECT"

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken = "ODL_SELEBT"
        HintBodyToken = "ODL_SELEBB"
        HintExtendedToken = "ODL_SELEBE"
        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
    )
    BigLineAction = ~/BigLineAction/ResizeFont

    ButtonMagnifiableWidthHeight = [400.0, 34.0]
    BackgroundBlockColorToken = "VertConfirm"
    ButtonAlignementToAnchor = [0.5, 0.5]
    ButtonAlignementToFather = [0.5, 0.5]
    BorderLineColorToken = "blanc_contour"
    HasBackground = true
    BorderThicknessToken = "2"
    TextColorToken = "blanc_contour"
    TextSizeToken = "26"
    TextTypefaceToken = "Liberator"
    TextPadding = TRTTILength4(Magnifiable = [2.0, 0.0, 2.0, 0.0])

    LeftClickSound = SoundEvent_SelectDeck
)

//-------------------------------------------------------------------------------------

DeckListBackButton is BUCKButtonDescriptor
(
    ElementName = "BackButton"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [25.0, 25.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
        MagnifiableOffset = [-260.0, 20.0]
    )

    IsTogglable = true
    DefaultToggleValue = false
    RadioButtonManager =  nil
    CannotDeselect = false
    ForceEvents = false
    LeftClickSound = SoundEvent_CloseDeckList
    Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ESCAPE ) )

    Components =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [20.0, 20.0]
            )
            TextureToken = "showRoomTexture_minimize"
            TextureColorToken = "MarronPanel_noir_toggled2"
            Rotation = -90
        ),
    ]
)

//-------------------------------------------------------------------------------------

BattlegroupsBriefRadioManager is TBUCKRadioButtonManager()

BattlegroupBriefDescriptorForSelection is BUCKSpecificDivisionBriefMainComponentDescriptor
(
    AlignementToAnchor = [0.0, 0.0]
    AlignementToFather = [0.0, 0.0]
    IsTogglable = true
    RadioButtonManager = nil
    HasBattlegroup = true
)

//-------------------------------------------------------------------------------------

BattlegroupBriefsScrollingContainer is BUCKSpecificScrollingContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
    )

    HasVerticalScrollbar = true
    ScrollStepSize = [0.0, 20.0]

    VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [DeckListScrollbarWidth, 0.0]
    )

    Components =
    [
        BUCKRackDescriptor
        (
            ElementName = "BattlegroupsBriefRack"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [DeckListListWidth, 0.0]
                AlignementToAnchor = [1.0, 0.0]
                AlignementToFather = [1.0, 0.0]
            )

            InterItemMargin = TRTTILength(Magnifiable = 0.0)

            BladeDescriptor = ~/BattlegroupBriefDescriptorForSelection

            ForegroundComponents =
            [
                ~/EffetStylePochettePlastique,
            ]
        ),
    ]
)

//-------------------------------------------------------------------------------------

template TextListe
[
    TextToken : string,
]
is
BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight= [86.0, 20.0]
        MagnifiableOffset = [0.0, 50.0]
    )

    Components =
    [
        BUCKPerspectiveWarpOffscreenContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            DistortTextureDrawer = $/UserInterface/UIDistortTextureDrawer
            PointerEventsToAllow = ~/EAllowablePointerEventType/Move

            MagnifiableTopLeftOffset = [0.0, 0.0]
            MagnifiableTopRightOffset = [-27.0, -61.0]
            MagnifiableBottomLeftOffset = [15.0, -10.0]
            MagnifiableBottomRightOffset = [-12.0,-68.0]


            Components =
            [
                BUCKTextDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [85.0, 20.0]
                        AlignementToAnchor = [0.0, 0.0]
                        AlignementToFather = [0.0, 0.0]
                    )

                    HasBorder = true
                    BorderLineColorToken = 'gris_ligne_battlegroup'
                    BorderThicknessToken = '1'
                    BordersToDraw = ~/TBorderSide/Top


                    HorizontalFitStyle = ~/FitStyle/UserDefined
                    VerticalFitStyle = ~/FitStyle/UserDefined
                    ParagraphStyle = TParagraphStyle
                    (
                        VerticalAlignment = ~/UIText_Up
                        Alignment = UIText_Left
                    )

                    TextPadding = TRTTILength4(Magnifiable = [20.0, 5.0, 0.0, 0.0])
                    TextStyle = 'Default'
                    TypefaceToken = "Eurostyle"
                    TextSize = '12'
                    TextColor = 'gris_load'
                    TextToken = <TextToken>
                    TextDico = ~/LocalisationConstantes/dico_interface_outgame
                )
            ]
        )
    ]
)


//-------------------------------------------------------------------------------------

DeckListTotalWidth is 600.0
DeckListScrollbarWidth is 10.0
DeckListListWidth is 580.0

DeckListColonne_Alliance is 68.0
DeckListColonne_Flag is 60.0
DeckListColonne_Division is 55.0
DeckListColonne_Power is 50.0
DeckListColonne_Type is 60.0

BattlegroupsBriefMultiListTitle is BUCKListDescriptor
(
    ElementName = "bandeauMultiliste"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 50.0]
    )

    Axis = ~/ListAxis/Horizontal
    FirstMargin = TRTTILength (Magnifiable = DeckListTotalWidth - DeckListListWidth)

    Elements =
    [
        //embleme
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'DivisionEmblemMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [DeckListColonne_Division, 0.0]
                )
                ShowCheckbox = false
                CheckBoxBorderLineColorToken = 'gris_load'
                SortingType = ~/MultiListSorting/Integer
                HidePointerEvents = false
                Components =
                [
                    TextListe( TextToken = 'DLS_DIVIS' ),
                    BUCKSpecificHintableArea
                    (
                        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                        HintBodyToken = "div_nameB"
                    )
                ]
            )
        ),
        // Country Flag
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'DivisionFlagMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [DeckListColonne_Flag, 0.0]
                )

                SortingType = ~/MultiListSorting/Integer
                ShowCheckbox = false
                CheckBoxBorderLineColorToken = 'gris_load'
                HidePointerEvents = true
                Components =
                [
                    TextListe( TextToken = 'DB_FLAG' ),
                    BUCKSpecificHintableArea
                    (
                        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                        HintBodyToken = "div_counB"
                    )

                ]
            )
        ),
        // AllianceText
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'AllianceTextMultiList'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [DeckListColonne_Alliance, 0.0]
                )
                ShowCheckbox = false

                CheckBoxBorderLineColorToken = 'gris_load'
                SortingType = ~/MultiListSorting/Integer

                HidePointerEvents = true
                Components =
                [
                    TextListe( TextToken = 'DB_ALLIA' ),
                    BUCKSpecificHintableArea
                    (
                        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                        HintBodyToken = "div_alliB"
                        HintExtendedToken = "div_alliE"
                    )
                ]
            )
        ),
        // BattlegroupName
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'BattlegroupNameMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )
                ShowCheckbox = false
                CheckBoxBorderLineColorToken = 'gris_load'
                SortingType = ~/MultiListSorting/String
                HidePointerEvents = true
                Components =
                [
                    TextListe( TextToken = 'DB_NAME' ),
                    BUCKSpecificHintableArea
                    (
                        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                        HintBodyToken = "div_BGB"
                    )
                ]
            )
        ),

        // Power rating
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'DivisionPowerTextMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [DeckListColonne_Power, 0.0]
                )
                ShowCheckbox = false
                CheckBoxBorderLineColorToken = 'gris_load'
                SortingType = ~/MultiListSorting/String
                HidePointerEvents = true
                Components =
                [
                    TextListe( TextToken = 'DB_RATE' ),
                    BUCKSpecificHintableArea
                    (
                        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                        HintBodyToken = "div_rateB"
                        HintExtendedToken = "div_rateE"
                    )
                ]
            )
        ),
        // Unit Type
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListTitleButtonDescriptor
            (
                ElementName = 'DivisionUnitTypeMultiList'

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [DeckListColonne_Type, 0.0]
                )
                ShowCheckbox = false
                CheckBoxBorderLineColorToken = 'gris_load'
                SortingType = ~/MultiListSorting/Integer
                HidePointerEvents = true
                Components =
                [
                    TextListe( TextToken = 'DB_UTYPE' ),
                    BUCKSpecificHintableArea
                    (
                        DicoToken = ~/LocalisationConstantes/dico_interface_outgame
                        HintBodyToken = "div_typeB"
                        HintExtendedToken = "div_typeE"
                    )
                ]
            )
        )
    ]
)

//-------------------------------------------------------------------------------------

BattlegroupsBriefMultiList is BUCKMultiListDescriptor
(
    ElementName = "BattlegroupsBriefMultiList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    ColumnNames =
    [
        "IncomeTextMultiList",
    ]

    SortableColumnNames =
    [
        "AllianceTextMultiList",
        "BattlegroupNameMultiList",

        "DivisionFlagMultiList",
        "DivisionEmblemMultiList",
        "DivisionUnitTypeMultiList",
        "DivisionPowerTextMultiList"
    ]

    SortingCriteriaListByPriority =
    [
        "AllianceTextMultiList",
        "BattlegroupNameMultiList",
    ]

    RackName = "BattlegroupsBriefRack"

    TitleDescriptor = ~/BattlegroupsBriefMultiListTitle
    ContentDescriptor = ~/BattlegroupBriefsScrollingContainer
)

//-------------------------------------------------------------------------------------

private template DeckSimpleText
[
    ComponentFrame,
    TextToken : string,
] is BUCKTextDescriptor
(
    ComponentFrame = <ComponentFrame>

    TextStyle = "TextStyleEcranMoniteur"
    TypefaceToken = "IBM"
    TextColor = 'Blanc'
    TextSize = '24'

    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextToken = <TextToken>

    BigLineAction = ~/BigLineAction/MultiLine
    VerticalFitStyle = ~/FitStyle/UserDefined

    ParagraphStyle = TParagraphStyle
    (
        Alignment = ~/UIText_Left
        VerticalAlignment = ~/UIText_VerticalCenter
        Balanced = true
        BigWordAction = ~/BigWordAction/BigWordNewLine
    )
)

//-------------------------------------------------------------------------------------

private DeckListModalImportDeckWindowFrame is SpecificModalWindow
(
    TitleToken = "DI_DCKIMPO"

    ButtonList =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalButton
            (
                ElementName = "ImportButton"
                TextToken = "AB_DCKIMPO"
                IsFilled = true
                Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ENTER ) )
            )
        ),
        BUCKListElementDescriptor(ComponentDescriptor = SpecificModalCancelButton)
    ]

    ElementsList =
    [
        // ligne ****
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalDottedLine
        ),
        BUCKListElementSpacer(Magnifiable = 20.0),
        // text 1
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalEditableText
            (
                TextToken = "ODL_DckCod"
                HasText = true
                EditableTextElementName = "DeckCodeEditableText"
                Flags = EditableTextFlag/ONE_LINE | EditableTextFlag/INPUT_DISABLED
            )
        ),
        // texte 2
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalEditableText
            (
                TextToken = "DC_DCKNAM"
                HasText = true
                EditableTextElementName = "DeckNameEditableText"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalErrorText
        ),
        // ligne ****
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalDottedLine
        )
    ]
)

//-------------------------------------------------------------------------------------

private DeckListModalExportDeckWindowFrame is SpecificModalWindow
(
    TitleToken = "DI_DCKEXPO"

    ButtonList =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalButton
            (
                ElementName = "OkButton"
                TextToken = "BTN_OK"
                Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ENTER ) )
                IsFilled = true
            )
        )
    ]

    ElementsList =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = SpecificModalText(ElementName = "ModalContent" TextToken = "DI_DCKCOP")
        )
    ]
)

//-------------------------------------------------------------------------------------

private SelectionButton is BUCKContainerDescriptor
(
    ElementName = "SelectionButtonContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 45.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    Components =
    [
        DeckListPanelSelectButton
    ]
)

//-------------------------------------------------------------------------------------

private BoutonsAction is BUCKListDescriptor
(
    ElementName = "ActionButtonsList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 45.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Horizontal
    InterItemMargin = TRTTILength (Magnifiable = 2.0)

    Elements =
    [
        BUCKListElementDescriptor( ComponentDescriptor = ~/DeckListPanelCreateButton ),
        BUCKListElementDescriptor( ComponentDescriptor = ~/DeckListPanelEditButton ),
        BUCKListElementDescriptor( ComponentDescriptor = ~/DeckListPanelViewButton ),
        BUCKListElementDescriptor( ComponentDescriptor = ~/DeckListPanelImportButton ),

    ]
)

//--------------------------------------------------------

private BoutonsDeck is BUCKListDescriptor
(
    ElementName = "DeckButtonsList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 45.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Horizontal
    InterItemMargin = TRTTILength (Magnifiable = 4.0)

    Elements =
    [

        BUCKListElementDescriptor( ComponentDescriptor = ~/DeckListPanelRenameButton ),
        BUCKListElementDescriptor( ComponentDescriptor = ~/DeckListPanelRemoveButton ),
        BUCKListElementDescriptor( ComponentDescriptor = ~/DeckListPanelCopyButton ),
        BUCKListElementDescriptor( ComponentDescriptor = ~/DeckListPanelExportButton ),
    ]
)

//--------------------------------------------------------

DeckListHorizontalOffset is 200.0
DeckListTextureWidth is 712.0
DeckListTextureHeight is 820.0

EcranDeckAnimated is WindowFrame
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableOffset = [0.0, -64.0]
    )

    ContentExtendWeight = 1.0
    ContentMagnifiableWidthHeight = [DeckListTextureWidth + 2*DeckListHorizontalOffset, 750.0]
    ContentAlignementToFather = [0.0, 1.0]
    ContentAlignementToAnchor = [0.0, 1.0]
    HasBackground = false
    UniqueName = 'DeckListHUB'
    HasTitle = false
    TitleToken = 'DLS_TITLE'
    ButtonList = []

    ContentComponents =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableOffset = [0.0, 48.0]
                AlignementToAnchor = [0.5, 0.0]
                AlignementToFather = [0.5, 0.0]
            )

            Components =
            [
                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [712.0, 820.0]
                        MagnifiableOffset = [0.0, 20.0]
                        AlignementToAnchor = [0.5, 1.0]
                        AlignementToFather = [0.5, 1.0]
                    )
                    TextureToken = 'deck_fond'
                    HidePointerEvents = true
                ),
                BUCKTextDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 0.0]
                        AlignementToFather = [0.5, 0.0]
                        AlignementToAnchor = [0.5, 0.0]
                        MagnifiableOffset = [0.0, 15.0]
                    )
                    ParagraphStyle = paragraphStyleTextCenter
                    TextStyle = "Default"
                    HorizontalFitStyle = ~/FitStyle/UserDefined
                    VerticalFitStyle = ~/FitStyle/FitToContent
                    TypefaceToken = "Liberator"
                    BigLineAction = ~/BigLineAction/MultiLine
                    TextToken = 'BATGR_BTN'
                    TextDico = ~/LocalisationConstantes/dico_interface_ingame
                    TextColor = "gris_load"
                    TextSize = "32"
                ),
                DeckListBackButton,
                BUCKCheckBoxListDescriptorExcelStyle
                (
                    ElementName = "FiltersList"
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableOffset = [DeckListHorizontalOffset + 60, 15.0]
                    )
                    BoxMagnifiableWidthHeight = [25.0, 25.0]
                    MagnifiableOffsetRack = [-210.0, 0.0]
                    TextureColorToken = 'Blanc_multi'
                    TextureToken = 'Outgame_deck_filter'
                    CheckboxHasBackground = false
                    CheckboxHasBorder = false
                    ListData = ~/BattlegroupFilterList
                    ArrowTextureColorToken = 'Blanc_multi'
                    ArrowTextureToken = 'Outgame_deck_arrow'
                    ArrowBoxMagnifiableWidthHeight = [20.0, 25.0]
                    ItemListBackgroundBlockColorToken = 'H2_bleu_2_hint'
                ),
                BUCKTextDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableOffset = [DeckListHorizontalOffset + 50, 42.0]
                        MagnifiableWidthHeight = [64.0, 0.0]
                    )
                    ElementName = "NbFilteredDecksText"
                    ParagraphStyle = paragraphStyleTextCenter
                    TextStyle = "Default"
                    HorizontalFitStyle = ~/FitStyle/UserDefined
                    VerticalFitStyle = ~/FitStyle/FitToContent
                    TypefaceToken = "Liberator"
                    BigLineAction = ~/BigLineAction/MultiLine
                    TextDico = ~/LocalisationConstantes/dico_interface_ingame
                    TextColor = "gris_load"
                    TextSize = "10"
                ),
                BUCKContainerDescriptor
                (
                    ElementName = "DeckListArea"
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        MagnifiableWidthHeight = [DeckListTotalWidth, 670.0]
                        AlignementToFather = [0.5, 0.0]
                        AlignementToAnchor = [0.5, 0.0]
                        MagnifiableOffset = [0.0, 90.0]
                    )

                    Components =
                    [
                        BUCKListDescriptor
                        (
                            ElementName = "DeckListVerticalList"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )
                            Axis = ~/ListAxis/Vertical

                            Elements =
                            [
                                BUCKListElementDescriptor
                                (
                                    ExtendWeight = 1.0
                                    ComponentDescriptor = BattlegroupsBriefMultiList
                                ),
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = SelectionButton
                                ),
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = BoutonsAction
                                ),
                                BUCKListElementDescriptor
                                (
                                    ComponentDescriptor = BoutonsDeck
                                ),
                            ]
                        ),
                    ]
                ),
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

testHauteur is 52.0
testlargeur is 610.0
EffetStylePochettePlastique is BUCKRackDescriptor
(
    ElementName = "PlaticPochettes"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    BladeDescriptor = BUCKTextureDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI
        (
            MagnifiableWidthHeight = [testlargeur, testHauteur]
        )
        TextureDrawer  = 'ColorMultiply_Additive'
        TextureToken =  'Outgame_deck_pochette'
    )
)

//--------------------------------------------------------

DeckOverviewWidth is 1050.0
DeckOverviewHeight is 1000.0

DeckOverview is BUCKTranslationAnimatedContainerDescriptor
(
    ElementName = "DeckOverview"
    FramePropertyBeforeAnimation = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [DeckOverviewWidth, DeckOverviewHeight]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 1.0]
        MagnifiableOffset = [-50.0, 0.0]
    )

    FramePropertyAfterAnimation = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [DeckOverviewWidth, DeckOverviewHeight]
        AlignementToAnchor = [1.0, 1.0]
        AlignementToFather = [1.0, 1.0]
        MagnifiableOffset = [-50.0, 0.0]
    )

    AnimationTotalDuration = 0.3
    AnimationModificator = ~/AnimModificator/CubicStep
    AnimationModificatorParameters = [0.0, 1.0]

    HasBackground = true
    BackgroundBlockColorToken = "Blanc220"

    HidePointerEvents = true

    Components =
    [
        OmbrePanel(),
        DeckOverviewContent
    ]
)

//--------------------------------------------------------

DeckOverviewContent is BUCKContainerDescriptor
(
    ElementName = "DeckOverviewContent"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        MagnifiableWidthHeight = [-180.0, -30.0]
        MagnifiableOffset = [130.0, 30.0]
    )

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "DeckOverviewContentList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical
            InterItemMargin = TRTTILength( Magnifiable = 5.0 )

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DeckOverviewInformationsHeadersFrame
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DeckOverviewInformationsContentFrame
                ),
                BUCKListElementSpacer( Magnifiable = 10.0 ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DeckOverviewActivationPointsFrame
                ),
                BUCKListElementSpacer( Magnifiable = 5.0 ),
                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = DeckOverviewUnitsFrame
                ),
            ]
        )
    ]
)

//--------------------------------------------------------

DeckOverviewInfosColumnWidth is 130.0

DeckOverviewInformationsHeadersFrame is BUCKContainerDescriptor
(
    ElementName = "DeckOverviewInformationsHeadersFrame"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 45.0]
    )

    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
                MagnifiableOffset = [0.0, 5.0]
            )
            Axis = ~/ListAxis/Horizontal

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DeckOverviewNameHeader"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        HorizontalFitStyle = ~/FitStyle/UserDefined

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_Bottom
                            InterLine = 0
                        )

                        TextStyle = "Default"
                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/ResizeFont
                        TextToken = "DLS_DNAME"
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "gris_load"
                        TextSize = "24"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DeckOverviewDivisionHeader"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [DeckOverviewInfosColumnWidth, 0.0]
                        )

                        HorizontalFitStyle = ~/FitStyle/UserDefined

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_Bottom
                            InterLine = 0
                        )

                        TextStyle = "Default"
                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/ResizeFont
                        TextToken = "DLS_DIVIS"
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "gris_load"
                        TextSize = "24"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DeckOverviewNationHeader"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [DeckOverviewInfosColumnWidth, 0.0]
                        )

                        HorizontalFitStyle = ~/FitStyle/UserDefined

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_Bottom
                            InterLine = 0
                        )

                        TextStyle = "Default"
                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/ResizeFont
                        TextToken = "DB_FLAG"
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "gris_load"
                        TextSize = "24"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DeckOverviewAllianceHeader"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [DeckOverviewInfosColumnWidth, 0.0]
                        )

                        HorizontalFitStyle = ~/FitStyle/UserDefined

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Center
                            VerticalAlignment = UIText_Bottom
                            InterLine = 0
                        )

                        TextStyle = "Default"
                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/ResizeFont
                        TextToken = "DB_ALLIA"
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "gris_load"
                        TextSize = "24"
                    )
                )
            ]
        )
    ]
)

//--------------------------------------------------------

DeckOverviewInformationsContentFrame is BUCKContainerDescriptor
(
    ElementName = "DeckOverviewInformationsContentFrame"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 30.0]
    )

    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
            )
            Axis = ~/ListAxis/Horizontal

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DeckOverviewName"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        ParagraphStyle = paragraphStyleTextLeftAlign

                        TextStyle = "Default"
                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/ResizeFont
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "gris_load"
                        TextSize = "28"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = "DeckOverviewDivision"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [DeckOverviewInfosColumnWidth, 0.0]
                        )
                        TextureFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )
                        ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = "DeckOverviewNation"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [DeckOverviewInfosColumnWidth, 0.0]
                        )
                        TextureFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )
                        ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = "DeckOverviewAlliance"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [DeckOverviewInfosColumnWidth, 0.0]
                        )
                        TextureFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )
                        ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                    )
                )
            ]
        )
    ]
)

//--------------------------------------------------------

DeckOverviewActivationPointsFrame is BUCKContainerDescriptor
(
    ElementName = "DeckOverviewActivationPointsFrame"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 30.0]
    )

    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
            )
            Axis = ~/ListAxis/Horizontal

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                        )

                        HorizontalFitStyle = ~/FitStyle/FitToContent
                        ParagraphStyle = paragraphStyleTextLeftAlign

                        TextStyle = "Default"
                        TextToken = "DCKO_ACTIV"
                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/ResizeFont
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "gris_load"
                        TextSize = "18"
                    )
                ),
                BUCKListElementSpacer(Magnifiable = 20.0),
                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DeckOverviewActivationPoints"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        ParagraphStyle = paragraphStyleTextLeftAlign

                        TextStyle = "Default"
                        TextToken = "DCKO_FMT"
                        TypefaceToken = "Liberator"
                        BigLineAction = ~/BigLineAction/ResizeFont
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "gris_load"
                        TextSize = "18"
                    )
                )
            ]
        )
    ]
)

//--------------------------------------------------------

DeckOverviewUnitsFrame is BUCKSpecificScrollingContainerDescriptor
(
    ElementName = "DeckOverviewUnitsFrame"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        MagnifiableWidthHeight = [20.0, -40.0]
        MagnifiableOffset = [-20.0, 0.0]
    )

    ExternalScrollbar = true
    ScrollStepSize = [0.0, 10.0]
    HasVerticalScrollbar = true

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )
            FitStyle = ~/ContainerFitStyle/FitToContentVertically

            Components =
            [
                BUCKListDescriptor
                (
                    ElementName = "DeckOverviewUnitsList"
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 0.0]
                        MagnifiableWidthHeight = [-30.0, 0.0]
                    )
                    Axis = ~/ListAxis/Vertical
                    ChildFitToContent = true

                    // List filled with DeckOverviewCategoryLine
                ),

                // This BUCKContainer acts like a screen to block mouse events to units
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                    )
                    HidePointerEvents = true
                ),
            ]
        )
    ]
)

//--------------------------------------------------------

DeckOverviewCategoryLine is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 75.0]
    )

    Axis = ~/ListAxis/Horizontal
    InterItemMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "CategoryName"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [60.0, 0.0]
                )

                HorizontalFitStyle = ~/FitStyle/UserDefined
                ParagraphStyle = paragraphStyleTextLeftAlign

                TextStyle = "Default"
                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/ResizeFont
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "gris_load"
                TextSize = "28"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (
                ElementName = "CategoryUnitRack"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )
                Axis = ~/ListAxis/Horizontal

                InterItemMargin = TRTTILength(Magnifiable = 5.0)

                BladeDescriptor = BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [0.0, 1.0]
                        MagnifiableWidthHeight = [113.0, 0.0]
                        AlignementToAnchor = [0.0, 0.5]
                        AlignementToFather = [0.0, 0.5]
                    )
                    HasBackground = true
                    BackgroundBlockColorToken = "Blanc220"

                    // Each container of this type is filled with one UnitPackComponentDescriptor
                )
            )
        )
    ]
)

//--------------------------------------------------------

template BUCKSpecificBattlegroupsListMainComponentDescriptor
[
    MagnifiableOffsetBefore : float2,
    MagnifiableOffsetAfter : float2,
]
is BUCKTranslationAnimatedContainerDescriptor
(
    ElementName = "BattlegroupsListAnimation"

    FramePropertyBeforeAnimation = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 1.0]
        MagnifiableOffset = <MagnifiableOffsetBefore>
    )

    TriggerWhenSetVisible = true
    AnimationTotalDuration = 0.3
    AnimationModificator = ~/AnimModificator/CubicStep
    AnimationModificatorParameters = [0.0, 1.0]

    FramePropertyAfterAnimation = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToAnchor = [0.5, 1.0]
        AlignementToFather = [0.5, 1.0]
        MagnifiableOffset = <MagnifiableOffsetAfter>
    )

    Components =
    [
        BUCKSensibleAreaDescriptor
        (
            ElementName = 'SensibleArea'

            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            HidePointerEvents = true
        ),
        ~/DeckOverview,
        ~/EcranDeckAnimated,
    ]
)

//-------------------------------------------------------------------------------------

// rester synchro avec TagsVector dans le descripteur de la vue
BattlegroupFilterList is
[
    (
        // Synchro la position de cette categorie avec CustomPremadeDeckCategoryIndex dans le descripteur de la vue
        "ReadOnly",
        [
            (
                "FLTDK_CUST",
                0 // Synchro avec CustomDeckFilterIndex dans le descripteur de la vue
            ),
            (
                "FLTDK_PREM",
                1 // Synchro avec PremadeDeckFilterIndex dans le descripteur de la vue
            ),
        ]
    ),
    (
        "Countries",
        [
            (
                "FLTDK_ALLY",
                2
            ),
            (
                "FLTDK_AXE",
                3
            ),
        ]
    ),
    (
        "Types",
        [
            (
                "FLTDK_ARM",
                4
            ),
            (
                "FLTDK_SOL",
                5
            ),
            (
                "FLTDK_MEC",
                6
            ),
        ]
    ),
    (
        "Ratings",
        [
            (
                "FLTDK_RK1",
                7
            ),
            (
                "FLTDK_RK2",
                8
            ),
            (
                "FLTDK_RK3",
                9
            ),
        ]
    ),
]

BattlegroupTagsVector is
[
    "", // Custom Deck - ne fait rien
    "", // Premade Deck - ne fait rien
    "Allied",
    "Axis",
    "armored",
    "infantryReg",
    "armoredRecon",
    "DC_PWR1",
    "DC_PWR2",
    "DC_PWR3",
]

//--------------------------------------------------------

template UISpecificBattlegroupsListViewDescriptor
[
    ContainerUniqueName : string,
    MagnifiableOffsetBefore : float2,
    MagnifiableOffsetAfter : float2,
]
is TUISpecificBattlegroupsListViewDescriptor
(
    MainComponentDescriptor = BUCKSpecificBattlegroupsListMainComponentDescriptor
    (
        MagnifiableOffsetBefore = <MagnifiableOffsetBefore>
        MagnifiableOffsetAfter = <MagnifiableOffsetAfter>
    )
    MainComponentContainerUniqueName = <ContainerUniqueName> // Permet d'indiquer l'endroit ou le composant doit s'insérer

    TagsVector = ~/BattlegroupTagsVector
    CustomPremadeDeckCategoryIndex = 0
    CustomDeckFilterIndex = 0
    PremadeDeckFilterIndex = 1

    FactoryDisplayOrder =
    [
        EDefaultFactories/Logistic,
        EDefaultFactories/Infantry,
        EDefaultFactories/Art,
        EDefaultFactories/Tanks,
        EDefaultFactories/Recons,
        EDefaultFactories/DCA,
        EDefaultFactories/Helis,
        EDefaultFactories/Planes,
    ]

    ColorForAlliance = MAP
    [
        (
            ECoalition/OTAN,
            "GameDebrief/TeamStats/PlayerLine/Nato"
        ),
        (
            ECoalition/PACT,
            "GameDebrief/TeamStats/PlayerLine/Pact"
        ),
    ]

    AllianceFlagTexture = MAP
    [
        (
            ECoalition/OTAN,
            "CommonTexture_MotherCountryFlag_NATO_small"
        ),
        (
            ECoalition/PACT,
            "CommonTexture_MotherCountryFlag_PACT_small"
        ),
    ]

    BattlegroupBriefDescriptor = UISpecificDivisionBriefViewDescriptor
    (
        HasBattlegroup = true
        MainComponentDescriptor = ~/BattlegroupBriefDescriptorForSelection
    )

    ModaleImportDeck = ~/DeckListModalImportDeckWindowFrame
    ModaleExportDeck = ~/DeckListModalExportDeckWindowFrame

    OverviewEmptyFieldPlaceholder = "CL_DEFVAL"
    OverviewUnitsPerLine = 7
    UnitPackComponentDescriptor = UISpecificUnitButtonViewDescriptor
    (
        TextureDrawerWhenUnitNotAvailable = "ColorMultiply_Grayscale"
        UnitNameColorWhenUnitNotAvailable = "Fulda2_Orange30"
        UnitCostColorWhenUnitNotAvailable = "Fulda2_Orange30"
        UnitCostColorWhenUnitCannotBeBought = "Fulda2_Orange30"
        UnitCostGlowColorIndexWhenUnitNotAvailable = "Fulda2_Orange100"
        UnitNameBackgroundColorWhenUnitNotAvailable = "Gris123"

        AceUnitNameColor = "Fulda2_Orange100"
        AceUnitBorderColorBySide =
        [
            (~/TBorderSide/Top | ~/TBorderSide/Left, "Fulda2_Orange100"),
            (~/TBorderSide/Bottom | ~/TBorderSide/Right, "Fulda2_Orange100"),
        ]
        AceUnitBorderThickness = "1"

        OpacityPercentWhenUnitNotAvailable = 1.0
        TimeInSecondBeforeAdditionalUnitSpecificContainerShowUp = 0.2
        MagnifierMultiplication = 0.85
        AcceptEvents = false
    )

    OverviewUnitLineDescriptor = DeckOverviewCategoryLine
    OverviewInterlineFillerDescriptor = BUCKContainerDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI
        (
            RelativeWidthHeight = [1.0, 0.0]
            MagnifiableWidthHeight = [0.0, 15.0]
        )
    )
)

UISpecificBattlegroupsListViewDescriptorForArmory is UISpecificBattlegroupsListViewDescriptor
(
    ContainerUniqueName = "BattlegroupsListForArmory"
    MagnifiableOffsetBefore = [0.0, -70.0]
    MagnifiableOffsetAfter = [0.0, 0.0]
)

UISpecificBattlegroupsListViewDescriptorForLobby is UISpecificBattlegroupsListViewDescriptor
(
    ContainerUniqueName = "LobbyForegroundContainer"
    MagnifiableOffsetBefore = [0.0, 0.0]
    MagnifiableOffsetAfter = [0.0, 0.0]
)