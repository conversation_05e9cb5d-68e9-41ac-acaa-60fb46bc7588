


UseOutGameTexture_WaitAnimation_0 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00000.png")
UseOutGameTexture_WaitAnimation_1 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00001.png")
UseOutGameTexture_WaitAnimation_2 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00002.png")
UseOutGameTexture_WaitAnimation_3 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00003.png")
UseOutGameTexture_WaitAnimation_4 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00004.png")
UseOutGameTexture_WaitAnimation_5 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00005.png")
UseOutGameTexture_WaitAnimation_6 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00006.png")
UseOutGameTexture_WaitAnimation_7 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00007.png")
UseOutGameTexture_WaitAnimation_8 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00008.png")
UseOutGameTexture_WaitAnimation_9 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00009.png")
UseOutGameTexture_WaitAnimation_10 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00010.png")
UseOutGameTexture_WaitAnimation_11 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00011.png")
UseOutGameTexture_WaitAnimation_12 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00012.png")
UseOutGameTexture_WaitAnimation_13 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00013.png")
UseOutGameTexture_WaitAnimation_14 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00014.png")
UseOutGameTexture_WaitAnimation_15 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00015.png")
UseOutGameTexture_WaitAnimation_16 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00016.png")
UseOutGameTexture_WaitAnimation_17 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00017.png")
UseOutGameTexture_WaitAnimation_18 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00018.png")
UseOutGameTexture_WaitAnimation_19 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00019.png")
UseOutGameTexture_WaitAnimation_20 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00020.png")
UseOutGameTexture_WaitAnimation_21 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00021.png")
UseOutGameTexture_WaitAnimation_22 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00022.png")
UseOutGameTexture_WaitAnimation_23 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00023.png")
UseOutGameTexture_WaitAnimation_24 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00024.png")
UseOutGameTexture_WaitAnimation_25 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00025.png")
UseOutGameTexture_WaitAnimation_26 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00026.png")
UseOutGameTexture_WaitAnimation_27 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00027.png")
UseOutGameTexture_WaitAnimation_28 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00028.png")
UseOutGameTexture_WaitAnimation_29 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00029.png")
UseOutGameTexture_WaitAnimation_30 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00030.png")
UseOutGameTexture_WaitAnimation_31 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00031.png")
UseOutGameTexture_WaitAnimation_32 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00032.png")
UseOutGameTexture_WaitAnimation_33 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00033.png")
UseOutGameTexture_WaitAnimation_34 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00034.png")
UseOutGameTexture_WaitAnimation_35 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00035.png")
UseOutGameTexture_WaitAnimation_36 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00036.png")
UseOutGameTexture_WaitAnimation_37 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00037.png")
UseOutGameTexture_WaitAnimation_38 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00038.png")
UseOutGameTexture_WaitAnimation_39 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00039.png")
UseOutGameTexture_WaitAnimation_40 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00040.png")
UseOutGameTexture_WaitAnimation_41 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00041.png")
UseOutGameTexture_WaitAnimation_42 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00042.png")
UseOutGameTexture_WaitAnimation_43 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00043.png")
UseOutGameTexture_WaitAnimation_44 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00044.png")
UseOutGameTexture_WaitAnimation_45 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00045.png")
UseOutGameTexture_WaitAnimation_46 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00046.png")
UseOutGameTexture_WaitAnimation_47 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00047.png")
UseOutGameTexture_WaitAnimation_48 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00048.png")
UseOutGameTexture_WaitAnimation_49 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00049.png")
UseOutGameTexture_WaitAnimation_50 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00050.png")
UseOutGameTexture_WaitAnimation_51 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00051.png")
UseOutGameTexture_WaitAnimation_52 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00052.png")
UseOutGameTexture_WaitAnimation_53 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00053.png")
UseOutGameTexture_WaitAnimation_54 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00054.png")
UseOutGameTexture_WaitAnimation_55 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00055.png")
UseOutGameTexture_WaitAnimation_56 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00056.png")
UseOutGameTexture_WaitAnimation_57 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00057.png")
UseOutGameTexture_WaitAnimation_58 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00058.png")
UseOutGameTexture_WaitAnimation_59 is TUIResourceTexture_Common(FileName = "GameData:/Assets/2D/Interface/UseOutGame/WaitAnim/animation_loading_00059.png")

waitAnimTextureList is [
    "UseOutGameTexture_WaitAnimation_0",
    "UseOutGameTexture_WaitAnimation_1",
    "UseOutGameTexture_WaitAnimation_2",
    "UseOutGameTexture_WaitAnimation_3",
    "UseOutGameTexture_WaitAnimation_4",
    "UseOutGameTexture_WaitAnimation_5",
    "UseOutGameTexture_WaitAnimation_6",
    "UseOutGameTexture_WaitAnimation_7",
    "UseOutGameTexture_WaitAnimation_8",
    "UseOutGameTexture_WaitAnimation_9",
    "UseOutGameTexture_WaitAnimation_10",
    "UseOutGameTexture_WaitAnimation_11",
    "UseOutGameTexture_WaitAnimation_12",
    "UseOutGameTexture_WaitAnimation_13",
    "UseOutGameTexture_WaitAnimation_14",
    "UseOutGameTexture_WaitAnimation_15",
    "UseOutGameTexture_WaitAnimation_16",
    "UseOutGameTexture_WaitAnimation_17",
    "UseOutGameTexture_WaitAnimation_18",
    "UseOutGameTexture_WaitAnimation_19",
    "UseOutGameTexture_WaitAnimation_20",
    "UseOutGameTexture_WaitAnimation_21",
    "UseOutGameTexture_WaitAnimation_22",
    "UseOutGameTexture_WaitAnimation_23",
    "UseOutGameTexture_WaitAnimation_24",
    "UseOutGameTexture_WaitAnimation_25",
    "UseOutGameTexture_WaitAnimation_26",
    "UseOutGameTexture_WaitAnimation_27",
    "UseOutGameTexture_WaitAnimation_28",
    "UseOutGameTexture_WaitAnimation_29",
    "UseOutGameTexture_WaitAnimation_30",
    "UseOutGameTexture_WaitAnimation_31",
    "UseOutGameTexture_WaitAnimation_32",
    "UseOutGameTexture_WaitAnimation_33",
    "UseOutGameTexture_WaitAnimation_34",
    "UseOutGameTexture_WaitAnimation_35",
    "UseOutGameTexture_WaitAnimation_36",
    "UseOutGameTexture_WaitAnimation_37",
    "UseOutGameTexture_WaitAnimation_38",
    "UseOutGameTexture_WaitAnimation_39",
    "UseOutGameTexture_WaitAnimation_40",
    "UseOutGameTexture_WaitAnimation_41",
    "UseOutGameTexture_WaitAnimation_42",
    "UseOutGameTexture_WaitAnimation_43",
    "UseOutGameTexture_WaitAnimation_44",
    "UseOutGameTexture_WaitAnimation_45",
    "UseOutGameTexture_WaitAnimation_46",
    "UseOutGameTexture_WaitAnimation_47",
    "UseOutGameTexture_WaitAnimation_48",
    "UseOutGameTexture_WaitAnimation_49",
    "UseOutGameTexture_WaitAnimation_50",
    "UseOutGameTexture_WaitAnimation_51",
    "UseOutGameTexture_WaitAnimation_52",
    "UseOutGameTexture_WaitAnimation_53",
    "UseOutGameTexture_WaitAnimation_54",
    "UseOutGameTexture_WaitAnimation_55",
    "UseOutGameTexture_WaitAnimation_56",
    "UseOutGameTexture_WaitAnimation_57",
    "UseOutGameTexture_WaitAnimation_58",
    "UseOutGameTexture_WaitAnimation_59",
]

template AnimatedWaitingComponent
[
    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI( MagnifiableWidthHeight = [45.0, 45.0] )
]
is BUCKTextureAnimationDescriptor
(
    ElementName = "WaitingAnim"
    ComponentFrame = <ComponentFrame>

    TextureDrawer = 'ColorMultiply_Grayscale'
    TextureList = ~/waitAnimTextureList
    IsCyclic = true
    AnimDuration = 4.0
)

private SavingComponent is BUCKListDescriptor
(
    Axis = ~/ListAxis/Horizontal

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0, 45.0]
        MagnifiableOffset = [0.0, 20.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FirstMargin = TRTTILength(Pixel = DistanceBetweenExternalAndInternalBorder + 1.0)
    LastMargin = TRTTILength(Pixel = DistanceBetweenExternalAndInternalBorder + 1.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = AnimatedWaitingComponent
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [45.0, 0.0]
                )
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                )

                ParagraphStyle = paragraphStyleTextCenter
                TextStyle = "TextStyleEcranMoniteur"

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/UserDefined
                TypefaceToken = "IBM"
                BigLineAction = ~/BigLineAction/MultiLine
                TextToken = "M_SAVING"
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "Blanc"
                TextSize = "24"
            )
        ),
    ]
    BackgroundComponents =
    [
        PanelRoundedCorner()
    ]
)

WaitAnimResource is TUISpecificOutGameWaitAnimResource
(
    AnimatedComponent = AnimatedWaitingComponent()
    SavingComponent = ~/SavingComponent
)
