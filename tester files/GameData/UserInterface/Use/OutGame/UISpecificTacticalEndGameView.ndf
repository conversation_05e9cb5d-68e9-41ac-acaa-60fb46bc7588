
TacticalEndgameTitleFont is "Liberator"
TacticalEndgameTitleFontSize is "Debrief/Title"

TacticalEndgameKillLossTextFont is "Liberator"
TacticalEndgameKillLossUnitTextFontSize is "Debrief/UnitName"
TacticalEndgameWinStreakInfoTextFontSize is "Debrief/SoldierName"
TacticalEndgameKillLossSoldierTextFontSize is "Debrief/SoldierName"
TacticalEndgameKillLossPlayerTextFontSize is "Debrief/SoldierName"

TacticalEndgameKillLossTimerTextFont is "Liberator"
TacticalEndgameKillLossTimerTextFontSize is "Debrief/Timer"

TacticalEndgameScoreTextFont is "Liberator"
TacticalEndgameScoreTextFontSize is "Debrief/Score"

TacticalEndgamePostItTextFont is "HandPen"
TacticalEndgamePostItTextFontSize is "Debrief/PostIt"

TacticalEndgameBattleInfosfont is "HandPen"

//-------------------------------------------------------------------

template DebriefBaseText
[
    ElementName : string = "",
    ComponentFrame : TUIFramePropertyRTTI,
    TextToken : string,
    TextColor : string,
    TextSize : string,
    TypefaceToken : string = "IBM",
    VerticalAlignment : int = ~/UIText_VerticalCenter,
]
is BUCKSpecificTextWithHint
(
    ElementName = <ElementName>
    ComponentFrame = <ComponentFrame>
    ParagraphStyle = TParagraphStyle
    (
        Alignment = ~/UIText_Right
        VerticalAlignment = <VerticalAlignment>
    )
    TypefaceToken = <TypefaceToken>
    TextStyle = "Default"
    TextToken = <TextToken>
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextColor = <TextColor>
    TextSize = <TextSize>

    HasAutoHint = true
    AutoHintElementName = <ElementName> + "AutoHint"
)

template DebriefHeaderText
[
    ElementName : string = "",
    ComponentFrame : TUIFramePropertyRTTI,
    TextToken : string,
    VerticalAlignment : int = ~/UIText_VerticalCenter,
    TextSize : string = "18",
]
is DebriefBaseText
(
    VerticalAlignment = <VerticalAlignment>
    TypefaceToken = TacticalEndgameKillLossTextFont
    ElementName = <ElementName>
    ComponentFrame = <ComponentFrame>
    TextToken = <TextToken>
    TextColor = "GrisTexte"
    TextSize = <TextSize>
)

template DebriefStandardText
[
    ElementName : string = "",
    ComponentFrame : TUIFramePropertyRTTI,
    TextToken : string,
    VerticalAlignment : int = ~/UIText_VerticalCenter,
    TextSize : string = "18",
]
is DebriefBaseText
(
    TypefaceToken = TacticalEndgameKillLossTextFont
    ElementName = <ElementName>
    ComponentFrame = <ComponentFrame>
    TextToken = <TextToken>
    TextColor = "GrisTexte"
    TextSize = <TextSize>
    VerticalAlignment = <VerticalAlignment>
)

//-------------------------------------------------------------------

template DebriefCategoryHeaderText
[
    ElementName : string = "",
    ComponentFrame : TUIFramePropertyRTTI,
    TextToken : string,
]
is DebriefBaseText
(
    ElementName = <ElementName>
    ComponentFrame = <ComponentFrame>
    TextToken = <TextToken>
    TextColor = "ListeExcel/Cartouche"
    TextSize = "SD2_Moyen"
)

//-------------------------------------------------------------------

template TacticalEndgameButton
[
    ElementName : string = "",
    TextToken : string = "",
    Mapping : TEugBMutablePBaseClass = nil,
] is BUCKSpecificButton
(
    ElementName = <ElementName>

    ButtonRelativeWidthHeight = [1.0, 0.0]
    ButtonMagnifiableWidthHeight = [-20, 30]
    ButtonAlignementToAnchor = [0.5, 0.0]
    ButtonAlignementToFather = [0.5, 0.0]

    TextToken = <TextToken>
    TextSizeToken = "28"
    TextColorToken = "NoirEndgameButton"
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    BigLineAction = ~/BigLineAction/MultiLine

    Mapping = <Mapping>

    HasAutoHint = true
    AutoHintElementName = <ElementName> + "AutoHint"
)

//-------------------------------------------------------------------------------------

PlainSeparator is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 5.0]
        RelativeWidthHeight = [0.85, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    //HasBackground = true
    BackgroundBlockColorToken = "GrisTexte"

    HasBorder = true
    BorderThicknessToken = "1"
    BorderLineColorToken = "GrisTexte"
    BordersToDraw = ~/TBorderSide/Bottom
    Components = []
)

//-------------------------------------------------------------------------------------

private XpLeftOffset is 10.0
private XpFirstColumnWidth is 400.0
private XpColumnWidth is 250.0
private XpCompleteLineWidth is XpFirstColumnWidth + 2*XpColumnWidth

//-------------------------------------------------------------------------------------

template DebriefXpLine
[
    LineName : string,
    HeaderToken : string,
    ModificatorToken : string,
    ValueToken : string,
    MagnifiableHeight : float = 24.0,
    VerticalAlignment : int = ~/UIText_VerticalCenter,
    TextSize : string = "18",
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset = [XpLeftOffset, 0.0]
        MagnifiableWidthHeight = [XpCompleteLineWidth, <MagnifiableHeight>]
    )

    Components =
    [
        DebriefHeaderText
        (
            VerticalAlignment = <VerticalAlignment>
            TextSize = <TextSize>
            ElementName = <LineName> + "Legend"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [XpFirstColumnWidth, 0.0]
                RelativeWidthHeight = [0.0, 1.0]

            )
            TextToken = <HeaderToken>
        ),
        DebriefStandardText
        (
            VerticalAlignment = <VerticalAlignment>
            ElementName = <LineName> + "Value"
            TextSize = <TextSize>
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [XpFirstColumnWidth, 0.0]
                MagnifiableWidthHeight = [XpColumnWidth, 0.0]
                RelativeWidthHeight = [0.0, 1.0]
            )
            TextToken = <ValueToken>
        ),
        DebriefStandardText
        (
            VerticalAlignment = <VerticalAlignment>
            ElementName = <LineName> + "Modificator"
            TextSize = <TextSize>
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [XpFirstColumnWidth + XpColumnWidth, 0.0]
                MagnifiableWidthHeight = [XpColumnWidth, 0.0]
                RelativeWidthHeight = [0.0, 1.0]
            )
            TextToken = <ModificatorToken>
        ),
    ]
)

//-------------------------------------------------------------------------------------

private DebriefXpTitleLine is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableOffset = [XpLeftOffset + XpFirstColumnWidth, 0.0]
        MagnifiableWidthHeight = [2*XpColumnWidth, 50.0]
    )

    Components =
    [
        DebriefHeaderText
        (
            ElementName = "XPTitleValue"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [XpColumnWidth, 0.0]
                RelativeWidthHeight = [0.0, 1.0]
            )
            TextToken = "EG_VALUE"
            TextSize = "24"
        ),
        DebriefHeaderText
        (
            ElementName = "XPModifValue"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [XpColumnWidth, 0.0]
                MagnifiableWidthHeight = [XpColumnWidth, 0.0]
                RelativeWidthHeight = [0.0, 1.0]
            )
            TextToken = "EG_MODIF"
            TextSize = "24"
        ),
    ]
)

//-------------------------------------------------------------------------------------

EndgameTVScreenDisplay is BUCKContainerDescriptor
(
    ElementName = "EcranXPContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [440, 282.0]
        MagnifiableOffset = [190.0, -110.0]
        AlignementToFather = [0.0, 1.0]
        AlignementToAnchor = [0.0, 1.0]
    )

    Components =
    [
        BUCKPerspectiveWarpOffscreenContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [450.0, 292.0]
            )
            DistortTextureDrawer = $/UserInterface/UIDistortTextureDrawer
            PointerEventsToAllow = ~/EAllowablePointerEventType/Move
            MagnifiableTopLeftOffset = [0.0, 0.0]
            MagnifiableTopRightOffset = [-70.0, 15.0]
            MagnifiableBottomLeftOffset = [0.0, 103.0]
            MagnifiableBottomRightOffset = [-70.0, 40.0]

            Components =
            [
                ~/EndgameTabButtons
            ]
        ),
    ]
)

//-------------------------------------------------------------------------------------

private EndgameTabButtons is BUCKListDescriptor
(
    ElementName = "EndgameTabButtons"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.5]
        AlignementToAnchor = [0.5, 0.5]
    )

    Axis = ~/ListAxis/Vertical
    InterItemMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EndgameTabButton
            (
                ElementName = "EndgameButtonScore"
                TextToken = "ENDBOV"
                RadioButtonManager = ~/EndgameRadioButtonManager
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EndgameTabButton
            (
                ElementName = "EndgameButtonKills"
                TextToken = "ENDBKL"
                RadioButtonManager = ~/EndgameRadioButtonManager
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EndgameTabButton
            (
                ElementName = "EndgameButtonXP"
                TextToken = "ENDBXP"
                RadioButtonManager = ~/EndgameRadioButtonManager
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EndgameTabButton
            (
                ElementName = "EndgameButtonProfile"
                TextToken = "ENDBPR"
                RadioButtonManager = ~/EndgameRadioButtonManager
            )
        ),
        // Navigation buttons
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EndgameTabButton
            (
                ElementName = "RematchButton"
                TextToken = "BTN_NEXT"
                BackgroundBlockColorToken = "loginBoutonBackground_highlightedCyan"
                TextStyle = "TextStyleEcranMoniteur_cyan"
                TextColorToken = "loginBlanc"
                BorderLineColorToken = "Cyan"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EndgameTabButton
            (
                ElementName = "RestartButton"
                TextToken = "AB_RESTART"
                BackgroundBlockColorToken = "loginBoutonBackground_highlightedCyan"
                TextStyle = "TextStyleEcranMoniteur_cyan"
                TextColorToken = "loginBlanc"
                BorderLineColorToken = "Cyan"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EndgameTabButton
            (
                ElementName = "SaveReplayButton"
                TextToken = "AB_SAVER"
                BackgroundBlockColorToken = "loginBoutonBackground_highlightedCyan"
                TextStyle = "TextStyleEcranMoniteur_cyan"
                TextColorToken = "loginBlanc"
                BorderLineColorToken = "Cyan"
            )
        ),
        //------------------------------------------------------
        // Exit button
        BUCKListElementDescriptor
        (
            ComponentDescriptor = EndgameTabButton
            (
                ElementName = "ExitButton"
                TextToken = "AB_QUITGAM"
                Mapping = TEugBMutablePBaseClass( Value = TUserInputMapping( KeyboardEventID = UserInputKeyboard/KEY_ESCAPE ) )
                BackgroundBlockColorToken = "loginBoutonBackground_highlightedCyan"
                TextStyle = "TextStyleEcranMoniteur_cyan"
                TextColorToken = "loginBlanc"
                BorderLineColorToken = "Cyan"
            )
        )
    ]
)

//-------------------------------------------------------------------------------------

private EndgameRadioButtonManager is TBUCKRadioButtonManager()

template EndgameTabButton
[
    ElementName : string = "",
    TextToken : string = "",
    Mapping : TEugBMutablePBaseClass = nil,
    TextStyle : string = "TextStyleEcranMoniteur",
    TextColorToken : string = "loginBlanc",
    BorderLineColorToken : string = "VertLogin",
    BackgroundBlockColorToken : string = "loginBoutonBackground_vert",
    RadioButtonManager : TBUCKRadioButtonManager = nil,
] is ConfirmButton
(
    ElementName = <ElementName>
    TextToken = <TextToken>
    ButtonAlignementToAnchor = [0.5, 0.0]
    ButtonAlignementToFather = [0.5, 0.0]

    IsTogglable = true
    RadioButtonManager = <RadioButtonManager>
    CannotDeselect = true

    ButtonMagnifiableWidthHeight = [300.0, 35.0]
    TextTypefaceToken = "UISecondFont"
    TextStyle = <TextStyle>
    TextColorToken = <TextColorToken>
    BorderLineColorToken = <BorderLineColorToken>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>
    HasBackground = true

    TextSizeToken = "28"
    BorderThicknessToken = "3"

    TextPadding = TRTTILength4( Magnifiable = [10.0, 0.0, 10.0, 0.0] )
    BigLineAction = ~/BigLineAction/ResizeFont

    LeftClickSound = ClickSound_Add_Card
    HoverSound = ClickSound_Change_Selector

    Mapping = <Mapping>
)

//-------------------------------------------------------------------------------------

private DebriefLevelProgressBar is BUCKListDescriptor
(
    ElementName = "DebriefLevelProgressBar"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [450.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FirstMargin = TRTTILength ( Magnifiable = 20)
    InterItemMargin = TRTTILength( Magnifiable = 25.0 )
    LastMargin = TRTTILength( Magnifiable = 0.0 )
    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelPlayerName
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelLevel
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelXPBarComponent
        ),
    ]
)

DebriefLevelPlayerName is BUCKTextDescriptor
(
    ElementName = "DebriefLevelPlayerName"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 27.0]
        RelativeWidthHeight = [0.8, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )
    ParagraphStyle = CenteredParagraphStyle
    TextStyle = "Default"
    HorizontalFitStyle = ~/FitStyle/UserDefined
    VerticalFitStyle = ~/FitStyle/UserDefined
    TypefaceToken = "Liberator"
    BigLineAction = ~/BigLineAction/CutByDots
    TextToken = ""
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextColor = "LDHintSolo_fond"
    TextSize = "46"
)

DebriefLevelLevel is BUCKTextDescriptor
(
    ElementName = "DebriefLevelLevel"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 20.0]
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )
    ParagraphStyle = paragraphStyleTextCenter
    TextStyle = "Default"
    HorizontalFitStyle = ~/FitStyle/UserDefined
    VerticalFitStyle = ~/FitStyle/UserDefined
    TypefaceToken = "Liberator"
    BigLineAction = ~/BigLineAction/MultiLine
    TextToken = "EG_Level"
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextColor = "LDHintSolo_fond"
    TextSize = "32"
)

DebriefLevelXPBarComponent is BUCKListDescriptor
(
    ElementName = "DebriefLevelXPBarComponent"

    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]

    )
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
    InterItemMargin = TRTTILength( Magnifiable = 1.0 )
    LastMargin = TRTTILength( Magnifiable = 0.0 )
    Axis = ~/ListAxis/Vertical
    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelXPBarTextUp
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelXPGauge
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelXPBarXpGained
        ),
    ]
)

DebriefLevelXPGaugeMax is 400.0
DebriefLevelXPGauge is BUCKGaugeDescriptor
(
    ElementName = "DebriefLevelXPGauge"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [DebriefLevelXPGaugeMax, 23.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    GaugeMax = DebriefLevelXPGaugeMax

    HasBackground = true
    BackgroundBlockColorToken = "LDHintSolo_fond"

    GaugeComponentNames =
    [
        "DebriefLevelXPGaugeProgressionValue",
        "DebriefLevelXPGaugeProgressionGainValue",
    ]

    Components =
    [
        BUCKGaugeValueDescriptor
        (
            ElementName = "DebriefLevelXPGaugeProgressionGainValue"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight    = [0.0, 1.0]
                AlignementToFather     = [0.0, 0.5]
                AlignementToAnchor     = [0.0, 0.5]
            )

            HasBackground = true
            BackgroundBlockColorToken = ""
        ),

        BUCKGaugeValueDescriptor
        (
            ElementName = "DebriefLevelXPGaugeProgressionValue"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight    = [0.0, 1.0]
                AlignementToFather     = [0.0, 0.5]
                AlignementToAnchor     = [0.0, 0.5]
            )

            HasBackground = true
            BackgroundBlockColorToken = ""
        ),
    ]
)

DebriefLevelXPBarTextUp is BUCKListDescriptor
(
    ElementName = "DebriefLevelXPBarTextUp"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 20.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FirstMargin = TRTTILength( Magnifiable = 10.0 )
    LastMargin = TRTTILength( Magnifiable = 10.0 )
    Axis = ~/ListAxis/Horizontal

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelXPBarPlayerScore
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKContainerDescriptor ( ComponentFrame = TUIFramePropertyRTTI ())
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelXPBarLevelThreshold
        ),
    ]
)

DebriefLevelXPBarPlayerScore is BUCKTextDescriptor
(
    ElementName = "DebriefLevelXPBarPlayerScore"

    ComponentFrame = TUIFramePropertyRTTI()
    ParagraphStyle = paragraphStyleTextCenter
    TextStyle = "Default"
    HorizontalFitStyle = ~/FitStyle/FitToContent
    VerticalFitStyle = ~/FitStyle/FitToContent
    TypefaceToken = "Liberator"
    BigLineAction = ~/BigLineAction/MultiLine
    TextToken = "EG_SBTE"
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextColor = "LDHintSolo_fond"
    TextSize = "20"
)

DebriefLevelXPBarLevelThreshold is BUCKTextDescriptor
(
    ElementName = "DebriefLevelXPBarLevelThreshold"

    ComponentFrame = TUIFramePropertyRTTI()
    ParagraphStyle = paragraphStyleTextCenter
    TextStyle = "Default"
    HorizontalFitStyle = ~/FitStyle/FitToContent
    VerticalFitStyle = ~/FitStyle/FitToContent
    TypefaceToken = "Liberator"
    BigLineAction = ~/BigLineAction/MultiLine
    TextToken = "EG_SBTE"
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextColor = "LDHintSolo_fond"
    TextSize = "20"
)

DebriefLevelXPBarXpGained is BUCKTextDescriptor
(
    ElementName = "DebriefLevelXPBarXpGained"

    ComponentFrame = TUIFramePropertyRTTI()
    ParagraphStyle = paragraphStyleTextCenter
    TextStyle = "Default"
    HorizontalFitStyle = ~/FitStyle/FitToContent
    VerticalFitStyle = ~/FitStyle/FitToContent
    TypefaceToken = "Liberator"
    BigLineAction = ~/BigLineAction/MultiLine
    TextToken = "PLUSFORMAT"
    TextDico = ~/LocalisationConstantes/dico_interface_outgame
    TextColor = "LDHintSolo_fond"
    TextSize = "20"
)

//-------------------------------------------------------------------------------------

EndGameExplanationForTacticFromStrategic is BUCKListDescriptor
(
    ElementName = "TacticFromStrategicEndGameExplanationContainer"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )
    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "EndGameReasonText"
                ComponentFrame = TUIFramePropertyRTTI ( RelativeWidthHeight = [0.9, 0.0] )

                ParagraphStyle = ~/paragraphStyleTextLeftAlign
                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
                TypefaceToken = "Eurostyle_Medium"
                BigLineAction = ~/BigLineAction/MultiLine
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "gris_load"
                TextSize = "20"
                TextPadding = TRTTILength4( Magnifiable = [0.0, 0.0, 0.0, 6.0] )
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "EndGameVictoryTypeExplanationText"
                ComponentFrame = TUIFramePropertyRTTI ( RelativeWidthHeight = [0.9, 0.0] )

                ParagraphStyle = ~/paragraphStyleTextLeftAlign
                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
                TypefaceToken = "Eurostyle"
                BigLineAction = ~/BigLineAction/MultiLine
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "gris_load"
                TextSize = "20"
                TextPadding = TRTTILength4( Magnifiable = [0.0, 0.0, 0.0, 6.0] )
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI ( RelativeWidthHeight = [0.9, 0.0] )

                ParagraphStyle = ~/paragraphStyleTextLeftAlign
                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
                TypefaceToken = "Eurostyle_Medium"
                BigLineAction = ~/BigLineAction/MultiLine
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextToken = "EG_TFSCon"
                TextColor = "gris_load"

                TextSize = "20"
                TextPadding = TRTTILength4( Magnifiable = [0.0, 16.0, 0.0, 8.0] )
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "EndGameConsequenceOurForceText"
                ComponentFrame = TUIFramePropertyRTTI ( RelativeWidthHeight = [0.9, 0.0] )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0.4
                )
                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
                TypefaceToken = "Eurostyle"
                BigLineAction = ~/BigLineAction/MultiLine
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "gris_load"

                TextSize = "20"
                TextPadding = TRTTILength4( Magnifiable = [0.0, 0.0, 0.0, 8.0] )
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "EndGameConsequenceEnemiesForceText"
                ComponentFrame = TUIFramePropertyRTTI ( RelativeWidthHeight = [0.9, 0.0] )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0.4
                )
                TextStyle = "Default"
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/FitToContent
                TypefaceToken = "Eurostyle"
                BigLineAction = ~/BigLineAction/MultiLine
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "gris_load"
                TextSize = "20"
                TextPadding = TRTTILength4( Magnifiable = [0.0, 0.0, 0.0, 32.0] )
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

template DebriefHeader
[
    TextToken : string,
    ElementName : string,
    RelativeWidthHeight : float2 = [1.0, 0.0]
] is BUCKContainerDescriptor
(
    ElementName = <ElementName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = <RelativeWidthHeight>
        MagnifiableWidthHeight = [0.0, ~/DebriefHeaderLineHeight]
    )

    HasBackground = true
    BackgroundBlockColorToken = "ListeExcel/Cartouche"

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = <ElementName> + "Text"
            TextToken = <TextToken>
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            TextStyle = "Default"

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                InterLine = 0
            )

            TypefaceToken = TacticalEndgameTitleFont
            TextDico = ~/LocalisationConstantes/dico_interface_outgame
            TextSize = TacticalEndgameTitleFontSize
            TextColor = "ListeExcel/Cartouche"
        ),
    ]
)

//-------------------------------------------------------------------------------------

private TacticalEndGameResultComponent is BUCKListDescriptor
(
    ElementName = "TacticalEndGameResultComponent"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1466.0, 0.0]
        MagnifiableOffset = [400.0, -45.0]
    )

    InterItemMargin = TRTTILength( Magnifiable = 6.0 )
    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "TeamResult"
                ComponentFrame = TUIFramePropertyRTTI ()

                ParagraphStyle = ~/paragraphStyleTextLeftAlign
                TextStyle = "EGWin"
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent
                TypefaceToken = "Eurostyle_Heavy"
                BigLineAction = ~/BigLineAction/MultiLine
                TextToken = ""
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "BlancTexte"
                TextSize = "60"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "VictoryResult"

                ComponentFrame = TUIFramePropertyRTTI ()

                ParagraphStyle = ~/paragraphStyleTextLeftAlign
                TextStyle = "EGWin2"
                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent
                TypefaceToken = "Eurostyle"
                BigLineAction = ~/BigLineAction/MultiLine
                TextToken = ""
                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextColor = "BlancTexte"
                TextSize = "60"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                )
                Axis = ~/ListAxis/Horizontal
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
                InterItemMargin = TRTTILength(Magnifiable = 5.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "VictoryExplanation"

                            ComponentFrame = TUIFramePropertyRTTI ()

                            ParagraphStyle = ~/paragraphStyleTextLeftAlign
                            TextStyle = "EGWin2"
                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent
                            TypefaceToken = "Eurostyle"
                            BigLineAction = ~/BigLineAction/MultiLine
                            TextToken = ""
                            TextDico = ~/LocalisationConstantes/dico_interface_outgame
                            TextColor = "BlancTexte"
                            TextSize = "20"
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "FinalScore"

                            ComponentFrame = TUIFramePropertyRTTI ()

                            ParagraphStyle = ~/paragraphStyleTextLeftAlign
                            TextStyle = "EGWin2"
                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent
                            TypefaceToken = "Eurostyle"
                            BigLineAction = ~/BigLineAction/MultiLine
                            TextToken = "EG_score"
                            TextDico = ~/LocalisationConstantes/dico_interface_outgame
                            TextColor = "BlancTexte"
                            TextSize = "20"
                        )
                    )
                ]
            )
        ),
        // elapsed time
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI ()

                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
                InterItemMargin = TRTTILength( Magnifiable = 15.0 )

                Axis = ~/ListAxis/Horizontal
                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ComponentFrame = TUIFramePropertyRTTI ()

                                ParagraphStyle = ~/paragraphStyleTextLeftAlign
                                TextStyle = "Default"
                                HorizontalFitStyle = ~/FitStyle/FitToContent
                                VerticalFitStyle = ~/FitStyle/FitToContent
                                TypefaceToken = "Eurostyle"
                                BigLineAction = ~/BigLineAction/MultiLine
                                TextToken = "EG_ELAPSED"
                                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                                TextColor = "gris_load"
                                TextSize = "20"
                            )
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "Time"
                            ComponentFrame = TUIFramePropertyRTTI ()

                            ParagraphStyle = ~/paragraphStyleTextLeftAlign
                            TextStyle = "Default"
                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent
                            TypefaceToken = "Eurostyle"
                            BigLineAction = ~/BigLineAction/MultiLine

                            TextDico = ~/LocalisationConstantes/dico_interface_outgame
                            TextColor = "gris_load"
                            TextSize = "20"
                        )
                    ),
                ]
            )
        ),
        // battlefield
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI ()

                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                Axis = ~/ListAxis/Horizontal
                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKTextDescriptor
                            (
                                ElementName = "MapNameTitle"
                                ComponentFrame = TUIFramePropertyRTTI()

                                ParagraphStyle = ~/paragraphStyleTextLeftAlign
                                TextStyle = "Default"
                                HorizontalFitStyle = ~/FitStyle/FitToContent
                                VerticalFitStyle = ~/FitStyle/FitToContent
                                TypefaceToken = "Eurostyle"
                                BigLineAction = ~/BigLineAction/MultiLine
                                TextToken = "GS_BATTLEF"
                                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                                TextColor = "gris_load"
                                TextSize = "20"
                                TextPadding = TRTTILength4(Magnifiable = [0.0, 0.0, 15.0, 0.0])
                            )
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "MapName"
                            ComponentFrame = TUIFramePropertyRTTI()

                            ParagraphStyle = ~/paragraphStyleTextLeftAlign
                            TextStyle = "Default"
                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent
                            TypefaceToken = "Eurostyle"
                            BigLineAction = ~/BigLineAction/MultiLine

                            TextDico = ~/LocalisationConstantes/dico_maps
                            TextColor = "gris_load"
                            TextSize = "20"
                        )
                    ),
                ]
            )
        ),
        // Challenge Difficulty
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKListDescriptor
            (
                ElementName = "ChallengeDifficultyElement"
                ComponentFrame = TUIFramePropertyRTTI()

                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                Axis = ~/ListAxis/Horizontal
                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "ChallengeDifficultyTitle"
                            ComponentFrame = TUIFramePropertyRTTI()

                            ParagraphStyle = ~/paragraphStyleTextLeftAlign
                            TextStyle = "Default"
                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent
                            TypefaceToken = "Eurostyle"
                            BigLineAction = ~/BigLineAction/MultiLine
                            TextToken = "OP_GM_N"
                            TextDico = ~/LocalisationConstantes/dico_interface_outgame
                            TextColor = "gris_load"
                            TextSize = "20"
                            TextPadding = TRTTILength4(Magnifiable = [0.0, 0.0, 15.0, 0.0])
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextDescriptor
                        (
                            ElementName = "ChallengeDifficultyName"
                            ComponentFrame = TUIFramePropertyRTTI()

                            ParagraphStyle = ~/paragraphStyleTextLeftAlign
                            TextStyle = "Default"
                            HorizontalFitStyle = ~/FitStyle/FitToContent
                            VerticalFitStyle = ~/FitStyle/FitToContent
                            TypefaceToken = "Eurostyle"
                            BigLineAction = ~/BigLineAction/MultiLine
                            TextDico = ~/LocalisationConstantes/dico_interface_outgame
                            TextColor = "gris_load"
                            TextSize = "20"
                            TextPadding = TRTTILength4(Magnifiable = [0.0, 0.0, 15.0, 0.0])
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKTextureDescriptor
                        (
                            ElementName = "ChallengeDifficultyTexture"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                AlignementToFather = [0.0, 0.5]
                                AlignementToAnchor = [0.0, 0.5]
                            )
                            TextureFrame = TUIFramePropertyRTTI()
                            ResizeMode = ~/TextureResizeMode/FitToContent
                            TextureColorToken = "gris_load"
                        )
                    ),
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

template TabTitle
[
    FirstColumnToken,
    SecondColumnToken,
]
is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, ~/DebriefKillLineHeight]
    )

    Components =
    [
        BUCKTextDescriptor
        (
            TextToken = <FirstColumnToken>
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.5, 1.0]
            )

            TextStyle = "Default"

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                InterLine = 0
            )

            TypefaceToken = TacticalEndgameTitleFont
            TextDico = ~/LocalisationConstantes/dico_interface_outgame
            TextSize = TacticalEndgameTitleFontSize
            TextColor = "GrisFoncePapier"
        ),

        BUCKTextDescriptor
        (
            TextToken = <SecondColumnToken>
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.5, 1.0]
                AlignementToFather = [0.5, 0.0]
            )

            TextStyle = "Default"

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                InterLine = 0
            )

            TypefaceToken = TacticalEndgameTitleFont
            TextDico = ~/LocalisationConstantes/dico_interface_outgame
            TextSize = TacticalEndgameTitleFontSize
            TextColor = "GrisFoncePapier"
        )
    ]
)

private KillTabTitle is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, ~/DebriefKillLineHeight]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                AlignementToFather = [0.0, 0.0]
            )

            Components =
            [
                BUCKContainerDescriptor is TabTitle
                (
                    FirstColumnToken = "EG_SHOOT"
                    SecondColumnToken = "EG_VICTIM"
                )
            ]
        )
    ]
)

private HistoryTabTitle is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, ~/DebriefKillLineHeight]
    )

    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
            )

            Axis = ~/ListAxis/Horizontal
            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromFrameProperty

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        TextToken = "EG_TIME"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [0.0, 1.0]
                            MagnifiableWidthHeight = [~/DebriefTimeWidth, 0.0]
                        )

                        TextStyle = "Default"

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_VerticalCenter
                            InterLine = 0
                        )

                        TypefaceToken = TacticalEndgameTitleFont
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextSize = TacticalEndgameTitleFontSize
                        TextColor = "GrisFoncePapier"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = TabTitle
                    (
                        FirstColumnToken = "EG_SHOOT"
                        SecondColumnToken = "EG_VICTIM"
                    )
                )
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

EngamePanelTab is BUCKContainerDescriptor
(
    ElementName = "EndGamePanelGameTab"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        // fond pour traces sur board
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [1525.0, 1026.0]
                AlignementToAnchor = [1.0, 0.0]
                AlignementToFather = [1.0, 0.0]
                MagnifiableOffset = [0.0, -52.0]
            )
            TextureToken = "Outgame_fond_EndGame_foreground"
        ),

        // Challenge Icon
        BUCKTextureDescriptor
        (
            ElementName = "ChallengeIcon"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [200.0, 200.0]
                AlignementToAnchor = [1.0, 0.0]
                AlignementToFather = [1.0, 0.0]
                MagnifiableOffset = [-31.0, -40.0]
            )

            TextureFrame = TUIFramePropertyRTTI()

            ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
        ),
        // Top header (win/loss, elapsed time, map, etc.)
        ~/TacticalEndGameResultComponent,

        // TV screen
        ~/EndgameTVScreenDisplay,

        // scrolls
        ~/TacticalEndgameTabsContainer,
    ]
)

//-------------------------------------------------------------------------------------

HistoricalOutcome is BUCKTextDescriptor
(
    ElementName = "HistoricalOutcome"

    ComponentFrame = TUIFramePropertyRTTI()

    TextStyle = "Default"

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Left
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0.3
    )

    BigLineAction = ~/BigLineAction/MultiLine

    HorizontalFitStyle = ~/FitStyle/FitToParent
    VerticalFitStyle = ~/FitStyle/FitToContent
    TypefaceToken = "Eurostyle"

    TextDico = ~/LocalisationConstantes/dico_maps

    TextColor = "gris_load"
    TextSize = "Debrief/UpdateProfile"
)

//-------------------------------------------------------------------------------------

TacticalEndgameTabsContainer is BUCKSpecificScrollingContainerDescriptor
(
    ElementName = "TacticalEndgameTabsContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1176.0, 770.0]
        MagnifiableOffset = [690.0, 210.0]
    )

    HasVerticalScrollbar = true
    VerticalScrollbarComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [10.0, 0.0]
        MagnifiableOffset = [15.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
    )

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "TacticalEndgameTabsContainerList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical
            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/HistoricalOutcome
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/TacticalEndgameScoreDetails
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/TacticalEndgameXPDetails
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/TacticalEndgameProfileDetails
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/TacticalEndgameKillsDetails
                ),
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------

TacticalEndgameScoreDetails is BUCKListDescriptor
(
    ElementName = "TacticalEndgameScoreDetails"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1176.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/EndGameExplanationForTacticFromStrategic
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefHeader
            (
                ElementName = "EndGamePanelScoreByTeamTitle"
                TextToken = "EG_SBTT"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKRackDescriptor
            (
                ElementName = "ScoreByTeamRack"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )

                Axis = ~/ListAxis/Vertical

                BladeDescriptor = ~/TeamStatContainer
            )
        ),
    ]
)

TacticalEndgameXPDetails is BUCKListDescriptor
(
    ElementName = "TacticalEndgameXPDetails"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    InterItemMargin = TRTTILength( Magnifiable = 7.0 )

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefHeader
            (
                ElementName = "EndGamePanelDebriefXPHeader"
                TextToken = "EG_xpd"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/DebriefXpTitleLine
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "Time"
                HeaderToken = "EG_ELAPSED"
                ModificatorToken = "PLUSFORMAT"
                ValueToken = "TMINFORMAT"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "VictoryLevel"
                HeaderToken = "EG_VICTLVL"
                ModificatorToken = "MULTFORMAT"
                ValueToken = ""
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = PlainSeparator
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "BaseXp"
                HeaderToken = "EG_BASEXP"
                ModificatorToken = "VALUFORMAT"
                ValueToken = ""
                VerticalAlignment = ~/UIText_Up
                MagnifiableHeight = 48.0
                TextSize = "28"
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "WinStreak"
                HeaderToken = "EG_WINSTRK"
                ModificatorToken = "MULTFORMAT"
                ValueToken = "VALUFORMAT"
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefStandardText
            (
                ElementName = "WinStreakAdditionalInfoLegend"
                TextSize = "16"
                VerticalAlignment = UIText_Up
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableOffset = [XpLeftOffset, 0.0]
                    MagnifiableWidthHeight = [XpFirstColumnWidth, 30.0]
                )
                TextToken = "EG_WINS_BO"
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "Conquest"
                HeaderToken = "EG_CONQUES"
                ModificatorToken = "PLUSFORMAT"
                ValueToken = "FPERFORMAT"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "Kills"
                HeaderToken = "EG_KILLSB"
                ModificatorToken = "PLUSFORMAT"
                ValueToken = "FPERFORMAT"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "Losses"
                HeaderToken = "EG_LOSSESB"
                ModificatorToken = "PLUSFORMAT"
                ValueToken = "FPERFORMAT"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = PlainSeparator
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "GameXp"
                HeaderToken = "EG_TOTGXP"
                ModificatorToken = "VALUFORMAT"
                ValueToken = ""
                VerticalAlignment = ~/UIText_Up
                MagnifiableHeight = 48.0
                TextSize = "28"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "GameModifier"
                HeaderToken = "EG_GAMEM"
                ModificatorToken = "MULTFORMAT"
                ValueToken = "VALUFORMAT"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = PlainSeparator
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefXpLine
            (
                LineName = "TotalXp"
                HeaderToken = "EG_TOTXP"
                ModificatorToken = "VALUFORMAT"
                ValueToken = ""
                VerticalAlignment = ~/UIText_Up
                MagnifiableHeight = 48.0
                TextSize = "28"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefLevelProgressBar
        ),
    ]
)

//-------------------------------------------------------------------------------------

TacticalEndgameProfileDetails is BUCKListDescriptor
(
    ElementName = "TacticalEndgameProfileDetails"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    InterItemMargin = TRTTILength( Magnifiable = 20.0 )

    Elements =
    [
        //-------------------------------------------------------------------------------------
        // common data (apm etc..)
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefHeader
            (
                ElementName = "EndGamePanelUpdateProfileCommon"
                TextToken = "EG_UPDPRC"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = UpdateProfileCommonData
        ),
        //-------------------------------------------------------------------------------------
        // specific update title (skirmish, challenge...)
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefHeader
            (
                ElementName = "EndGamePanelUpdateProfile"
                TextToken = "EG_UPDPRS"
            )
        ),
        //-------------------------------------------------------------------------------------
        // skirmish data
        BUCKListElementDescriptor
        (
            ComponentDescriptor = UpdateProfileSkirmish
        ),
        //-------------------------------------------------------------------------------------
        // challenge data
        BUCKListElementDescriptor
        (
            ComponentDescriptor = UpdateProfileChallenge
        ),
    ]
)

TacticalEndgameKillsDetails is BUCKListDescriptor
(
    ElementName = "TacticalEndgameKillsDetails"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    Elements =
    [
        //-------------------------------------------------------------------------------------
        // Kills
        BUCKListElementSpacer( Magnifiable = 25.0),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefHeader
            (
                ElementName = "EndGamePanelKillsHeader"
                TextToken = "EG_MKILLS"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/KillTabTitle
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ElementName = "EndGamePanelKillsTab"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )
                FitStyle = ~/ContainerFitStyle/FitToContentVertically
                Components =
                [
                    DebriefKillRack
                    (
                        ElementName = "EndGamePanelKills"
                        DisplayChronology = false
                        LeftUnitName = "Left"
                        RightUnitName = "Right"
                    )
                ]
            )
        ),
        //-------------------------------------------------------------------------------------
        // Losses
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefHeader
            (
                ElementName = "EndGamePanelLossesHeader"
                TextToken = "EG_MLOSSES"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/KillTabTitle
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ElementName = "EndGamePanelLossesTab"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )
                FitStyle = ~/ContainerFitStyle/FitToContentVertically
                Components =
                [
                    DebriefKillRack
                    (
                        ElementName = "EndGamePanelLosses"
                        DisplayChronology = false
                        LeftUnitName = "Left"
                        RightUnitName = "Right"
                    )
                ]
            )
        ),
        //-------------------------------------------------------------------------------------
        // History
        BUCKListElementSpacer( Magnifiable = 25.0),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefHeader
            (
                ElementName = "EndGamePanelHistoryHeader"
                TextToken = "EG_MHIST"
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/HistoryTabTitle
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ElementName = "EndGamePanelHistTab"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                )
                FitStyle = ~/ContainerFitStyle/FitToContentVertically
                Components =
                [
                    DebriefKillRack
                    (
                        ElementName = "EndGamePanelHist"
                        DisplayChronology = true
                        LeftUnitName = "Left"
                        RightUnitName = "Right"
                    )
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

template DebriefProfileUpdateLine
[
    LineName : string,
    TextToken : string,
    ValueToken : string
]
is BUCKListDescriptor
(
    ElementName = "DebriefProfileUpdateLine" + <LineName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 24.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Horizontal
    FirstMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.5
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = <LineName> + "Text"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                TextStyle = "Default"

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Right
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/UserDefined
                BigLineAction = ~/BigLineAction/CutByDots
                // TypefaceToken = TacticalEndgameTitleFont
                TypefaceToken = "Eurostyle"


                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextToken  = <TextToken>

                TextColor = "gris_load"
                TextSize = "18"
            )
        ),
        BUCKListElementSpacer( Magnifiable = 20.0 ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 0.5
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = <LineName> + "Value"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                TextStyle = "Default"

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/UserDefined
                BigLineAction = ~/BigLineAction/CutByDots
                // TypefaceToken = TacticalEndgameTitleFont
                TypefaceToken = "Eurostyle"


                TextDico = ~/LocalisationConstantes/dico_interface_outgame
                TextToken  = <ValueToken>

                TextColor = "gris_load"
                TextSize = "18"
            )
        ),
    ]
)

private UpdateProfileSkirmishData is BUCKContainerDescriptor
(
    ElementName = "UpdateProfileSkirmishDataContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "UpdateProfileSkirmishData"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical

            InterItemMargin = TRTTILength( Magnifiable = 3.0 )

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileSkirmishDataXp"
                        TextToken = "EG_UPSDX"
                        ValueToken = "EG_UPSG"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileSkirmishDataTime"
                        TextToken = "EG_UPSDT"
                        ValueToken = "EG_UPST"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileSkirmishDataGames"
                        TextToken = "EG_UPSDG"
                        ValueToken = "EG_UPSG"
                    )
                ),
            ]
        )
    ]
)

private UpdateProfileSkirmishVictory is BUCKContainerDescriptor
(
    ElementName = "UpdateProfileSkirmishVictoryContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "UpdateProfileSkirmishVictory"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical

            InterItemMargin = TRTTILength( Magnifiable = 3.0 )

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileSkirmishVictoryNumber"
                        TextToken = "EG_UPSVN"
                        ValueToken = "EG_UPSS"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileSkirmishVictoryAgainst"
                        TextToken = "EG_UPSVA"
                        ValueToken = "EG_UPSG"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileSkirmishVictoryRatio"
                        TextToken = "EG_UPSVR"
                        ValueToken = "EG_UPSPct"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileSkirmishVictoryWith"
                        TextToken = "EG_UPSVW"
                        ValueToken = "EG_UPSG"
                    )
                ),
            ]
        )
    ]
)

private UpdateProfileChallengeData is BUCKContainerDescriptor
(
    ElementName = "UpdateProfileChallengeDataContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "UpdateProfileChallengeData"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical

            InterItemMargin = TRTTILength( Magnifiable = 3.0 )

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileChallengeDataXp"
                        TextToken = "EG_UPCDX"
                        ValueToken = "EG_UPSG"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileChallengeDataTime"
                        TextToken = "EG_UPCDT"
                        ValueToken = "EG_UPST"
                    )
                ),
            ]
        )
    ]
)

private UpdateProfileChallengeVictory is BUCKContainerDescriptor
(
    ElementName = "UpdateProfileChallengeVictoryContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "UpdateProfileChallengeVictory"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )

            Axis = ~/ListAxis/Vertical

            InterItemMargin = TRTTILength( Magnifiable = 3.0 )

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileChallengeVictoryNumber"
                        TextToken = "EG_UPCVN"
                        ValueToken = "EG_UPSS"
                    )
                ),
            ]
        )
    ]
)

private UpdateProfileCommonData is BUCKContainerDescriptor
(
    ElementName = "UpdateProfileRecapContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "UpdateProfileRecap"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            Axis = ~/ListAxis/Vertical

            InterItemMargin = TRTTILength( Magnifiable = 3.0 )

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileRecapAPM"
                        TextToken = "EG_UPRA"
                        ValueToken = "EG_UPStd"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileRecapUnit"
                        TextToken = "EG_UPRU"
                        ValueToken = "EG_UPSV"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileRecapContribution"
                        TextToken = "EG_UPRC"
                        ValueToken = "EG_UPSR"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileRecapRatio"
                        TextToken = "EG_UPRR"
                        ValueToken = "EG_UPSRP"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = DebriefProfileUpdateLine
                    (
                        LineName = "UpdateProfileRecapBest"
                        TextToken = "EG_UPRB"
                        ValueToken = "EG_UPSPts"
                    )
                ),
            ]
        )
    ]
)

private UpdateProfileSkirmish is BUCKListDescriptor
(
    ElementName = "UpdateProfileSkirmish"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    InterItemMargin = TRTTILength( Magnifiable = 20.0 )

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = UpdateProfileSkirmishData
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = UpdateProfileSkirmishVictory
        ),
    ]
)

private UpdateProfileChallenge is BUCKListDescriptor
(
    ElementName = "UpdateProfileChallenge"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1176.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    InterItemMargin = TRTTILength( Magnifiable = 3.0 )

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = UpdateProfileChallengeData
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = UpdateProfileChallengeVictory
        ),
    ]
)

//-------------------------------------------------------------------------------------
//-------------------------------------------------------------------------------------

ScoreByTeamTitle is BUCKContainerDescriptor
(
    ElementName = "ScoreByTeamTitle"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, ~/DebriefHeaderLineHeight]
    )

    HasBackground = true
    BackgroundBlockColorToken = "UIPlayerSpecific/Otan"

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "ScoreByTeamTitleName"
            TextToken = "TWOVALFORM"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                AlignementToFather = [0.0, 0.5]
                AlignementToAnchor = [0.0, 0.5]
            )

            TextStyle = "Default"

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Left
                VerticalAlignment = UIText_VerticalCenter
                InterLine = 0
            )

            TypefaceToken = TacticalEndgameTitleFont
            TextDico = ~/LocalisationConstantes/dico_interface_outgame
            TextSize = TacticalEndgameTitleFontSize
            TextColor = "ListeExcel/Cartouche"
            TextPadding = TRTTILength4(Magnifiable = [10.0, 0.0, 0.0, 0.0])
        ),
        BUCKTextDescriptor
        (
            ElementName = "ScoreByTeamTitleVictoryResult"
            TextToken = "EG_SBTS"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            TextStyle = "Default"

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                InterLine = 0
            )

            TypefaceToken = TacticalEndgameTitleFont
            TextDico = ~/LocalisationConstantes/dico_interface_outgame
            TextSize = TacticalEndgameTitleFontSize
            TextColor = "ListeExcel/Cartouche"
        ),
    ]
)

TeamStatContainer is BUCKListDescriptor
(
    ElementName = "ScoreByTeam"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [1176.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/ScoreByTeamTitle
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DebriefTeamStatsMultiList()
        ),
    ]
)

//-------------------------------------------------------------------------------------

EndgameDefeatAnimationTextureList is ["OTAN_DEFEAT000", "OTAN_DEFEAT001", "OTAN_DEFEAT002", "OTAN_DEFEAT003", "OTAN_DEFEAT004", "OTAN_DEFEAT005", "OTAN_DEFEAT006", "OTAN_DEFEAT007", "OTAN_DEFEAT008", "OTAN_DEFEAT009", "OTAN_DEFEAT010", "OTAN_DEFEAT011", "OTAN_DEFEAT012", "OTAN_DEFEAT013", "OTAN_DEFEAT014", "OTAN_DEFEAT015", "OTAN_DEFEAT016", "OTAN_DEFEAT017", "OTAN_DEFEAT018", "OTAN_DEFEAT019", "OTAN_DEFEAT020", "OTAN_DEFEAT021", "OTAN_DEFEAT022", "OTAN_DEFEAT023", "OTAN_DEFEAT024", "OTAN_DEFEAT025", "OTAN_DEFEAT026", "OTAN_DEFEAT027", "OTAN_DEFEAT028", "OTAN_DEFEAT029", "OTAN_DEFEAT030", "OTAN_DEFEAT031", "OTAN_DEFEAT032", "OTAN_DEFEAT033", "OTAN_DEFEAT034", "OTAN_DEFEAT035", "OTAN_DEFEAT036", "OTAN_DEFEAT037", "OTAN_DEFEAT038", "OTAN_DEFEAT039", "OTAN_DEFEAT040", "OTAN_DEFEAT041", "OTAN_DEFEAT042", "OTAN_DEFEAT043", "OTAN_DEFEAT044", "OTAN_DEFEAT045", "OTAN_DEFEAT046", "OTAN_DEFEAT047", "OTAN_DEFEAT048", "OTAN_DEFEAT049", "OTAN_DEFEAT050", "OTAN_DEFEAT051", "OTAN_DEFEAT052", "OTAN_DEFEAT053", "OTAN_DEFEAT054", "OTAN_DEFEAT055", "OTAN_DEFEAT056", "OTAN_DEFEAT057", "OTAN_DEFEAT058", "OTAN_DEFEAT059", "OTAN_DEFEAT060", "OTAN_DEFEAT061", "OTAN_DEFEAT062", "OTAN_DEFEAT063", "OTAN_DEFEAT064", "OTAN_DEFEAT065", "OTAN_DEFEAT066", "OTAN_DEFEAT067", "OTAN_DEFEAT068", "OTAN_DEFEAT069", "OTAN_DEFEAT070", "OTAN_DEFEAT071", "OTAN_DEFEAT072", "OTAN_DEFEAT073", "OTAN_DEFEAT074", "OTAN_DEFEAT075", "OTAN_DEFEAT076", "OTAN_DEFEAT077", "OTAN_DEFEAT078", "OTAN_DEFEAT079", "OTAN_DEFEAT080", "OTAN_DEFEAT081", "OTAN_DEFEAT082", "OTAN_DEFEAT083", "OTAN_DEFEAT084", "OTAN_DEFEAT085", "OTAN_DEFEAT086", "OTAN_DEFEAT087", "OTAN_DEFEAT088", "OTAN_DEFEAT089", "OTAN_DEFEAT090", "OTAN_DEFEAT091", "OTAN_DEFEAT092", "OTAN_DEFEAT093", "OTAN_DEFEAT094", "OTAN_DEFEAT095", "OTAN_DEFEAT096", "OTAN_DEFEAT097", "OTAN_DEFEAT098", "OTAN_DEFEAT099", "OTAN_DEFEAT100", "OTAN_DEFEAT101", "OTAN_DEFEAT102", "OTAN_DEFEAT103", "OTAN_DEFEAT104", "OTAN_DEFEAT105", "OTAN_DEFEAT106", "OTAN_DEFEAT107", "OTAN_DEFEAT108", "OTAN_DEFEAT109", "OTAN_DEFEAT110", "OTAN_DEFEAT111", "OTAN_DEFEAT112", "OTAN_DEFEAT113", "OTAN_DEFEAT114", "OTAN_DEFEAT115", "OTAN_DEFEAT116", "OTAN_DEFEAT117", "OTAN_DEFEAT118", "OTAN_DEFEAT119", "OTAN_DEFEAT120", "OTAN_DEFEAT121", "OTAN_DEFEAT122", "OTAN_DEFEAT123", "OTAN_DEFEAT124", "OTAN_DEFEAT125", "OTAN_DEFEAT126", "OTAN_DEFEAT127", "OTAN_DEFEAT128", "OTAN_DEFEAT129", "OTAN_DEFEAT130", "OTAN_DEFEAT131", "OTAN_DEFEAT132", "OTAN_DEFEAT133", "OTAN_DEFEAT134", "OTAN_DEFEAT135", "OTAN_DEFEAT136", "OTAN_DEFEAT137", "OTAN_DEFEAT138", "OTAN_DEFEAT139", "OTAN_DEFEAT140", "OTAN_DEFEAT141", "OTAN_DEFEAT142", "OTAN_DEFEAT143", "OTAN_DEFEAT144", "OTAN_DEFEAT145", "OTAN_DEFEAT146", "OTAN_DEFEAT147", "OTAN_DEFEAT148", "OTAN_DEFEAT149", "OTAN_DEFEAT150", "OTAN_DEFEAT151", "OTAN_DEFEAT152", "OTAN_DEFEAT153", "OTAN_DEFEAT154", "OTAN_DEFEAT155", "OTAN_DEFEAT156", "OTAN_DEFEAT157", "OTAN_DEFEAT158", "OTAN_DEFEAT159", "OTAN_DEFEAT160", "OTAN_DEFEAT161", "OTAN_DEFEAT162", "OTAN_DEFEAT163", "OTAN_DEFEAT164", "OTAN_DEFEAT165", "OTAN_DEFEAT166", "OTAN_DEFEAT167", "OTAN_DEFEAT168", "OTAN_DEFEAT169", "OTAN_DEFEAT170", "OTAN_DEFEAT171", "OTAN_DEFEAT172", "OTAN_DEFEAT173", "OTAN_DEFEAT174", "OTAN_DEFEAT175", "OTAN_DEFEAT176", "OTAN_DEFEAT177", "OTAN_DEFEAT178", "OTAN_DEFEAT179", "OTAN_DEFEAT180"]
EndgameVictoryAnimationTextureList is ["OTAN_VICTORY000", "OTAN_VICTORY001", "OTAN_VICTORY002", "OTAN_VICTORY003", "OTAN_VICTORY004", "OTAN_VICTORY005", "OTAN_VICTORY006", "OTAN_VICTORY007", "OTAN_VICTORY008", "OTAN_VICTORY009", "OTAN_VICTORY010", "OTAN_VICTORY011", "OTAN_VICTORY012", "OTAN_VICTORY013", "OTAN_VICTORY014", "OTAN_VICTORY015", "OTAN_VICTORY016", "OTAN_VICTORY017", "OTAN_VICTORY018", "OTAN_VICTORY019", "OTAN_VICTORY020", "OTAN_VICTORY021", "OTAN_VICTORY022", "OTAN_VICTORY023", "OTAN_VICTORY024", "OTAN_VICTORY025", "OTAN_VICTORY026", "OTAN_VICTORY027", "OTAN_VICTORY028", "OTAN_VICTORY029", "OTAN_VICTORY030", "OTAN_VICTORY031", "OTAN_VICTORY032", "OTAN_VICTORY033", "OTAN_VICTORY034", "OTAN_VICTORY035", "OTAN_VICTORY036", "OTAN_VICTORY037", "OTAN_VICTORY038", "OTAN_VICTORY039", "OTAN_VICTORY040", "OTAN_VICTORY041", "OTAN_VICTORY042", "OTAN_VICTORY043", "OTAN_VICTORY044", "OTAN_VICTORY045", "OTAN_VICTORY046", "OTAN_VICTORY047", "OTAN_VICTORY048", "OTAN_VICTORY049", "OTAN_VICTORY050", "OTAN_VICTORY051", "OTAN_VICTORY052", "OTAN_VICTORY053", "OTAN_VICTORY054", "OTAN_VICTORY055", "OTAN_VICTORY056", "OTAN_VICTORY057", "OTAN_VICTORY058", "OTAN_VICTORY059", "OTAN_VICTORY060", "OTAN_VICTORY061", "OTAN_VICTORY062", "OTAN_VICTORY063", "OTAN_VICTORY064", "OTAN_VICTORY065", "OTAN_VICTORY066", "OTAN_VICTORY067", "OTAN_VICTORY068", "OTAN_VICTORY069", "OTAN_VICTORY070", "OTAN_VICTORY071", "OTAN_VICTORY072", "OTAN_VICTORY073", "OTAN_VICTORY074", "OTAN_VICTORY075", "OTAN_VICTORY076", "OTAN_VICTORY077", "OTAN_VICTORY078", "OTAN_VICTORY079", "OTAN_VICTORY080", "OTAN_VICTORY081", "OTAN_VICTORY082", "OTAN_VICTORY083", "OTAN_VICTORY084", "OTAN_VICTORY085", "OTAN_VICTORY086", "OTAN_VICTORY087", "OTAN_VICTORY088", "OTAN_VICTORY089", "OTAN_VICTORY090", "OTAN_VICTORY091", "OTAN_VICTORY092", "OTAN_VICTORY093", "OTAN_VICTORY094", "OTAN_VICTORY095", "OTAN_VICTORY096", "OTAN_VICTORY097", "OTAN_VICTORY098", "OTAN_VICTORY099", "OTAN_VICTORY100", "OTAN_VICTORY101", "OTAN_VICTORY102", "OTAN_VICTORY103", "OTAN_VICTORY104", "OTAN_VICTORY105", "OTAN_VICTORY106", "OTAN_VICTORY107", "OTAN_VICTORY108", "OTAN_VICTORY109", "OTAN_VICTORY110", "OTAN_VICTORY111", "OTAN_VICTORY112", "OTAN_VICTORY113", "OTAN_VICTORY114", "OTAN_VICTORY115", "OTAN_VICTORY116", "OTAN_VICTORY117", "OTAN_VICTORY118", "OTAN_VICTORY119", "OTAN_VICTORY120", "OTAN_VICTORY121", "OTAN_VICTORY122", "OTAN_VICTORY123", "OTAN_VICTORY124", "OTAN_VICTORY125", "OTAN_VICTORY126", "OTAN_VICTORY127", "OTAN_VICTORY128", "OTAN_VICTORY129", "OTAN_VICTORY130", "OTAN_VICTORY131", "OTAN_VICTORY132", "OTAN_VICTORY133", "OTAN_VICTORY134", "OTAN_VICTORY135", "OTAN_VICTORY136", "OTAN_VICTORY137", "OTAN_VICTORY138", "OTAN_VICTORY139", "OTAN_VICTORY140", "OTAN_VICTORY141", "OTAN_VICTORY142", "OTAN_VICTORY143", "OTAN_VICTORY144", "OTAN_VICTORY145", "OTAN_VICTORY146", "OTAN_VICTORY147", "OTAN_VICTORY148", "OTAN_VICTORY149", "OTAN_VICTORY150", "OTAN_VICTORY151", "OTAN_VICTORY152", "OTAN_VICTORY153", "OTAN_VICTORY154", "OTAN_VICTORY155", "OTAN_VICTORY156", "OTAN_VICTORY157", "OTAN_VICTORY158", "OTAN_VICTORY159", "OTAN_VICTORY160", "OTAN_VICTORY161", "OTAN_VICTORY162", "OTAN_VICTORY163", "OTAN_VICTORY164", "OTAN_VICTORY165", "OTAN_VICTORY166", "OTAN_VICTORY167", "OTAN_VICTORY168", "OTAN_VICTORY169", "OTAN_VICTORY170", "OTAN_VICTORY171", "OTAN_VICTORY172", "OTAN_VICTORY173", "OTAN_VICTORY174", "OTAN_VICTORY175", "OTAN_VICTORY176", "OTAN_VICTORY177", "OTAN_VICTORY178", "OTAN_VICTORY179", "OTAN_VICTORY180"]

EndgameResultAnimationComponent is BUCKContainerDescriptor
(
    ElementName = "EndgameAnimationContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )

    Components =
    [
        BUCKTextureAnimationDescriptor
        (
            ElementName = "EndgameAnimationTextureAnimation"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [1280.0, 800.0]
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
            )

            AnimDuration = 6
            AlphaAnimDuration = 0
        ),
        BUCKTranslationAnimatedContainerDescriptor
        (
            ElementName = "EndgameAnimationTextAnimation"
            FramePropertyBeforeAnimation = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [1,100]
                MagnifiableOffset = [0,-210]
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
            )

            AnimationTotalDuration = 4

            FramePropertyAfterAnimation = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [600,100]
                MagnifiableOffset = [0,-210]
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
            )

            Components =
            [
               BUCKTextDescriptor
                (
                    ElementName = "AnimatedText"
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1,1]
                        AlignementToFather = [0.5, 0.0]
                        AlignementToAnchor = [0.5, 0.0]
                    )

                    ParagraphStyle = TParagraphStyle
                    (
                        Alignment = UIText_Center
                        VerticalAlignment = UIText_VerticalCenter
                        InterLine = 0.5
                    )
                    TextStyle = "Default"

                    HorizontalFitStyle = ~/FitStyle/UserDefined
                    VerticalFitStyle = ~/FitStyle/UserDefined

                    TextPadding = TRTTILength4 (Magnifiable = [10.0, 0.0, 10.0, 0.0])

                    TypefaceToken = "Bombardier"
                    BigLineAction = ~/BigLineAction/Banner

                    TextColor =  "Blanc"
                    TextSize = "90"
                    TextDico = ~/LocalisationConstantes/dico_interface_ingame
                ),
            ]
        )
    ]
)

//-------------------------------------------------------------------------------------
BUCKSpecificTacticalEndGameMainComponentDescriptor is WindowFrame
(
    ContentRelativeWidthHeight = [1.0, 1.0]
    ContentExtendWeight = 1.0
    HasBackground = false

    ContentComponents =
    [
        BUCKTextureDescriptor
        (
            ElementName = "EndgameBackgroundTexture"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [1920.0, 1080.0]
                AlignementToAnchor = [0.0, 1.0]
                AlignementToFather = [0.0, 1.0]
            )
            TextureToken = "Outgame_fond_EndGame"
        ),
        ~/EngamePanelTab,
        ~/EndgameResultAnimationComponent
    ]
)

//-------------------------------------------------------------------------------------

UISpecificTacticalEndGameViewDescriptor is TUISpecificTacticalEndGameViewDescriptor
(
    MainComponentDescriptor = ~/BUCKSpecificTacticalEndGameMainComponentDescriptor
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    ExperienceTexturesResources     = ~/ExperienceTexturesResources

    AllianceStyleBlockColors = MAP
    [
        (EAllianceStyle/BLUFOR, "playerHelper/Otan_line_Endgame"),
        (EAllianceStyle/REDFOR, "playerHelper/Pact_line"),
    ]

    ForceDico = ~/LocalisationConstantes/dico_interface_outgame
    ForceTokens = MAP
    [
        (EAllianceStyle/BLUFOR, "BLUFOR"),
        (EAllianceStyle/REDFOR, "REDFOR"),
    ]

    CoalitionDico = ~/LocalisationConstantes/dico_interface_outgame
    CoalitionTokens = MAP
    [
        (ECoalition/OTAN, "NATO"),
        (ECoalition/PACT, "PACT"),
    ]

    IADico = ~/LocalisationConstantes/dico_interface_outgame
    IATokens =
    [
        "",         // Player
        "AB_AI_0",  // Very easy
        "AB_AI_1",  // Easy
        "AB_AI_2",  // Medium
        "AB_AI_3",  // Hard
        "AB_AI_4",  // Very hard
        "AB_AI_5",  // Hardest
    ]

    RatioDico = ~/LocalisationConstantes/dico_interface_outgame
    RatioThresholds = // du plus grand au plus petit, strictement décroissant, la dernière valeur est celle par défaut
    [
        3.0,
        2.0,
        1.0,
        0.5
    ]
    RatioTokens =
    [
        "EG_RVG",   // Very good
        "EG_RG",    // Good
        "EG_RNB",   // Not Bad
        "EG_RCBB",  // Could be better
    ]

    ContributionDico = ~/LocalisationConstantes/dico_interface_outgame
    ContributionThresholds = // du plus grand au plus petit, strictement décroissant, la dernière valeur est celle par défaut
    [
        0.8,
        0.6,
        0.4,
        0.1
    ]
    ContributionTokens =
    [
        "EG_RVG",   // Very good
        "EG_RG",    // Good
        "EG_RNB",   // Not Bad
        "EG_RCBB",  // Could be better
    ]

    WinLossDico = ~/LocalisationConstantes/dico_interface_outgame
    WinToken = "EG_won"
    LossToken = "EG_loss"
    DrawToken = "EG_draw"
    DrawExplanationToken = "EG_drawEx"
    DrawTimeReachedToken = "EG_drawTL"

    ChallengeVictoryToken = "EG_OPEVICT"
    ChallengeDefeatToken = "EG_OPEDEFE"

    WinTokenByGameEndingCondition = MAP
    [
        (~/EGameEndingCondition/ScoreVictory,           "EG_pointW"),
        (~/EGameEndingCondition/ScoreDefeat,            "EG_scDefW"),
        (~/EGameEndingCondition/PlayerDisconnected,     "EG_discW"),
        (~/EGameEndingCondition/Surrender,              "EG_surrW"),
        (~/EGameEndingCondition/NoMoreZone,             "EG_nozoneW"),
        (~/EGameEndingCondition/NoMoreProd,             "EG_noprodW"),
        (~/EGameEndingCondition/Time,                   "EG_scWTL"),
        (~/EGameEndingCondition/ScriptDecided ,         ""),
    ]

    LossTokenByGameEndingCondition = MAP
    [
        (~/EGameEndingCondition/ScoreVictory,           "EG_pointL"),
        (~/EGameEndingCondition/ScoreDefeat,            "EG_scDefL"),
        (~/EGameEndingCondition/PlayerDisconnected,     "EG_discL"),
        (~/EGameEndingCondition/Surrender,              "EG_surrL"),
        (~/EGameEndingCondition/NoMoreZone,             "EG_nozoneL"),
        (~/EGameEndingCondition/NoMoreProd,             "EG_noprodL"),
        (~/EGameEndingCondition/Time,                   "EG_scLTL"),
        (~/EGameEndingCondition/ScriptDecided ,         ""),
    ]

    VictoryTokens = MAP
    [
        (~/EVictoryType/TotalDefeat,  "EG_TOTDEFE"),
        (~/EVictoryType/MajorDefeat,  "EG_MAJDEFE"),
        (~/EVictoryType/MinorDefeat,  "EG_MINDEFE"),
        (~/EVictoryType/Draw,         "EG_DRAW"),
        (~/EVictoryType/MinorVictory, "EG_MINVICT"),
        (~/EVictoryType/MajorVictory, "EG_MAJVICT"),
        (~/EVictoryType/TotalVictory, "EG_TOTVICT"),
    ]

    TacticFromStrategicEndGameReasonToken = MAP
    [
        (~/EGameEndingCondition/ScoreVictory,       "EG_TFSpoin"),
        (~/EGameEndingCondition/ScoreDefeat,        "EG_TFSmora"),
        (~/EGameEndingCondition/PlayerDisconnected, "EG_TFSdisc"),
        (~/EGameEndingCondition/Surrender,          "EG_TFSsurr"),
        (~/EGameEndingCondition/NoMoreZone,         "EG_TFSnozo"),
        (~/EGameEndingCondition/Time,               "EG_TFStime"),
    ]

    TacticFromStrategicWinDetailToken = MAP
    [
        (~/EGameEndingCondition/ScoreVictory,           "EG_TFSpoEV"),
        (~/EGameEndingCondition/ScoreDefeat,            "EG_TFSmoEV"),
    ]

    TacticFromStrategicLossDetailToken = MAP
    [
        (~/EGameEndingCondition/ScoreVictory,           "EG_TFSpoEL"),
        (~/EGameEndingCondition/ScoreDefeat,            "EG_TFSmoEL"),
    ]

    // Tokens de terrain à mettre dans le dico MAPS.csv
    TacticFromStrategicMapNameByStrategicTerrainType = MAP
    [
        (ETerrainType/StrategicForest, "FOREST"),
        (ETerrainType/StrategicPlain, "PLAIN"),
        (ETerrainType/StrategicSemiUrban, "SEMIURB"),
        (ETerrainType/StrategicUrban, "URB"),
    ]

    EndgameAnimationTextures = MAP
    [
        (~/EVictoryType/TotalDefeat,  EndgameDefeatAnimationTextureList),
        (~/EVictoryType/MajorDefeat,  EndgameDefeatAnimationTextureList),
        (~/EVictoryType/MinorDefeat,  EndgameDefeatAnimationTextureList),
        //TODO : (~/EVictoryType/Draw, ??),
        (~/EVictoryType/MinorVictory, EndgameVictoryAnimationTextureList),
        (~/EVictoryType/MajorVictory, EndgameVictoryAnimationTextureList),
        (~/EVictoryType/TotalVictory, EndgameVictoryAnimationTextureList),
    ]

    EndgameAnimationTextTokens = MAP
    [
        (~/EVictoryType/TotalDefeat,  "lose"),
        (~/EVictoryType/MajorDefeat,  "lose"),
        (~/EVictoryType/MinorDefeat,  "lose"),
        //TODO : (~/EVictoryType/Draw, ??),
        (~/EVictoryType/MinorVictory, "win"),
        (~/EVictoryType/MajorVictory, "win"),
        (~/EVictoryType/TotalVictory, "win"),
    ]

    EndgameAnimationSounds = MAP
    [
        (~/EVictoryType/TotalDefeat,  ~/HudSoundOnDefeat),
        (~/EVictoryType/MajorDefeat,  ~/HudSoundOnDefeat),
        (~/EVictoryType/MinorDefeat,  ~/HudSoundOnDefeat),
        //TODO : (~/EVictoryType/Draw, ??),
        (~/EVictoryType/MinorVictory, ~/HudSoundOnVictory),
        (~/EVictoryType/MajorVictory, ~/HudSoundOnVictory),
        (~/EVictoryType/TotalVictory, ~/HudSoundOnVictory),
    ]

    XpMainColor = "VertLogin"
    XpGainColor = "LDHintSolo_texte_or"

    DataDico = ~/LocalisationConstantes/dico_interface_outgame
    SkirmishToken = "EG_skir"
    ChallengeToken = "EG_chal"
    MultiToken = "EG_multi"
    SoloToken = "EG_solo"

    FadedAnimation = TUIAnimFactorRTTI
    (
        ParamsAnim = [0.5]
        AnimDuration = 2.0
        Modificator = ~/AnimModificator/SquareAccelerate
    )
)

//-------------------------------------------------------------------------------------
