
template BUCKSpecificDivisionBriefMainComponentDescriptor
[
    AlignementToAnchor : float2,
    AlignementToFather : float2,
    IsTogglable : bool,
    CannotDeselect : bool = false,
    RadioButtonManager : TBUCKRadioButtonManager,
    HasBattlegroup : bool,
    MagnifierMultiplication : float = 1.0,
    DivisionNameWidth : float = 503.0,
] is BUCKButtonDescriptor
(
    ElementName = "MainButton"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = (<HasBattlegroup> == true ? [DeckListListWidth, 52.0] : [285.0 + <DivisionNameWidth>, 50.0])
        AlignementToAnchor = <AlignementToAnchor>
        AlignementToFather = <AlignementToFather>
    )

    IsTogglable = <IsTogglable>
    CannotDeselect = <CannotDeselect>
    RadioButtonManager = <RadioButtonManager>

    MagnifierMultiplication = <MagnifierMultiplication>

    Components =
    [
        DivisionBriefList
        (
            HasBattlegroup = <HasBattlegroup>
            DivisionNameWidth = <DivisionNameWidth>
        )
    ]
)
//-------------------------------------------------------------------------------------
template DivisionBriefList
[
    HasBattlegroup : bool,
    DivisionNameWidth : float = 503.0,
]
is BUCKListDescriptor
(
    ElementName = "DivisionBriefList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = (<HasBattlegroup> == true ? [0.0, 31.0] : [0.0, 40.0])
        AlignementToAnchor = [0.5, 0.5]
        AlignementToFather = [0.5, 0.5]
    )

    HasBackground = true
    BackgroundBlockColorToken = "Gray"

    Axis = ~/ListAxis/Horizontal

    Elements =
    [
        // emblem
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DivisionInfoEmblem
        ),
        // flag
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DivisionInfoDrapeauNation
        ),
        // alliance
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKMultiListContentElementDescriptor
            (
                ElementName = "AllianceTextMultiList"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [DeckListColonne_Alliance, 0.0]
                )

                Components =
                [
                    BUCKTextureDescriptor
                    (
                        ElementName = "AllianceTexture"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        TextureFrame = TUIFramePropertyRTTI
                        (
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )

                        ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                    ),
                ]
            )
        ),
    ] +
    //-------------------------------------------------------------------------------------
    (<HasBattlegroup> == true ?
    [
        // affichage nom du deck
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = DivisionInfoDeckName
        ),
    ] :
    [
        // affichage nom de la division
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = DivisionInfoName ( DivisionNameWidth = <DivisionNameWidth> )
        ),
    ]) +
    //-------------------------------------------------------------------------------------
    [
        // division power
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DivisionPower
        ),
        // division type
        BUCKListElementDescriptor
        (
            ComponentDescriptor = DivisionType
        ),
    ]

    BackgroundComponents = (<HasBattlegroup> == true ?
    [
        BUCKContainerDescriptor
        (
            ElementName = "ButtonOverlay"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            HasBackground = true
            BackgroundBlockColorToken = "DivisionBrief/ButtonOverlay"
        )
    ] :
    [
        BUCKContainerDescriptor
        (
            ElementName = "ButtonOverlay"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )
            HasBackground = true
            BackgroundBlockColorToken = "loginBoutonBackground_vert"
        )
    ])

    ForegroundComponents =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                MagnifiableOffset = [0.0, 0.0]
            )
            TextureToken = 'UseInGame_UnitInfoPanel_foreground_panel'
            TextureDrawer = 'ColorMultiply_Additive'
            ClipTextureToComponent = true
        )
    ]
)
//-------------------------------------------------------------------------------------
private DivisionInfoDeckName is BUCKMultiListContentElementDescriptor
(
    ElementName = 'BattlegroupNameMultiList'
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )
    Components =
    [
        BUCKSpecificTextWithHint
        (
            ElementName = 'BattlegroupName'
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Left
                VerticalAlignment = ~/UIText_VerticalCenter
                BigWordAction = ~/BigWordAction/BigWordNewLine
            )

            BigLineAction = ~/BigLineAction/MultiLine

            TextStyle = "Default"
            TypefaceToken = "Liberator"

            TextDico = ~/LocalisationConstantes/dico_interface_outgame

            TextColor = "BlancEquipe"
            TextSize = "14"

            TextPadding = TRTTILength4(Magnifiable = [8.0, 0.0, 8.0, 0.0])
        ),
    ]
)
//-------------------------------------------------------------------------------------
private DivisionInfoDrapeauNation is BUCKMultiListContentElementDescriptor
(
    ElementName = "DivisionFlagMultiList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        MagnifiableWidthHeight = [DeckListColonne_Flag, 0.0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            Components =
            [
                BUCKTextureDescriptor
                (
                    ElementName = "DivisionFlag"

                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                    )

                    TextureFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [0.0, 0.0]
                        MagnifiableWidthHeight = [0.0, 0.0]
                        AlignementToAnchor = [0.5, 0.5]
                        AlignementToFather = [0.5, 0.5]
                    )

                    ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                ),
            ]
        )
    ]
)
//-------------------------------------------------------------------------------------
private DivisionPower is BUCKMultiListContentElementDescriptor
(
    ElementName = "DivisionPowerTextMultiList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        MagnifiableWidthHeight = [DeckListColonne_Power, 0.0]
    )

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "DivisionPowerText"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
            )

            BigLineAction = ~/BigLineAction/CutByDots
            TextStyle = "TextStyleEcranMoniteur"
            TypefaceToken = "IBM"

            TextDico = ~/LocalisationConstantes/dico_interface_outgame

            TextColor = "Blanc"
            TextSize = "30"
            HasBorder = true
            BorderThicknessToken = '1'
            BorderLineColorToken = "MarronPanel_noir"
            BordersToDraw  = ~/TBorderSide/Left | ~/TBorderSide/Right
        )
    ]
)
//-------------------------------------------------------------------------------------
private DivisionType is BUCKMultiListContentElementDescriptor
(
    ElementName = "DivisionUnitTypeMultiList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        MagnifiableWidthHeight = [DeckListColonne_Type -7 , 0.0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            Components =
            [
                BUCKTextureDescriptor
                (
                    ElementName = "DivisionUnitType"
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                        AlignementToAnchor = [0.5, 0.5]
                        AlignementToFather = [0.5, 0.5]
                    )

                    TextureFrame = TUIFramePropertyRTTI
                    (
                        AlignementToAnchor = [0.5, 0.5]
                        AlignementToFather = [0.5, 0.5]
                    )

                    ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                    TextureColorToken = "White"
                ),
            ]
        )
    ]
)
//-------------------------------------------------------------------------------------
private template DivisionInfoName
[
    DivisionNameWidth : float,
]
is BUCKMultiListContentElementDescriptor
(
    ElementName = "DivisionNameMultiList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentHorizontally

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "DivisionNameList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [<DivisionNameWidth>, 0.0]
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
            )

            HasBackground = false
            Axis = ~/ListAxis/Vertical

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ExtendWeight = 0.5
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DivisionName"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [<DivisionNameWidth>, 0.0]
                            RelativeWidthHeight = [0.0, 1.0]
                            AlignementToFather = [0.5, 0.0]
                            AlignementToAnchor = [0.5, 0.0]
                        )
                        ParagraphStyle = paragraphStyleTextCenter
                        TextStyle = "Default"

                        VerticalFitStyle = ~/FitStyle/UserDefined
                        TypefaceToken = "IBM"
                        BigLineAction = ~/BigLineAction/MultiLine
                        TextToken = 'HINT_EX_SP'
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "Multiliste_division_text"
                        TextSize = "24"

                        BackgroundBlockColorToken = 'Orange'
                    )
                ),
                BUCKListElementDescriptor
                (
                    ExtendWeight = 0.5
                    ComponentDescriptor = BUCKTextDescriptor
                    (
                        ElementName = "DlcName"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            AlignementToFather = [0.5, 0.0]
                            AlignementToAnchor = [0.5, 0.0]
                        )
                        ParagraphStyle = paragraphStyleTextCenter
                        TextStyle = "Default"

                        VerticalFitStyle = ~/FitStyle/UserDefined
                        TypefaceToken = "IBM"
                        BigLineAction = ~/BigLineAction/CutByDots
                        TextToken = 'DIVSEL_DLC'
                        TextDico = ~/LocalisationConstantes/dico_interface_outgame
                        TextColor = "Multiliste_division_text"
                        TextSize = "18"

                        BackgroundBlockColorToken = 'Orange'
                    )
                ),
            ]
        )
    ]
)
//-------------------------------------------------------------------------------------
private DivisionInfoEmblem is BUCKMultiListContentElementDescriptor
(
    ElementName = "DivisionEmblemMultiList"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        MagnifiableWidthHeight = [DeckListColonne_Division, 0.0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            Components =
            [
                BUCKTextureDescriptor
                (
                    ElementName = "DivisionEmblem"
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                    )

                    TextureFrame = TUIFramePropertyRTTI
                    (
                        AlignementToAnchor = [0.5, 0.5]
                        AlignementToFather = [0.5, 0.5]
                    )

                    ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                ),
                BUCKTextureDescriptor
                (
                    ElementName = "DlcLock"

                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                    )

                    TextureFrame = TUIFramePropertyRTTI
                    (
                        AlignementToAnchor = [0.5, 0.5]
                        AlignementToFather = [0.5, 0.5]
                    )

                    ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                ),
            ]
        )
    ]
)
//-------------------------------------------------------------------------------------

template UISpecificDivisionBriefViewDescriptor
[
    UniqueName : string = "", // Permet d'indiquer l'endroit ou le composant doit s'insérer

    HasBattlegroup = false,
    PremadeBattlegroupTextColor = "Gold",
    MainComponentDescriptor : TBUCKButtonDescriptor,
] is TUISpecificDivisionBriefViewDescriptor
(
    MainComponentDescriptor = <MainComponentDescriptor>
    MainComponentContainerUniqueName = <UniqueName>

    AlliesTextureToken = "CommonTexture_MotherCountryFlag_NATO_small"
    AxisTextureToken = "CommonTexture_MotherCountryFlag_PACT_small"

    PremadeBattlegroupTextColor = <PremadeBattlegroupTextColor>
    HasBattlegroup = <HasBattlegroup>
)
