private DummyComponentForBlur is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1, 1]
    )
)

ShowRoomResource is TUISpecificShowRoomResources
(
    Handler = $/UI/ResourcesHandler/ShowRoomResourcesHandler

    UILayer                     = $/UserInterface/UILayer_2DInterface_ShowRoom
    UILayerAboveBlur            = $/UserInterface/UILayer_2DInterfaceAboveBlur_ShowRoom
    UILayerLDHint               = $/UserInterface/UILayer_2DInterfaceLDHint

    CommonResources             = ~/UICommonResource
    InGameResourcesHandler      = $/UI/ResourcesHandler/InGameResourcesHandler
    OutGameResourcesHandler     = $/UI/ResourcesHandler/OutGameResourcesHandler
    MousePointerMaskLayer       = $/UserInterface/UILayer_ShowRoomMousePointerMask

    MainStyleGuide              = ~/DefaultStyleGuide

    ViewDescriptors = MAP
    [
        ("UISpecificUnitInfoPanelViewDescriptor", UISpecificUnitInfoPanelViewDescriptor(IsShowRoom = true)),
        ("ArmoryComponentDescriptor", ~/ArmoryComponentDescriptor),
        ("DeckCreatorScreenComponentDescriptor", ~/DeckCreatorScreenComponentDescriptor),
        ("UISpecificShowRoomGroupsDeckCreatorScreenViewDescriptor", UISpecificShowRoomGroupsDeckCreatorScreenViewDescriptor(IsForChallenge = false)),
        ("UISpecificShowRoomGroupsChallengeScreenViewDescriptor",
            UISpecificShowRoomGroupsDeckCreatorScreenViewDescriptor
            (
                IsForChallenge = true
                ChallengeDescriptionType = "SummaryCampaignShowroom" // Left page
                ChallengeMoreDescriptionType = "ScenarioMapDescription" // Right page
            )
        ),
    ]

    GroupSelectionReminderResources = ~/GroupSelectionReminder
    LabelResource                   = ~/CommonLabelResource
    MainContainerResource           = ~/ShowRoomMainContainerResource
    MousePolicyManagerResources     = nil

    WarningPanelResources           = ~/WarningPanelInGameResources

    SelectionHandlerInGameResources = ~/SelectionResources
    MagnifierMultiplier = 0.9

    DummyComponentForBlur = ~/DummyComponentForBlur

    AllianceToCountriesToShow = MAP [
        (ECoalition/PACT, ["SOV","POL","DDR","TCH"]),
        (ECoalition/NATO, ["US","UK","FR","CAN","RFA","NL","BEL","LUX","ESP"])
    ]

    SpecificHidingViewDescriptor = ~/UISpecificHidingViewDescriptor
    DeckCreatorViewHelperDescriptor = ~/DeckCreatorScreenViewHelperDescriptor

    CameraMoverManager = $/Camera/CameraMoverManager

    ShortcutUserInputLayer = $/M3D/Input/UserInputLayerHandler/InputLayer_InGameShortcuts
    LDScriptUserInputLayer =  $/M3D/Input/UserInputLayerHandler/InputLayer_LDScriptInterceptor
)

ShowRoomMainContainerResource is TUICommonMainContainerResource
(
    ContentRefSize = [ 2134 , 1440 ]
    SafeBoxMin     = [  107 ,   80 ]
    SafeBoxSize    = ~/FullScreenSafeBoxSize

    ForegroundComponents = BUCKContainerDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1,1] )
        Components =
        [
            BUCKContainerDescriptor
            (
                UniqueName = "ShowRoomChatContainer"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                )
            ),
        ]
    )
)
