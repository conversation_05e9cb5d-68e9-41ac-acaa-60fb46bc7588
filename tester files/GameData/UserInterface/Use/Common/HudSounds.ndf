HudSoundOnUnitDestroyed is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/SFX/SA/event_03.wav"
)
HudSoundFlarePlaced is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/FlarePlaced.wav"
)
HudSoundFlareHelpPlaced is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/FlareHelpPlaced.wav"
)
HudSoundFlareCustomPlaced is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/FlareCustomPlaced.wav"
)
HudSoundFlareSpottedPlaced is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/FlareSpottedPlaced.wav"
)
HudSoundFlareAttkPlaced is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/FlareAttkPlaced.wav"
)
HudSoundAirContact is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/AirRaid.wav"
)
HudSoundAlarm is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/Alarm_Lost_Sector.wav"
)
HudSoundGainSector is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/Gain_Sector.wav"
)
HudSoundEnnemyGainSector is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Hud/Gain_Ennemy_Sector.wav"
)
HudSoundUnitContact is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/SFX/SA/event_02.wav"
)
HudSoundOnUnitUnderAttack is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/SFX/SA/event_01.wav"
)
HudSoundOnObjectiveCaptured is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/SFX/SA/Test_16b_event_04.wav"
)
HudSoundOnObjectiveLost is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/SFX/SA/Test_16b_event_05.wav"
)
HudSoundOnVictory is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Solo/MUSIC_VICTORY.ogg"
)
HudSoundOnDefeat is TemplateSoundDescriptor
(
    FileName  = "GameData:/Assets/Sounds/Solo/MUSIC_DEFEAT.ogg"
)