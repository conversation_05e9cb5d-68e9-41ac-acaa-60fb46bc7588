

Base_AdditionalTextureBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP [
        ("OutgameTexture_ModCenter_Star",                            MAP [(~/ComponentState/Normal, ~/OutgameTexture_ModCenter_Star)]),
        ("OutgameTexture_LevelUp_Cadre",                             MAP [(~/ComponentState/Normal, ~/OutgameTexture_LevelUp_Cadre)]),
        ("OutgameTexture_LevelUp_Background",                        MAP [(~/ComponentState/Normal, ~/OutgameTexture_LevelUp_Background)]),
        ("StyleDeskTexture_CheckBox",                                MAP [
                                                                            (~/ComponentState/Normal,                   ~/StyleDeskTexture_CheckBox_Vide),
                                                                            (~/ComponentState/Highlighted,              ~/StyleDeskTexture_CheckBox_Vide),
                                                                            (~/ComponentState/Clicked,                  ~/StyleDeskTexture_CheckBox_Vide),
                                                                            (~/ComponentState/Intermediate,             ~/StyleDeskTexture_CheckBox_Eclair),
                                                                            (~/ComponentState/IntermediateHighlighted,  ~/StyleDeskTexture_CheckBox_Eclair),
                                                                            (~/ComponentState/Toggled,                  ~/StyleDeskTexture_CheckBox_Tick),
                                                                            (~/ComponentState/ToggleGrayed,             ~/StyleDeskTexture_CheckBox_Tick),
                                                                            (~/ComponentState/ToggleHighlighted,        ~/StyleDeskTexture_CheckBox_Tick),
                                                                            (~/ComponentState/ToggleClicked,            ~/StyleDeskTexture_CheckBox_Tick),
                                                                     ]),
        ("StyleDeskTexture_RadioButton",                             MAP [
                                                                            (~/ComponentState/Normal,                   ~/StyleDeskTexture_RadioButton_Empty),
                                                                            (~/ComponentState/Highlighted,              ~/StyleDeskTexture_RadioButton_Empty),
                                                                            (~/ComponentState/Clicked,                  ~/StyleDeskTexture_RadioButton_Empty),
                                                                            (~/ComponentState/Intermediate,             ~/StyleDeskTexture_RadioButton_Tick),
                                                                            (~/ComponentState/IntermediateHighlighted,  ~/StyleDeskTexture_RadioButton_Tick),
                                                                            (~/ComponentState/Toggled,                  ~/StyleDeskTexture_RadioButton_Tick),
                                                                            (~/ComponentState/ToggleGrayed,             ~/StyleDeskTexture_RadioButton_Tick),
                                                                            (~/ComponentState/ToggleHighlighted,        ~/StyleDeskTexture_RadioButton_Tick),
                                                                            (~/ComponentState/ToggleClicked,            ~/StyleDeskTexture_RadioButton_Tick),
                                                                     ]),
        ("StyleDeskTexture_SortingCheckBox",                         MAP [
                                                                            (~/ComponentState/Normal, ~/StyleDeskTexture_CheckBox_UpArrow),
                                                                            (~/ComponentState/Highlighted, ~/StyleDeskTexture_CheckBox_UpArrow),
                                                                            (~/ComponentState/Clicked, ~/StyleDeskTexture_CheckBox_UpArrow),
                                                                            (~/ComponentState/Toggled, ~/StyleDeskTexture_CheckBox_DownArrow),
                                                                            (~/ComponentState/ToggleHighlighted, ~/StyleDeskTexture_CheckBox_DownArrow),
                                                                            (~/ComponentState/ToggleClicked, ~/StyleDeskTexture_CheckBox_DownArrow),
                                                                            (~/ComponentState/Intermediate, ~/StyleDeskTexture_CheckBox_Vide),
                                                                            (~/ComponentState/IntermediateHighlighted, ~/StyleDeskTexture_CheckBox_Vide),
                                                                     ]),
        ("StyleDeskTexture_treeview_plus_moins",                         MAP [
                                                                            (~/ComponentState/Normal, ~/StyleDeskTexture_treeview_plus),
                                                                            (~/ComponentState/Highlighted, ~/StyleDeskTexture_treeview_plus),
                                                                            (~/ComponentState/Clicked, ~/StyleDeskTexture_treeview_plus),
                                                                            (~/ComponentState/Toggled, ~/StyleDeskTexture_treeview_moins),
                                                                            (~/ComponentState/ToggleHighlighted, ~/StyleDeskTexture_treeview_moins),
                                                                            (~/ComponentState/ToggleClicked, ~/StyleDeskTexture_treeview_moins),
                                                                            (~/ComponentState/Intermediate, ~/StyleDeskTexture_CheckBox_Vide),
                                                                            (~/ComponentState/IntermediateHighlighted, ~/StyleDeskTexture_CheckBox_Vide),
                                                                     ]),
        ("CommonTexture_Spotlight_TopTileTexture",                   MAP [(~/ComponentState/Normal, ~/CommonTexture_Spotlight_TopTileTexture)]),
        ("CommonTexture_Spotlight_BottomTileTexture",                MAP [(~/ComponentState/Normal, ~/CommonTexture_Spotlight_BottomTileTexture)]),
        ("CommonTexture_Spotlight_LeftTileTexture",                  MAP [(~/ComponentState/Normal, ~/CommonTexture_Spotlight_LeftTileTexture)]),
        ("CommonTexture_Spotlight_RightTileTexture",                 MAP [(~/ComponentState/Normal, ~/CommonTexture_Spotlight_RightTileTexture)]),
        ("CommonTexture_Spotlight_CornerTexture_TopLeft",            MAP [(~/ComponentState/Normal, ~/CommonTexture_Spotlight_CornerTexture_TopLeft)]),
        ("CommonTexture_Spotlight_CornerTexture_TopRight",           MAP [(~/ComponentState/Normal, ~/CommonTexture_Spotlight_CornerTexture_TopRight)]),
        ("CommonTexture_Spotlight_CornerTexture_BottomRight",        MAP [(~/ComponentState/Normal, ~/CommonTexture_Spotlight_CornerTexture_BottomRight)]),
        ("CommonTexture_Spotlight_CornerTexture_BottomLeft",         MAP [(~/ComponentState/Normal, ~/CommonTexture_Spotlight_CornerTexture_BottomLeft)]),

        ("CommonTexture_Chanfrein_VerticalTileTexture",              MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein_VerticalTileTexture)]),
        ("CommonTexture_Chanfrein_HorizontalTileTexture",            MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein_HorizontalTileTexture)]),
        ("CommonTexture_Chanfrein_CornerTexture_TopLeft",            MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein_CornerTexture_TopLeft)]),
        ("CommonTexture_Chanfrein_CornerTexture_TopRight",           MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein_CornerTexture_TopRight)]),
        ("CommonTexture_Chanfrein_CornerTexture_BottomRight",        MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein_CornerTexture_BottomRight)]),
        ("CommonTexture_Chanfrein_CornerTexture_BottomLeft",         MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein_CornerTexture_BottomLeft)]),

        ("CommonTexture_Chanfrein5_VerticalTileTexture",              MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein5_VerticalTileTexture)]),
        ("CommonTexture_Chanfrein5_HorizontalTileTexture",            MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein5_HorizontalTileTexture)]),
        ("CommonTexture_Chanfrein5_CornerTexture_TopLeft",            MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein5_CornerTexture_TopLeft)]),
        ("CommonTexture_Chanfrein5_CornerTexture_TopRight",           MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein5_CornerTexture_TopRight)]),
        ("CommonTexture_Chanfrein5_CornerTexture_BottomRight",        MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein5_CornerTexture_BottomRight)]),
        ("CommonTexture_Chanfrein5_CornerTexture_BottomLeft",         MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein5_CornerTexture_BottomLeft)]),

        ("CommonTexture_Chanfrein4_VerticalTileTexture",              MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein4_VerticalTileTexture)]),
        ("CommonTexture_Chanfrein4_HorizontalTileTexture",            MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein4_HorizontalTileTexture)]),
        ("CommonTexture_Chanfrein4_CornerTexture_TopLeft",            MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein4_CornerTexture_TopLeft)]),
        ("CommonTexture_Chanfrein4_CornerTexture_TopRight",           MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein4_CornerTexture_TopRight)]),
        ("CommonTexture_Chanfrein4_CornerTexture_BottomRight",        MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein4_CornerTexture_BottomRight)]),
        ("CommonTexture_Chanfrein4_CornerTexture_BottomLeft",         MAP [(~/ComponentState/Normal, ~/CommonTexture_Chanfrein4_CornerTexture_BottomLeft)]),

        ("CommonTexture_ChanfreinTransparent5_VerticalTileTexture",              MAP [(~/ComponentState/Normal, ~/CommonTexture_ChanfreinTransparent5_VerticalTileTexture)]),
        ("CommonTexture_ChanfreinTransparent5_HorizontalTileTexture",            MAP [(~/ComponentState/Normal, ~/CommonTexture_ChanfreinTransparent5_HorizontalTileTexture)]),
        ("CommonTexture_ChanfreinTransparent5_CornerTexture_TopLeft",            MAP [(~/ComponentState/Normal, ~/CommonTexture_ChanfreinTransparent5_CornerTexture_TopLeft)]),
        ("CommonTexture_ChanfreinTransparent5_CornerTexture_TopRight",           MAP [(~/ComponentState/Normal, ~/CommonTexture_ChanfreinTransparent5_CornerTexture_TopRight)]),
        ("CommonTexture_ChanfreinTransparent5_CornerTexture_BottomRight",        MAP [(~/ComponentState/Normal, ~/CommonTexture_ChanfreinTransparent5_CornerTexture_BottomRight)]),
        ("CommonTexture_ChanfreinTransparent5_CornerTexture_BottomLeft",         MAP [(~/ComponentState/Normal, ~/CommonTexture_ChanfreinTransparent5_CornerTexture_BottomLeft)]),

        ("CommonTexture_Ombre_TopTileTexture",                   MAP [(~/ComponentState/Normal, ~/CommonTexture_Ombre_TopTileTexture)]),
        ("CommonTexture_Ombre_BottomTileTexture",                MAP [(~/ComponentState/Normal, ~/CommonTexture_Ombre_BottomTileTexture)]),
        ("CommonTexture_Ombre_LeftTileTexture",                  MAP [(~/ComponentState/Normal, ~/CommonTexture_Ombre_LeftTileTexture)]),
        ("CommonTexture_Ombre_RightTileTexture",                 MAP [(~/ComponentState/Normal, ~/CommonTexture_Ombre_RightTileTexture)]),
        ("CommonTexture_Ombre_CornerTexture_TopLeft",            MAP [(~/ComponentState/Normal, ~/CommonTexture_Ombre_CornerTexture_TopLeft)]),
        ("CommonTexture_Ombre_CornerTexture_TopRight",           MAP [(~/ComponentState/Normal, ~/CommonTexture_Ombre_CornerTexture_TopRight)]),
        ("CommonTexture_Ombre_CornerTexture_BottomRight",        MAP [(~/ComponentState/Normal, ~/CommonTexture_Ombre_CornerTexture_BottomRight)]),
        ("CommonTexture_Ombre_CornerTexture_BottomLeft",         MAP [(~/ComponentState/Normal, ~/CommonTexture_Ombre_CornerTexture_BottomLeft)]),

        ("OutgameTexture_DivisionBackground",                        MAP [(~/ComponentState/Normal, ~/OutgameTexture_DivisionBackground)]),
        ("OutgameTexture_DivisionBackground_Frontal_Assault",        MAP [(~/ComponentState/Normal, ~/OutgameTexture_DivisionBackground_Frontal_Assault)]),

        ("OutgameTexture_GameRoomList_Observable",                                  MAP [(~/ComponentState/Normal, ~/OutgameTexture_GameRoomList_Observable)]),
        ("OutgameTexture_Division_Dlc_Corner",                                      MAP [(~/ComponentState/Normal, ~/OutgameTexture_Division_Dlc_Corner)]),



        ("OutgameTexture_PlayerBanner",                                             MAP [(~/ComponentState/Normal, ~/OutgameTexture_PlayerBanner)]),
        ("OutgameTexture_UnknownMap",                                               MAP [(~/ComponentState/Normal, ~/OutgameTexture_UnknownMap)]),
        ("OutgameTexture_ConquestModeTexture",                                      MAP [(~/ComponentState/Normal, ~/OutgameTexture_ConquestModeTexture)]),
        ("OutgameTexture_ConquestModeTexture2",                                      MAP [(~/ComponentState/Normal, ~/OutgameTexture_ConquestModeTexture2)]),
        ("OutgameTexture_BreakthroughModeTexture",                                  MAP [(~/ComponentState/Normal, ~/OutgameTexture_BreakthroughModeTexture)]),
        ("OutgameTexture_DestructionTypeTexture",                                   MAP [(~/ComponentState/Normal, ~/OutgameTexture_DestructionTypeTexture)]),
        ("OutgameTexture_DestructionTypeTexture2",                                   MAP [(~/ComponentState/Normal, ~/OutgameTexture_DestructionTypeTexture2)]),
        ("OutgameTexture_ConquestTypeTexture",                                      MAP [(~/ComponentState/Normal, ~/OutgameTexture_ConquestTypeTexture)]),
        ("OutgameTexture_ConquestTypeTexture2",                                     MAP [(~/ComponentState/Normal, ~/OutgameTexture_ConquestTypeTexture2)]),
        ("OutgameTexture_StrategicModeTexture",                                     MAP [(~/ComponentState/Normal, ~/OutgameTexture_StrategicModeTexture)]),

        ("OutgameTexture_BlueAttack",                                               MAP [(~/ComponentState/Normal, ~/OutgameTexture_BlueAttack)]),
        ("OutgameTexture_RedAttack",                                                MAP [(~/ComponentState/Normal, ~/OutgameTexture_RedAttack)]),
        ("OutgameTexture_Mode_EncounterBatte",                                      MAP [(~/ComponentState/Normal, ~/OutgameTexture_Mode_EncounterBatte)]),
        ("OutgameTexture_Mode_Breakthrough2",                                       MAP [(~/ComponentState/Normal, ~/OutgameTexture_Mode_Breakthrough2)]),


        ("NotificationFrame_DefaultBackground",                                     MAP [(~/ComponentState/Normal, ~/NotificationFrame_DefaultBackground)]),
        ("OutgameTexture_GameRoomList_ModNotInLocal",                               MAP [(~/ComponentState/Normal, ~/OutgameTexture_GameRoomList_ModNotInLocal)]),
        ("OutgameTexture_GameRoomList_ModInLocal",                                  MAP [(~/ComponentState/Normal, ~/OutgameTexture_GameRoomList_ModInLocal)]),
        ("OutgameTexture_GameRoomList_ModActive",                                   MAP [(~/ComponentState/Normal, ~/OutgameTexture_GameRoomList_ModActive)]),

        ("OutgameTexture_WG2_BgOutGame_Erase_Button",                               MAP [(~/ComponentState/Normal, ~/OutgameTexture_WG2_BgOutGame_Erase_Button)]),
        ("OutgameTexture_WG2_BgOutGame_AddFriend_Button",                           MAP [(~/ComponentState/Normal, ~/OutgameTexture_WG2_BgOutGame_AddFriend_Button)]),
        ("OutgameTexture_WG2_BgOutGame_BlackList_Button",                           MAP [(~/ComponentState/Normal, ~/OutgameTexture_WG2_BgOutGame_BlackList_Button)]),

        ("ModCenter_LineBackground",                                                MAP [(~/ComponentState/Normal, ~/ModCenter_LineBackground)]),

        ("ExpandSectionTexture", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/Expandable/vitesseUpn.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/Expandable/vitesseUp.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/Expandable/vitesseUp.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/Expandable/vitesseUpt.png' )),
                            ] ),
        ("ShrinkSectionTexture", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/Expandable/vitesseDownn.png' )),
                            (~/ComponentState/Clicked, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/Expandable/vitesseDown.png' )),
                            (~/ComponentState/Highlighted, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/Expandable/vitesseDown.png' )),
                            (~/ComponentState/Toggled, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/Expandable/vitesseDownt.png' )),
                            ] ),

        ("AddFriendTexture", MAP [
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseOutGame/InviteLobby.png' )),
                            ] ),

        ("PreviousFriendTexture", MAP [
                            (~/ComponentState/Grayed, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseOutGame/PreviousLock.png' )),
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseOutGame/Previous.png' )),
                            ] ),

        ("NextFriendTexture", MAP [
                            (~/ComponentState/Grayed, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseOutGame/NextLock.png' )),
                            (~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseOutGame/Next.png' )),
                            ] ),

        ("texture_difficulty_expert",                               MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/LDHint/difficulty_expert.png' ))]),
        ("texture_difficulty_normal",                               MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/LDHint/difficulty_normal.png' ))]),
        ("UseInGame_LDhint_Intel_Arty",                               MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/UseInGame/LDHint/Intel_Arty.png' ))]),


        //-------------------------------------------------------------------------------------
        //traits
        ("Texture_Trait_Icon_wip",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/WIP.png' )), ] ),
        ("Texture_Trait_Icon_he",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/HE2.png' )), ] ),
        ("Texture_Trait_Icon_ke",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/KE.png' )), ] ),
        ("Texture_Trait_Icon_antiair",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/antiair.png' )), ] ),
        ("Texture_Trait_Icon_motion",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/motion.png' )), ] ),
        ("Texture_Trait_Icon_heat",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/HEAT2.png' )), ] ),
        ("Texture_Trait_Icon_static",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/static.png' )), ] ),
        ("Texture_Trait_Icon_sead2",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/sead2.png' )), ] ),
        ("Texture_Trait_Icon_radar",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/radar.png' )), ] ),
        ("Texture_Trait_Icon_fireAndForget",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/fireAndForget.png' )), ] ),
        ("Texture_Trait_Icon_semiAuto",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/semiAuto.png' )), ] ),
        ("Texture_Trait_Icon_manual",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/manual2.png' )), ] ),
        ("Texture_Trait_Icon_indirect",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/indirect.png' )), ] ),
        ("Texture_Trait_Icon_indirect_CLGP",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/indirect_CLGP.png' )), ] ),
        ("Texture_Trait_Icon_cac",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/CaC.png' )), ] ),
        ("Texture_Trait_Icon_napalm",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/napalm.png' )), ] ),
        ("Texture_Trait_Icon_cluster",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/cluster.png' )), ] ),
        ("Texture_Trait_Icon_autoloader",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/autoloader.png' )), ] ),
        ("Texture_Trait_Icon_tripod",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/tripod.png' )), ] ),
        ("Texture_Trait_Icon_smoke",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/smoke.png' )), ] ),
        ("Texture_Trait_Icon_coax",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/coax.png' )), ] ),
        ("Texture_Trait_Icon_remote",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/remote.png' )), ] ),
        ("Texture_Trait_Icon_tandem",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/tandem.png' )), ] ),
        ("Texture_Trait_Icon_OTA",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/OTA.png' )), ] ),
        ("Texture_Trait_Icon_thermobaric",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/thermobaric.png' )), ] ),

        ("Texture_TraitUni_Icon_half",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/half.png' )), ] ),
        ("Texture_TraitUni_Icon_para",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/para.png' )), ] ),
        ("Texture_TraitUni_Icon_shock",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/shock.png' )), ] ),
        ("Texture_TraitUni_Icon_militia",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/militia.png' )), ] ),
        ("Texture_TraitUni_Icon_sf",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/sf.png' )), ] ),
        ("Texture_TraitUni_Icon_ifv",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/ifv.png' )), ] ),
        ("Texture_TraitUni_Icon_cmd",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/cmd.png' )), ] ),
        ("Texture_TraitUni_Icon_mp",                          MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName='GameData:/Assets/2D/Interface/Common/traits/mp.png' )), ] ),


        ("size_s", MAP [( ~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/Common/TFS/small.png" )),]),
        ("size_m", MAP [( ~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/Common/TFS/medium.png" )),]),
        ("size_l", MAP [( ~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/Common/TFS/large.png" )),]),
        ("size_xl", MAP [( ~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/Common/TFS/xl.png" )),]),
        ("size_xs", MAP [( ~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/Common/TFS/xs.png" )),]),


        //-------------------------------------------------------------------------------------
        ("OutGameTexture_LevelUpAnimation_0",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_0)]),
        ("OutGameTexture_LevelUpAnimation_1",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_1)]),
        ("OutGameTexture_LevelUpAnimation_2",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_2)]),
        ("OutGameTexture_LevelUpAnimation_3",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_3)]),
        ("OutGameTexture_LevelUpAnimation_4",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_4)]),
        ("OutGameTexture_LevelUpAnimation_5",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_5)]),
        ("OutGameTexture_LevelUpAnimation_6",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_6)]),
        ("OutGameTexture_LevelUpAnimation_7",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_7)]),
        ("OutGameTexture_LevelUpAnimation_8",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_8)]),
        ("OutGameTexture_LevelUpAnimation_9",                                                MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_9)]),
        ("OutGameTexture_LevelUpAnimation_10",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_10)]),
        ("OutGameTexture_LevelUpAnimation_11",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_11)]),
        ("OutGameTexture_LevelUpAnimation_12",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_12)]),
        ("OutGameTexture_LevelUpAnimation_13",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_13)]),
        ("OutGameTexture_LevelUpAnimation_14",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_14)]),
        ("OutGameTexture_LevelUpAnimation_15",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_15)]),
        ("OutGameTexture_LevelUpAnimation_16",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_16)]),
        ("OutGameTexture_LevelUpAnimation_17",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_17)]),
        ("OutGameTexture_LevelUpAnimation_18",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_18)]),
        ("OutGameTexture_LevelUpAnimation_19",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_19)]),
        ("OutGameTexture_LevelUpAnimation_20",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_20)]),
        ("OutGameTexture_LevelUpAnimation_21",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_21)]),
        ("OutGameTexture_LevelUpAnimation_22",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_22)]),
        ("OutGameTexture_LevelUpAnimation_23",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_23)]),
        ("OutGameTexture_LevelUpAnimation_24",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_24)]),
        ("OutGameTexture_LevelUpAnimation_25",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_25)]),
        ("OutGameTexture_LevelUpAnimation_26",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_26)]),
        ("OutGameTexture_LevelUpAnimation_27",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_27)]),
        ("OutGameTexture_LevelUpAnimation_28",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_28)]),
        ("OutGameTexture_LevelUpAnimation_29",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_29)]),
        ("OutGameTexture_LevelUpAnimation_30",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_30)]),
        ("OutGameTexture_LevelUpAnimation_31",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_31)]),
        ("OutGameTexture_LevelUpAnimation_32",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_32)]),
        ("OutGameTexture_LevelUpAnimation_33",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_33)]),
        ("OutGameTexture_LevelUpAnimation_34",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_34)]),
        ("OutGameTexture_LevelUpAnimation_35",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_35)]),
        ("OutGameTexture_LevelUpAnimation_36",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_36)]),
        ("OutGameTexture_LevelUpAnimation_37",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_37)]),
        ("OutGameTexture_LevelUpAnimation_38",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_38)]),
        ("OutGameTexture_LevelUpAnimation_39",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_39)]),
        ("OutGameTexture_LevelUpAnimation_40",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_40)]),
        ("OutGameTexture_LevelUpAnimation_41",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_41)]),
        ("OutGameTexture_LevelUpAnimation_42",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_42)]),
        ("OutGameTexture_LevelUpAnimation_43",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_43)]),
        ("OutGameTexture_LevelUpAnimation_44",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_44)]),
        ("OutGameTexture_LevelUpAnimation_45",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_45)]),
        ("OutGameTexture_LevelUpAnimation_46",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_46)]),
        ("OutGameTexture_LevelUpAnimation_47",                                               MAP [(~/ComponentState/Normal, ~/OutGameTexture_LevelUpAnimation_47)]),
        ("UseOutGameTexture_WaitAnimation_0",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_0)]),
        ("UseOutGameTexture_WaitAnimation_1",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_1)]),
        ("UseOutGameTexture_WaitAnimation_2",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_2)]),
        ("UseOutGameTexture_WaitAnimation_3",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_3)]),
        ("UseOutGameTexture_WaitAnimation_4",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_4)]),
        ("UseOutGameTexture_WaitAnimation_5",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_5)]),
        ("UseOutGameTexture_WaitAnimation_6",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_6)]),
        ("UseOutGameTexture_WaitAnimation_7",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_7)]),
        ("UseOutGameTexture_WaitAnimation_8",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_8)]),
        ("UseOutGameTexture_WaitAnimation_9",                                                MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_9)]),
        ("UseOutGameTexture_WaitAnimation_10",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_10)]),
        ("UseOutGameTexture_WaitAnimation_11",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_11)]),
        ("UseOutGameTexture_WaitAnimation_12",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_12)]),
        ("UseOutGameTexture_WaitAnimation_13",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_13)]),
        ("UseOutGameTexture_WaitAnimation_14",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_14)]),
        ("UseOutGameTexture_WaitAnimation_15",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_15)]),
        ("UseOutGameTexture_WaitAnimation_16",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_16)]),
        ("UseOutGameTexture_WaitAnimation_17",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_17)]),
        ("UseOutGameTexture_WaitAnimation_18",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_18)]),
        ("UseOutGameTexture_WaitAnimation_19",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_19)]),
        ("UseOutGameTexture_WaitAnimation_20",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_20)]),
        ("UseOutGameTexture_WaitAnimation_21",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_21)]),
        ("UseOutGameTexture_WaitAnimation_22",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_22)]),
        ("UseOutGameTexture_WaitAnimation_23",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_23)]),
        ("UseOutGameTexture_WaitAnimation_24",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_24)]),
        ("UseOutGameTexture_WaitAnimation_25",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_25)]),
        ("UseOutGameTexture_WaitAnimation_26",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_26)]),
        ("UseOutGameTexture_WaitAnimation_27",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_27)]),
        ("UseOutGameTexture_WaitAnimation_28",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_28)]),
        ("UseOutGameTexture_WaitAnimation_29",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_29)]),
        ("UseOutGameTexture_WaitAnimation_30",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_30)]),
        ("UseOutGameTexture_WaitAnimation_31",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_31)]),
        ("UseOutGameTexture_WaitAnimation_32",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_32)]),
        ("UseOutGameTexture_WaitAnimation_33",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_33)]),
        ("UseOutGameTexture_WaitAnimation_34",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_34)]),
        ("UseOutGameTexture_WaitAnimation_35",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_35)]),
        ("UseOutGameTexture_WaitAnimation_36",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_36)]),
        ("UseOutGameTexture_WaitAnimation_37",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_37)]),
        ("UseOutGameTexture_WaitAnimation_38",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_38)]),
        ("UseOutGameTexture_WaitAnimation_39",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_39)]),
        ("UseOutGameTexture_WaitAnimation_40",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_40)]),
        ("UseOutGameTexture_WaitAnimation_41",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_41)]),
        ("UseOutGameTexture_WaitAnimation_42",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_42)]),
        ("UseOutGameTexture_WaitAnimation_43",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_43)]),
        ("UseOutGameTexture_WaitAnimation_44",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_44)]),
        ("UseOutGameTexture_WaitAnimation_45",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_45)]),
        ("UseOutGameTexture_WaitAnimation_46",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_46)]),
        ("UseOutGameTexture_WaitAnimation_47",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_47)]),
        ("UseOutGameTexture_WaitAnimation_48",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_48)]),
        ("UseOutGameTexture_WaitAnimation_49",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_49)]),
        ("UseOutGameTexture_WaitAnimation_50",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_50)]),
        ("UseOutGameTexture_WaitAnimation_51",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_51)]),
        ("UseOutGameTexture_WaitAnimation_52",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_52)]),
        ("UseOutGameTexture_WaitAnimation_53",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_53)]),
        ("UseOutGameTexture_WaitAnimation_54",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_54)]),
        ("UseOutGameTexture_WaitAnimation_55",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_55)]),
        ("UseOutGameTexture_WaitAnimation_56",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_56)]),
        ("UseOutGameTexture_WaitAnimation_57",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_57)]),
        ("UseOutGameTexture_WaitAnimation_58",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_58)]),
        ("UseOutGameTexture_WaitAnimation_59",                                               MAP [(~/ComponentState/Normal, ~/UseOutGameTexture_WaitAnimation_59)]),
//-------------------------------------------------------------------------------------
        ("OTAN_DEFEAT000", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT000.png" ))]),
        ("OTAN_DEFEAT001", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT001.png" ))]),
        ("OTAN_DEFEAT002", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT002.png" ))]),
        ("OTAN_DEFEAT003", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT003.png" ))]),
        ("OTAN_DEFEAT004", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT004.png" ))]),
        ("OTAN_DEFEAT005", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT005.png" ))]),
        ("OTAN_DEFEAT006", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT006.png" ))]),
        ("OTAN_DEFEAT007", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT007.png" ))]),
        ("OTAN_DEFEAT008", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT008.png" ))]),
        ("OTAN_DEFEAT009", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT009.png" ))]),
        ("OTAN_DEFEAT010", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT010.png" ))]),
        ("OTAN_DEFEAT011", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT011.png" ))]),
        ("OTAN_DEFEAT012", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT012.png" ))]),
        ("OTAN_DEFEAT013", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT013.png" ))]),
        ("OTAN_DEFEAT014", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT014.png" ))]),
        ("OTAN_DEFEAT015", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT015.png" ))]),
        ("OTAN_DEFEAT016", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT016.png" ))]),
        ("OTAN_DEFEAT017", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT017.png" ))]),
        ("OTAN_DEFEAT018", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT018.png" ))]),
        ("OTAN_DEFEAT019", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT019.png" ))]),
        ("OTAN_DEFEAT020", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT020.png" ))]),
        ("OTAN_DEFEAT021", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT021.png" ))]),
        ("OTAN_DEFEAT022", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT022.png" ))]),
        ("OTAN_DEFEAT023", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT023.png" ))]),
        ("OTAN_DEFEAT024", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT024.png" ))]),
        ("OTAN_DEFEAT025", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT025.png" ))]),
        ("OTAN_DEFEAT026", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT026.png" ))]),
        ("OTAN_DEFEAT027", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT027.png" ))]),
        ("OTAN_DEFEAT028", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT028.png" ))]),
        ("OTAN_DEFEAT029", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT029.png" ))]),
        ("OTAN_DEFEAT030", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT030.png" ))]),
        ("OTAN_DEFEAT031", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT031.png" ))]),
        ("OTAN_DEFEAT032", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT032.png" ))]),
        ("OTAN_DEFEAT033", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT033.png" ))]),
        ("OTAN_DEFEAT034", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT034.png" ))]),
        ("OTAN_DEFEAT035", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT035.png" ))]),
        ("OTAN_DEFEAT036", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT036.png" ))]),
        ("OTAN_DEFEAT037", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT037.png" ))]),
        ("OTAN_DEFEAT038", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT038.png" ))]),
        ("OTAN_DEFEAT039", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT039.png" ))]),
        ("OTAN_DEFEAT040", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT040.png" ))]),
        ("OTAN_DEFEAT041", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT041.png" ))]),
        ("OTAN_DEFEAT042", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT042.png" ))]),
        ("OTAN_DEFEAT043", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT043.png" ))]),
        ("OTAN_DEFEAT044", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT044.png" ))]),
        ("OTAN_DEFEAT045", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT045.png" ))]),
        ("OTAN_DEFEAT046", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT046.png" ))]),
        ("OTAN_DEFEAT047", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT047.png" ))]),
        ("OTAN_DEFEAT048", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT048.png" ))]),
        ("OTAN_DEFEAT049", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT049.png" ))]),
        ("OTAN_DEFEAT050", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT050.png" ))]),
        ("OTAN_DEFEAT051", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT051.png" ))]),
        ("OTAN_DEFEAT052", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT052.png" ))]),
        ("OTAN_DEFEAT053", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT053.png" ))]),
        ("OTAN_DEFEAT054", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT054.png" ))]),
        ("OTAN_DEFEAT055", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT055.png" ))]),
        ("OTAN_DEFEAT056", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT056.png" ))]),
        ("OTAN_DEFEAT057", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT057.png" ))]),
        ("OTAN_DEFEAT058", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT058.png" ))]),
        ("OTAN_DEFEAT059", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT059.png" ))]),
        ("OTAN_DEFEAT060", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT060.png" ))]),
        ("OTAN_DEFEAT061", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT061.png" ))]),
        ("OTAN_DEFEAT062", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT062.png" ))]),
        ("OTAN_DEFEAT063", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT063.png" ))]),
        ("OTAN_DEFEAT064", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT064.png" ))]),
        ("OTAN_DEFEAT065", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT065.png" ))]),
        ("OTAN_DEFEAT066", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT066.png" ))]),
        ("OTAN_DEFEAT067", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT067.png" ))]),
        ("OTAN_DEFEAT068", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT068.png" ))]),
        ("OTAN_DEFEAT069", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT069.png" ))]),
        ("OTAN_DEFEAT070", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT070.png" ))]),
        ("OTAN_DEFEAT071", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT071.png" ))]),
        ("OTAN_DEFEAT072", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT072.png" ))]),
        ("OTAN_DEFEAT073", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT073.png" ))]),
        ("OTAN_DEFEAT074", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT074.png" ))]),
        ("OTAN_DEFEAT075", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT075.png" ))]),
        ("OTAN_DEFEAT076", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT076.png" ))]),
        ("OTAN_DEFEAT077", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT077.png" ))]),
        ("OTAN_DEFEAT078", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT078.png" ))]),
        ("OTAN_DEFEAT079", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT079.png" ))]),
        ("OTAN_DEFEAT080", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT080.png" ))]),
        ("OTAN_DEFEAT081", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT081.png" ))]),
        ("OTAN_DEFEAT082", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT082.png" ))]),
        ("OTAN_DEFEAT083", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT083.png" ))]),
        ("OTAN_DEFEAT084", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT084.png" ))]),
        ("OTAN_DEFEAT085", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT085.png" ))]),
        ("OTAN_DEFEAT086", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT086.png" ))]),
        ("OTAN_DEFEAT087", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT087.png" ))]),
        ("OTAN_DEFEAT088", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT088.png" ))]),
        ("OTAN_DEFEAT089", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT089.png" ))]),
        ("OTAN_DEFEAT090", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT090.png" ))]),
        ("OTAN_DEFEAT091", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT091.png" ))]),
        ("OTAN_DEFEAT092", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT092.png" ))]),
        ("OTAN_DEFEAT093", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT093.png" ))]),
        ("OTAN_DEFEAT094", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT094.png" ))]),
        ("OTAN_DEFEAT095", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT095.png" ))]),
        ("OTAN_DEFEAT096", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT096.png" ))]),
        ("OTAN_DEFEAT097", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT097.png" ))]),
        ("OTAN_DEFEAT098", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT098.png" ))]),
        ("OTAN_DEFEAT099", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT099.png" ))]),
        ("OTAN_DEFEAT100", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT100.png" ))]),
        ("OTAN_DEFEAT101", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT101.png" ))]),
        ("OTAN_DEFEAT102", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT102.png" ))]),
        ("OTAN_DEFEAT103", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT103.png" ))]),
        ("OTAN_DEFEAT104", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT104.png" ))]),
        ("OTAN_DEFEAT105", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT105.png" ))]),
        ("OTAN_DEFEAT106", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT106.png" ))]),
        ("OTAN_DEFEAT107", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT107.png" ))]),
        ("OTAN_DEFEAT108", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT108.png" ))]),
        ("OTAN_DEFEAT109", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT109.png" ))]),
        ("OTAN_DEFEAT110", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT110.png" ))]),
        ("OTAN_DEFEAT111", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT111.png" ))]),
        ("OTAN_DEFEAT112", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT112.png" ))]),
        ("OTAN_DEFEAT113", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT113.png" ))]),
        ("OTAN_DEFEAT114", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT114.png" ))]),
        ("OTAN_DEFEAT115", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT115.png" ))]),
        ("OTAN_DEFEAT116", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT116.png" ))]),
        ("OTAN_DEFEAT117", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT117.png" ))]),
        ("OTAN_DEFEAT118", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT118.png" ))]),
        ("OTAN_DEFEAT119", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT119.png" ))]),
        ("OTAN_DEFEAT120", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT120.png" ))]),
        ("OTAN_DEFEAT121", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT121.png" ))]),
        ("OTAN_DEFEAT122", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT122.png" ))]),
        ("OTAN_DEFEAT123", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT123.png" ))]),
        ("OTAN_DEFEAT124", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT124.png" ))]),
        ("OTAN_DEFEAT125", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT125.png" ))]),
        ("OTAN_DEFEAT126", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT126.png" ))]),
        ("OTAN_DEFEAT127", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT127.png" ))]),
        ("OTAN_DEFEAT128", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT128.png" ))]),
        ("OTAN_DEFEAT129", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT129.png" ))]),
        ("OTAN_DEFEAT130", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT130.png" ))]),
        ("OTAN_DEFEAT131", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT131.png" ))]),
        ("OTAN_DEFEAT132", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT132.png" ))]),
        ("OTAN_DEFEAT133", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT133.png" ))]),
        ("OTAN_DEFEAT134", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT134.png" ))]),
        ("OTAN_DEFEAT135", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT135.png" ))]),
        ("OTAN_DEFEAT136", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT136.png" ))]),
        ("OTAN_DEFEAT137", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT137.png" ))]),
        ("OTAN_DEFEAT138", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT138.png" ))]),
        ("OTAN_DEFEAT139", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT139.png" ))]),
        ("OTAN_DEFEAT140", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT140.png" ))]),
        ("OTAN_DEFEAT141", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT141.png" ))]),
        ("OTAN_DEFEAT142", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT142.png" ))]),
        ("OTAN_DEFEAT143", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT143.png" ))]),
        ("OTAN_DEFEAT144", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT144.png" ))]),
        ("OTAN_DEFEAT145", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT145.png" ))]),
        ("OTAN_DEFEAT146", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT146.png" ))]),
        ("OTAN_DEFEAT147", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT147.png" ))]),
        ("OTAN_DEFEAT148", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT148.png" ))]),
        ("OTAN_DEFEAT149", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT149.png" ))]),
        ("OTAN_DEFEAT150", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT150.png" ))]),
        ("OTAN_DEFEAT151", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT151.png" ))]),
        ("OTAN_DEFEAT152", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT152.png" ))]),
        ("OTAN_DEFEAT153", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT153.png" ))]),
        ("OTAN_DEFEAT154", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT154.png" ))]),
        ("OTAN_DEFEAT155", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT155.png" ))]),
        ("OTAN_DEFEAT156", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT156.png" ))]),
        ("OTAN_DEFEAT157", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT157.png" ))]),
        ("OTAN_DEFEAT158", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT158.png" ))]),
        ("OTAN_DEFEAT159", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT159.png" ))]),
        ("OTAN_DEFEAT160", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT160.png" ))]),
        ("OTAN_DEFEAT161", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT161.png" ))]),
        ("OTAN_DEFEAT162", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT162.png" ))]),
        ("OTAN_DEFEAT163", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT163.png" ))]),
        ("OTAN_DEFEAT164", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT164.png" ))]),
        ("OTAN_DEFEAT165", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT165.png" ))]),
        ("OTAN_DEFEAT166", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT166.png" ))]),
        ("OTAN_DEFEAT167", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT167.png" ))]),
        ("OTAN_DEFEAT168", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT168.png" ))]),
        ("OTAN_DEFEAT169", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT169.png" ))]),
        ("OTAN_DEFEAT170", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT170.png" ))]),
        ("OTAN_DEFEAT171", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT171.png" ))]),
        ("OTAN_DEFEAT172", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT172.png" ))]),
        ("OTAN_DEFEAT173", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT173.png" ))]),
        ("OTAN_DEFEAT174", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT174.png" ))]),
        ("OTAN_DEFEAT175", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT175.png" ))]),
        ("OTAN_DEFEAT176", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT176.png" ))]),
        ("OTAN_DEFEAT177", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT177.png" ))]),
        ("OTAN_DEFEAT178", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT178.png" ))]),
        ("OTAN_DEFEAT179", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT179.png" ))]),
        ("OTAN_DEFEAT180", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/DEFEAT/OTAN_DEFEAT180.png" ))]),
//-------------------------------------------------------------------------------------
        ("OTAN_VICTORY000", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY000.png" ))]),
        ("OTAN_VICTORY001", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY001.png" ))]),
        ("OTAN_VICTORY002", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY002.png" ))]),
        ("OTAN_VICTORY003", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY003.png" ))]),
        ("OTAN_VICTORY004", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY004.png" ))]),
        ("OTAN_VICTORY005", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY005.png" ))]),
        ("OTAN_VICTORY006", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY006.png" ))]),
        ("OTAN_VICTORY007", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY007.png" ))]),
        ("OTAN_VICTORY008", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY008.png" ))]),
        ("OTAN_VICTORY009", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY009.png" ))]),
        ("OTAN_VICTORY010", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY010.png" ))]),
        ("OTAN_VICTORY011", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY011.png" ))]),
        ("OTAN_VICTORY012", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY012.png" ))]),
        ("OTAN_VICTORY013", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY013.png" ))]),
        ("OTAN_VICTORY014", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY014.png" ))]),
        ("OTAN_VICTORY015", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY015.png" ))]),
        ("OTAN_VICTORY016", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY016.png" ))]),
        ("OTAN_VICTORY017", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY017.png" ))]),
        ("OTAN_VICTORY018", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY018.png" ))]),
        ("OTAN_VICTORY019", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY019.png" ))]),
        ("OTAN_VICTORY020", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY020.png" ))]),
        ("OTAN_VICTORY021", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY021.png" ))]),
        ("OTAN_VICTORY022", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY022.png" ))]),
        ("OTAN_VICTORY023", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY023.png" ))]),
        ("OTAN_VICTORY024", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY024.png" ))]),
        ("OTAN_VICTORY025", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY025.png" ))]),
        ("OTAN_VICTORY026", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY026.png" ))]),
        ("OTAN_VICTORY027", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY027.png" ))]),
        ("OTAN_VICTORY028", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY028.png" ))]),
        ("OTAN_VICTORY029", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY029.png" ))]),
        ("OTAN_VICTORY030", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY030.png" ))]),
        ("OTAN_VICTORY031", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY031.png" ))]),
        ("OTAN_VICTORY032", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY032.png" ))]),
        ("OTAN_VICTORY033", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY033.png" ))]),
        ("OTAN_VICTORY034", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY034.png" ))]),
        ("OTAN_VICTORY035", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY035.png" ))]),
        ("OTAN_VICTORY036", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY036.png" ))]),
        ("OTAN_VICTORY037", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY037.png" ))]),
        ("OTAN_VICTORY038", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY038.png" ))]),
        ("OTAN_VICTORY039", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY039.png" ))]),
        ("OTAN_VICTORY040", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY040.png" ))]),
        ("OTAN_VICTORY041", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY041.png" ))]),
        ("OTAN_VICTORY042", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY042.png" ))]),
        ("OTAN_VICTORY043", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY043.png" ))]),
        ("OTAN_VICTORY044", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY044.png" ))]),
        ("OTAN_VICTORY045", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY045.png" ))]),
        ("OTAN_VICTORY046", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY046.png" ))]),
        ("OTAN_VICTORY047", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY047.png" ))]),
        ("OTAN_VICTORY048", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY048.png" ))]),
        ("OTAN_VICTORY049", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY049.png" ))]),
        ("OTAN_VICTORY050", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY050.png" ))]),
        ("OTAN_VICTORY051", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY051.png" ))]),
        ("OTAN_VICTORY052", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY052.png" ))]),
        ("OTAN_VICTORY053", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY053.png" ))]),
        ("OTAN_VICTORY054", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY054.png" ))]),
        ("OTAN_VICTORY055", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY055.png" ))]),
        ("OTAN_VICTORY056", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY056.png" ))]),
        ("OTAN_VICTORY057", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY057.png" ))]),
        ("OTAN_VICTORY058", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY058.png" ))]),
        ("OTAN_VICTORY059", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY059.png" ))]),
        ("OTAN_VICTORY060", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY060.png" ))]),
        ("OTAN_VICTORY061", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY061.png" ))]),
        ("OTAN_VICTORY062", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY062.png" ))]),
        ("OTAN_VICTORY063", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY063.png" ))]),
        ("OTAN_VICTORY064", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY064.png" ))]),
        ("OTAN_VICTORY065", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY065.png" ))]),
        ("OTAN_VICTORY066", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY066.png" ))]),
        ("OTAN_VICTORY067", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY067.png" ))]),
        ("OTAN_VICTORY068", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY068.png" ))]),
        ("OTAN_VICTORY069", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY069.png" ))]),
        ("OTAN_VICTORY070", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY070.png" ))]),
        ("OTAN_VICTORY071", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY071.png" ))]),
        ("OTAN_VICTORY072", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY072.png" ))]),
        ("OTAN_VICTORY073", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY073.png" ))]),
        ("OTAN_VICTORY074", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY074.png" ))]),
        ("OTAN_VICTORY075", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY075.png" ))]),
        ("OTAN_VICTORY076", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY076.png" ))]),
        ("OTAN_VICTORY077", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY077.png" ))]),
        ("OTAN_VICTORY078", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY078.png" ))]),
        ("OTAN_VICTORY079", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY079.png" ))]),
        ("OTAN_VICTORY080", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY080.png" ))]),
        ("OTAN_VICTORY081", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY081.png" ))]),
        ("OTAN_VICTORY082", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY082.png" ))]),
        ("OTAN_VICTORY083", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY083.png" ))]),
        ("OTAN_VICTORY084", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY084.png" ))]),
        ("OTAN_VICTORY085", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY085.png" ))]),
        ("OTAN_VICTORY086", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY086.png" ))]),
        ("OTAN_VICTORY087", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY087.png" ))]),
        ("OTAN_VICTORY088", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY088.png" ))]),
        ("OTAN_VICTORY089", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY089.png" ))]),
        ("OTAN_VICTORY090", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY090.png" ))]),
        ("OTAN_VICTORY091", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY091.png" ))]),
        ("OTAN_VICTORY092", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY092.png" ))]),
        ("OTAN_VICTORY093", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY093.png" ))]),
        ("OTAN_VICTORY094", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY094.png" ))]),
        ("OTAN_VICTORY095", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY095.png" ))]),
        ("OTAN_VICTORY096", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY096.png" ))]),
        ("OTAN_VICTORY097", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY097.png" ))]),
        ("OTAN_VICTORY098", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY098.png" ))]),
        ("OTAN_VICTORY099", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY099.png" ))]),
        ("OTAN_VICTORY100", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY100.png" ))]),
        ("OTAN_VICTORY101", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY101.png" ))]),
        ("OTAN_VICTORY102", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY102.png" ))]),
        ("OTAN_VICTORY103", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY103.png" ))]),
        ("OTAN_VICTORY104", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY104.png" ))]),
        ("OTAN_VICTORY105", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY105.png" ))]),
        ("OTAN_VICTORY106", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY106.png" ))]),
        ("OTAN_VICTORY107", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY107.png" ))]),
        ("OTAN_VICTORY108", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY108.png" ))]),
        ("OTAN_VICTORY109", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY109.png" ))]),
        ("OTAN_VICTORY110", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY110.png" ))]),
        ("OTAN_VICTORY111", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY111.png" ))]),
        ("OTAN_VICTORY112", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY112.png" ))]),
        ("OTAN_VICTORY113", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY113.png" ))]),
        ("OTAN_VICTORY114", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY114.png" ))]),
        ("OTAN_VICTORY115", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY115.png" ))]),
        ("OTAN_VICTORY116", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY116.png" ))]),
        ("OTAN_VICTORY117", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY117.png" ))]),
        ("OTAN_VICTORY118", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY118.png" ))]),
        ("OTAN_VICTORY119", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY119.png" ))]),
        ("OTAN_VICTORY120", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY120.png" ))]),
        ("OTAN_VICTORY121", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY121.png" ))]),
        ("OTAN_VICTORY122", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY122.png" ))]),
        ("OTAN_VICTORY123", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY123.png" ))]),
        ("OTAN_VICTORY124", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY124.png" ))]),
        ("OTAN_VICTORY125", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY125.png" ))]),
        ("OTAN_VICTORY126", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY126.png" ))]),
        ("OTAN_VICTORY127", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY127.png" ))]),
        ("OTAN_VICTORY128", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY128.png" ))]),
        ("OTAN_VICTORY129", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY129.png" ))]),
        ("OTAN_VICTORY130", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY130.png" ))]),
        ("OTAN_VICTORY131", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY131.png" ))]),
        ("OTAN_VICTORY132", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY132.png" ))]),
        ("OTAN_VICTORY133", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY133.png" ))]),
        ("OTAN_VICTORY134", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY134.png" ))]),
        ("OTAN_VICTORY135", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY135.png" ))]),
        ("OTAN_VICTORY136", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY136.png" ))]),
        ("OTAN_VICTORY137", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY137.png" ))]),
        ("OTAN_VICTORY138", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY138.png" ))]),
        ("OTAN_VICTORY139", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY139.png" ))]),
        ("OTAN_VICTORY140", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY140.png" ))]),
        ("OTAN_VICTORY141", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY141.png" ))]),
        ("OTAN_VICTORY142", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY142.png" ))]),
        ("OTAN_VICTORY143", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY143.png" ))]),
        ("OTAN_VICTORY144", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY144.png" ))]),
        ("OTAN_VICTORY145", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY145.png" ))]),
        ("OTAN_VICTORY146", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY146.png" ))]),
        ("OTAN_VICTORY147", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY147.png" ))]),
        ("OTAN_VICTORY148", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY148.png" ))]),
        ("OTAN_VICTORY149", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY149.png" ))]),
        ("OTAN_VICTORY150", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY150.png" ))]),
        ("OTAN_VICTORY151", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY151.png" ))]),
        ("OTAN_VICTORY152", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY152.png" ))]),
        ("OTAN_VICTORY153", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY153.png" ))]),
        ("OTAN_VICTORY154", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY154.png" ))]),
        ("OTAN_VICTORY155", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY155.png" ))]),
        ("OTAN_VICTORY156", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY156.png" ))]),
        ("OTAN_VICTORY157", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY157.png" ))]),
        ("OTAN_VICTORY158", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY158.png" ))]),
        ("OTAN_VICTORY159", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY159.png" ))]),
        ("OTAN_VICTORY160", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY160.png" ))]),
        ("OTAN_VICTORY161", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY161.png" ))]),
        ("OTAN_VICTORY162", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY162.png" ))]),
        ("OTAN_VICTORY163", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY163.png" ))]),
        ("OTAN_VICTORY164", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY164.png" ))]),
        ("OTAN_VICTORY165", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY165.png" ))]),
        ("OTAN_VICTORY166", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY166.png" ))]),
        ("OTAN_VICTORY167", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY167.png" ))]),
        ("OTAN_VICTORY168", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY168.png" ))]),
        ("OTAN_VICTORY169", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY169.png" ))]),
        ("OTAN_VICTORY170", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY170.png" ))]),
        ("OTAN_VICTORY171", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY171.png" ))]),
        ("OTAN_VICTORY172", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY172.png" ))]),
        ("OTAN_VICTORY173", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY173.png" ))]),
        ("OTAN_VICTORY174", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY174.png" ))]),
        ("OTAN_VICTORY175", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY175.png" ))]),
        ("OTAN_VICTORY176", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY176.png" ))]),
        ("OTAN_VICTORY177", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY177.png" ))]),
        ("OTAN_VICTORY178", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY178.png" ))]),
        ("OTAN_VICTORY179", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY179.png" ))]),
        ("OTAN_VICTORY180", MAP [(~/ComponentState/Normal, TUIResourceTexture_Common( FileName = "GameData:/Assets/2D/Interface/UseInGame/LDHint/endGame/OTAN/VICTORY/OTAN_VICTORY180.png" ))]),
]
)
