template BUCKSpecificScrollingContainerDescriptor
[
    ElementName : string = "",
    UniqueName : string = "",

    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0]),

    FitStyle : int = ~/ContainerFitStyle/None,

    HidePointerEvents : bool = false,

    HasBorder : bool = false,
    BorderThicknessToken : string = "",
    BorderLineColorToken : string = "",

    HasBackground : bool = false,
    BackgroundBlockColorToken : string = "",

    FitToMaximumSize : TRTTILength2 = nil,

    ExternalScrollbar : bool = true,
    CanScrollVertically : bool = true,
    CanScrollHorizontally : bool = true,
    HasVerticalScrollbar : bool = false,
    HasHorizontalScrollbar : bool = false,
    ScrollStepSize : float2 = [0.0, 0.0],
    ScrollBarBackgroundToken : string = "Scrollbars/Background",
    ScrollBarElevatorBackgroundToken : string = "Scrollbars/Elevator/Background",
    ChildFitToContent : bool = false,

    HorizontalScrollbarComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 7.0]
        AlignementToAnchor = [0.0, 1.0]
        AlignementToFather = [0.0, 1.0]
    ),

    VerticalScrollbarComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [7.0, 0.0]
        MagnifiableOffset = [-6.0, 0.0]
        AlignementToFather = [1.0, 0.0]
        AlignementToAnchor = [1.0, 0.0]
    ),

    Components : LIST<TBUCKContainerDescriptor> = [],
    BackgroundComponents : LIST<TBUCKContainerDescriptor> = []
]
is BUCKScrollingContainerDescriptor
(
    ElementName = <ElementName>
    UniqueName = <UniqueName>
    ComponentFrame = <ComponentFrame>
    FitStyle = <FitStyle>
    ChildFitToContent = <ChildFitToContent>
    ExternalScrollbar = <ExternalScrollbar>
    HidePointerEvents = <HidePointerEvents>
    HasBorder = <HasBorder>
    BorderThicknessToken = <BorderThicknessToken>
    BorderLineColorToken = <BorderLineColorToken>

    HasBackground = <HasBackground>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    ScrollStepSize = <ScrollStepSize>

    CanScrollVertically = <CanScrollVertically>
    CanScrollHorizontally = <CanScrollHorizontally>

    FitToMaximumSize = <FitToMaximumSize>
    BackgroundComponents = <BackgroundComponents>
    // ---------------------------------------------------------------------------------------------
    // VERTICAL SCROLLBAR
    // ---------------------------------------------------------------------------------------------
    VerticalScrollbar = (<HasVerticalScrollbar> ? BUCKScrollbarDescriptor
    (
        ComponentFrame = <VerticalScrollbarComponentFrame>

        FirstMargin = TRTTILength(Magnifiable = 4.0)
        InterItemMargin = TRTTILength(Magnifiable = 4.0)
        LastMargin = TRTTILength(Magnifiable = 4.0)
        ElevatorCageAreaName = <ElementName> + '/VerticalScrollBar/Cage'
        ElevatorButtonName = <ElementName> + '/VerticalScrollBar/Elevator'
        ElevatorCageArea = BUCKSensibleAreaDescriptor
        (
            ElementName = <ElementName> + '/VerticalScrollBar/Cage'
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            MaskEvents = true

            HasBackground = true
            BackgroundBlockColorToken = <ScrollBarBackgroundToken>

            Components =
            [
                BUCKSensibleAreaDescriptor
                (
                    ElementName = <ElementName> + '/VerticalScrollBar/Elevator'
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 0.0]
                    )

                    MaskEvents = true

                    Components =
                    [
                        BUCKContainerDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 1.0]
                            )

                            HasBackground = true
                            BackgroundBlockColorToken = <ScrollBarElevatorBackgroundToken>
                        )
                    ]
                )
            ]
        )
    ) : nil)

    // ---------------------------------------------------------------------------------------------
    // HORIZONTAL SCROLLBAR
    // ---------------------------------------------------------------------------------------------
    HorizontalScrollbar = (<HasHorizontalScrollbar> ? BUCKScrollbarDescriptor
    (
        ComponentFrame = <HorizontalScrollbarComponentFrame>

        Axis = ~/ListAxis/Horizontal
        FirstMargin = TRTTILength(Magnifiable = 4.0)
        InterItemMargin = TRTTILength(Magnifiable = 4.0)
        LastMargin = TRTTILength(Magnifiable = 4.0)
        ElevatorCageAreaName = <ElementName> + '/HorizontalScrollBar/Cage'
        ElevatorButtonName = <ElementName> + '/HorizontalScrollBar/Elevator'
        ElevatorCageArea = BUCKSensibleAreaDescriptor
        (
            ElementName = <ElementName> + '/HorizontalScrollBar/Cage'
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            MaskEvents = true

            HasBackground = true
            BackgroundBlockColorToken = "Scrollbars/Background"

            Components =
            [
                BUCKSensibleAreaDescriptor
                (
                    ElementName = <ElementName> + '/HorizontalScrollBar/Elevator'
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [0.0, 1.0]
                    )

                    MaskEvents = true

                    Components =
                    [
                        BUCKContainerDescriptor
                        (
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 1.0]
                            )

                            HasBackground = true
                            BackgroundBlockColorToken = "Scrollbars/Elevator/Background"
                        )
                    ]
                )
            ]
        )
    ) : nil)

    Components = <Components>
)
