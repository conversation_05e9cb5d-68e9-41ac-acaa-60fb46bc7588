
// -------------------------------------------------------------------------------------------------
// Specific Checkbox
// -------------------------------------------------------------------------------------------------

DefaultCheckBoxDims is [ 24.0, 24.0 ]

template BUCKSpecificCheckBoxDescriptor
[
    UniqueName : string = '',
    ElementName : string = '',

    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = DefaultCheckBoxDims
        MagnifiableOffset = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
        AlignementToAnchor = [0.0, 0.0]
    ),

    IsFocusable : bool = false,
    FocusMapping : TEugBMutablePBaseClass = nil,
    ComponentStateLocked : bool = true,
    LeftClickSound : TSoundDescriptor = nil,
    BackgroundBlockColorToken : string = "Checkbox/Background",
    BorderLineColorToken : string = "Checkbox/Frame",
    TextureToken : string = "StyleDeskTexture_CheckBox",
    TextureColorToken : string = '',
    HasBackground : bool = true,
    HasBorder : bool = true,
    Components : LIST<TBUCKContainerDescriptor> = [],
]
is BUCKCheckBoxDescriptor
(
    ElementName = <ElementName>
    UniqueName = <UniqueName>

    ComponentFrame = <ComponentFrame>

    IsFocusable = <IsFocusable>
    FocusMapping = <FocusMapping>

    LeftClickSound = <LeftClickSound>

    HidePointerEvents = true
    ComponentStateLocked = <ComponentStateLocked>

    HasBackground = <HasBackground>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    HasBorder = <HasBorder>
    BorderLineColorToken = <BorderLineColorToken>
    BorderThicknessToken = "1"

    Components =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            TextureToken = <TextureToken>
            TextureColorToken = <TextureColorToken>
        )
    ]
)

// -------------------------------------------------------------------------------------------------
// Sorting Checkbox
// -------------------------------------------------------------------------------------------------

template BUCKSpecificSortingCheckBoxDescriptor
[
    UniqueName : string = '',
    ElementName : string = '',

    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI(
        MagnifiableWidthHeight = DefaultCheckBoxDims
        MagnifiableOffset = [0.0, 0.0]
        AlignementToFather = [0.0, 0.0]
        AlignementToAnchor = [0.0, 0.0]
    ),

    HasBackground : bool = true,
    HasBorder : bool = true,
]
is BUCKCheckBoxDescriptor
(
    UniqueName = <UniqueName>
    ElementName = <ElementName>

    ComponentFrame = <ComponentFrame>
    UniformDrawer = $/UserInterface/UIUniformDrawer

    HasBackground = <HasBackground>
    BackgroundBlockColorToken = "SortingCheckbox/Background"

    HasBorder = <HasBorder>
    BorderLineColorToken = "SortingCheckbox/Frame"
    BorderThicknessToken = "1"

    Components =
    [
        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
            )

            TextureToken   = "StyleDeskTexture_SortingCheckBox"
        )
    ]
)

