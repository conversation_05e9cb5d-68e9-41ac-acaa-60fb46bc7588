UIInGameResource is TUISpecificInGameResources
(
    Handler = $/UI/ResourcesHandler/InGameResourcesHandler

    UILayer                     = $/UserInterface/UILayer_2DInterface_InGame
    UILayerLDHint               = $/UserInterface/UILayer_2DInterfaceLDHint
    CommonResources             = ~/UICommonResource

    MainStyleGuide              = ~/DefaultStyleGuide

    UnitReticleDescriptor = ~/UISpecificUnitLabelReticleDescriptor
    LabelRackForAggregationDescriptor = ~/UISpecificInGameLabelRackForAggregation

    ViewDescriptors = MAP
    [
        ("UISpecificIngameChatViewDescriptor", UISpecificIngameChatViewDescriptor(PanelColorStyle = "TacticPanel" ButtonColorStyle = "TacticButton_highlightable")),
        ("UISpecificInGameFlarePanelViewDescriptor", ~/UIInGameFlarePanelViewDescriptor),
        ("UISpecificIngameHUDTransportAndUnitsInfoViewDescriptor", ~/UISpecificInGameHUDTransportAndUnitsInfoViewDescriptor),
        ("UISpecificUnitInfoPanelViewDescriptor", UISpecificUnitInfoPanelViewDescriptor()),
        ("SkirmishProductionMenu", UISpecificSkirmishProductionMenuViewDescriptor()),
        ("SkirmishFromStrategicProductionMenu", UISpecificSkirmishProductionMenuViewDescriptor(IsFromStrategic = true)),
        ("InGameUnitLabel", ~/UISpecificUnitLabelViewDescriptor),
        ("InGameUnitLabelNameOnly", ~/UISpecificUnitLabelViewDescriptorNameOnly),
        ("InGameUnitLabelForMultiSelection", ~/UISpecificUnitLabelViewForMultiSelectionDescriptor),
        ("UISpecificUnitLabelAggregation", ~/UISpecificUnitLabelAggregationViewDescriptor),
        ("UISpecificUnitLabelAggregationNameOnly", ~/UISpecificUnitLabelAggregationViewNameOnlyDescriptor),
        ("UISpecificUnitLabelSmartGroup", ~/UISpecificUnitLabelSmartGroupViewDescriptor),
        ("UISpecificUnitLabelSmartGroupNameOnly", ~/UISpecificUnitLabelSmartGroupViewNameOnlyDescriptor),
        ("UISpecificTacticCubeActionViewDescriptor", ~/UISpecificTacticCubeActionViewDescriptor),
        ("UISpecificTacticEngagementRulesViewDescriptor", ~/UISpecificTacticEngagementRulesViewDescriptor),
        ("UISpecificOpticalFieldViewDescriptor", ~/UISpecificOpticalFieldViewDescriptor),
        ("UISpecificInGameHUDTimePanelViewDescriptor", ~/UISpecificInGameHUDTimePanelViewDescriptor),
        ("UICommonHUDPingDisplayViewDescriptor", ~/UICommonHUDPingDisplayViewDescriptor),
        ("UISpecificOffMapViewDescriptor", ~/UISpecificOffMapViewDescriptor),
        ("UISpecificOffMapAirplaneViewDescriptor", ~/UISpecificOffMapAirplaneViewDescriptor),
        ("UISpecificHUDScoreViewDescriptor", ~/UISpecificHUDScoreViewDescriptor),
        ("UISpecificHUDScoreAllianceViewDescriptor", ~/UISpecificHUDScoreAllianceViewDescriptor),
        ("UISpecificHUDScorePlayerViewDescriptor", ~/UISpecificHUDScorePlayerViewDescriptor),
        ("UIDisplayStartingInformationViewDescriptor", ~/UISpecificDisplayStartingInformationViewDescriptor),
        ("UIAutoDeployViewDescriptor", ~/UISpecificAutoDeployViewDescriptor),
        ("UILaunchBattleViewDescriptor", ~/UILaunchBattleViewDescriptor),
        ("UIIdleUnitViewDescriptor", ~/UIIdleUnitViewDescriptor),
        ("UIReinforcementLabelViewDescriptor", ~/UISpecificReinforcementLabelViewDescriptor),
        ("UICommandZoneLabelViewDescriptor", ~/UISpecificCommandZoneLabelViewDescriptor),
        ("ObjectiveViewDescriptor", ~/UISpecificInGameObjectiveBriefViewDescriptor),
        ("MultiSelectionPanelViewDescriptor", ~/UISpecificHUDMultiSelectionPanelViewDescriptor),
        ("SmartGroupSelectionPanelViewDescriptor", UISpecificSmartGroupSelectionPanelViewDescriptor(MainComponentContainerUniqueName = "UISpecificSmartGroupSelectionPanelView")),
        ("LDVideoViewDescriptor", ~/UIInGameLDVideoViewDescriptor),
    ]

    MainContainerResource       = ~/InGameMainContainerResource
    ShortcutUserInputLayer      = $/M3D/Input/UserInputLayerHandler/InputLayer_InGameShortcuts
    LDScriptUserInputLayer      =  $/M3D/Input/UserInputLayerHandler/InputLayer_LDScriptInterceptor

    CommonHUDResources          = ~/InGameHUDResource
    HUDResource                 = ~/InGameHUDResource
    LabelResource               = ~/CommonLabelResource
    FlareLabelResources         = ~/CommonFlareLabelResources
    PlayerMissionLabelResources = ~/SpecificInGamePlayerMissionLabelResources
    GabaritResources            = ~/InGameGabaritResources
    UnitLabelResource           = ~/SpecificInGameUnitLabelResources
    OutmapFeedbackResource      = ~/UISpecificOutmapFeedbackResources
    FeedbackLineOfSightResource = ~/FeedbackLineOfSightResource
    WarningPanelResources       = ~/WarningPanelInGameResources
    LabelsOnMapResources        = ~/InGameLabelsOnMapResources
    ScriptedLabelResources      = ~/IngameScriptedLabelResources
    FeedbackManagerDescriptor   = ~/IngameFeedbackMessageManagerDescriptor
    GameRulesHelper             = ~/GameRulesHelper
    CorrectedShotRessources     = ~/InGameCorrectedShotFeedbackRessources
    ReinforcementResource       = ~/ReinforcementResource

    SelectionHandlerInGameResources = ~/SelectionResources
    GroupSelectionReminderResources = ~/GroupSelectionReminder
    PositionEvents              = ~/MainPositionEvents
    MousePolicyManagerResources = ~/InGameMousePolicyManagerResources
    OptionManager               = $/GameOptionsInterface/GameOptionsInterfaceWrapper

    CommanderResources          = ~/CommanderResources

    HelperVisibility            = ~/InGameVisibilityHelper

    CommandementZoneResources        = ~/CommandementZoneResources
)

//-------------------------------------------------------------------------------------

IngameHUDRightFramesWidth is 320.0

InGameMainContainerResource is TUICommonMainContainerResource
(
    ContentRefSize = [ 2134 , 1440 ]
    SafeBoxMin     = [  107 ,   80 ]
    SafeBoxSize    = ~/FullScreenSafeBoxSize

    ForegroundComponents = BUCKContainerDescriptor
    (
        ComponentFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [1.0, 1.0])

        Components =
        [
            BUCKContainerDescriptor
            (
                UniqueName = "SpecificOffMapViewMainContainer"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableOffset = [0.0, -5.0]
                    AlignementToAnchor = [0.0, 1.0]
                    AlignementToFather = [0.0, 1.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContent
            ),

            BUCKContainerDescriptor
            (
                UniqueName = "barre_du_haut"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 0.0]
                    MagnifiableWidthHeight  = [0, 40.0]
                    AlignementToAnchor = [0, 0.0]
                    AlignementToFather = [0, 0.0]
                    // MagnifiableOffset = [8,0]
                )

                HasBackground = true
                BackgroundBlockColorToken = "H2_bleu_1"
            ),

            ~/PanelInfoPartie,


            BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableOffset = [-8.0 ,0.0]

                    MagnifiableWidthHeight = [IngameHUDRightFramesWidth, 0.0]
                    AlignementToAnchor = [1.0, 0.0]
                    AlignementToFather = [1.0, 0.0]
                )
                InterItemMargin = TRTTILength( Magnifiable = 4.0 )

                Axis = ~/ListAxis/Vertical
                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "SpecificInGameHUDTimePanelViewMainContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )

                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "InGameGlobalObjectivesContainer"

                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "UISpecificMiniMapInfoViewMainContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "UICommonFlarePanelViewMainContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "SpecificInGameHUDScoreViewMainContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                ]
            ),
            BUCKContainerDescriptor
            (
                UniqueName = "SpecificLaunchBattleMainComponentDescriptor"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]

                    MagnifiableOffset = [0.0, 78.0]
                    MagnifiableWidthHeight = [DeploymentPhasePanelWidth, DeploymentPhasePanelHeight]
                )
            ),
            BUCKContainerDescriptor
            (
                UniqueName = "SpecificDisplayStartingInformationMainComponentDescriptor"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToFather = [1.0, 1.0]
                    AlignementToAnchor = [1.0, 1.0]
                    MagnifiableOffset = [-5.0, -35.0]
                )

                FitStyle = ~/ContainerFitStyle/FitToContent
            ),

            BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableOffset = [10.0, 0.0]
                    RelativeWidthHeight = [1.0, 0.0]
                )

                Axis = ~/ListAxis/Vertical

                FirstMargin = TRTTILength(Magnifiable = 4.0)
                InterItemMargin = TRTTILength(Magnifiable = 4.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "SpecificInGameHUDProductionViewMainContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "UnitContainerListScrollingContainerContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                ]
            ),

            ~/ShinyBorder,

            BUCKListDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToFather = [ 0.5, 1.0 ]
                    AlignementToAnchor = [ 0.5, 1.0 ]
                )

                Axis = ~/ListAxis/Vertical
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                LastMargin = TRTTILength(Magnifiable = 5.0)
                InterItemMargin = TRTTILength(Magnifiable = 2.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "UISpecificUnitSelectionPanelView"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                AlignementToFather = [0.5, 0.0]
                                AlignementToAnchor = [0.5, 0.0]
                            )
                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "UISpecificSmartGroupSelectionPanelView"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                AlignementToFather = [ 0.5, 0.0 ]
                                AlignementToAnchor = [ 0.5, 0.0 ]
                            )

                            FitStyle = ~/ContainerFitStyle/FitToContent
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "UISpecificHUDMultiSelectionPanelView"
                            ComponentFrame = TUIFramePropertyRTTI()
                            FitStyle = ~/ContainerFitStyle/FitToContent
                        )
                    ),
                ]
            ),

            BUCKListDescriptor
            (
                UniqueName = "AllCubeAction"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    AlignementToFather = [ 1.0, 1.0 ]
                    AlignementToAnchor = [ 1.0, 1.0 ]
                    MagnifiableOffset = [ -10.0, 0.0 ]
                )

                Axis = ~/ListAxis/Vertical
                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                InterItemMargin = TRTTILength(Magnifiable = 2.0)
                LastMargin = TRTTILength(Magnifiable = 5.0)

                Elements =
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "UISpecificTacticCubeActionViewMainContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                AlignementToFather = [1.0, 0.0]
                                AlignementToAnchor = [1.0, 0.0]
                            )
                            FitStyle = ~/ContainerFitStyle/FitToContent
                        )
                    ),
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = BUCKContainerDescriptor
                        (
                            UniqueName = "UISpecificShortcutsForSelectionViewContainer"
                            ComponentFrame = TUIFramePropertyRTTI
                            (
                                RelativeWidthHeight = [1.0, 0.0]
                            )
                            FitStyle = ~/ContainerFitStyle/FitToContentVertically
                        )
                    ),
                ]
            ),
        ]
    )
)
