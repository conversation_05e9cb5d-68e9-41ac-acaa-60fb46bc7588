
private SkirmishProductionMenuBarTextHorizontalOffset is -6.0
// Skirmish Production Menu Container
template BUCKSpecificSkirmishProductionMenuMainComponentDescriptor
[
    IsFromStrategic : bool,
] is BUCKContainerDescriptor
(
    UniqueName = "HUDTopElement"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 0.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        BUCKListDescriptor
        (
            ElementName = "UnitProductionList"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [0.0, 0.0]
                AlignementToFather = [0.0, 0.5]
                AlignementToAnchor = [0.0, 0.5]
            )

            Axis = ~/ListAxis/Horizontal
            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

            FirstMargin = TRTTILength(Magnifiable = (<IsFromStrategic> ? 0 : 0.0))
            InterItemMargin = TRTTILength(Magnifiable = 30.0)
            LastMargin = TRTTILength(Magnifiable = 10.0)

            ChildFitToContent = true

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/ProductionTypeSelectionList
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = ~/SkirmishProductionMenuCategoryList
                ),
            ] +
            (<IsFromStrategic> ?
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = ~/SkirmishFromStrategicProductionMenuList
                    ),
                ] :
                [
                    BUCKListElementDescriptor
                    (
                        ComponentDescriptor = ~/SkirmishProductionMenuCombatGroupList
                    ),
                ]
            )
        ),
    ]
)

// -------------------------------------------------------------------------------------------------

private PanelInfoPartie is BUCKListDescriptor
(
    ElementName = "ListeDesRessources"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 32.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
        MagnifiableOffset = [0,4]
    )

    Axis = ~/ListAxis/Horizontal
    HidePointerEvents = true
    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/PointsCommandement
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/ChronoPointCommandement
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/NombreSecteur
        ),
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner
        (
            BackgroundBlockColorToken = "H2_bleu_2_40p"
            BorderLineColorToken = "H2_bleu_2_40p"
        )
    ]
)

//-------------------------------------------------------------------------------------

private PointsCommandement is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI (RelativeWidthHeight = [0.0, 1.0])

    Axis = ~/ListAxis/Horizontal
    FirstMargin = TRTTILength( Magnifiable = 16.0 )
    InterItemMargin = TRTTILength( Magnifiable = 4.0 )
    LastMargin = TRTTILength( Magnifiable = 16.0 )

    HasBorder = true
    BorderThicknessToken = "1"
    BorderLineColorToken = "LigneSeparatricePanelRessources"
    BordersToDraw =  ~/TBorderSide/Right

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextureDescriptor
            (
                ElementName = "CommandPoints"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [16.0, 16.0]

                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                TextureToken = "UseInGame_CommandPoints"
                Components =
                 [
                    BUCKSpecificHintableArea
                    (
                        HintBodyToken = "LR_cmd"
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                UniqueName = "CommmandPointsText"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    AlignementToFather =  [0.0, 0.5]
                    AlignementToAnchor  = [0.0, 0.5]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Right
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/CutByDots

                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TextToken = "HPROD_CMDP"

                TextColor = "Gold"
                TextSize = "26"

                Hint = BUCKSpecificHintableArea
                (
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    HintBodyToken = "LR_cmd"
                )
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                UniqueName = "CommmandPointsIncomeText"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor  = [0.0, 0.5]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Right
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/CutByDots

                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TextToken = "HPROD_CMDI"

                TextColor = "Gold"
                TextSize = "16"

                Hint = BUCKSpecificHintableArea
                (
                    UniqueName = "CommmandPointsIncomeHintText"
                    ForbiddenTags = ["StrategicScenario"]
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    HintTitleToken = "HPD_INCT"
                    HintBodyToken = "LR_incob"
                )
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

private ChronoPointCommandement is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI (RelativeWidthHeight = [0.0, 1.0])

    Axis = ~/ListAxis/Horizontal
    FirstMargin = TRTTILength( Magnifiable = 16.0 )
    InterItemMargin = TRTTILength( Magnifiable = 4.0 )
    LastMargin = TRTTILength( Magnifiable = 8.0 )
    HasBorder = true
    BorderThicknessToken = "1"
    BorderLineColorToken = "LigneSeparatricePanelRessources"
    BordersToDraw =  ~/TBorderSide/Right

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [16.0, 16.0]

                    AlignementToFather = [0.0,0.5]
                    AlignementToAnchor = [0.0,0.5]
                )
                Components =
                [
                    BUCKChronoAnimatedTextureDescriptor
                    (
                        UniqueName = "CommmandPointsIncomeChrono"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        ChronoDrawer = $/UserInterface/ChronoDrawerCommon

                        ChronoTexture = "UseInGame_RequisitionTimer_fond"
                        TextureToken = "UseInGame_RequisitionTimer"

                        ChronoBackgroundColor = "Cyan"
                        ChronoForegroundColor = "Transparent"

                        TextureFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                        )

                        Components =
                        [
                            BUCKSpecificHintableArea
                            (
                                ForbiddenTags = ["StrategicScenario"]

                                DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                                HintTitleToken = "HPD_CHRONT"
                                HintBodyToken = "LR_timeB"
                            ),
                        ]
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                UniqueName = "CommmandPointsIncomeChronoText"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [32.0, 0.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor  = [0.0, 0.5]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/UserDefined

                TypefaceToken = "Liberator"
                BigLineAction = ~/BigLineAction/CutByDots

                TextColor = "CyanChrono"
                TextSize = "16"

                Hint = BUCKSpecificHintableArea
                (
                    ForbiddenTags = ["StrategicScenario"]
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    HintTitleToken = "HPD_CHRONT"
                    HintBodyToken = "LR_timeB"
                )
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------
// Container for Command Points, income and income countdown
private template SectorGaugeValue
[
    ElementName : string,
    BackgroundBlockColorToken : string,
    TextColor : string = "UIPlayerSpecific/Grey/Primary",
] is BUCKGaugeValueDescriptor
(
    ElementName = "SectorPointsGauge" + <ElementName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight    = [0.0, 1.0]
        AlignementToFather     = [0.0, 0.5]
        AlignementToAnchor     = [0.0, 0.5]
    )

    HasBackground = true
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    ValueTextName = "SectorPointsGauge" + <ElementName> + "Text"

    Components =
    [
        BUCKTextDescriptor
        (
            ElementName = "SectorPointsGauge" + <ElementName> + "Text"
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
                AlignementToFather  = [0.5, 0.5]
                AlignementToAnchor  = [0.5, 0.5]
            )

            ParagraphStyle = TParagraphStyle
            (
                Alignment = UIText_Center
                VerticalAlignment = UIText_VerticalCenter
                InterLine = 0
            )

            TextStyle = "Default"
            HorizontalFitStyle = ~/FitStyle/FitToContent
            TypefaceToken = "UIMainFont"

            TextSize = "20"
            TextColor = <TextColor>
        )
    ]
)

private NombreSecteur is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI(RelativeWidthHeight = [0.0, 1.0])

    Axis = ~/ListAxis/Horizontal
    FirstMargin = TRTTILength( Magnifiable = 8.0 )
    InterItemMargin = TRTTILength( Magnifiable = 4.0 )
    LastMargin = TRTTILength( Magnifiable = 16.0 )
    // HasBorder = true
    BorderThicknessToken = "1"
    BorderLineColorToken = "LigneSeparatricePanelRessources"
    BordersToDraw =  ~/TBorderSide/Right

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                UniqueName = "SectorPointsText"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [32.0, 0.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor  = [0.0, 0.5]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/UserDefined

                TypefaceToken = "Liberator"

                TextColor = "CyanChrono"
                TextSize = "24"

                TextDico = ~/LocalisationConstantes/dico_interface_ingame

                Components =
                [
                    BUCKSpecificHintableArea
                    (
                        HintTitleToken = "LR_sect"
                        HintBodyToken = "LR_sectB"
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKGaugeDescriptor
            (
                UniqueName = "SectorPointsGauge"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [120.0, 0.0]
                    RelativeWidthHeight    = [0.0, 0.65]
                    AlignementToFather     = [0.0, 0.5]
                    AlignementToAnchor     = [0.0, 0.5]
                )

                GridAlign = false
                HasBackground = false
                HasBorder = true
                BorderThicknessToken = '1'
                BorderLineColorToken = "BoutonTemps_Line"
                BordersToDraw = ~/TBorderSide/All

                DurationForGaugeFullChange = 1
                FullGaugeIfNoMax = true

                GaugeComponentNames =
                [
                    "SectorPointsGaugeBlue",
                    "SectorPointsGaugeGray",
                    "SectorPointsGaugeRed",
                ]

                Components =
                [
                    SectorGaugeValue
                    (
                        ElementName = "Blue"
                        TextColor = "playerHelper/texte/Otan_line2"
                        BackgroundBlockColorToken = "playerHelper/Otan_background"
                    ),
                    SectorGaugeValue
                    (
                        ElementName = "Gray"
                        BackgroundBlockColorToken = "LighterGray"
                    ),
                    SectorGaugeValue
                    (
                        ElementName = "Red"
                        TextColor = "score_05b"
                        BackgroundBlockColorToken = "playerHelper/Pact_background"
                    ),
                ]
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

// Container for Command Points, income and income countdown
private SkirmishProductionMenuCommandPointsAndIncomeContainer is BUCKSpecificButton
(
    ElementName = "MainButton"

    UniqueName = "ProductionMenuMainButton"

    ButtonMagnifiableOffset = [5.0, 5.0]
    ButtonMagnifiableWidthHeight=[170.0, 40.0]

    BackgroundBlockColorToken = "Transparent"
)

// -------------------------------------------------------------------------------------------------
// CategoryList
// -------------------------------------------------------------------------------------------------

// Categories List
private SkirmishProductionMenuCategoryList is BUCKListDescriptor
(
    ElementName = "CategoryButtonList"

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    InterItemMargin = TRTTILength(Magnifiable = 1.0)

    // envoie SkirmishProductionMenuCategoryButton
)

// -------------------------------------------------------------------------------------------------

// Combat Group List
private SkirmishProductionMenuCombatGroupList is BUCKListDescriptor
(
    ElementName = "CombatGroupButtonList"

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    InterItemMargin = TRTTILength(Magnifiable = 3.0)

    // envoie SkirmishProductionMenuCombatGroupButton
)

// -------------------------------------------------------------------------------------------------

// Pawn from strategic List
private SkirmishFromStrategicProductionMenuPawnList is BUCKListDescriptor
(
    ElementName = "SkirmishProductionMenuPawnList"

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    InterItemMargin = TRTTILength(Magnifiable = 3.0)

    // envoie SkirmishProductionMenuCombatGroupButton
)

// -------------------------------------------------------------------------------------------------

// Pawn from strategic List
private SkirmishFromStrategicProductionMenuList is BUCKListDescriptor
(
    ElementName = "SkirmishFromStrategicProductionMenuList"

    Axis = ~/ListAxis/Vertical
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    InterItemMargin = TRTTILength(Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/SkirmishFromStrategicProductionMenuPawnList
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/SkirmishProductionMenuCombatGroupList
        )
    ]
)

// -------------------------------------------------------------------------------------------------

private ProductionTypeButtonManager is TBUCKRadioButtonManager()

private SmartGroupTypeProductionButton is BUCKSpecificButton
(
    ElementName = "SmartGroupTypeProductionButton"
    // style spécial pour ces boutons
    BackgroundBlockColorToken  = "H2_bleu_5"

    TextColorToken = "BlancEquipe"
    TextSizeToken = "12"
    ButtonRelativeWidthHeight = [0.0, 0.0]
    ButtonMagnifiableWidthHeight = [96.0, 16]

    RoundedCorner = true
    RoundedVertexes = [true, false, false, true]

    IsTogglable = true
    CannotDeselect = true
    DefaultToggleValue = false

    RadioButtonManager = ~/ProductionTypeButtonManager

    HidePointerEvents = true

    OverrideTextElementName = true

    HasText = true
    TextElementName = ""
    TextToken = "HUD_PRDGRP"

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintBodyToken  = "deckSGop"
    )

    TextComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToFather = [0.5, 0.5]
        AlignementToAnchor = [0.5, 0.5]
    )
    TextParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
    )
)

// -------------------------------------------------------------------------------------------------

private DeckTypeProductionButton is BUCKSpecificButton
(
    ElementName = "DeckTypeProductionButton"
    // style spécial pour ces boutons
    BackgroundBlockColorToken  = "H2_bleu_5"

    TextColorToken = "BlancEquipe"
    TextSizeToken = "12"
    ButtonRelativeWidthHeight = [0.0, 0.0]
    ButtonMagnifiableWidthHeight = [96.0, 16]

    RoundedCorner = true
    RoundedVertexes = [false, true, true, false]

    IsTogglable = true
    CannotDeselect = true
    DefaultToggleValue = true

    RadioButtonManager = ~/ProductionTypeButtonManager

    HidePointerEvents = true

    OverrideTextElementName = true

    HasText = true
    TextElementName = ""
    TextToken = "HUD_PRDDCK"

    HintableAreaComponent = BUCKSpecificHintableArea
    (
        HintTitleToken  = ""
        HintBodyToken  = "deckOp"
        HintExtendedToken  = ""
    )

    TextComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToFather = [0.5, 0.5]
        AlignementToAnchor = [0.5, 0.5]
    )
    TextParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
    )
)

// -------------------------------------------------------------------------------------------------

private ProductionTypeSelectionList is BUCKListDescriptor
(
    ElementName = "ProductionTypeSelectionList"

    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Vertical
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/DeckTypeProductionButton
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/SmartGroupTypeProductionButton
        ),
    ]
)

// -------------------------------------------------------------------------------------------------
// SkirmishProductionMenuDivisionText
// -------------------------------------------------------------------------------------------------

private SkirmishProductionMenuDivisionText is BUCKTextDescriptor
(
    ElementName = "DivisionName"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Left
        VerticalAlignment = UIText_Bottom
        InterLine = 0
    )

    TextStyle = "Default"
    TextPadding = TRTTILength4( Magnifiable = [0.0, 0.0, 0.0, 6.0] )

    HorizontalFitStyle = ~/FitStyle/FitToContent

    TypefaceToken = "UIMainFont"
    BigLineAction = ~/BigLineAction/CutByDots

    TextSize = "24"
    TextDico = ~/LocalisationConstantes/dico_units
    TextColor = "LighterGray"
)

// -------------------------------------------------------------------------------------------------
// CategoryButton
// -------------------------------------------------------------------------------------------------

private SkirmishProductionMenuCategoryButtonRadioButtonManager is TBUCKRadioButtonManager()

// Unit Category Button

SkirmishProductionMenuCategoryButton is BUCKSpecificButton
(
    ElementName = "SkirmishProductionMenuCategoryButton"

    // style spécial pour ces boutons
    RoundedCorner = true
    BackgroundBlockColorToken  = ""

    TextColorToken = "ButtonHUD/Text2"
    TextSizeToken = "22"
    ButtonRelativeWidthHeight = [0.0, 0.0]
    ButtonMagnifiableWidthHeight = [55.0, 30.0]
    BigLineAction = ~/BigLineAction/ResizeFont

    IsTogglable = true
    RadioButtonManager = ~/SkirmishProductionMenuCategoryButtonRadioButtonManager

    TextPadding = TRTTILength4( Magnifiable = [2.0, 0.0, 2.0, 0.0] )

    HidePointerEvents = true

    OverrideTextElementName = true

    HasText = true
    TextElementName = "ButtonTextElement"
    TextDico = ~/LocalisationConstantes/dico_interface_ingame
    HintableAreaComponent = BUCKSpecificHintableArea
    (
        ElementName = "ButtonHintableArea"
        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
    )

    TextComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToFather = [0.5, 0.5]
        AlignementToAnchor = [0.5, 0.5]
    )

    TextParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
    )
)

// -------------------------------------------------------------------------------------------------

private SkirmishProductionMenuPawnButtonRadioButtonManager is TBUCKRadioButtonManager()

// boutons de production en Steelman
SkirmishProductionMenuPawnButton is BUCKButtonDescriptor
(
    ElementName = "SkirmishProductionMenuPawnButton"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [130.0, 32.0] // This width must be changed very carefuly as the buttons can overlap the cmd points
    )

    IsTogglable = true
    RadioButtonManager = ~/SkirmishProductionMenuPawnButtonRadioButtonManager
    HidePointerEvents = true

    Components =
    [
        PanelRoundedCorner
        (
            Radius = 3
            BackgroundBlockColorToken = "BoutonTemps_pawn"
            BorderLineColorToken = "BoutonTemps_Line"
            RoundedVertexes = [true, false, false, true]
        ),
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )
            BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
            Axis = ~/ListAxis/Horizontal

            FirstMargin = TRTTILength(Magnifiable = 4.0)
            LastMargin = TRTTILength(Magnifiable = 4.0)
            InterItemMargin = TRTTILength(Magnifiable = 1.0)

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = "PawnIconTexture"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [30.0, 30.0]
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        TextureFrame = TUIFramePropertyRTTI()
                        TextureColorToken = "ButtonHUD/Text2_pawn"
                        ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                    )
                ),
                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKSpecificTextWithHint
                    (
                        ElementName = "ButtonTextElement"

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 0.0]
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        ParagraphStyle = TParagraphStyle
                        (
                            Alignment = UIText_Left
                            VerticalAlignment = UIText_VerticalCenter
                            InterLine = 0
                        )

                        TextStyle = "Default"
                        TypefaceToken = "Liberator"

                        TextDico = ~/LocalisationConstantes/dico_interface_ingame

                        TextColor = "ButtonHUD/Text2_pawn"
                        TextSize = "14"
                        BigLineAction = ~/BigLineAction/CutByDots
                        HorizontalFitStyle = ~/FitStyle/UserDefined
                        VerticalFitStyle = ~/FitStyle/FitToContent
                    )
                )
            ]
        )
    ]
)

// -------------------------------------------------------------------------------------------------

private SkirmishProductionMenuCombatGroupButtonRadioButtonManager is TBUCKRadioButtonManager()

SkirmishProductionMenuCombatGroupButton is BUCKSpecificButton
(
    ElementName = "SkirmishProductionMenuCombatGroupButton"

    // style spécial pour ces boutons
    RoundedCorner = true
    BackgroundBlockColorToken  = ""
    PanelRoundedCorner_BackgroundBlockColorToken = "BoutonTemps_pawn"
    TextColorToken = "ButtonHUD/Text2_pawn"
    TextSizeToken = "14"
    ButtonRelativeWidthHeight = [0.0, 0.0]
    ButtonMagnifiableWidthHeight = [130, 30.0] // This width must be changed very carefuly as the buttons can overlap the cmd points
    ButtonMagnifiableOffset = [0.0, -2.0]
    TextTypefaceToken = "Eurostyle_Italic"
    IsTogglable = true
    RadioButtonManager = ~/SkirmishProductionMenuCombatGroupButtonRadioButtonManager

    HidePointerEvents = true

    OverrideTextElementName = true

    HasText = true
    TextElementName = "ButtonTextElement"
    BigLineAction = ~/BigLineAction/ResizeFont
    TextPadding = TRTTILength4( Magnifiable = [2.0, 0.0, 2.0, 0.0])

    TextDico = ~/LocalisationConstantes/dico_interface_ingame
    TextComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
        AlignementToFather = [0.5, 0.5]
        AlignementToAnchor = [0.5, 0.5]
    )

    TextParagraphStyle = TParagraphStyle
    (
        Alignment = UIText_Center
        VerticalAlignment = UIText_VerticalCenter
        InterLine = 0
    )
)



// -------------------------------------------------------------------------------------------------
// UnitButtonList
// -------------------------------------------------------------------------------------------------

// Unit Buttons List
private SkirmishProductionMenuUnitList is BUCKListDescriptor
(
    ElementName = "UnitContainerList"
    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
)

// -------------------------------------------------------------------------------------------------
// SmartGroupProductionButtonDescriptor
// -------------------------------------------------------------------------------------------------

private SmartGroupProductionButtonContainerDescriptor is BUCKContainerDescriptor
(
    ElementName = "SmartGroupProductionContainer"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [~/SmartGroupUnitButtonSize[0], 20.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                AlignementToFather = [0.5, 0.5]
                AlignementToAnchor = [0.5, 0.5]
            )

            FitStyle = ~/ContainerFitStyle/FitToContent

            Components =
            [
                BUCKListDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        AlignementToFather = [0.5, 0.5]
                        AlignementToAnchor = [0.5, 0.5]
                    )

                    Axis = ~/ListAxis/Vertical
                    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                    ChildFitToContent = true

                    Elements =
                    [
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = SmartGroupInfosContainer
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 0.0]
                                )
                                IsEditable = false
                            )
                        ),
                        BUCKListElementDescriptor
                        (
                            ComponentDescriptor = BUCKListDescriptor
                            (
                                ElementName = "SmartGroupButtonList"

                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    AlignementToFather = [0.5, 0.0]
                                    AlignementToAnchor = [0.5, 0.0]
                                )

                                Axis = ~/ListAxis/Vertical
                                BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                                Elements =
                                [
                                    // Ajout du bouton d'unite ici
                                ]
                            )
                        )
                    ]
                ),
            ]
        )
    ]
)

// -------------------------------------------------------------------------------------------------
// UnitListScrollingContainer
// -------------------------------------------------------------------------------------------------

// Unit List Scroll Container
private SkirmishProductionMenuUnitListScrollingContainer is BUCKSpecificScrollingContainerDescriptor
(
    ElementName = "UnitContainerListScrollingContainer"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    HasHorizontalScrollbar = true
    ExternalScrollbar = true

    Components =
    [
        ~/SkirmishProductionMenuUnitList
    ]
)

// -------------------------------------------------------------------------------------------------

template ProductionSpotlight
[
    UniqueName = "",
] is BUCKSpotlightDescriptor
(
    UniqueName = <UniqueName>
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 1.0]
    )
)

// -------------------------------------------------------------------------------------------------
// UISpecificSkirmishProductionMenuViewDescriptor
// -------------------------------------------------------------------------------------------------

template UISpecificSkirmishProductionMenuViewDescriptor
[
    IsFromStrategic : bool = false,
] is TUISpecificSkirmishProductionMenuViewDescriptor
(
    MainComponentDescriptor = BUCKSpecificSkirmishProductionMenuMainComponentDescriptor(IsFromStrategic = <IsFromStrategic>)
    MainComponentContainerUniqueName = "SpecificInGameHUDProductionViewMainContainer" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    UnitButtonListContainerDescriptor = ~/SkirmishProductionMenuUnitListScrollingContainer
    UnitButtonListContainerContainerUniqueName = "UnitContainerListScrollingContainerContainer"

    ShortcutUILayer = $/M3D/Input/UserInputLayerHandler/InputLayer_InGameShortcuts

    NbMaxPlanes = 9

    SquadGroupName = "HPROD_SQD"
    HalfSquadGroupName = "HPROD_HSQD"

    CategoryButtonDescriptor = ~/SkirmishProductionMenuCategoryButton
    CategoryRadioButtonManager = ~/SkirmishProductionMenuCategoryButtonRadioButtonManager

    PawnButtonDescriptor = ~/SkirmishProductionMenuPawnButton
    PawnRadioButtonManager = ~/SkirmishProductionMenuPawnButtonRadioButtonManager

    CombatGroupButtonDescriptor = ~/SkirmishProductionMenuCombatGroupButton
    CombatGroupRadioButtonManager = ~/SkirmishProductionMenuCombatGroupButtonRadioButtonManager

    SmartGroupButtonContainerDescriptor = ~/SmartGroupProductionButtonContainerDescriptor

    ProductionMenuButtonSpotlights = MAP
    [
        (EFactory/Infantry, ProductionSpotlight(UniqueName = "Spotlight_logInf")),
        (EFactory/Recons, ProductionSpotlight(UniqueName = "Spotlight_logRec"))
    ]

    UnitContainerDescriptor = UISpecificUnitButtonViewDescriptor
    (
        ComponentFrameAlignment = [0.0, 0.0]

        // Temps en secondes avant que le conteneur de l'unité additionnelle apparaisse lorsqu'on survole le bouton de l"unité
        TimeInSecondBeforeAdditionalUnitSpecificContainerShowUp = 0.2

        // Transparence (en pourcentage entre 0 et 1) ajouté au conteneur d'une unité lorsque cette unité n'est pas disponible
        OpacityPercentWhenUnitNotAvailable = 0.6

        // Couleur et drawer pour les textures et les textes quand l'unité n"est pas disponible
        TextureDrawerWhenUnitNotAvailable = "ColorMultiply_Grayscale"
        UnitNameColorWhenUnitNotAvailable = "ProductionMenu/UnitNameWhenNotAvailable"
        UnitCostColorWhenUnitNotAvailable = "ProductionMenu/UnitCostWhenNotAvailable"
        UnitCostGlowColorIndexWhenUnitNotAvailable = "ProductionMenu/UnitCostWhenNotAvailable/Glow"
        UnitCostColorWhenUnitCannotBeBought = "ProductionMenu/UnitCostWhenCannotBeBought"
        UnitNameBackgroundColorWhenUnitNotAvailable = "Gris123"

        CannotDeselect = true
        ForceEvents = true
    )

    TokenFormatCommandPointsByCombatRule = MAP
    [
        (ECombatRule/Conquest,        "SP_CNQ_TFS"),
        (ECombatRule/Destruction,     "SP_COM_TFS")
    ]
)
