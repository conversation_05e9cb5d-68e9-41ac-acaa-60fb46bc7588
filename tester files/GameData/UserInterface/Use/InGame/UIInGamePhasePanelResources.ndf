PhasePanel is BUCKContainerDescriptor
(
    UniqueName = "PhasePanel"
    ElementName = "PhasePanel"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [350, 65]
        AlignementToFather = [1, 0]
        AlignementToAnchor = [1, 0]
    )

    Components =
    [
        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 1.0]
                PixelWidthHeight = [-2.0, -1.0]
                PixelOffset = [-2.0, 1.0]
                AlignementToFather = [1, 0]
                AlignementToAnchor = [1, 0]
            )

            HidePointerEvents = true
        ),

        BUCKTextureDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI( MagnifiableWidthHeight = [350, 74] )

            TextureFrame   = TUIFramePropertyRTTI( MagnifiableWidthHeight = [350, 74] )
            TextureToken        = "InGameTexture_PhasePanel"
        ),

        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [35, 11]
                MagnifiableWidthHeight = [285, 24]
            )

            Components =
            [
                BUCKTextDescriptor
                (
                    UniqueName = "PhaseText"
                    ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1,1] )

                    TextSize = "PanneauPhase/TextePhase"
                    TextColor = "PanneauPhase/TextePhase"
                    TextToken = ""
                    TextDico = ~/LocalisationConstantes/dico_interface_ingame
                    TypefaceToken = "UIMainFont"

                    BigLineAction = ~/BigLineAction/CutByDots
                    TextStyle = "Default"
                    ParagraphStyle = TParagraphStyle
                    (
                        Alignment = UIText_Center
                        VerticalAlignment = UIText_VerticalCenter
                    )
                    HorizontalFitStyle = ~/FitStyle/UserDefined
                    VerticalFitStyle = ~/FitStyle/UserDefined
                ),
                BUCKSpecificHintableArea
                (
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    HintTitleToken = "HPH_NXPHST"
                    HintBodyToken = "HPH_NXPHSB"
                    HintExtendedToken = "HPH_NXPHSE"
                ),
            ]
        ),

        BUCKContainerDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableOffset = [35, 39]
                MagnifiableWidthHeight = [285, 21]
            )

            Components =
            [
                BUCKTextDescriptor
                (
                    UniqueName = "TimeText"
                    ComponentFrame = TUIFramePropertyRTTI( RelativeWidthHeight = [1,1] )

                    TextSize = "PanneauPhase/TexteTemps"
                    TextColor = "PanneauPhase/TexteTemps"
                    TextToken = ""
                    TextDico = ~/LocalisationConstantes/dico_interface_ingame
                    TypefaceToken = "UIMainFont"

                    BigLineAction = ~/BigLineAction/CutByDots
                    TextStyle = "Default"
                    ParagraphStyle = TParagraphStyle
                    (
                        Alignment = UIText_Center
                        VerticalAlignment = UIText_VerticalCenter
                    )
                    HorizontalFitStyle = ~/FitStyle/UserDefined
                    VerticalFitStyle = ~/FitStyle/UserDefined
                ),
                BUCKSpecificHintableArea
                (
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    HintTitleToken = "HPH_TIME_T"
                    HintBodyToken = "HPH_TIME_B"
                    HintExtendedToken = "HPH_TIME_E"
                ),
            ]
        ),
    ]
)

// le premier élément est vide, parce qu'il n'y a rien avant la phase A
PhasePanelElementNames is [ "", "PhaseBTimeOverload", "PhaseCTimeOverload" ]
