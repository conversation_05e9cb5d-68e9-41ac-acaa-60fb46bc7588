
//-------------------------------------------------------------------------------------

UIInGameLDVideoViewDescriptor is TUICommonLDVideoViewDescriptor
(
    Components = MAP
    [
        (
            "FullScreenCutSceneVideo", ~/CommonFullScreenCutSceneVideo
        ),
        (
            "DefaultSubtitleDescriptor", ~/CommonDefaultSubtitleDescriptor
        ),

        ("cutscene_panoramique",~/Panoramique_video),
        ("cutscene_25",~/Cutscene25_video),
        ("cutscene_10",~/Cutscene10_video),
        ("fullscreen_noSound",~/FullScreen_video_noSound),
        ("transparent",~/TransparentSubtitleDescriptor),
        ("subtitle_25",~/Cutscene25_subtitle),
        ("subtitle_panoramique",~/Panoramique_subtitle),

    ]
    MainComponentDescriptor = BUCKContainerDescriptor(ComponentFrame=TUIFramePropertyRTTI())
    MainComponentContainerUniqueName = "CutSceneVideoContainer"
    SkipVideoOption = $/MiscOptions/SkipMovies
    ShowSubtitles = $/GameplayOption/ShowSubtitles
    VideoLoadingPriority = 1000
)
//-------------------------------------------------------------------------------------
FullScreen_video_noSound is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1,1]
        // MagnifiableWidthHeight = [0,300]
    )
    BackgroundBlockColorToken = 'PureBlack'
    HasBackground = true

    Components =
    [
        PanelRoundedCorner
        (
            RoundedVertexes = [true, true, false, false]
            Radius = 5
            BackgroundBlockColorToken = 'PureBlack'
            BorderLineColorToken = 'LDHintSolo_texte'
            BorderThicknessToken = '2'
        ),
        BUCKVideoDescriptor
        (
            ElementName = "CutSceneVideo"

            ComponentFrame = TUIFramePropertyRTTI
            (
               RelativeWidthHeight = [1,1]
               AlignementToAnchor = [0.5, 0.5]
               AlignementToFather = [0.5, 0.5]
            )

            SoundVolume = 0.0

        )
    ]
)

Panoramique_video is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1,0]
        MagnifiableWidthHeight = [0,300]
    )

    Components =
    [
        PanelRoundedCorner
        (
            RoundedVertexes = [true, true, false, false]
            Radius = 5
            BackgroundBlockColorToken = 'LDHintSolo_fond'
            BorderLineColorToken = 'LDHintSolo_texte'
            BorderThicknessToken = '2'
        ),
        BUCKVideoDescriptor
        (
            ElementName = "CutSceneVideo"

            ComponentFrame = TUIFramePropertyRTTI
            (
               RelativeWidthHeight = [1,1]
               AlignementToAnchor = [0.5, 0.5]
               AlignementToFather = [0.5, 0.5]
            )

            SoundVolume = 3.0
        )
    ]
)

Cutscene25_video is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
        MagnifiableWidthHeight = [780.0, 0.0]
    )

    Components =
    [
        PanelRoundedCorner
        (
            RoundedVertexes = [true, true, false, false]
            Radius = 5
            BackgroundBlockColorToken = 'LDHintSolo_fond'
            BorderLineColorToken = 'LDHintSolo_texte'
            BorderThicknessToken = '2'
        ),
        BUCKVideoDescriptor
        (
            ElementName = "CutSceneVideo"

            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [780.0, 1080.0] // Keeps constant video ratio when changing the game window size
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]
            )

            SoundVolume = 3.0
        )
    ]
)
Cutscene10_video is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        // RelativeWidthHeight = [0,1]
        MagnifiableWidthHeight = [690,425]
        AlignementToAnchor = [1.0, 0.0]
        AlignementToFather = [1.0, 0.0]
        MagnifiableOffset = [0,38+5]
    )

    Components =
    [
        PanelRoundedCorner
        (
            RoundedVertexes = [true, true, false, false]
            Radius = 5
            BackgroundBlockColorToken = 'LDHintSolo_fond'
            BorderLineColorToken = 'LDHintSolo_texte'
            BorderThicknessToken = '2'
        ),
        BUCKVideoDescriptor
        (
            ElementName = "CutSceneVideo"

            ComponentFrame = TUIFramePropertyRTTI
            (
               RelativeWidthHeight = [1,1]
               AlignementToAnchor = [0.5, 0.5]
               AlignementToFather = [0.5, 0.5]
            )

            SoundVolume = 3.0
        )
    ]
)

TransparentSubtitleDescriptor is BUCKTextDescriptor
(
    ElementName = "SubtitleText"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 60.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
        MagnifiableOffset = [0.0, 260.0]
    )

    ParagraphStyle = ~/VideoSubtitleParagraphStyle
    TextStyle = "DefaultSubtitle"

    TypefaceToken = "Eurostyle"

    BigLineAction = ~/BigLineAction/MultiLine

    TextColor = "Transparent"
    TextSize = "22"

    TextDico = ~/LocalisationConstantes/dico_interface_ingame
)
Cutscene25_subtitle is BUCKTextDescriptor
(
    ElementName = "SubtitleText"

    ComponentFrame = TUIFramePropertyRTTI
    (
        // RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [700.0, 0.0]
        AlignementToFather = [0, 0.0]
        AlignementToAnchor = [0, 0.0]
        MagnifiableOffset = [40.0, 10.0]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment         = UIText_Center
        VerticalAlignment = UIText_Up
        InterLine         = 0.3
        LineWidth         = 0
        Balanced          = true
    )
    TextStyle = "DefaultSubtitle"
    VerticalFitStyle = ~/ContainerFitStyle/FitToContent


    TypefaceToken = "Eurostyle"

    BigLineAction = ~/BigLineAction/MultiLine

    TextColor = "Subtitle/Text"
    TextSize = "25"

    TextDico = ~/LocalisationConstantes/dico_interface_ingame
)
Panoramique_subtitle is BUCKTextDescriptor
(
    ElementName = "SubtitleText"

    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.8, 0.0]
        // MagnifiableWidthHeight = [0.0, 60.0]
        AlignementToFather = [0.5, 0.0]
        AlignementToAnchor = [0.5, 0.0]
        MagnifiableOffset = [0.0, 10.0]
    )

    ParagraphStyle = TParagraphStyle
    (
        Alignment         = UIText_Center
        VerticalAlignment = UIText_Up
        InterLine         = 0.3
        LineWidth         = 0
        Balanced          = true
    )
    VerticalFitStyle = ~/ContainerFitStyle/FitToContent

    TextStyle = "DefaultSubtitle"

    TypefaceToken = "Eurostyle"

    BigLineAction = ~/BigLineAction/MultiLine

    TextColor = "Subtitle/Text"
    TextSize = "25"

    TextDico = ~/LocalisationConstantes/dico_interface_ingame
)
