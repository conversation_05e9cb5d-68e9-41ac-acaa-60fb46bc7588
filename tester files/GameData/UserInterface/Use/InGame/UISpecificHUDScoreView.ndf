DestructionPointElementName is "DestructionScore"
ZonePointElementName is "ZoneScore"
UnitLossElementName is "Morale"

private template AllianceScoreLine
[
    ElementName : string,
    IconTexture : string = "",
    GaugeHint : string = "",
    IconHint : string = "",
    IconHintTitle : string = "",
    IconHintExtended : string = "",
    GaugeMagnifiableHeight : int,
    GaugeIconMagnifiableHeight : int,
    GaugeTextSize : string = "20",
    GaugeColorToken : string = "",
] is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 24.0]
    )

    ElementName = <ElementName> + "Container"
    HidePointerEvents = true
    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [0.0, 1.0]
            )


            Axis = ~/ListAxis/Horizontal
            InterItemMargin = TRTTILength( Magnifiable = 4.0 )

            Elements =
            [
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ElementName = <ElementName> + "PreGaugeContainer"
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [32.0, <GaugeMagnifiableHeight>]
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        HasBackground = true
                        BackgroundBlockColorToken = "PanelScore/ScoreBackground"

                        Components =
                        [
                            BUCKTextureDescriptor
                            (
                                ElementName = "IconeCurrent" + <ElementName>
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    MagnifiableWidthHeight = [<GaugeIconMagnifiableHeight>, <GaugeIconMagnifiableHeight>]

                                    AlignementToFather = [0.5, 0.5]
                                    AlignementToAnchor = [0.5, 0.5]
                                )

                                TextureToken = <IconTexture>
                                TextureColorToken = "BlancEquipe"

                                Components =
                                [
                                    BUCKSpecificHintableArea
                                    (
                                        HintTitleToken = <IconHintTitle>
                                        HintBodyToken = <IconHint>
                                        HintExtendedToken = <IconHintExtended>
                                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                                    )
                                ]
                            )
                        ]
                    )
                ),

                BUCKListElementDescriptor
                (
                    ExtendWeight = 1.0
                    ComponentDescriptor = BUCKContainerDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            MagnifiableWidthHeight = [0.0, 0.0]
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        Components =
                        [
                            BUCKGaugeDescriptor
                            (
                                ElementName = <ElementName> + "Gauge"

                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [1.0, 0.0]
                                    MagnifiableWidthHeight = [0.0, <GaugeMagnifiableHeight>]
                                    AlignementToFather = [0.0, 0.5]
                                    AlignementToAnchor = [0.0, 0.5]
                                )

                                HasBackground = true
                                BackgroundBlockColorToken = "PanelScore/ScoreBackground"

                                GaugeValueMinSize = TRTTILength( Pixel = 1.0 )
                                DurationForGaugeFullChange = 1.0

                                GaugeComponentNames =
                                [
                                    <ElementName> + "GaugeValue"
                                ]

                                Components =
                                [
                                    BUCKGaugeValueDescriptor
                                    (
                                        ElementName = <ElementName> + "GaugeValue"
                                        ComponentFrame = TUIFramePropertyRTTI
                                        (
                                            RelativeWidthHeight = [0.0, 1.0]
                                        )

                                        HasBackground = true
                                        BackgroundBlockColorToken = <GaugeColorToken>
                                    ),
                                ]
                            ),

                            BUCKListDescriptor
                            (
                                ComponentFrame = TUIFramePropertyRTTI
                                (
                                    RelativeWidthHeight = [0.0, 1.0]
                                )

                                Axis = ~/ListAxis/Horizontal
                                InterItemMargin = TRTTILength( Magnifiable = 4.0 )
                                LastMargin = TRTTILength( Magnifiable = 4.0 )

                                Elements =
                                [
                                    BUCKListElementDescriptor
                                    (
                                        ExtendWeight = 1.0
                                        ComponentDescriptor = BUCKTextDescriptor
                                        (
                                            ElementName = <ElementName> + "Text"
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight = [1.0, 1.0]
                                                AlignementToFather = [0.0, 0.5]
                                                AlignementToAnchor = [0.0, 0.5]
                                            )

                                            TextStyle = "Default"

                                            ParagraphStyle = TParagraphStyle
                                            (
                                                Alignment = UIText_Left
                                                VerticalAlignment = UIText_VerticalCenter
                                            )
                                            TextPadding = TRTTILength4 (Magnifiable = [4,0,0,0])
                                            TextSize = <GaugeTextSize>
                                            TextColor = "BlancEquipe"
                                            TextToken = "DES_VALUE"
                                            TextDico = ~/LocalisationConstantes/dico_interface_ingame
                                            TypefaceToken = "UIMainFont"

                                            BigLineAction = ~/BigLineAction/CutByDots

                                            HorizontalFitStyle = ~/FitStyle/UserDefined
                                            VerticalFitStyle = ~/FitStyle/UserDefined
                                        )
                                    ),
                                    BUCKListElementSpacer(Magnifiable = 10.0),
                                    BUCKListElementDescriptor
                                    (
                                        ComponentDescriptor = BUCKTextDescriptor
                                        (
                                            ElementName = <ElementName> + "IncomeText"
                                            ComponentFrame = TUIFramePropertyRTTI
                                            (
                                                RelativeWidthHeight = [0.0, 1.0]
                                                AlignementToFather = [0.0, 0.5]
                                                AlignementToAnchor = [0.0, 0.5]
                                            )

                                            TextStyle = "Default"

                                            ParagraphStyle = TParagraphStyle
                                            (
                                                Alignment = UIText_Right
                                                VerticalAlignment = UIText_VerticalCenter
                                            )

                                            TextSize = "20"
                                            TextColor = "BlancEquipe"
                                            TextToken = "FRM_PLUS"
                                            TextDico = ~/LocalisationConstantes/dico_interface_ingame
                                            TypefaceToken = "UIMainFont"

                                            BigLineAction = ~/BigLineAction/CutByDots

                                            HorizontalFitStyle = ~/FitStyle/FitToContent
                                            VerticalFitStyle = ~/FitStyle/UserDefined
                                        )
                                    )
                                ]

                                ForegroundComponents =
                                [
                                    BUCKTextDescriptor
                                    (
                                        ElementName = <ElementName> + "VictoryTimer"
                                        ComponentFrame = TUIFramePropertyRTTI
                                        (
                                            RelativeWidthHeight = [0.0, 1.0]
                                            AlignementToFather = [0.5, 0.5]
                                            AlignementToAnchor = [0.5, 0.5]
                                        )

                                        TextStyle = "Default"

                                        ParagraphStyle = TParagraphStyle
                                        (
                                            Alignment = UIText_Right
                                            VerticalAlignment = UIText_VerticalCenter
                                        )

                                        TextSize = "10"
                                        TextColor = "BlancEquipe"
                                        TextToken = "VIC_TIMR"
                                        TextDico = ~/LocalisationConstantes/dico_interface_ingame
                                        TypefaceToken = "Eurostyle"

                                        BigLineAction = ~/BigLineAction/CutByDots

                                        HorizontalFitStyle = ~/FitStyle/FitToContent
                                        VerticalFitStyle = ~/FitStyle/UserDefined
                                    )
                                ]
                            ),
                            BUCKSpecificHintableArea
                            (
                                HintBodyToken = <GaugeHint>
                                DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                            )
                        ]
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = <ElementName> + "ControlIndicator"
                        RequiredTags = [ "StrategicScenario" ]

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            MagnifiableWidthHeight = [20.0, 20.0]

                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        TextureToken = "UseInGame_StrategicScenario_Control"
                        TextureFrame   = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )
                        TileTextureInComponent = false
                        ClipTextureToComponent = true

                        Components =
                        [
                            BUCKSpecificHintableArea
                            (
                                DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                                HintTitleToken = "HSC_CONTRT"
                                HintBodyToken = "HSC_CONTRB"
                                HintExtendedToken = "HSC_CONTRE"
                            ),
                        ]
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKTextureDescriptor
                    (
                        ElementName = <ElementName> + "DestructionIndicator"
                        RequiredTags = [ "StrategicScenario" ]

                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            AlignementToFather = [0.0, 0.5]
                            AlignementToAnchor = [0.0, 0.5]
                        )

                        TextureToken = "UseInGame_StrategicScenario_Destruction"
                        TextureFrame   = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0] )
                        TileTextureInComponent = false
                        ClipTextureToComponent = true

                        Components =
                        [
                            BUCKSpecificHintableArea
                            (
                                DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                                HintTitleToken = "HSC_DESTRT"
                                HintBodyToken = "HSC_DESTRB"
                                HintExtendedToken = "HSC_DESTRE"
                            ),
                        ]
                    )
                ),
            ]
        ),
    ]
)

//--------------------------------------------------
BUCKSpecificHUDScorePlayerMainComponentDescriptor is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
        MagnifiableWidthHeight = [0.0, 20.0]
    )

    Components =
    [
        ~/BUCKSpecificHUDScorePlayerOneLine
    ]
)

//--------------------------------------------------

BUCKSpecificHUDScorePlayerOneLine is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [0.0, 1.0]
    )

    Axis = ~/ListAxis/Horizontal

    InterItemMargin = TRTTILength( Magnifiable = 5.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextureDescriptor
            (
                ElementName = "DivisionIcon"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [20.0, 0.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                TextureFrame   = TUIFramePropertyRTTI( RelativeWidthHeight = [1.0, 1.0])
                TileTextureInComponent = false
                ClipTextureToComponent = true

                Components =
                [
                    BUCKSpecificHintableArea
                    (
                        ElementName = "DivisionIconHint"
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame

                        HintTitleToken = "HUD_UNKN"
                    ),
                ]
            )
        ),

        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = BUCKSpecificTextWithHint
            (
                ElementName = "Name"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Left
                    VerticalAlignment = UIText_VerticalCenter
                    BigWordAction = ~/BigWordAction/BigWordCut
                )

                TextSize = "16"
                TextColor = "Cyan"
                TextToken = ""
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TypefaceToken = "IBM"

                BigLineAction = ~/BigLineAction/CutByDots
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/UserDefined

                TextFormatScript = nil
                AutoHintTextFormatScript = nil
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "Points"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [50.0, 0.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )

                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Right
                    VerticalAlignment = UIText_VerticalCenter
                    BigWordAction = ~/BigWordAction/BigWordCut
                )

                TextSize = "14"
                TextColor = "Cyan"
                TextToken = ""
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TypefaceToken = "IBM"

                BigLineAction = ~/BigLineAction/ResizeFont
                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/UserDefined
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKContainerDescriptor
            (
                ElementName = "PingArea"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )
                FitStyle = ~/ContainerFitStyle/FitToContentHorizontally
            )
        ),

        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKButtonDescriptor
            (
                ElementName = "MuteButton"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [16.0, -4.0]
                    AlignementToFather = [0.0, 0.5]
                    AlignementToAnchor = [0.0, 0.5]
                )
                IsTogglable = true

                Components =
                [
                    PanelRoundedCorner
                    (
                        Radius = 3
                        BackgroundBlockColorToken = "BoutonTemps_Background"
                        BorderLineColorToken = "BoutonTemps_Line"
                    ),
                    BUCKTextureDescriptor
                    (
                        ComponentFrame = TUIFramePropertyRTTI
                        (
                            RelativeWidthHeight = [1.0, 1.0]
                            MagnifiableWidthHeight = [-2.0, -2.0]
                            AlignementToAnchor = [0.5, 0.5]
                            AlignementToFather = [0.5, 0.5]
                        )

                        TextureFrame = TUIFramePropertyRTTI()

                        ResizeMode = ~/TextureResizeMode/FitToParentConservingRatio
                        TextureColorToken = "CouleurTexture_boutonShortcuts"
                        TextureToken = "texturePlayerMuted"
                    ),
                    BUCKSpecificHintableArea
                    (
                        ElementName = "MuteButtonHintableArea"
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    ),
                ]
            )
        )
    ]
)

//--------------------------------------------------

ScorePanelPlayerList is BUCKListDescriptor
(
    ElementName = "ScorePanelPlayerList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    Axis = ~/ListAxis/Vertical

    FirstMargin = TRTTILength( Magnifiable = 4.0 )
    InterItemMargin = TRTTILength( Magnifiable = 1.0 )
    LastMargin = TRTTILength( Magnifiable = 4.0 )
    // Filled with BUCKSpecificHUDScorePlayerMainComponentDescriptor
)

//--------------------------------------------------

BUCKSpecificHUDScoreAllianceMainComponentDescriptor is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )

    FitStyle = ~/ContainerFitStyle/FitToContentVertically

    Components =
    [
        BUCKListDescriptor
        (
            ComponentFrame = TUIFramePropertyRTTI
            (
                RelativeWidthHeight = [1.0, 0.0]
            )
            Axis = ~/ListAxis/Vertical

            FirstMargin = TRTTILength(Magnifiable = 4.0)

            Elements =
            [
                // for destruction mode
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = AllianceScoreLine
                    (
                        ElementName = DestructionPointElementName
                        IconTexture = "icone_scorePoints"
                        IconHintExtended = "lr_destre"
                        IconHint = "lr_destrt"
                        IconHintTitle = "lr_destrb"
                        GaugeHint = "LR_team"
                        GaugeMagnifiableHeight = 16
                        GaugeIconMagnifiableHeight = 24
                        GaugeTextSize = "20"
                    )
                ),
                // for conquest mode
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = AllianceScoreLine
                    (
                        ElementName = ZonePointElementName
                        IconTexture = "icone_scorePoints"
                        IconHint = "lr_conqueb"
                        IconHintTitle = "lr_conquet"
                        IconHintExtended = "lr_conquee"
                        GaugeHint = "LR_team"
                        GaugeMagnifiableHeight = 16
                        GaugeIconMagnifiableHeight = 24
                        GaugeTextSize = "20"
                    )
                ),
                // for AG moral mode
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = AllianceScoreLine
                    (
                        ElementName = UnitLossElementName
                        IconTexture = "icone_moralePoints"
                        IconHintTitle = "LR_morale"
                        IconHint = "LR_moraleb"
                        IconHintExtended = "LR_moralee"
                        GaugeHint = "LR_teamM"
                        GaugeMagnifiableHeight = 8
                        GaugeIconMagnifiableHeight = 20
                        GaugeTextSize = "16"
                        GaugeColorToken = "MoraleGauge"
                    )
                ),
                BUCKListElementDescriptor
                (
                    ComponentDescriptor = BUCKListDescriptor
                    (
                        ElementName = "DropDownList"

                        Axis = ~/ListAxis/Horizontal
                        BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild

                        Elements =
                        [
                            BUCKListElementDescriptor
                            (
                                ExtendWeight = 1.0
                                ComponentDescriptor = ScorePanelPlayerList
                            )
                        ]
                    )
                ),
            ]
        )
    ]
)

//--------------------------------------------------

private PanelDeScoreParEquipe is BUCKListDescriptor
(
    ElementName = "AllianceList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        RelativeWidthHeight = [1.0, 0.0]
    )
    Axis = ~/ListAxis/Vertical
    // rempli avec deux BUCKSpecificHUDScoreAllianceMainComponentDescriptor, une à gauche et une à droite
)

//------------------------------------------------------------------------------
private PlayerScorePanelButton is BUCKContainerDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [56,60]
    )

    Components =
    [
        BUCKButtonDescriptor
        (
            ElementName = "PlayerScorePanelButton"
            ComponentFrame = TUIFramePropertyRTTI
            (
                MagnifiableWidthHeight = [24.0, 24.0]
                AlignementToAnchor = [0.5, 0.5]
                AlignementToFather = [0.5, 0.5]

            )

            IsTogglable = true
            DefaultToggleValue = false
            HasBorder = false
            Mapping = $/KeyboardOption/Mapping_ToggleScorePanel

            Components =
            [
                BUCKContainerDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                        AlignementToFather = [0.5, 0.5]
                        AlignementToAnchor = [0.5, 0.5]
                    )

                    Components =
                    [
                        PanelRoundedCorner
                        (
                            BackgroundBlockColorToken = "BoutonTemps_Background"
                            BorderLineColorToken = "CouleurBordure_boutonShortcuts"
                        )
                    ]
                ),

                BUCKTextureDescriptor
                (
                    ComponentFrame = TUIFramePropertyRTTI
                    (
                       RelativeWidthHeight = [1.0, 1.0]
                       AlignementToFather = [0.5, 0.5]
                       AlignementToAnchor = [0.5, 0.5]
                    )

                    TextureToken = "icone_score"
                    TextureColorToken = "CouleurTexture_boutonShortcuts"

                    TextureFrame = TUIFramePropertyRTTI
                    (
                        RelativeWidthHeight = [1.0, 1.0]
                        MagnifiableWidthHeight = [0.0, 0.0]
                        AlignementToFather = [0.5, 0.5]
                        AlignementToAnchor = [0.5, 0.5]
                    )
                ),

                BUCKSpecificHintableArea
                (
                    HintBodyToken = 'LR_player'
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                )
            ]
        )
    ]
)



//------------------------------------------------------------------------------

private PanelScoreMax is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [40.0+16, 0.0]
    )

    Axis = ~/ListAxis/Vertical
    FirstMargin = TRTTILength( Magnifiable = 4.0 )
    InterItemMargin = TRTTILength( Magnifiable = 4.0 )
    LastMargin = TRTTILength( Magnifiable = 4.0 )

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextureDescriptor
            (
                ElementName = 'IconeScoreMax'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [32.0, 32.0]

                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                TextureToken = "icone_scoreVictoire"
                TextureColorToken = 'BlancEquipe'

                Components =
                [
                    BUCKSpecificHintableArea
                    (
                        HintBodyToken = 'LR_score'
                        DicoToken = ~/LocalisationConstantes/dico_interface_ingame
                    )
                ]
            )
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = 'ScoreToReachText'
                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 0.0]
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )

                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                    InterLine = 0
                )

                TextStyle = "Default"

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/FitToContent

                TypefaceToken = "UIMainFont"
                BigLineAction = ~/BigLineAction/CutByDots

                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TextToken = "WIP_8"

                TextColor = "BlancEquipe"
                TextSize = "16"

                Hint = BUCKSpecificHintableArea
                (
                    ForbiddenTags = ['StrategicScenario']
                    DicoToken = ~/LocalisationConstantes/dico_interface_ingame

                    HintBodyToken = "LR_score"
                )
            )
        ),
    ]
)

private MainScorePanelComponent is BUCKListDescriptor
(

    ElementName = "MainScorePanelComponent"
    ComponentFrame = TUIFramePropertyRTTI()

    Axis = ~/ListAxis/Horizontal
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild
    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/PlayerScorePanelButton
        ),
        BUCKListElementDescriptor
        (
            ExtendWeight = 1.0
            ComponentDescriptor = ~/PanelDeScoreParEquipe
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = ~/PanelScoreMax
        ),
    ]

    BackgroundComponents =
    [
        PanelRoundedCorner()
    ]
)

//------------------------------------------------------------------------------

UISpecificHUDScorePlayerViewDescriptor is TUISpecificHUDScorePlayerViewDescriptor
(
    MainComponentDescriptor = BUCKSpecificHUDScorePlayerMainComponentDescriptor
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    MutedHintTitleText = "MTBTmt"
    MutedHintBodyText = "MTBTmb"
    UnmutedHintTitleText = "MTBTut"
    UnmutedHintBodyText = "MTBTub"
)

//------------------------------------------------------------------------------

UISpecificHUDScoreAllianceViewDescriptor is TUISpecificHUDScoreAllianceViewDescriptor
(
    MainComponentDescriptor = BUCKSpecificHUDScoreAllianceMainComponentDescriptor
    MainComponentContainerUniqueName = "" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    ScorePointTypeToElementName = MAP
    [
        (~/EScorePointType/UnitDestruction, DestructionPointElementName),
        (~/EScorePointType/ZonePoint, ZonePointElementName),
        (~/EScorePointType/UnitLoss, UnitLossElementName)
    ]

    ScoreTypeToDisplayFromMaxToMin = [ ~/EScorePointType/UnitLoss ]
)

//------------------------------------------------------------------------------

UISpecificHUDScoreViewDescriptor is TUISpecificHUDScoreViewDescriptor
(
    MainComponentDescriptor = ~/MainScorePanelComponent
    MainComponentContainerUniqueName = "SpecificInGameHUDScoreViewMainContainer" // Permet d'indiquer l'endroit ou le composant doit s'insérer

    ScoreToReachToDisplay =
    [
        ~/EScorePointType/UnitDestruction,
        ~/EScorePointType/ZonePoint
    ]
)
