
WarningPanelInGameResources is TUICommonWarningPanelResource
(
    ComponentByName = MAP[
        ( "WarningPositive", ~/WarningPositiveComponent ),
        ( "WarningNegative", ~/WarningNegativeComponent ),
        ( "ObservatorMode", ~/WarningPositiveComponent ),
        ( "WaitingForPlayersMessage", ~/WarningPositiveComponent ),
    ]

    LifeAnimDurationForTemporary = 4.0
    FadeInDuration = 0.4
    FadeOutDuration = 0.8
)
