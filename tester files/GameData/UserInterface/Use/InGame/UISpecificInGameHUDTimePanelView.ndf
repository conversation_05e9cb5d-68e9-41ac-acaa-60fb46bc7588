// A faire correspondre avec l'enum dans InterfaceSpecific/UISpecificInGameHUDTimePanelView.h

GameSpeed_Pause        is 0
GameSpeed_BulletTime   is 1
GameSpeed_Slow         is 2
GameSpeed_Normal       is 3
GameSpeed_Fast         is 4
GameSpeed_VeryFast     is 5

private GameSpeedPanelButtonWidth is 24
private GameSpeedPanelButtonHeight is 24

private GameSpeedPanelOffsetY is 7.5

private ButtonManager is TBUCKRadioButtonManager()

//-------------------------------------------------------------------------------------

SpecificInGameHUDTimePanelViewMainComponent is BUCKListDescriptor
(
    ElementName = "InGameTimePanel"

    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 40.0]
        AlignementToAnchor = [0.5, 0.0]
        AlignementToFather = [0.5, 0.0]
    )

    Axis = ~/ListAxis/Horizontal

    InterItemMargin = TRTTILength(Pixel = 16.0)

    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = AffichageTempsEtVitesseEnTexte
        ),
        BUCKListElementDescriptor
        (
            ComponentDescriptor = TimePanelSpeedButtons
        )
    ]
)

//-------------------------------------------------------------------------------------

private AffichageTempsEtVitesseEnTexte is BUCKListDescriptor
(
    ComponentFrame = TUIFramePropertyRTTI
    (
        AlignementToAnchor = [0.0, 0.5]
        AlignementToFather = [0.0, 0.5]
    )

    Axis = ~/ListAxis/Vertical
    BreadthComputationMode = ~/BreadthComputationMode/ComputeBreadthFromLargestChild



    Elements =
    [
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "NextActionTimeText"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0.0, 16.0]
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )


                TextSize = "15"
                TextToken = "TIME_DISPL"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TypefaceToken = "UIMainFont"

                TextColor = "BlancEquipe"

                HorizontalFitStyle = ~/FitStyle/FitToContent
                VerticalFitStyle = ~/FitStyle/UserDefined

                BigLineAction = ~/BigLineAction/ResizeFont
                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )
            )
        ),
         BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKTextDescriptor
            (
                ElementName = "GameSpeedText"
                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [80, 16.0]
                    AlignementToFather = [0.5, 0.0]
                    AlignementToAnchor = [0.5, 0.0]
                )


                TextSize = "12"
                TextToken = "GS_NORMAL"
                TextDico = ~/LocalisationConstantes/dico_interface_ingame
                TypefaceToken = "UIMainFont"

                TextColor = "BlancEquipe"

                HorizontalFitStyle = ~/FitStyle/UserDefined
                VerticalFitStyle = ~/FitStyle/UserDefined

                BigLineAction = ~/BigLineAction/ResizeFont
                TextStyle = "Default"
                ParagraphStyle = TParagraphStyle
                (
                    Alignment = UIText_Center
                    VerticalAlignment = UIText_VerticalCenter
                )
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

private TimePanelSpeedButtons is BUCKListDescriptor
(
    ElementName = "InGameSpeedButtonList"
    ComponentFrame = TUIFramePropertyRTTI
    (
        MagnifiableWidthHeight = [0.0, 24.0]
        AlignementToAnchor = [0.0, 0.5]
        AlignementToFather = [0.0, 0.5]
    )

    Axis = ~/ListAxis/Horizontal

    InterItemMargin = TRTTILength(Pixel = 8.0)

    Elements =
    [
        // Bouton Pause
        BUCKListElementDescriptor
        (
            ComponentDescriptor = HUDButton
            (
                ElementName = "SpeedPauseButton"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [GameSpeedPanelButtonWidth, 0.0]
                )

                HasBackground = false
                BackgroundColorToken = "BoutonTimePanel"

                Mapping = $/KeyboardOption/Mapping_SwitchPause
                IsTogglable = true
                CannotDeselect = false
                RadioButtonManager = ~/ButtonManager

                HasBorder = false
                BorderLineColorToken = 'TimePanel/ButtonBorder'
                BorderThickness = "1"

                BackgroundTexture = "vitesse01"

                HintTitleToken = "HTP_SPDPAT"
                HintBodyToken = "HTP_SPDPAB"
                HintExtendedToken = "HTP_SPDPAE"
                HintDico = ~/LocalisationConstantes/dico_interface_ingame

            )
        ),

        // Bouton caché pour la vitesse bullet time
        BUCKListElementDescriptor
        (
            ComponentDescriptor = BUCKButtonDescriptor
            (
                ElementName = "SpeedBulletTimeButton"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [0, 0]
                )

                Mapping = $/KeyboardOption/Mapping_SetBulletTimeSpeed
                IsTogglable = true
                CannotDeselect = false
                RadioButtonManager = ~/ButtonManager
            )
        ),

        // Bouton décélérer
        BUCKListElementDescriptor
        (
            ComponentDescriptor = HUDButton
            (
                ElementName = "SpeedSlowButton"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    MagnifiableWidthHeight = [25, 25.0]
                )

                HasBackground = false

                IsTogglable = true
                CannotDeselect = false
                RadioButtonManager = ~/ButtonManager

                HasBorder = false

                BackgroundTexture = "vitesse02"
                HintTitleToken = "HTP_SPDSLT"
                HintBodyToken = "HTP_SPDSLB"
                HintExtendedToken = "HTP_SPDSLE"
                HintDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),

        // Bouton lecture
        BUCKListElementDescriptor
        (
            ComponentDescriptor = HUDButton
            (
                ElementName = "SpeedPlayButton"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [GameSpeedPanelButtonWidth, 0.0]
                )
                HasBackground = false

                IsTogglable = true
                CannotDeselect = true
                RadioButtonManager = ~/ButtonManager

                HasBorder = false

                BackgroundTexture = "vitesse03"
                BackgroundTextureFrameProperty = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    MagnifiableWidthHeight = [0.0, 0.0]
                    AlignementToFather = [0.5, 0.5]
                    AlignementToAnchor = [0.5, 0.5]
                )

                HintTitleToken = "HTP_SPDPLT"
                HintBodyToken = "HTP_SPDPLB"
                HintExtendedToken = "HTP_SPDPLE"
                HintDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),

        // Bouton accélérer
        BUCKListElementDescriptor
        (
            ComponentDescriptor = HUDButton
            (
                ElementName = "SpeedFastButton"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [GameSpeedPanelButtonWidth, 0.0]
                )
                HasBackground = false

                IsTogglable = true
                CannotDeselect = true
                RadioButtonManager = ~/ButtonManager

                HasBorder = false

                BackgroundTexture = "vitesse04"
                BackgroundTextureFrameProperty = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    AlignementToFather = [0.5, 0.5]
                    AlignementToAnchor = [0.5, 0.5]
                )

                HintTitleToken = "HTP_SPDFAT"
                HintBodyToken = "HTP_SPDFAB"
                HintExtendedToken = "HTP_SPDFAE"
                HintDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),

        // Bouton forte accélération
        BUCKListElementDescriptor
        (
            ComponentDescriptor = HUDButton
            (
                ElementName = "SpeedVeryFastButton"

                ComponentFrame = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [0.0, 1.0]
                    MagnifiableWidthHeight = [GameSpeedPanelButtonWidth, 0.0]
                )

                HasBackground = false

                IsTogglable = true
                CannotDeselect = true
                RadioButtonManager = ~/ButtonManager

                HasBorder = false

                BackgroundTexture = "vitesse05"
                BackgroundTextureFrameProperty = TUIFramePropertyRTTI
                (
                    RelativeWidthHeight = [1.0, 1.0]
                    AlignementToFather = [0.5, 0.5]
                    AlignementToAnchor = [0.5, 0.5]
                )

                HintTitleToken = "HTP_SPVFAT"
                HintBodyToken = "HTP_SPVFAB"
                HintExtendedToken = "HTP_SPVFAE"
                HintDico = ~/LocalisationConstantes/dico_interface_ingame
            )
        ),
    ]
)

//-------------------------------------------------------------------------------------

UISpecificInGameHUDTimePanelViewDescriptor is TUISpecificInGameHUDTimePanelViewDescriptor
(
    GameSpeedInfos =
    [
        //GameSpeed_Pause
        TUIGameSpeedDescriptor(
            GameSpeedValue = 0.0
            GameSpeedToken = "GS_PAUSE"
        ),
        //GameSpeed_BulletTime
        TUIGameSpeedDescriptor(
            GameSpeedValue = 0.001
            GameSpeedToken = "GS_BULTIME"
        ),
        //GameSpeed_Slow
        TUIGameSpeedDescriptor(
            GameSpeedValue = 0.33
            GameSpeedToken = "GS_SLOW"
        ),
        //GameSpeed_Normal
        TUIGameSpeedDescriptor(
            GameSpeedValue = 1.0
            GameSpeedToken = "GS_NORMAL"
        ),
        //GameSpeed_Fast
        TUIGameSpeedDescriptor(
            GameSpeedValue = 2.0
            GameSpeedToken = "GS_FAST"
        ),
        //GameSpeed_VeryFast
        TUIGameSpeedDescriptor(
            GameSpeedValue = 3.0
            GameSpeedToken = "GS_VFAST"
        ),
    ]

    MainComponentDescriptor = ~/SpecificInGameHUDTimePanelViewMainComponent
    MainComponentContainerUniqueName = "SpecificInGameHUDTimePanelViewMainContainer"
)


