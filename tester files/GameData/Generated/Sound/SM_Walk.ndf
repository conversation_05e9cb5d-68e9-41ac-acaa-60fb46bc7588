// Ne pas éditer, ce fichier est généré par WalkMovementSoundFileWriter


export DepictionOperator_MovementSound_SM_Infanterie is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_Run.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_8.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_9.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_8.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_FR is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_FR_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_FR_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_FR_Idle_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_FR_Idle_4.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_3.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_8.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_6.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_GER is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_GER_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_GER_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_GER_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_GER_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_GER_Run_2.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_01.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_02.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_03.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_04.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_05.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_06.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_07.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_08.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_09.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_10.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_5.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_NL is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_NL_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_NL_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_NL_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_NL_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_NL_Run_2.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_01.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_02.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_03.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_3.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_NL_Engine is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Kraka_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_NL_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_NL_Run_2.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_01.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_02.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_03.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_3.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_POL is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_POL_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_POL_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_POL_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_POL_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_POL_Run_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_POL_Run_3.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_POL_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_POL_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_POL_Start_3.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_POL_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_POL_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_POL_Stop_3.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_SOVIET is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_SOVIET_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_SOVIET_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_SOVIET_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_SOVIET_Run.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_6.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_SOVIET_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_SOVIET_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_SOVIET_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_SOVIET_Stop_4.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_SPAIN_Engine is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Kraka_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_3.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_8.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_6.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_UK is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_US_Run.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_8.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_9.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_8.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_Crew_US is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_US_Run.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_8.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_9.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_8.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_FR is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_FR_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_FR_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_FR_Idle_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_FR_Idle_4.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_FR_Run_3.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_FR_Start_8.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_FR_Stop_6.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_GER is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_GER_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_GER_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_GER_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_GER_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_GER_Run_2.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_01.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_02.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_03.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_04.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_05.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_06.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_07.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_08.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_09.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_GER_Start_10.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_GER_Stop_5.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_NL is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_NL_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_NL_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_NL_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_NL_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_NL_Run_2.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_01.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_02.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_NL_Start_03.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_NL_Stop_3.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_POL is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_POL_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_POL_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_POL_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_POL_Run_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_POL_Run_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_POL_Run_3.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_POL_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_POL_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_POL_Start_3.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_POL_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_POL_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_POL_Stop_3.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_SOVIET is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_SOVIET_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_SOVIET_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_SOVIET_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_SOVIET_Run.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_SOVIET_Start_6.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_SOVIET_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_SOVIET_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_SOVIET_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_SOVIET_Stop_4.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_UK is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_US_Run.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_8.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_9.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_8.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Infanterie_US is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0.5
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Infanterie_US_Idle_3.wav"
            )
        ),
    ]
    MaxVolume = 0.5
    MaxVolumeSpeed = 20
    MinVolume = 0.1
    OperatorId = "walkSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.25
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Infanterie_US_Run.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_8.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Infanterie_US_Start_9.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_2.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_3.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_4.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_5.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_6.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_7.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Deplacement_Infanterie
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Infanterie_US_Stop_8.wav"
            )
        ),
    ]
)
export DepictionOperator_MovementSound_SM_Walk_Debug is TCosmeticWalkMovementSoundOperatorDesc
(
    IdleFadeOutDuration = 0
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/Debug/beep_330_loop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/Debug/beep_330_loop_2.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 1
    MinVolume = 1
    OperatorId = "walkSound"
    RunFadeInDuration = 0
    RunFadeOutDuration = 0
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 1
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/Debug/beep_330_loop_1.wav"
            )
        ),
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/Debug/beep_330_loop_2.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/Debug/beep_554.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Default
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/Debug/beep_880.wav"
            )
        ),
    ]
)
