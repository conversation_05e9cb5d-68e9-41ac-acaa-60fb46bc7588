// Ne pas éditer, ce fichier est généré par DriveMovementSoundFileWriter


export DepictionOperator_MovementSound_SM_812 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/812_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/812_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/812_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/812_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_AEC_Militant is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/AEC_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/AEC_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/AEC_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Bedford_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_AML is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/AML_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/AML_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/AML_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/AML_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_AMX_10 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/AMX10RC_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/AMX_13_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/AMX10RC_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/AMX10RC_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_AMX_10_RC is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/AMX10RC_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/AMX10RC_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/AMX10RC_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/AMX10RC_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_AMX_13 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/AMX_13_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/AMX_13_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/AMX_13_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/AMX_13_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_AMX_30 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/AMX30_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/AMX_Auf1_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/AMX30_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/AMX30_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_AMX_AuF1 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/AMX_Auf1_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/AMX_Auf1_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/AMX_Auf1_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/AMX_Auf1_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Akatsiya is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Akatsiya_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Akatsiya_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Akatsiya_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Akatsiya_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_BAZ is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/BAZ_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/BAZ_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/BRDM_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/BTR_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_BM30 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/BM30_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/BM30_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/BM30_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/BM30_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_BMD is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/BMD_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/BMD_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/BMD_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/BMD_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_BRDM is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/BRDM_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/BRDM_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/BRDM_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/BRDM_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_BTR is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/BTR_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/BTR_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/BRDM_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/BTR_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_BTR_152 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/BTR152_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/BTR152_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/BTR152_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/BTR_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Bedford is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Bedford_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Bedford_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Bedford_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Bedford_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Bmp1 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Bmp1_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Bmp1_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Bmp1_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Bmp2_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Bmp2 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Bmp2_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Bmp2_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Bmp2_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Bmp2_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Bmp3 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Bmp3_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Bmp3_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/T80_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Bmp3_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Bradley is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Bradley_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 4
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Bradley_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Bradley_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Bradley_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 1
)
export DepictionOperator_MovementSound_SM_Buggy is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 1
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Buggy_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Buggy_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Buggy_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Buggy_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_CUCV is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/CUCV_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/CUCV_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/CUCV_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/CUCV_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Centurion is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Centurion_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Centurion_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Centurion_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Centurion_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Challenger_1 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Challenger1_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Challenger1_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Challenger1_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Challenger1_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Chieftain is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Chieftain_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Chieftain_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Chieftain_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Chieftain_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_DAF is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/DAF_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/DAF_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/DAF_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/TRM2000_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_EBR is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/EBR_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/EBR_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/EBR_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/EBR_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_FV_432 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/FV432_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/FV432_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/FV432_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/FV432_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_FV_721 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/FV721_Fox_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Ferret_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/FV721_Fox_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/FV721_Fox_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_FV_Scimitar is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/FV107_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/FV107_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/FV107_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/FV107_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Ferret_Mk2 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Ferret_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Ferret_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Ferret_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Ferret_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_GAZ_66 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/GAZ66_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/GAZ66_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/GAZ66_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/GAZ66_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_GOAT is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.75
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/GOAT_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.75
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/GOAT_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/GOAT_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/GOAT_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_GTMU is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 5
    IdleFadeInDuration = 1
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/GTMU_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/GTMU_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/GTMU_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/GTMU_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Gepard is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Gepard_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Gepard_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Gepard_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Gepard_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_HEMTT is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/HEMTT_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/HEMTT_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/HEMTT_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/HEMTT_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_HS30 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/HS30_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/HS30_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/HS30_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/HS30_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Humvee is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Humvee_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Humvee_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Humvee_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Humvee_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_IS2 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 20
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/IS2_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/IS2_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/IS2_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/IS2_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Iltis is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Iltis_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Iltis_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Iltis_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Iltis_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Kraka is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Kraka_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Kraka_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Kraka_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Kraka_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_LARS is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/LARS_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.5
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/LARS_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/LARS_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/LARS_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_LAV25 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/LAV25_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 4
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/LAV25_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/LAV25_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/LAV25_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 1
)
export DepictionOperator_MovementSound_SM_LO1800 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/LO1800_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/LO1800_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/LO1800_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/LO1800_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Land_Rover is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/LandRover_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/LandRover_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/LandRover_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/LandRover_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Leopard_1 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M1_Leo1_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M1_Leo1_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M1_Leo1_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M1_Leo1_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Leopard_2 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M1_Leo2_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M1_Leo2_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M1_Leo2_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M1_Leo2_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Luaz is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/LUAZ_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/LUAZ_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/LUAZ_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/LUAZ_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Lynx is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Lynx_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 4
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Lynx_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Lynx_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M113_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M109 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M109_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 4
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M109_Tracked_artillery_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M109_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M109_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M110 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M110_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 4
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M110_Tracked_artillery_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M110_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M110_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M113 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M113_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 4
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M113_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M113_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M113_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M1_Abrams is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M1_Abrams_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M1_Abrams_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M1_Abrams_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M1_Abrams_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M270_MLRS is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/MLRS_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M270_MLRS_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Bradley_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M270_MLRS_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M274_Mule is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/MULE_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/MULE_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/MULE_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/MULE_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M35 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M35_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/HEMTT_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M35_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M35_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M41 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M41_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M41_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M41_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M60_Patton_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M551_Sheridan is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M551_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M551_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M551_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M551_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M577 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M577_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 4
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M577_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M577_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M113_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_M60_Patton is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M60_Patton_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M60_Patton_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M60_Patton_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M60_Patton_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_MAZ_547 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/MAZ537_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/MAZ537_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/MAZ537_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/MAZ537_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_MTLB is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/MTLB_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/MTLB_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/MTLB_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/MTLB_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Marder is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Marder_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Marder_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Marder_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Marder_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Mutt_M151 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 2
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/M151_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/M151_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/M151_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/M151_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_P4 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 1
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/P4_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/P4_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/P4_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/P4_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_PT76 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 20
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/PT76_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/PT76_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/PT76_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/PT76_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Pegaso is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Pegaso_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Pegaso_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Pegaso_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/TRM2000_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Praga is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Praga_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Praga_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Praga_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Praga_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Saladin is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Saladin_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Saladin_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Saladin_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Saladin_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Saxon is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Saxon_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Saxon_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Saxon_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Saxon_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Shilka is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 20
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/PT76_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/PT76_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/PT76_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/PT76_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_SpPz_2_Luchs is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Luchs_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Luchs_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Luchs_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Luchs_Wheeled_vehicle_Stop_Engine.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Star_266 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Star266_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Star266_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Star266_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Star266_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Supacat is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Supacat_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Supacat_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Supacat_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Supacat_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_T34 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 20
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/T34_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/T34_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/T34_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/T34_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_T55 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/T55_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/T55_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/T55_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/T55_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_T64 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/T64_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/T64_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/T64_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/T64_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_T72 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/T72_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/T72_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/T72_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/T72_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_T80 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/T80_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/T80_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/T80_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/T80_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_T80_Turbine is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/T80_Turbine_Tracked_MBT_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/T80_Turbine_Tracked_MBT_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/T80_Turbine_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/T80_Turbine_Tracked_MBT_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_TPz_Fuchs is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Fuchs_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Fuchs_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Fuchs_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Luchs_Wheeled_vehicle_Stop_Engine.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_TRM2000 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/TRM2000_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/TRM2000_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/TRM2000_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/TRM2000_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Tatra is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Tatra_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Tatra_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Tatra_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Tatra_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Tracked_Rapier is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Rapier_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Rapier_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Rapier_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Rapier_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Tunguska is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 30
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Tunguska_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Tunguska_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/T80_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Tunguska_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_UAZ is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/UAZ_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/UAZ_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/UAZ_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/UAZ_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Unimog is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Unimog_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.5
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Unimog_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Unimog_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Ural_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Ural is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Ural_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Ural_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Ural_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Ural_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_VAB is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/VAB_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/VAB_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/VAB_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/VAB_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_VLRA is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/VLRA_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 2
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/VLRA_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/VLRA_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/VLRA_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_VW_T2b is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 1
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/VW_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/VW_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/VW_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/VW_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Volvo is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Volvo_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 0.5
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Volvo_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Volvo_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Volvo_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_W50 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/W50_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/W50_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/W50_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/W50_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Warrior is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Warrior_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Warrior_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Warrior_Tracked_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Warrior_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_Wiesel is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/Wiesel_Tracked_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 1
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Weasel_Tracked_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/Wiesel_Tracked_MBT_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/Weasel_Tracked_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
export DepictionOperator_MovementSound_SM_ZIL_157 is TCosmeticDriveMovementSoundOperatorDesc
(
    IdleDuration = 10
    IdleFadeInDuration = 0.5
    IdleFadeOutDuration = 2
    IdleSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Idle/ZIL157_Wheeled_vehicle_LoopIdle.wav"
            )
        ),
    ]
    MaxVolume = 1
    MaxVolumeSpeed = 80
    MinVolume = 0.5
    OperatorId = "driveSound"
    RunFadeInDuration = 0.5
    RunFadeOutDuration = 2
    RunOutPitchToGo = 0
    RunOutPitchVelocity = 0.4
    RunSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/ZIL157_Wheeled_vehicle_Loop.wav"
            )
        ),
    ]
    StartSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Start/ZIL157_Wheeled_vehicle_Start_Engine.wav"
            )
        ),
    ]
    StopSounds = 
    [
        TSoundDescriptor
        (
            SoundSettings = $/SoundSettings/SoundSettings_Moteur
            TheSoundStream = TSoundStream
            (
                FileName = "GameData:/Assets/Sounds/SFX/Moteurs/Stop/ZIL157_Wheeled_vehicle_Stop.wav"
            )
        ),
    ]
    VolumeIdleStop = 0.8
)
