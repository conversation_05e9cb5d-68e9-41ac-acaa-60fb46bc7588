// Ne pas éditer, ce fichier est auto-généré !

//----------------------------------------------------------------------------------
// TAcknowUnitType
//----------------------------------------------------------------------------------
unnamed TAcknowUnitType
(
    Values = [
        "TAcknowUnitType_ABInf",
        "TAcknowUnitType_ATGun",
        "TAcknowUnitType_ATInf",
        "TAcknowUnitType_AirRecon",
        "TAcknowUnitType_AirSup",
        "TAcknowUnitType_Air_CAS",
        "TAcknowUnitType_ArtObs",
        "TAcknowUnitType_ArtRckt",
        "TAcknowUnitType_ArtRcktVehicle",
        "TAcknowUnitType_ArtShell",
        "TAcknowUnitType_ArtShellVehicle",
        "TAcknowUnitType_AttackHeli",
        "TAcknowUnitType_CanonAA",
        "TAcknowUnitType_CanonAAVehicle",
        "TAcknowUnitType_CdoInf",
        "TAcknowUnitType_Command",
        "TAcknowUnitType_CommandVehicle",
        "TAcknowUnitType_Command_Infantry",
        "TAcknowUnitType_Engineer",
        "TAcknowUnitType_Engineer2",
        "TAcknowUnitType_Engineers",
        "TAcknowUnitType_Flame",
        "TAcknowUnitType_FlameVehicle",
        "TAcknowUnitType_GroundAtk",
        "TAcknowUnitType_GunArtillery",
        "TAcknowUnitType_HMG",
        "TAcknowUnitType_HeliAttack",
        "TAcknowUnitType_HeliTransport",
        "TAcknowUnitType_Inf",
        "TAcknowUnitType_Inf2",
        "TAcknowUnitType_Inf_Elite",
        "TAcknowUnitType_Inf_Legion",
        "TAcknowUnitType_Inf_Legion_Allemand",
        "TAcknowUnitType_Inf_Legion_Anglais",
        "TAcknowUnitType_Inf_Legion_Espagnol",
        "TAcknowUnitType_Inf_Militia",
        "TAcknowUnitType_Inf_Para_Legion_Pol",
        "TAcknowUnitType_KaJaPa",
        "TAcknowUnitType_Legion_Transport_ITA",
        "TAcknowUnitType_LiInf",
        "TAcknowUnitType_Logistic",
        "TAcknowUnitType_MLRS",
        "TAcknowUnitType_MissileAA",
        "TAcknowUnitType_Multirole",
        "TAcknowUnitType_Officer",
        "TAcknowUnitType_Reco",
        "TAcknowUnitType_RecoVehicle",
        "TAcknowUnitType_ReconInfantry",
        "TAcknowUnitType_Recon_INF",
        "TAcknowUnitType_Recon_Vehicle",
        "TAcknowUnitType_SAM",
        "TAcknowUnitType_SFInf",
        "TAcknowUnitType_Sniper",
        "TAcknowUnitType_Tank",
        "TAcknowUnitType_TankDestroyer",
        "TAcknowUnitType_TankDestroyerMissile",
        "TAcknowUnitType_TankDestroyer_Legion_DE",
        "TAcknowUnitType_Tank_Legion",
        "TAcknowUnitType_Tank_Legion_CZ",
        "TAcknowUnitType_Transport",
        "TAcknowUnitType_Transport2",
        "TAcknowUnitType_Transport3",
        "TAcknowUnitType_Vehicle",
    ]
)
