// Ne pas éditer, ce fichier est généré par AbstractButtonTextureFileWriter



UnitButtonTextureAdditionalBank is TBUCKToolAdditionalTextureBank
(
    Textures = MAP
    [

        ("Texture_Button_Unit_2K11_KRUG_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2K11_KRUG_DDR.png"))]),
        ("Texture_Button_Unit_2K11_KRUG_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2K11_KRUG_POL.png"))]),
        ("Texture_Button_Unit_2K11_KRUG_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2K11_KRUG_SOV.png"))]),
        ("Texture_Button_Unit_2K12_KUB_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2K12_KUB_DDR.png"))]),
        ("Texture_Button_Unit_2K12_KUB_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2K12_KUB_POL.png"))]),
        ("Texture_Button_Unit_2K12_KUB_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2K12_KUB_SOV.png"))]),
        ("Texture_Button_Unit_2S19_MstaS_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S19_MstaS_SOV.png"))]),
        ("Texture_Button_Unit_2S1M_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S1M_POL.png"))]),
        ("Texture_Button_Unit_2S1_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S1_DDR.png"))]),
        ("Texture_Button_Unit_2S1_Gvozdika_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S1_Gvozdika_SOV.png"))]),
        ("Texture_Button_Unit_2S1_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S1_POL.png"))]),
        ("Texture_Button_Unit_2S23_Nona_SVK_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S23_Nona_SVK_SOV.png"))]),
        ("Texture_Button_Unit_2S3M1_Akatsiya_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S3M1_Akatsiya_SOV.png"))]),
        ("Texture_Button_Unit_2S3M_Akatsiya_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S3M_Akatsiya_SOV.png"))]),
        ("Texture_Button_Unit_2S3_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S3_DDR.png"))]),
        ("Texture_Button_Unit_2S5_GiatsintS_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S5_GiatsintS_SOV.png"))]),
        ("Texture_Button_Unit_2S7M_Malka_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S7M_Malka_SOV.png"))]),
        ("Texture_Button_Unit_2S7_Pion_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S7_Pion_POL.png"))]),
        ("Texture_Button_Unit_2S9_Nona_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/2S9_Nona_SOV.png"))]),
        ("Texture_Button_Unit_81mm_mortar_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/81mm_mortar_Aero_US.png"))]),
        ("Texture_Button_Unit_81mm_mortar_CLU_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/81mm_mortar_CLU_UK.png"))]),
        ("Texture_Button_Unit_81mm_mortar_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/81mm_mortar_NG_US.png"))]),
        ("Texture_Button_Unit_81mm_mortar_Para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/81mm_mortar_Para_UK.png"))]),
        ("Texture_Button_Unit_81mm_mortar_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/81mm_mortar_UK.png"))]),
        ("Texture_Button_Unit_81mm_mortar_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/81mm_mortar_US.png"))]),
        ("Texture_Button_Unit_A222_Bereg_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/A222_Bereg_SOV.png"))]),
        ("Texture_Button_Unit_AEC_Militant_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AEC_Militant_UK.png"))]),
        ("Texture_Button_Unit_AIFV_B_50_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_50_BEL.png"))]),
        ("Texture_Button_Unit_AIFV_B_50_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_50_NL.png"))]),
        ("Texture_Button_Unit_AIFV_B_C25_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_C25_BEL.png"))]),
        ("Texture_Button_Unit_AIFV_B_C25_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_C25_NL.png"))]),
        ("Texture_Button_Unit_AIFV_B_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_CMD_BEL.png"))]),
        ("Texture_Button_Unit_AIFV_B_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_CMD_NL.png"))]),
        ("Texture_Button_Unit_AIFV_B_Cargo_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_Cargo_NL.png"))]),
        ("Texture_Button_Unit_AIFV_B_MILAN_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_MILAN_BEL.png"))]),
        ("Texture_Button_Unit_AIFV_B_Radar_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_Radar_NL.png"))]),
        ("Texture_Button_Unit_AIFV_B_TOW_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AIFV_B_TOW_NL.png"))]),
        ("Texture_Button_Unit_AML_60_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AML_60_FR.png"))]),
        ("Texture_Button_Unit_AML_60_Gendarmerie_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AML_60_Gendarmerie_FR.png"))]),
        ("Texture_Button_Unit_AML_90_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AML_90_CMD_FR.png"))]),
        ("Texture_Button_Unit_AML_90_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AML_90_FR.png"))]),
        ("Texture_Button_Unit_AML_90_Reserve_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AML_90_Reserve_FR.png"))]),
        ("Texture_Button_Unit_AMX_10_HOT_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_10_HOT_FR.png"))]),
        ("Texture_Button_Unit_AMX_10_PC_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_10_PC_CMD_FR.png"))]),
        ("Texture_Button_Unit_AMX_10_P_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_10_P_FR.png"))]),
        ("Texture_Button_Unit_AMX_10_P_MILAN_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_10_P_MILAN_FR.png"))]),
        ("Texture_Button_Unit_AMX_10_P_VOA_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_10_P_VOA_FR.png"))]),
        ("Texture_Button_Unit_AMX_10_RCR_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_10_RCR_FR.png"))]),
        ("Texture_Button_Unit_AMX_10_RC_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_10_RC_FR.png"))]),
        ("Texture_Button_Unit_AMX_13_90mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_13_90mm_FR.png"))]),
        ("Texture_Button_Unit_AMX_13_DCA_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_13_DCA_FR.png"))]),
        ("Texture_Button_Unit_AMX_13_VCI_12_7mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_13_VCI_12_7mm_FR.png"))]),
        ("Texture_Button_Unit_AMX_13_VCI_20mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_13_VCI_20mm_FR.png"))]),
        ("Texture_Button_Unit_AMX_13_mod56_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_13_mod56_CMD_BEL.png"))]),
        ("Texture_Button_Unit_AMX_13_mod56_MILAN_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_13_mod56_MILAN_BEL.png"))]),
        ("Texture_Button_Unit_AMX_13_mod56_Mortier_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_13_mod56_Mortier_BEL.png"))]),
        ("Texture_Button_Unit_AMX_13_mod56_VCI_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_13_mod56_VCI_BEL.png"))]),
        ("Texture_Button_Unit_AMX_30_AuF1_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_30_AuF1_FR.png"))]),
        ("Texture_Button_Unit_AMX_30_B2_Brennus_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_30_B2_Brennus_FR.png"))]),
        ("Texture_Button_Unit_AMX_30_B2_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_30_B2_CMD_FR.png"))]),
        ("Texture_Button_Unit_AMX_30_B2_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_30_B2_FR.png"))]),
        ("Texture_Button_Unit_AMX_30_B_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_30_B_CMD_FR.png"))]),
        ("Texture_Button_Unit_AMX_30_B_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_30_B_FR.png"))]),
        ("Texture_Button_Unit_AMX_30_EBG_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AMX_30_EBG_FR.png"))]),
        ("Texture_Button_Unit_ASU_85_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ASU_85_CMD_POL.png"))]),
        ("Texture_Button_Unit_ASU_85_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ASU_85_POL.png"))]),
        ("Texture_Button_Unit_AT_2A45_SprutB_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_2A45_SprutB_SOV.png"))]),
        ("Texture_Button_Unit_AT_D44_85mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_D44_85mm_DDR.png"))]),
        ("Texture_Button_Unit_AT_D44_85mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_AT_D44_85mm_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_D44_85mm_VDV_SOV.png"))]),
        ("Texture_Button_Unit_AT_D48_85mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_D48_85mm_POL.png"))]),
        ("Texture_Button_Unit_AT_Group_Gurkhas_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AT_Group_Gurkhas_UK.png"))]),
        ("Texture_Button_Unit_AT_Group_TA_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AT_Group_TA_UK.png"))]),
        ("Texture_Button_Unit_AT_KSM65_100mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_KSM65_100mm_SOV.png"))]),
        ("Texture_Button_Unit_AT_T12R_Ruta_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_AT_T12_Rapira_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_T12_Rapira_DDR.png"))]),
        ("Texture_Button_Unit_AT_T12_Rapira_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_T12_Rapira_SOV.png"))]),
        ("Texture_Button_Unit_AT_ZiS2_57mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_ZiS2_57mm_DDR.png"))]),
        ("Texture_Button_Unit_AT_vz52_85mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/AT_vz52_85mm_DDR.png"))]),
        ("Texture_Button_Unit_ATteam_Fagot_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_Fagot_DDR.png"))]),
        ("Texture_Button_Unit_ATteam_Fagot_FJ_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_Fagot_FJ_DDR.png"))]),
        ("Texture_Button_Unit_ATteam_Fagot_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_Fagot_SOV.png"))]),
        ("Texture_Button_Unit_ATteam_ITOW_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_ITOW_NG_US.png"))]),
        ("Texture_Button_Unit_ATteam_ITOW_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_ITOW_NL.png"))]),
        ("Texture_Button_Unit_ATteam_ITOW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_ITOW_US.png"))]),
        ("Texture_Button_Unit_ATteam_KonkursM_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_KonkursM_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_ATteam_Konkurs_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_Konkurs_DDR.png"))]),
        ("Texture_Button_Unit_ATteam_Konkurs_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_Konkurs_SOV.png"))]),
        ("Texture_Button_Unit_ATteam_Konkurs_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_Konkurs_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_1_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_1_BEL.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_1_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_1_FR.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_1_RFA.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_1_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_1_UK.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_1_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_1_para_BEL.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_1_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_1_para_FR.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_BEL.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_FR.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_RFA.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_RIMa_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_RIMa_FR.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_UK.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_para_BEL.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_para_FR.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_para_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_para_RFA.png"))]),
        ("Texture_Button_Unit_ATteam_Milan_2_para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_Milan_2_para_UK.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_B11_Reserve_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_RCL_B11_Reserve_SOV.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_M40A1_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_RCL_M40A1_FR.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_M40A1_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_RCL_M40A1_NG_US.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_M40A1_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_RCL_M40A1_NL.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_M40A1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_RCL_M40A1_RFA.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_SPG9_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_RCL_SPG9_DDR.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_SPG9_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_RCL_SPG9_DShV_SOV.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_SPG9_FJ_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_RCL_SPG9_FJ_DDR.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_SPG9_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_RCL_SPG9_POL.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_SPG9_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_RCL_SPG9_Para_POL.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_SPG9_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_RCL_SPG9_SOV.png"))]),
        ("Texture_Button_Unit_ATteam_RCL_SPG9_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ATteam_RCL_SPG9_VDV_SOV.png"))]),
        ("Texture_Button_Unit_ATteam_TOW2A_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_TOW2A_US.png"))]),
        ("Texture_Button_Unit_ATteam_TOW2_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_TOW2_Aero_US.png"))]),
        ("Texture_Button_Unit_ATteam_TOW2_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_TOW2_NL.png"))]),
        ("Texture_Button_Unit_ATteam_TOW2_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_TOW2_US.png"))]),
        ("Texture_Button_Unit_ATteam_TOW2_para_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_TOW2_para_US.png"))]),
        ("Texture_Button_Unit_ATteam_TOW_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_TOW_NL.png"))]),
        ("Texture_Button_Unit_ATteam_TOW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ATteam_TOW_US.png"))]),
        ("Texture_Button_Unit_AeroEngineer_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AeroEngineer_CMD_US.png"))]),
        ("Texture_Button_Unit_AeroEngineers_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AeroEngineers_US.png"))]),
        ("Texture_Button_Unit_AeroRifles_AB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AeroRifles_AB_US.png"))]),
        ("Texture_Button_Unit_AeroRifles_AT4_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AeroRifles_AT4_US.png"))]),
        ("Texture_Button_Unit_AeroRifles_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AeroRifles_CMD_US.png"))]),
        ("Texture_Button_Unit_AeroRifles_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AeroRifles_Dragon_US.png"))]),
        ("Texture_Button_Unit_AeroRifles_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AeroRifles_US.png"))]),
        ("Texture_Button_Unit_Aero_half_AT4_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Aero_half_AT4_US.png"))]),
        ("Texture_Button_Unit_Aero_half_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Aero_half_CMD_US.png"))]),
        ("Texture_Button_Unit_Aero_half_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Aero_half_Dragon_US.png"))]),
        ("Texture_Button_Unit_Airborne_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_CMD_US.png"))]),
        ("Texture_Button_Unit_Airborne_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_Dragon_US.png"))]),
        ("Texture_Button_Unit_Airborne_Engineer_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_Engineer_CMD_US.png"))]),
        ("Texture_Button_Unit_Airborne_Engineers_Flash_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_Engineers_Flash_US.png"))]),
        ("Texture_Button_Unit_Airborne_Engineers_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_Engineers_US.png"))]),
        ("Texture_Button_Unit_Airborne_HMG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_HMG_US.png"))]),
        ("Texture_Button_Unit_Airborne_MP_RCL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_MP_RCL_US.png"))]),
        ("Texture_Button_Unit_Airborne_MP_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_MP_US.png"))]),
        ("Texture_Button_Unit_Airborne_Scout_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_Scout_US.png"))]),
        ("Texture_Button_Unit_Airborne_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_US.png"))]),
        ("Texture_Button_Unit_Airborne_half_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_half_Dragon_US.png"))]),
        ("Texture_Button_Unit_Airborne_half_LAW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airborne_half_LAW_US.png"))]),
        ("Texture_Button_Unit_Airmobile_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airmobile_CMD_UK.png"))]),
        ("Texture_Button_Unit_Airmobile_MILAN_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airmobile_MILAN_UK.png"))]),
        ("Texture_Button_Unit_Airmobile_Mot_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airmobile_Mot_CMD_UK.png"))]),
        ("Texture_Button_Unit_Airmobile_Mot_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airmobile_Mot_UK.png"))]),
        ("Texture_Button_Unit_Airmobile_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Airmobile_UK.png"))]),
        ("Texture_Button_Unit_Alfa_Group_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Alfa_Group_SOV.png"))]),
        ("Texture_Button_Unit_Alvis_Stalwart_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alvis_Stalwart_UK.png"))]),
        ("Texture_Button_Unit_Atteam_Dragon_Marines_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Atteam_Dragon_Marines_NL.png"))]),
        ("Texture_Button_Unit_Atteam_Fagot_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Atteam_Fagot_DShV_SOV.png"))]),
        ("Texture_Button_Unit_Atteam_Fagot_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Atteam_Fagot_POL.png"))]),
        ("Texture_Button_Unit_Atteam_Fagot_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Atteam_Fagot_Para_POL.png"))]),
        ("Texture_Button_Unit_Atteam_Fagot_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Atteam_Fagot_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Atteam_Konkurs_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Atteam_Konkurs_DShV_SOV.png"))]),
        ("Texture_Button_Unit_Atteam_Konkurs_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Atteam_Konkurs_POL.png"))]),
        ("Texture_Button_Unit_Atteam_Konkurs_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Atteam_Konkurs_VDV_SOV.png"))]),
        ("Texture_Button_Unit_BAV_485_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BAV_485_POL.png"))]),
        ("Texture_Button_Unit_BAV_485_Supply_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BAV_485_Supply_POL.png"))]),
        ("Texture_Button_Unit_BGS_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/BGS_RFA.png"))]),
        ("Texture_Button_Unit_BGS_hvy_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/BGS_hvy_RFA.png"))]),
        ("Texture_Button_Unit_BM14M_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM14M_POL.png"))]),
        ("Texture_Button_Unit_BM21V_GradV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM21V_GradV_SOV.png"))]),
        ("Texture_Button_Unit_BM21_Grad_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM21_Grad_DDR.png"))]),
        ("Texture_Button_Unit_BM21_Grad_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM21_Grad_POL.png"))]),
        ("Texture_Button_Unit_BM21_Grad_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM21_Grad_SOV.png"))]),
        ("Texture_Button_Unit_BM24M_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM24M_DDR.png"))]),
        ("Texture_Button_Unit_BM24M_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM24M_POL.png"))]),
        ("Texture_Button_Unit_BM24M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM24M_SOV.png"))]),
        ("Texture_Button_Unit_BM27_Uragan_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM27_Uragan_SOV.png"))]),
        ("Texture_Button_Unit_BM30_Smerch_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BM30_Smerch_SOV.png"))]),
        ("Texture_Button_Unit_BMD_1K_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_1K_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BMD_1P_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_1P_SOV.png"))]),
        ("Texture_Button_Unit_BMD_1_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_1_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BMD_1_Reostat_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_1_Reostat_SOV.png"))]),
        ("Texture_Button_Unit_BMD_1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_1_SOV.png"))]),
        ("Texture_Button_Unit_BMD_2_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_2_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BMD_2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_2_SOV.png"))]),
        ("Texture_Button_Unit_BMD_3_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_3_SOV.png"))]),
        ("Texture_Button_Unit_BMD_3_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMD_3_reco_SOV.png"))]),
        ("Texture_Button_Unit_BMP_1PG_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1PG_SOV.png"))]),
        ("Texture_Button_Unit_BMP_1P_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1P_DDR.png"))]),
        ("Texture_Button_Unit_BMP_1P_Konkurs_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1P_Konkurs_DDR.png"))]),
        ("Texture_Button_Unit_BMP_1P_Konkurs_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1P_Konkurs_SOV.png"))]),
        ("Texture_Button_Unit_BMP_1P_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1P_SOV.png"))]),
        ("Texture_Button_Unit_BMP_1P_reco_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1P_reco_DDR.png"))]),
        ("Texture_Button_Unit_BMP_1P_reco_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_BMP_1P_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1P_reco_SOV.png"))]),
        ("Texture_Button_Unit_BMP_1_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1_CMD_DDR.png"))]),
        ("Texture_Button_Unit_BMP_1_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1_CMD_POL.png"))]),
        ("Texture_Button_Unit_BMP_1_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BMP_1_SP1_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1_SP1_DDR.png"))]),
        ("Texture_Button_Unit_BMP_1_SP2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1_SP2_DDR.png"))]),
        ("Texture_Button_Unit_BMP_1_SP2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1_SP2_POL.png"))]),
        ("Texture_Button_Unit_BMP_1_SP2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1_SP2_SOV.png"))]),
        ("Texture_Button_Unit_BMP_1_SP2_reco_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_1_SP2_reco_POL.png"))]),
        ("Texture_Button_Unit_BMP_2AG_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_2AG_SOV.png"))]),
        ("Texture_Button_Unit_BMP_2D_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_2D_SOV.png"))]),
        ("Texture_Button_Unit_BMP_2D_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_2D_reco_SOV.png"))]),
        ("Texture_Button_Unit_BMP_2_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_2_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BMP_2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_2_DDR.png"))]),
        ("Texture_Button_Unit_BMP_2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_2_POL.png"))]),
        ("Texture_Button_Unit_BMP_2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_2_SOV.png"))]),
        ("Texture_Button_Unit_BMP_2_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_2_reco_SOV.png"))]),
        ("Texture_Button_Unit_BMP_3_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BMP_3_SOV.png"))]),
        ("Texture_Button_Unit_BRDM_1_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_1_DDR.png"))]),
        ("Texture_Button_Unit_BRDM_1_DShK_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_1_DShK_POL.png"))]),
        ("Texture_Button_Unit_BRDM_1_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_1_POL.png"))]),
        ("Texture_Button_Unit_BRDM_1_PSNR1_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_1_PSNR1_POL.png"))]),
        ("Texture_Button_Unit_BRDM_2_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_CMD_DDR.png"))]),
        ("Texture_Button_Unit_BRDM_2_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_CMD_POL.png"))]),
        ("Texture_Button_Unit_BRDM_2_CMD_R5_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_CMD_R5_POL.png"))]),
        ("Texture_Button_Unit_BRDM_2_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BRDM_2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_DDR.png"))]),
        ("Texture_Button_Unit_BRDM_2_Konkurs_M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_Konkurs_M_SOV.png"))]),
        ("Texture_Button_Unit_BRDM_2_Konkurs_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_Konkurs_POL.png"))]),
        ("Texture_Button_Unit_BRDM_2_Konkurs_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_Konkurs_SOV.png"))]),
        ("Texture_Button_Unit_BRDM_2_Malyu_P_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_Malyu_P_POL.png"))]),
        ("Texture_Button_Unit_BRDM_2_Malyu_P_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_Malyu_P_SOV.png"))]),
        ("Texture_Button_Unit_BRDM_2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_POL.png"))]),
        ("Texture_Button_Unit_BRDM_2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_2_SOV.png"))]),
        ("Texture_Button_Unit_BRDM_Konkurs_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_Konkurs_DDR.png"))]),
        ("Texture_Button_Unit_BRDM_Malyu_P_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_Malyu_P_DDR.png"))]),
        ("Texture_Button_Unit_BRDM_Strela_1_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_Strela_1_DDR.png"))]),
        ("Texture_Button_Unit_BRDM_Strela_1_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_Strela_1_POL.png"))]),
        ("Texture_Button_Unit_BRDM_Strela_1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRDM_Strela_1_SOV.png"))]),
        ("Texture_Button_Unit_BRM_1_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRM_1_DDR.png"))]),
        ("Texture_Button_Unit_BRM_1_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRM_1_POL.png"))]),
        ("Texture_Button_Unit_BRM_1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BRM_1_SOV.png"))]),
        ("Texture_Button_Unit_BTR_152A_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_152A_DDR.png"))]),
        ("Texture_Button_Unit_BTR_152A_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_152A_SOV.png"))]),
        ("Texture_Button_Unit_BTR_152K_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_152K_SOV.png"))]),
        ("Texture_Button_Unit_BTR_152S_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_152S_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BTR_40A_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_40A_SOV.png"))]),
        ("Texture_Button_Unit_BTR_40B_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_40B_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BTR_40_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_40_SOV.png"))]),
        ("Texture_Button_Unit_BTR_50_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_50_CMD_DDR.png"))]),
        ("Texture_Button_Unit_BTR_50_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_50_DDR.png"))]),
        ("Texture_Button_Unit_BTR_50_MRF_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_50_MRF_DDR.png"))]),
        ("Texture_Button_Unit_BTR_60_CHAIKA_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_60_CHAIKA_CMD_DDR.png"))]),
        ("Texture_Button_Unit_BTR_60_CHAIKA_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_60_CHAIKA_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BTR_60_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_60_CMD_DDR.png"))]),
        ("Texture_Button_Unit_BTR_60_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_60_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BTR_60_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_60_DDR.png"))]),
        ("Texture_Button_Unit_BTR_60_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_60_SOV.png"))]),
        ("Texture_Button_Unit_BTR_60_reco_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_60_reco_DDR.png"))]),
        ("Texture_Button_Unit_BTR_60_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_60_reco_SOV.png"))]),
        ("Texture_Button_Unit_BTR_70D_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_70D_SOV.png"))]),
        ("Texture_Button_Unit_BTR_70_AGS_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_70_AGS_SOV.png"))]),
        ("Texture_Button_Unit_BTR_70_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_70_DDR.png"))]),
        ("Texture_Button_Unit_BTR_70_MP_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_70_MP_SOV.png"))]),
        ("Texture_Button_Unit_BTR_70_Rys_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_70_Rys_SOV.png"))]),
        ("Texture_Button_Unit_BTR_70_S5_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_70_S5_SOV.png"))]),
        ("Texture_Button_Unit_BTR_70_S8_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_70_S8_SOV.png"))]),
        ("Texture_Button_Unit_BTR_70_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_70_SOV.png"))]),
        ("Texture_Button_Unit_BTR_80_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_80_CMD_SOV.png"))]),
        ("Texture_Button_Unit_BTR_80_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_80_SOV.png"))]),
        ("Texture_Button_Unit_BTR_D_Robot_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_D_Robot_SOV.png"))]),
        ("Texture_Button_Unit_BTR_D_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_D_SOV.png"))]),
        ("Texture_Button_Unit_BTR_D_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_D_reco_SOV.png"))]),
        ("Texture_Button_Unit_BTR_ZD_Skrezhet_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/BTR_ZD_Skrezhet_SOV.png"))]),
        ("Texture_Button_Unit_Bedford_MJ_4t_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bedford_MJ_4t_UK.png"))]),
        ("Texture_Button_Unit_Bedford_MJ_4t_trans_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bedford_MJ_4t_trans_UK.png"))]),
        ("Texture_Button_Unit_Bofors_40mm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bofors_40mm_RFA.png"))]),
        ("Texture_Button_Unit_Bofors_40mm_capture_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Bofors_40mm_capture_DDR.png"))]),
        ("Texture_Button_Unit_Buk_9K37M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Buk_9K37M_SOV.png"))]),
        ("Texture_Button_Unit_CGage_Peacekeeper_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CGage_Peacekeeper_US.png"))]),
        ("Texture_Button_Unit_CGage_V150_Commando_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_CUCV_AGL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CUCV_AGL_US.png"))]),
        ("Texture_Button_Unit_CUCV_HMG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CUCV_HMG_US.png"))]),
        ("Texture_Button_Unit_CUCV_Hellfire_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CUCV_Hellfire_US.png"))]),
        ("Texture_Button_Unit_CUCV_MP_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CUCV_MP_US.png"))]),
        ("Texture_Button_Unit_CUCV_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CUCV_US.png"))]),
        ("Texture_Button_Unit_CUCV_trans_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CUCV_trans_US.png"))]),
        ("Texture_Button_Unit_Centurion_AVRE_105_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Centurion_AVRE_105_UK.png"))]),
        ("Texture_Button_Unit_Challenger_1_Mk1_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Challenger_1_Mk1_CMD_UK.png"))]),
        ("Texture_Button_Unit_Challenger_1_Mk1_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Challenger_1_Mk1_UK.png"))]),
        ("Texture_Button_Unit_Challenger_1_Mk3_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Challenger_1_Mk3_UK.png"))]),
        ("Texture_Button_Unit_Chasseurs_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Chasseurs_CMD_FR.png"))]),
        ("Texture_Button_Unit_Chasseurs_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Chasseurs_FR.png"))]),
        ("Texture_Button_Unit_Commando_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Commando_CMD_NL.png"))]),
        ("Texture_Button_Unit_Commando_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Commando_NL.png"))]),
        ("Texture_Button_Unit_Commandos_Air_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Commandos_Air_FR.png"))]),
        ("Texture_Button_Unit_Commandos_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Commandos_CMD_POL.png"))]),
        ("Texture_Button_Unit_Commandos_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Commandos_FR.png"))]),
        ("Texture_Button_Unit_Commandos_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Commandos_POL.png"))]),
        ("Texture_Button_Unit_Commandos_Para_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Commandos_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Commandos_Para_POL.png"))]),
        ("Texture_Button_Unit_Crotale_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Crotale_FR.png"))]),
        ("Texture_Button_Unit_DAF_YA_4400_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DAF_YA_4400_NL.png"))]),
        ("Texture_Button_Unit_DAF_YA_4400_supply_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DAF_YA_4400_supply_NL.png"))]),
        ("Texture_Button_Unit_DAF_YHZ_2300_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DAF_YHZ_2300_NL.png"))]),
        ("Texture_Button_Unit_DAF_YHZ_2300_trans_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DAF_YHZ_2300_trans_NL.png"))]),
        ("Texture_Button_Unit_DANA_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DANA_POL.png"))]),
        ("Texture_Button_Unit_DCA_53T2_20mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_53T2_20mm_FR.png"))]),
        ("Texture_Button_Unit_DCA_53T2_20mm_Para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_53T2_20mm_Para_FR.png"))]),
        ("Texture_Button_Unit_DCA_76T2_20mm_CPA_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_76T2_20mm_CPA_FR.png"))]),
        ("Texture_Button_Unit_DCA_AZP_S60_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_AZP_S60_DDR.png"))]),
        ("Texture_Button_Unit_DCA_AZP_S60_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_AZP_S60_POL.png"))]),
        ("Texture_Button_Unit_DCA_AZP_S60_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_AZP_S60_SOV.png"))]),
        ("Texture_Button_Unit_DCA_Bofors_L60_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_Bofors_L60_FR.png"))]),
        ("Texture_Button_Unit_DCA_Bofors_upgrade_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_Bofors_upgrade_NL.png"))]),
        ("Texture_Button_Unit_DCA_FASTA_4_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_FASTA_4_DDR.png"))]),
        ("Texture_Button_Unit_DCA_FK20_2_20mm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_FK20_2_20mm_RFA.png"))]),
        ("Texture_Button_Unit_DCA_FK20_2_20mm_Zwillinge_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_FK20_2_20mm_Zwillinge_RFA.png"))]),
        ("Texture_Button_Unit_DCA_I_Hawk_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_I_Hawk_BEL.png"))]),
        ("Texture_Button_Unit_DCA_I_Hawk_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_I_Hawk_NL.png"))]),
        ("Texture_Button_Unit_DCA_I_Hawk_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_I_Hawk_RFA.png"))]),
        ("Texture_Button_Unit_DCA_I_Hawk_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_I_Hawk_US.png"))]),
        ("Texture_Button_Unit_DCA_I_Hawk_capture_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_I_Hawk_capture_DDR.png"))]),
        ("Texture_Button_Unit_DCA_Javelin_LML_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_Javelin_LML_UK.png"))]),
        ("Texture_Button_Unit_DCA_KS19_100mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_KS19_100mm_DDR.png"))]),
        ("Texture_Button_Unit_DCA_KS30_130mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_KS30_130mm_SOV.png"))]),
        ("Texture_Button_Unit_DCA_M167A2_Vulcan_20mm_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_M167A2_Vulcan_20mm_Aero_US.png"))]),
        ("Texture_Button_Unit_DCA_M167A2_Vulcan_20mm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_M167A2_Vulcan_20mm_US.png"))]),
        ("Texture_Button_Unit_DCA_M167_Vulcan_20mm_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_M167_Vulcan_20mm_BEL.png"))]),
        ("Texture_Button_Unit_DCA_M167_Vulcan_20mm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_M167_Vulcan_20mm_US.png"))]),
        ("Texture_Button_Unit_DCA_M167_Vulcan_20mm_nonPara_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_M167_Vulcan_20mm_nonPara_US.png"))]),
        ("Texture_Button_Unit_DCA_M167_Vulcan_para_20mm_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_M167_Vulcan_para_20mm_BEL.png"))]),
        ("Texture_Button_Unit_DCA_M55_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_M55_NL.png"))]),
        ("Texture_Button_Unit_DCA_Oerlikon_GDF_002_35mm_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_Oerlikon_GDF_002_35mm_UK.png"))]),
        ("Texture_Button_Unit_DCA_Rapier_Darkfire_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_Rapier_Darkfire_UK.png"))]),
        ("Texture_Button_Unit_DCA_Rapier_FSA_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_Rapier_UK.png"))]),
        ("Texture_Button_Unit_DCA_Rapier_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_Rapier_UK.png"))]),
        ("Texture_Button_Unit_DCA_XM85_Chaparral_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_XM85_Chaparral_US.png"))]),
        ("Texture_Button_Unit_DCA_XMIM_115A_Roland_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DCA_XMIM_115A_Roland_US.png"))]),
        ("Texture_Button_Unit_DCA_ZPU4_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZPU4_DDR.png"))]),
        ("Texture_Button_Unit_DCA_ZPU4_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZPU4_POL.png"))]),
        ("Texture_Button_Unit_DCA_ZUR_23_2S_JOD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZUR_23_2S_JOD_POL.png"))]),
        ("Texture_Button_Unit_DCA_ZUR_23_2S_JOD_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZUR_23_2S_JOD_Para_POL.png"))]),
        ("Texture_Button_Unit_DCA_ZU_23_2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZU_23_2_DDR.png"))]),
        ("Texture_Button_Unit_DCA_ZU_23_2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZU_23_2_POL.png"))]),
        ("Texture_Button_Unit_DCA_ZU_23_2_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZU_23_2_Para_POL.png"))]),
        ("Texture_Button_Unit_DCA_ZU_23_2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZU_23_2_SOV.png"))]),
        ("Texture_Button_Unit_DCA_ZU_23_2_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZU_23_2_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_DCA_ZU_23_2_nonPara_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DCA_ZU_23_2_nonPara_SOV.png"))]),
        ("Texture_Button_Unit_DShV_Afgantsy_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DShV_Afgantsy_SOV.png"))]),
        ("Texture_Button_Unit_DShV_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DShV_CMD_SOV.png"))]),
        ("Texture_Button_Unit_DShV_HMG_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DShV_HMG_SOV.png"))]),
        ("Texture_Button_Unit_DShV_Hvy_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DShV_Hvy_SOV.png"))]),
        ("Texture_Button_Unit_DShV_Metis_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DShV_Metis_SOV.png"))]),
        ("Texture_Button_Unit_DShV_RPG16_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DShV_RPG16_SOV.png"))]),
        ("Texture_Button_Unit_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/DShV_SOV.png"))]),
        ("Texture_Button_Unit_DeltaForce_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/DeltaForce_US.png"))]),
        ("Texture_Button_Unit_Dragoon_300_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Dragoon_300_US.png"))]),
        ("Texture_Button_Unit_EBR_90mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/EBR_90mm_FR.png"))]),
        ("Texture_Button_Unit_ERC_90_Sagaie_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ERC_90_Sagaie_CMD_FR.png"))]),
        ("Texture_Button_Unit_ERC_90_Sagaie_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ERC_90_Sagaie_FR.png"))]),
        ("Texture_Button_Unit_ERC_90_Sagaie_reco_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ERC_90_Sagaie_reco_FR.png"))]),
        ("Texture_Button_Unit_Engineer_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineer_CMD_US.png"))]),
        ("Texture_Button_Unit_Engineers_AGI_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_AGI_DDR.png"))]),
        ("Texture_Button_Unit_Engineers_AT_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_AT_BEL.png"))]),
        ("Texture_Button_Unit_Engineers_AT_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_AT_RFA.png"))]),
        ("Texture_Button_Unit_Engineers_AT_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_AT_UK.png"))]),
        ("Texture_Button_Unit_Engineers_Airmobile_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_Airmobile_UK.png"))]),
        ("Texture_Button_Unit_Engineers_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_BEL.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_CMD_BEL.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_CMD_DShV_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_CMD_NL.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_CMD_POL.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_CMD_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_CMD_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_CMD_UK.png"))]),
        ("Texture_Button_Unit_Engineers_CMD_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_CMD_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_DDR.png"))]),
        ("Texture_Button_Unit_Engineers_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_DShV_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_Dragon_US.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_Flam_BEL.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Flam_DDR.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Flam_DShV_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Flam_POL.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_Flam_RFA.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_Reserve_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Flam_Reserve_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Flam_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Flam_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_Flam_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Flam_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_Flash_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_Flash_US.png"))]),
        ("Texture_Button_Unit_Engineers_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_NL.png"))]),
        ("Texture_Button_Unit_Engineers_Naval_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Naval_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Engineers_Naval_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Naval_DDR.png"))]),
        ("Texture_Button_Unit_Engineers_Naval_Flam_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Naval_Flam_DDR.png"))]),
        ("Texture_Button_Unit_Engineers_Naval_Scout_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Naval_Scout_DDR.png"))]),
        ("Texture_Button_Unit_Engineers_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_POL.png"))]),
        ("Texture_Button_Unit_Engineers_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_RFA.png"))]),
        ("Texture_Button_Unit_Engineers_Reserve_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_Reserve_RFA.png"))]),
        ("Texture_Button_Unit_Engineers_Reserve_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Reserve_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_Scout_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_Scout_NL.png"))]),
        ("Texture_Button_Unit_Engineers_Scout_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Scout_POL.png"))]),
        ("Texture_Button_Unit_Engineers_Scout_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Scout_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_Scout_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_Scout_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_TA_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_TA_UK.png"))]),
        ("Texture_Button_Unit_Engineers_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_UK.png"))]),
        ("Texture_Button_Unit_Engineers_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Engineers_US.png"))]),
        ("Texture_Button_Unit_Engineers_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Engineers_paras_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_paras_CMD_POL.png"))]),
        ("Texture_Button_Unit_Engineers_paras_Flam_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_paras_Flam_POL.png"))]),
        ("Texture_Button_Unit_Engineers_paras_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Engineers_paras_POL.png"))]),
        ("Texture_Button_Unit_Escorte_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Escorte_FR.png"))]),
        ("Texture_Button_Unit_FAV_AGL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FAV_AGL_US.png"))]),
        ("Texture_Button_Unit_FAV_HMG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FAV_HMG_US.png"))]),
        ("Texture_Button_Unit_FAV_TOW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FAV_TOW_US.png"))]),
        ("Texture_Button_Unit_FAV_trans_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FAV_trans_US.png"))]),
        ("Texture_Button_Unit_FH70_155mm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FH70_155mm_RFA.png"))]),
        ("Texture_Button_Unit_FH70_155mm_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FH70_155mm_RFA.png"))]),
        ("Texture_Button_Unit_FV101_Scorpion_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV101_Scorpion_BEL.png"))]),
        ("Texture_Button_Unit_FV101_Scorpion_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV101_Scorpion_UK.png"))]),
        ("Texture_Button_Unit_FV101_Scorpion_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV101_Scorpion_para_BEL.png"))]),
        ("Texture_Button_Unit_FV102_Striker_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV102_Striker_BEL.png"))]),
        ("Texture_Button_Unit_FV102_Striker_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV102_Striker_UK.png"))]),
        ("Texture_Button_Unit_FV102_Striker_para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV102_Striker_para_UK.png"))]),
        ("Texture_Button_Unit_FV103_Spartan_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV103_Spartan_BEL.png"))]),
        ("Texture_Button_Unit_FV103_Spartan_GSR_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV103_Spartan_GSR_UK.png"))]),
        ("Texture_Button_Unit_FV103_Spartan_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV103_Spartan_UK.png"))]),
        ("Texture_Button_Unit_FV103_Spartan_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV103_Spartan_para_BEL.png"))]),
        ("Texture_Button_Unit_FV105_Sultan_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV105_Sultan_BEL.png"))]),
        ("Texture_Button_Unit_FV105_Sultan_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV105_Sultan_UK.png"))]),
        ("Texture_Button_Unit_FV105_Sultan_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV105_Sultan_para_BEL.png"))]),
        ("Texture_Button_Unit_FV105_Sultan_para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV105_Sultan_para_UK.png"))]),
        ("Texture_Button_Unit_FV107_Scimitar_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV107_Scimitar_BEL.png"))]),
        ("Texture_Button_Unit_FV107_Scimitar_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV107_Scimitar_UK.png"))]),
        ("Texture_Button_Unit_FV107_Scimitar_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV107_Scimitar_para_BEL.png"))]),
        ("Texture_Button_Unit_FV120_Spartan_MCT_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV120_Spartan_MCT_UK.png"))]),
        ("Texture_Button_Unit_FV4003_Centurion_AVRE_ROMOR_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV4003_Centurion_AVRE_ROMOR_UK.png"))]),
        ("Texture_Button_Unit_FV4003_Centurion_AVRE_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV4003_Centurion_AVRE_UK.png"))]),
        ("Texture_Button_Unit_FV4201_Chieftain_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV4201_Chieftain_CMD_UK.png"))]),
        ("Texture_Button_Unit_FV4201_Chieftain_Mk11_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV4201_Chieftain_Mk11_CMD_UK.png"))]),
        ("Texture_Button_Unit_FV4201_Chieftain_Mk11_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV4201_Chieftain_Mk11_UK.png"))]),
        ("Texture_Button_Unit_FV4201_Chieftain_Mk6_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV4201_Chieftain_Mk6_UK.png"))]),
        ("Texture_Button_Unit_FV4201_Chieftain_Mk9_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV4201_Chieftain_Mk9_UK.png"))]),
        ("Texture_Button_Unit_FV4201_Chieftain_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV4201_Chieftain_UK.png"))]),
        ("Texture_Button_Unit_FV432_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV432_CMD_UK.png"))]),
        ("Texture_Button_Unit_FV432_MILAN_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV432_MILAN_UK.png"))]),
        ("Texture_Button_Unit_FV432_Mortar_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV432_Mortar_UK.png"))]),
        ("Texture_Button_Unit_FV432_Rarden_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV432_Rarden_UK.png"))]),
        ("Texture_Button_Unit_FV432_SCAT_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV432_SCAT_UK.png"))]),
        ("Texture_Button_Unit_FV432_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV432_UK.png"))]),
        ("Texture_Button_Unit_FV432_WOMBAT_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV432_WOMBAT_UK.png"))]),
        ("Texture_Button_Unit_FV432_supply_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV432_supply_UK.png"))]),
        ("Texture_Button_Unit_FV433_Abbot_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV433_Abbot_UK.png"))]),
        ("Texture_Button_Unit_FV438_Swingfire_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV438_Swingfire_UK.png"))]),
        ("Texture_Button_Unit_FV601_Saladin_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV601_Saladin_UK.png"))]),
        ("Texture_Button_Unit_FV603_Saracen_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV603_Saracen_UK.png"))]),
        ("Texture_Button_Unit_FV721_Fox_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FV721_Fox_UK.png"))]),
        ("Texture_Button_Unit_Fallschirm_B1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Fallschirm_B1_RFA.png"))]),
        ("Texture_Button_Unit_Fallschirm_Engineers_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Fallschirm_Engineers_RFA.png"))]),
        ("Texture_Button_Unit_Fallschirm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Fallschirm_RFA.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Fallschirmjager_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Fallschirmjager_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Fallschirmjager_DDR.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_FalseFlag_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Fallschirmjager_FalseFlag_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_FalseFlag_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Fallschirmjager_FalseFlag_DDR.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_FlaseFlag_Demo_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Fallschirmjager_FlaseFlag_Demo_DDR.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_HMG_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Fallschirmjager_HMG_DDR.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_Metys_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Fallschirmjager_Metys_DDR.png"))]),
        ("Texture_Button_Unit_Fallschirmjager_Scout_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Fallschirmjager_Scout_RFA.png"))]),
        ("Texture_Button_Unit_Faun_Kraka_20mm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Faun_Kraka_20mm_RFA.png"))]),
        ("Texture_Button_Unit_Faun_Kraka_Log_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Faun_Kraka_Log_RFA.png"))]),
        ("Texture_Button_Unit_Faun_Kraka_TOW_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Faun_Kraka_TOW_RFA.png"))]),
        ("Texture_Button_Unit_Faun_kraka_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Faun_kraka_RFA.png"))]),
        ("Texture_Button_Unit_Feldgendarmerie_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Feldgendarmerie_RFA.png"))]),
        ("Texture_Button_Unit_Fernspaher_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Fernspaher_RFA.png"))]),
        ("Texture_Button_Unit_Ferret_Mk2_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Ferret_Mk2_UK.png"))]),
        ("Texture_Button_Unit_FireSupport_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/FireSupport_SOV.png"))]),
        ("Texture_Button_Unit_FireSupport_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/FireSupport_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_GAZ_46_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_46_DDR.png"))]),
        ("Texture_Button_Unit_GAZ_46_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_46_POL.png"))]),
        ("Texture_Button_Unit_GAZ_46_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_46_SOV.png"))]),
        ("Texture_Button_Unit_GAZ_66B_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66B_POL.png"))]),
        ("Texture_Button_Unit_GAZ_66B_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66B_SOV.png"))]),
        ("Texture_Button_Unit_GAZ_66B_ZU_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66B_ZU_SOV.png"))]),
        ("Texture_Button_Unit_GAZ_66B_supply_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66B_supply_POL.png"))]),
        ("Texture_Button_Unit_GAZ_66B_supply_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66B_supply_SOV.png"))]),
        ("Texture_Button_Unit_GAZ_66_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66_POL.png"))]),
        ("Texture_Button_Unit_GAZ_66_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66_SOV.png"))]),
        ("Texture_Button_Unit_GAZ_66_supply_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66_supply_SOV.png"))]),
        ("Texture_Button_Unit_GAZ_66_trans_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GAZ_66_trans_POL.png"))]),
        ("Texture_Button_Unit_GTMU_1D_AGS_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GTMU_1D_AGS_SOV.png"))]),
        ("Texture_Button_Unit_GTMU_1D_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GTMU_1D_SOV.png"))]),
        ("Texture_Button_Unit_GTMU_1D_SPG9_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GTMU_1D_SPG9_SOV.png"))]),
        ("Texture_Button_Unit_GTMU_1D_ZU_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/GTMU_1D_ZU_SOV.png"))]),
        ("Texture_Button_Unit_Gama_Goat_supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gama_Goat_supply_US.png"))]),
        ("Texture_Button_Unit_Gama_Goat_trans_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gama_Goat_trans_US.png"))]),
        ("Texture_Button_Unit_Gendarmerie_Air_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gendarmerie_Air_FR.png"))]),
        ("Texture_Button_Unit_Gendarmerie_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gendarmerie_FR.png"))]),
        ("Texture_Button_Unit_Gepard_1A2_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gepard_1A2_BEL.png"))]),
        ("Texture_Button_Unit_Gepard_1A2_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gepard_1A2_NL.png"))]),
        ("Texture_Button_Unit_Gepard_1A2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gepard_1A2_RFA.png"))]),
        ("Texture_Button_Unit_GreenBerets_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/GreenBerets_CMD_US.png"))]),
        ("Texture_Button_Unit_GreenBerets_MP5_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/GreenBerets_MP5_US.png"))]),
        ("Texture_Button_Unit_GreenBerets_ODA_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/GreenBerets_ODA_US.png"))]),
        ("Texture_Button_Unit_GreenBerets_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/GreenBerets_US.png"))]),
        ("Texture_Button_Unit_Grenzer_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Grenzer_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Grenzer_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Grenzer_DDR.png"))]),
        ("Texture_Button_Unit_Grenzer_Flam_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Grenzer_Flam_DDR.png"))]),
        ("Texture_Button_Unit_Grenzer_Mot_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Grenzer_Mot_DDR.png"))]),
        ("Texture_Button_Unit_Groupe_AT_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Groupe_AT_FR.png"))]),
        ("Texture_Button_Unit_Groupe_AT_Marines_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Groupe_AT_Marines_NL.png"))]),
        ("Texture_Button_Unit_Groupe_AT_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Groupe_AT_POL.png"))]),
        ("Texture_Button_Unit_Groupe_AT_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Groupe_AT_Para_POL.png"))]),
        ("Texture_Button_Unit_Groupe_AT_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Groupe_AT_US.png"))]),
        ("Texture_Button_Unit_Groupe_AT_Wach_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Groupe_AT_Wach_DDR.png"))]),
        ("Texture_Button_Unit_Groupe_AT_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Groupe_AT_para_FR.png"))]),
        ("Texture_Button_Unit_Gun_Group_Paras_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gun_Group_Paras_UK.png"))]),
        ("Texture_Button_Unit_Gun_Group_TA_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gun_Group_TA_UK.png"))]),
        ("Texture_Button_Unit_Gun_Group_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gun_Group_UK.png"))]),
        ("Texture_Button_Unit_Gvardeitsy_BTR_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Gvardeitsy_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Gvardeitsy_HMG_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Gvardeitsy_Metis_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Gvardeitsy_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Gvardeitsy_SVD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_HEMTT_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HEMTT_US.png"))]),
        ("Texture_Button_Unit_HMGteam_AANF1_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_AANF1_FR.png"))]),
        ("Texture_Button_Unit_HMGteam_AANF1_Reserve_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_AANF1_Reserve_FR.png"))]),
        ("Texture_Button_Unit_HMGteam_AANF1_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_AANF1_para_FR.png"))]),
        ("Texture_Button_Unit_HMGteam_AGS17_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_AGS17_DDR.png"))]),
        ("Texture_Button_Unit_HMGteam_AGS17_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_AGS17_DShV_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_AGS17_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_HMGteam_AGS17_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_AGS17_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_AGS17_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_AGS17_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_AGS17_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_AGS17_VDV_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_DShK_AA_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_DShK_AA_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_DShK_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_DShK_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_KPVT_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_KPVT_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_M1919A4_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M1919A4_NL.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_AB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_AB_US.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_Aero_US.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_BEL.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_FR.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_LUX.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_M63_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_M63_US.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_NG_US.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_NL.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_RIMa_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_RIMa_FR.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_UK.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_US.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_para_FR.png"))]),
        ("Texture_Button_Unit_HMGteam_M2HB_para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M2HB_para_UK.png"))]),
        ("Texture_Button_Unit_HMGteam_M60_AB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M60_AB_US.png"))]),
        ("Texture_Button_Unit_HMGteam_M60_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M60_Aero_US.png"))]),
        ("Texture_Button_Unit_HMGteam_M60_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M60_NG_US.png"))]),
        ("Texture_Button_Unit_HMGteam_M60_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_M60_US.png"))]),
        ("Texture_Button_Unit_HMGteam_MAG_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_MAG_BEL.png"))]),
        ("Texture_Button_Unit_HMGteam_MAG_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_MAG_NL.png"))]),
        ("Texture_Button_Unit_HMGteam_MAG_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_MAG_UK.png"))]),
        ("Texture_Button_Unit_HMGteam_MAG_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_MAG_para_BEL.png"))]),
        ("Texture_Button_Unit_HMGteam_MAG_para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_MAG_para_UK.png"))]),
        ("Texture_Button_Unit_HMGteam_MG3_FJ_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_MG3_FJ_RFA.png"))]),
        ("Texture_Button_Unit_HMGteam_MG3_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_MG3_RFA.png"))]),
        ("Texture_Button_Unit_HMGteam_Maxim_Reserve_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_Maxim_Reserve_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_Mk19_AB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_Mk19_AB_US.png"))]),
        ("Texture_Button_Unit_HMGteam_Mk19_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HMGteam_Mk19_US.png"))]),
        ("Texture_Button_Unit_HMGteam_NSV_6U6_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_NSV_6U6_VDV_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_NSV_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_NSV_DDR.png"))]),
        ("Texture_Button_Unit_HMGteam_NSV_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_NSV_DShV_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_NSV_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_NSV_POL.png"))]),
        ("Texture_Button_Unit_HMGteam_NSV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_NSV_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_NSV_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_NSV_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_NSV_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_NSV_VDV_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_DDR.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_DShV_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_FJ_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_FJ_DDR.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_Naval_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_Naval_POL.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_POL.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_VDV_SOV.png"))]),
        ("Texture_Button_Unit_HMGteam_PKM_para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HMGteam_PKM_para_POL.png"))]),
        ("Texture_Button_Unit_HS30_Panzermorser_120mm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HS30_Panzermorser_120mm_RFA.png"))]),
        ("Texture_Button_Unit_HeimatschutzJager_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HeimatschutzJager_RFA.png"))]),
        ("Texture_Button_Unit_Hibneryt_KG_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Hibneryt_KG_POL.png"))]),
        ("Texture_Button_Unit_Hibneryt_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Hibneryt_POL.png"))]),
        ("Texture_Button_Unit_Honker_4011_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Honker_4011_POL.png"))]),
        ("Texture_Button_Unit_Honker_RYS_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Honker_RYS_POL.png"))]),
        ("Texture_Button_Unit_Howz_2A36_Giatsint_B_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_2A36_Giatsint_B_SOV.png"))]),
        ("Texture_Button_Unit_Howz_A19_122mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_A19_122mm_POL.png"))]),
        ("Texture_Button_Unit_Howz_B4M_203mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_B4M_203mm_SOV.png"))]),
        ("Texture_Button_Unit_Howz_BS3_100mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_BS3_100mm_SOV.png"))]),
        ("Texture_Button_Unit_Howz_Br5M_280mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_Br5M_280mm_SOV.png"))]),
        ("Texture_Button_Unit_Howz_D1_152mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Howz_D1_152mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_D1_152mm_SOV.png"))]),
        ("Texture_Button_Unit_Howz_D20_152mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_D20_152mm_DDR.png"))]),
        ("Texture_Button_Unit_Howz_D20_152mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_D20_152mm_SOV.png"))]),
        ("Texture_Button_Unit_Howz_D30_122mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_D30_122mm_DDR.png"))]),
        ("Texture_Button_Unit_Howz_D30_122mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_D30_122mm_SOV.png"))]),
        ("Texture_Button_Unit_Howz_D30_122mm_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_D30_122mm_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Howz_L118_105mm_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_L118_105mm_LUX.png"))]),
        ("Texture_Button_Unit_Howz_L118_105mm_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_L118_105mm_UK.png"))]),
        ("Texture_Button_Unit_Howz_M101_105mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M101_105mm_FR.png"))]),
        ("Texture_Button_Unit_Howz_M101_105mm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M101_105mm_RFA.png"))]),
        ("Texture_Button_Unit_Howz_M101_105mm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M101_105mm_US.png"))]),
        ("Texture_Button_Unit_Howz_M101_105mm_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M101_105mm_para_BEL.png"))]),
        ("Texture_Button_Unit_Howz_M102_105mm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M102_105mm_US.png"))]),
        ("Texture_Button_Unit_Howz_M114_155mm_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M114_155mm_NL.png"))]),
        ("Texture_Button_Unit_Howz_M114_39_155mm_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M114_39_155mm_NL.png"))]),
        ("Texture_Button_Unit_Howz_M119_105mm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M119_105mm_US.png"))]),
        ("Texture_Button_Unit_Howz_M198_155mm_Copperhead_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M198_155mm_Copperhead_US.png"))]),
        ("Texture_Button_Unit_Howz_M198_155mm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Howz_M198_155mm_US.png"))]),
        ("Texture_Button_Unit_Howz_M30_122mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_M30_122mm_DDR.png"))]),
        ("Texture_Button_Unit_Howz_M30_122mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_M30_122mm_POL.png"))]),
        ("Texture_Button_Unit_Howz_M46_130mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_M46_130mm_DDR.png"))]),
        ("Texture_Button_Unit_Howz_M46_130mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Howz_ML20_152mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_ML20_152mm_POL.png"))]),
        ("Texture_Button_Unit_Howz_MstaB_150mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_MstaB_150mm_SOV.png"))]),
        ("Texture_Button_Unit_Howz_ZiS3_76mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Howz_ZiS3_76mm_DDR.png"))]),
        ("Texture_Button_Unit_HvyScout_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HvyScout_DDR.png"))]),
        ("Texture_Button_Unit_HvyScout_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HvyScout_DShV_SOV.png"))]),
        ("Texture_Button_Unit_HvyScout_NG_Alaska_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HvyScout_NG_Alaska_US.png"))]),
        ("Texture_Button_Unit_HvyScout_NG_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HvyScout_NG_Dragon_US.png"))]),
        ("Texture_Button_Unit_HvyScout_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/HvyScout_NG_US.png"))]),
        ("Texture_Button_Unit_HvyScout_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HvyScout_POL.png"))]),
        ("Texture_Button_Unit_HvyScout_Reserve_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HvyScout_Reserve_SOV.png"))]),
        ("Texture_Button_Unit_HvyScout_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HvyScout_SOV.png"))]),
        ("Texture_Button_Unit_HvyScout_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/HvyScout_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_IS2M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/IS2M_SOV.png"))]),
        ("Texture_Button_Unit_Iltis_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_CMD_BEL.png"))]),
        ("Texture_Button_Unit_Iltis_HMG_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_HMG_BEL.png"))]),
        ("Texture_Button_Unit_Iltis_MILAN_2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_Iltis_MILAN_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_MILAN_BEL.png"))]),
        ("Texture_Button_Unit_Iltis_MILAN_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_MILAN_RFA.png"))]),
        ("Texture_Button_Unit_Iltis_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_RFA.png"))]),
        ("Texture_Button_Unit_Iltis_para_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_para_CMD_BEL.png"))]),
        ("Texture_Button_Unit_Iltis_para_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_para_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Iltis_trans_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_trans_BEL.png"))]),
        ("Texture_Button_Unit_Iltis_trans_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Iltis_trans_RFA.png"))]),
        ("Texture_Button_Unit_Jager_Aufk_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jager_Aufk_RFA.png"))]),
        ("Texture_Button_Unit_Jager_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jager_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Jager_Carl_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jager_Carl_RFA.png"))]),
        ("Texture_Button_Unit_Jager_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jager_RFA.png"))]),
        ("Texture_Button_Unit_Jager_noAT_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jager_noAT_RFA.png"))]),
        ("Texture_Button_Unit_Jaguar_1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_1_RFA.png"))]),
        ("Texture_Button_Unit_Jaguar_2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_2_RFA.png"))]),
        ("Texture_Button_Unit_KSK18_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/KSK18_DDR.png"))]),
        ("Texture_Button_Unit_KanJagdPanzer_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/KanJagdPanzer_BEL.png"))]),
        ("Texture_Button_Unit_KanJagdPanzer_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/KanJagdPanzer_RFA.png"))]),
        ("Texture_Button_Unit_KdA_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/KdA_CMD_DDR.png"))]),
        ("Texture_Button_Unit_KdA_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/KdA_DDR.png"))]),
        ("Texture_Button_Unit_KrAZ_255B_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/KrAZ_255B_POL.png"))]),
        ("Texture_Button_Unit_KrAZ_255B_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/KrAZ_255B_SOV.png"))]),
        ("Texture_Button_Unit_KrAZ_255B_supply_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/KrAZ_255B_supply_DDR.png"))]),
        ("Texture_Button_Unit_KrAZ_255B_supply_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/KrAZ_255B_supply_POL.png"))]),
        ("Texture_Button_Unit_KrAZ_255B_supply_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/KrAZ_255B_supply_SOV.png"))]),
        ("Texture_Button_Unit_LAV_25_M1047_US_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LAV_25_M1047_US_US.png"))]),
        ("Texture_Button_Unit_LO_1800_FASTA_4_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LO_1800_FASTA_4_DDR.png"))]),
        ("Texture_Button_Unit_LO_1800_ZPU_2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_LRRP_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LRRP_Aero_US.png"))]),
        ("Texture_Button_Unit_LRRP_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LRRP_BEL.png"))]),
        ("Texture_Button_Unit_LRRP_CEWI_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LRRP_CEWI_US.png"))]),
        ("Texture_Button_Unit_LRRP_FOLT_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LRRP_FOLT_US.png"))]),
        ("Texture_Button_Unit_LRRP_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LRRP_FR.png"))]),
        ("Texture_Button_Unit_LRRP_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LRRP_NL.png"))]),
        ("Texture_Button_Unit_LRRP_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LRRP_UK.png"))]),
        ("Texture_Button_Unit_LRRP_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LRRP_US.png"))]),
        ("Texture_Button_Unit_LSV_M2HB_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LSV_M2HB_UK.png"))]),
        ("Texture_Button_Unit_LSV_MILAN_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LSV_MILAN_UK.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_AGL_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_AGL_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_AGL_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_AGL_VDV_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_CMD_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_CMD_VDV_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_FAO_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_FAO_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_Fagot_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_Fagot_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_Fagot_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_Fagot_VDV_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_SPG9_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_SPG9_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_SPG9_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_SPG9_VDV_SOV.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_LUAZ_967M_supply_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LUAZ_967M_supply_SOV.png"))]),
        ("Texture_Button_Unit_LandRover_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_CMD_NL.png"))]),
        ("Texture_Button_Unit_LandRover_CMD_Para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_CMD_Para_UK.png"))]),
        ("Texture_Button_Unit_LandRover_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_CMD_UK.png"))]),
        ("Texture_Button_Unit_LandRover_MILAN_Para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_MILAN_Para_UK.png"))]),
        ("Texture_Button_Unit_LandRover_MILAN_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_MILAN_UK.png"))]),
        ("Texture_Button_Unit_LandRover_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_NL.png"))]),
        ("Texture_Button_Unit_LandRover_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_UK.png"))]),
        ("Texture_Button_Unit_LandRover_WOMBAT_Gurkhas_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_WOMBAT_Gurkhas_UK.png"))]),
        ("Texture_Button_Unit_LandRover_WOMBAT_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LandRover_WOMBAT_UK.png"))]),
        ("Texture_Button_Unit_Lars_2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Lars_2_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_1A1A1_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A1A1_CMD_NL.png"))]),
        ("Texture_Button_Unit_Leopard_1A1A1_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A1A1_NL.png"))]),
        ("Texture_Button_Unit_Leopard_1A1_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A1_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_1A1_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A1_NL.png"))]),
        ("Texture_Button_Unit_Leopard_1A1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A1_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_1A5_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A5_BEL.png"))]),
        ("Texture_Button_Unit_Leopard_1A5_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A5_CMD_BEL.png"))]),
        ("Texture_Button_Unit_Leopard_1A5_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A5_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_1A5_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1A5_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_1BE_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1BE_BEL.png"))]),
        ("Texture_Button_Unit_Leopard_1BE_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_1BE_CMD_BEL.png"))]),
        ("Texture_Button_Unit_Leopard_2A1_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_2A1_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_2A1_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_Leopard_2A1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_2A1_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_2A3_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_2A3_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_2A3_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_2A3_RFA.png"))]),
        ("Texture_Button_Unit_Leopard_2A4B_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_2A4B_CMD_NL.png"))]),
        ("Texture_Button_Unit_Leopard_2A4B_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_2A4B_NL.png"))]),
        ("Texture_Button_Unit_Leopard_2A4_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_Leopard_2A4_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_2A4_NL.png"))]),
        ("Texture_Button_Unit_Leopard_2A4_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Leopard_2A4_RFA.png"))]),
        ("Texture_Button_Unit_LightRifles_AT4_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LightRifles_RCL_US.png"))]),
        ("Texture_Button_Unit_LightRifles_CMD_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LightRifles_CMD_LUX.png"))]),
        ("Texture_Button_Unit_LightRifles_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_CMD_US.png"))]),
        ("Texture_Button_Unit_LightRifles_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LightRifles_Dragon_US.png"))]),
        ("Texture_Button_Unit_LightRifles_LAW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LightRifles_RCL_US.png"))]),
        ("Texture_Button_Unit_LightRifles_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LightRifles_LUX.png"))]),
        ("Texture_Button_Unit_LightRifles_RCL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LightRifles_RCL_US.png"))]),
        ("Texture_Button_Unit_LightRifles_Viper_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LightRifles_Viper_US.png"))]),
        ("Texture_Button_Unit_LuAZ_967M_AA_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/LuAZ_967M_AA_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Luchs_A1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Luchs_A1_RFA.png"))]),
        ("Texture_Button_Unit_Luftsturmjager_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Luftsturmjager_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Luftsturmjager_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Luftsturmjager_DDR.png"))]),
        ("Texture_Button_Unit_Luftsturmjager_Metis_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Luftsturmjager_Metis_DDR.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_AGL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_AGL_airborne_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_AGL_nonPara_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_AGL_nonPara_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_CMD_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_CMD_LUX.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_CMD_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_CMD_para_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_CMD_para_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_GVLLD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_GVLLD_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_HMG_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_HMG_LUX.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_MP_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_MP_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_TOW_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_TOW_LUX.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_TOW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_TOW_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_TOW_para_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_TOW_para_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_scout_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_scout_US.png"))]),
        ("Texture_Button_Unit_M1025_Humvee_scout_tuto_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_scout_US.png"))]),
        ("Texture_Button_Unit_M1038_Humvee_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1038_Humvee_LUX.png"))]),
        ("Texture_Button_Unit_M1038_Humvee_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1038_Humvee_US.png"))]),
        ("Texture_Button_Unit_M106A2_HOWZ_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M106A2_HOWZ_US.png"))]),
        ("Texture_Button_Unit_M106A2_Howz_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M106A2_Howz_NG_US.png"))]),
        ("Texture_Button_Unit_M106A2_Mortar_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M106A2_Mortar_NL.png"))]),
        ("Texture_Button_Unit_M107A2_175mm_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M107A2_175mm_UK.png"))]),
        ("Texture_Button_Unit_M109A2_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M109A2_BEL.png"))]),
        ("Texture_Button_Unit_M109A2_HOWZ_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M109A2_HOWZ_US.png"))]),
        ("Texture_Button_Unit_M109A2_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M109A2_NG_US.png"))]),
        ("Texture_Button_Unit_M109A2_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M109A2_NL.png"))]),
        ("Texture_Button_Unit_M109A2_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M109A2_UK.png"))]),
        ("Texture_Button_Unit_M109A3G_HOWZ_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M109A3G_HOWZ_RFA.png"))]),
        ("Texture_Button_Unit_M110A2_HOWZ_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M110A2_HOWZ_BEL.png"))]),
        ("Texture_Button_Unit_M110A2_HOWZ_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M110A2_HOWZ_NL.png"))]),
        ("Texture_Button_Unit_M110A2_HOWZ_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M110A2_HOWZ_US.png"))]),
        ("Texture_Button_Unit_M110A2_Howz_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M110A2_Howz_NG_US.png"))]),
        ("Texture_Button_Unit_M110A2_Howz_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M110A2_Howz_RFA.png"))]),
        ("Texture_Button_Unit_M110A2_Howz_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M110A2_Howz_UK.png"))]),
        ("Texture_Button_Unit_M113A1B_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1B_BEL.png"))]),
        ("Texture_Button_Unit_M113A1B_MILAN_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1B_MILAN_BEL.png"))]),
        ("Texture_Button_Unit_M113A1B_Radar_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1B_Radar_BEL.png"))]),
        ("Texture_Button_Unit_M113A1G_MILAN_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1G_MILAN_RFA.png"))]),
        ("Texture_Button_Unit_M113A1G_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1G_RFA.png"))]),
        ("Texture_Button_Unit_M113A1G_reco_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1G_reco_RFA.png"))]),
        ("Texture_Button_Unit_M113A1G_supply_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1G_supply_RFA.png"))]),
        ("Texture_Button_Unit_M113A1_ACAV_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1_ACAV_NG_US.png"))]),
        ("Texture_Button_Unit_M113A1_Dragon_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1_Dragon_NG_US.png"))]),
        ("Texture_Button_Unit_M113A1_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1_NG_US.png"))]),
        ("Texture_Button_Unit_M113A1_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1_NL.png"))]),
        ("Texture_Button_Unit_M113A1_TOW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1_TOW_US.png"))]),
        ("Texture_Button_Unit_M113A1_reco_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A1_reco_NL.png"))]),
        ("Texture_Button_Unit_M113A2_TOW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A2_TOW_US.png"))]),
        ("Texture_Button_Unit_M113A2_supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A2_supply_US.png"))]),
        ("Texture_Button_Unit_M113A3_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113A3_US.png"))]),
        ("Texture_Button_Unit_M113_ACAV_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113_ACAV_NG_US.png"))]),
        ("Texture_Button_Unit_M113_ACAV_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113_ACAV_US.png"))]),
        ("Texture_Button_Unit_M113_CV_25mm_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113_CV_25mm_NL.png"))]),
        ("Texture_Button_Unit_M113_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113_Dragon_US.png"))]),
        ("Texture_Button_Unit_M113_GreenArcher_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113_GreenArcher_NL.png"))]),
        ("Texture_Button_Unit_M113_GreenArcher_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113_GreenArcher_RFA.png"))]),
        ("Texture_Button_Unit_M113_GreenArcher_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113_GreenArcher_UK.png"))]),
        ("Texture_Button_Unit_M113_PzMorser_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M113_PzMorser_RFA.png"))]),
        ("Texture_Button_Unit_M125_HOWZ_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M125_HOWZ_NG_US.png"))]),
        ("Texture_Button_Unit_M125_HOWZ_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M125_HOWZ_US.png"))]),
        ("Texture_Button_Unit_M151A2_TOW_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M151A2_TOW_NG_US.png"))]),
        ("Texture_Button_Unit_M151A2_scout_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M151A2_scout_US.png"))]),
        ("Texture_Button_Unit_M151C_RCL_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M151C_RCL_NG_US.png"))]),
        ("Texture_Button_Unit_M151_MUTT_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M151_MUTT_CMD_US.png"))]),
        ("Texture_Button_Unit_M151_MUTT_trans_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/M151_MUTT_trans_DDR.png"))]),
        ("Texture_Button_Unit_M151_MUTT_trans_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M151_MUTT_trans_US.png"))]),
        ("Texture_Button_Unit_M163_CS_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M163_CS_US.png"))]),
        ("Texture_Button_Unit_M163_PIVADS_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M163_PIVADS_US.png"))]),
        ("Texture_Button_Unit_M1A1HA_Abrams_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1A1HA_Abrams_CMD_US.png"))]),
        ("Texture_Button_Unit_M1A1HA_Abrams_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1A1HA_Abrams_US.png"))]),
        ("Texture_Button_Unit_M1A1_Abrams_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1A1_Abrams_CMD_US.png"))]),
        ("Texture_Button_Unit_M1A1_Abrams_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1A1_Abrams_US.png"))]),
        ("Texture_Button_Unit_M1A1_Abrams_reco_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1A1_Abrams_reco_US.png"))]),
        ("Texture_Button_Unit_M1IP_Abrams_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1IP_Abrams_CMD_US.png"))]),
        ("Texture_Button_Unit_M1IP_Abrams_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1IP_Abrams_US.png"))]),
        ("Texture_Button_Unit_M1_Abrams_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1_Abrams_CMD_US.png"))]),
        ("Texture_Button_Unit_M1_Abrams_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1_Abrams_NG_US.png"))]),
        ("Texture_Button_Unit_M1_Abrams_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1_Abrams_US.png"))]),
        ("Texture_Button_Unit_M201_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M201_CMD_FR.png"))]),
        ("Texture_Button_Unit_M201_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M201_FR.png"))]),
        ("Texture_Button_Unit_M201_MG_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M201_MG_FR.png"))]),
        ("Texture_Button_Unit_M201_MILAN_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M201_MILAN_FR.png"))]),
        ("Texture_Button_Unit_M270_MLRS_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M270_MLRS_RFA.png"))]),
        ("Texture_Button_Unit_M270_MLRS_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M270_MLRS_US.png"))]),
        ("Texture_Button_Unit_M270_MLRS_cluster_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M270_MLRS_cluster_NL.png"))]),
        ("Texture_Button_Unit_M270_MLRS_cluster_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M270_MLRS_cluster_UK.png"))]),
        ("Texture_Button_Unit_M270_MLRS_cluster_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M270_MLRS_cluster_US.png"))]),
        ("Texture_Button_Unit_M274_Mule_ITOW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M274_Mule_ITOW_US.png"))]),
        ("Texture_Button_Unit_M274_Mule_M2HB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M274_Mule_M2HB_US.png"))]),
        ("Texture_Button_Unit_M274_Mule_RCL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M274_Mule_RCL_US.png"))]),
        ("Texture_Button_Unit_M274_Mule_supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M274_Mule_supply_US.png"))]),
        ("Texture_Button_Unit_M2A1_Bradley_IFV_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M2A1_Bradley_IFV_US.png"))]),
        ("Texture_Button_Unit_M2A1_Bradley_Leader_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M2A1_Bradley_Leader_US.png"))]),
        ("Texture_Button_Unit_M2A2_Bradley_IFV_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M2A2_Bradley_IFV_US.png"))]),
        ("Texture_Button_Unit_M2A2_Bradley_Leader_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M2A2_Bradley_Leader_US.png"))]),
        ("Texture_Button_Unit_M2_Bradley_IFV_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M2_Bradley_IFV_NG_US.png"))]),
        ("Texture_Button_Unit_M35_supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M35_supply_US.png"))]),
        ("Texture_Button_Unit_M35_trans_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/M35_trans_DDR.png"))]),
        ("Texture_Button_Unit_M35_trans_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M35_trans_US.png"))]),
        ("Texture_Button_Unit_M35_trans_tuto_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M35_trans_US.png"))]),
        ("Texture_Button_Unit_M38A1_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M38A1_CMD_NL.png"))]),
        ("Texture_Button_Unit_M38A1_MG_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M38A1_MG_NL.png"))]),
        ("Texture_Button_Unit_M38A1_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M38A1_NL.png"))]),
        ("Texture_Button_Unit_M38A1_RCL_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M38A1_RCL_NL.png"))]),
        ("Texture_Button_Unit_M38A1_TOW_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M38A1_TOW_NL.png"))]),
        ("Texture_Button_Unit_M3A1_Bradley_CFV_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M3A1_Bradley_CFV_US.png"))]),
        ("Texture_Button_Unit_M3A2_Bradley_CFV_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M3A2_Bradley_CFV_US.png"))]),
        ("Texture_Button_Unit_M42_Duster_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M42_Duster_US.png"))]),
        ("Texture_Button_Unit_M48A2C_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M48A2C_RFA.png"))]),
        ("Texture_Button_Unit_M48A2GA2_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M48A2GA2_CMD_RFA.png"))]),
        ("Texture_Button_Unit_M48A2GA2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M48A2GA2_RFA.png"))]),
        ("Texture_Button_Unit_M48A5_reco_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M48A5_reco_NG_US.png"))]),
        ("Texture_Button_Unit_M48_Chaparral_MIM72F_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M48_Chaparral_MIM72F_US.png"))]),
        ("Texture_Button_Unit_M548A2_supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M548A2_supply_US.png"))]),
        ("Texture_Button_Unit_M551A1_ACAV_Sheridan_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M551A1_ACAV_Sheridan_US.png"))]),
        ("Texture_Button_Unit_M551A1_TTS_Sheridan_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M551A1_TTS_Sheridan_CMD_US.png"))]),
        ("Texture_Button_Unit_M551A1_TTS_Sheridan_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M551A1_TTS_Sheridan_US.png"))]),
        ("Texture_Button_Unit_M577_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M577_NL.png"))]),
        ("Texture_Button_Unit_M577_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M577_RFA.png"))]),
        ("Texture_Button_Unit_M577_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M577_US.png"))]),
        ("Texture_Button_Unit_M60A1_AVLM_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M60A1_AVLM_US.png"))]),
        ("Texture_Button_Unit_M60A1_RISE_Passive_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M60A1_RISE_Passive_CMD_US.png"))]),
        ("Texture_Button_Unit_M60A1_RISE_Passive_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M60A1_RISE_Passive_US.png"))]),
        ("Texture_Button_Unit_M60A1_RISE_Passive_reco_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M60A1_RISE_Passive_reco_US.png"))]),
        ("Texture_Button_Unit_M60A3_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M60A3_CMD_US.png"))]),
        ("Texture_Button_Unit_M60A3_ERA_Patton_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M60A3_ERA_Patton_US.png"))]),
        ("Texture_Button_Unit_M60A3_Patton_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M60A3_Patton_NG_US.png"))]),
        ("Texture_Button_Unit_M60A3_Patton_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M60A3_Patton_US.png"))]),
        ("Texture_Button_Unit_M728_CEV_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M728_CEV_NG_US.png"))]),
        ("Texture_Button_Unit_M728_CEV_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M728_CEV_US.png"))]),
        ("Texture_Button_Unit_M812_supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M812_supply_US.png"))]),
        ("Texture_Button_Unit_M901A1_ITW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M901A1_ITW_US.png"))]),
        ("Texture_Button_Unit_M901_TOW_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M901_TOW_NG_US.png"))]),
        ("Texture_Button_Unit_M901_TOW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M901_TOW_US.png"))]),
        ("Texture_Button_Unit_M981_FISTV_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M981_FISTV_US.png"))]),
        ("Texture_Button_Unit_M998_Avenger_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M998_Avenger_US.png"))]),
        ("Texture_Button_Unit_M998_Avenger_nonPara_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M998_Avenger_nonPara_US.png"))]),
        ("Texture_Button_Unit_M998_Humvee_AGL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M998_Humvee_AGL_US.png"))]),
        ("Texture_Button_Unit_M998_Humvee_Delta_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M998_Humvee_Delta_US.png"))]),
        ("Texture_Button_Unit_M998_Humvee_HMG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M998_Humvee_HMG_US.png"))]),
        ("Texture_Button_Unit_M998_Humvee_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M998_Humvee_LUX.png"))]),
        ("Texture_Button_Unit_M998_Humvee_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M998_Humvee_US.png"))]),
        ("Texture_Button_Unit_MANPAD_Blowpipe_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Blowpipe_UK.png"))]),
        ("Texture_Button_Unit_MANPAD_Igla_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Igla_DDR.png"))]),
        ("Texture_Button_Unit_MANPAD_Igla_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Igla_DShV_SOV.png"))]),
        ("Texture_Button_Unit_MANPAD_Igla_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Igla_SOV.png"))]),
        ("Texture_Button_Unit_MANPAD_Igla_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Igla_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_MANPAD_Igla_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Igla_VDV_SOV.png"))]),
        ("Texture_Button_Unit_MANPAD_Javelin_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Javelin_UK.png"))]),
        ("Texture_Button_Unit_MANPAD_Javelin_para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Javelin_para_UK.png"))]),
        ("Texture_Button_Unit_MANPAD_Mistral_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Mistral_BEL.png"))]),
        ("Texture_Button_Unit_MANPAD_Mistral_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Mistral_FR.png"))]),
        ("Texture_Button_Unit_MANPAD_Mistral_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Mistral_para_BEL.png"))]),
        ("Texture_Button_Unit_MANPAD_Mistral_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Mistral_para_FR.png"))]),
        ("Texture_Button_Unit_MANPAD_Redeye_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Redeye_RFA.png"))]),
        ("Texture_Button_Unit_MANPAD_Redeye_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Redeye_US.png"))]),
        ("Texture_Button_Unit_MANPAD_Stinger_C_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Stinger_C_Aero_US.png"))]),
        ("Texture_Button_Unit_MANPAD_Stinger_C_Marine_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Stinger_C_Marine_NL.png"))]),
        ("Texture_Button_Unit_MANPAD_Stinger_C_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Stinger_C_NL.png"))]),
        ("Texture_Button_Unit_MANPAD_Stinger_C_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Stinger_C_US.png"))]),
        ("Texture_Button_Unit_MANPAD_Stinger_C_para_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Stinger_C_para_US.png"))]),
        ("Texture_Button_Unit_MANPAD_Stinger_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MANPAD_Stinger_NG_US.png"))]),
        ("Texture_Button_Unit_MANPAD_Strela_2M_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Strela_2M_DDR.png"))]),
        ("Texture_Button_Unit_MANPAD_Strela_2M_FJ_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Strela_2M_FJ_DDR.png"))]),
        ("Texture_Button_Unit_MANPAD_Strela_2M_Naval_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Strela_2M_Naval_POL.png"))]),
        ("Texture_Button_Unit_MANPAD_Strela_2M_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Strela_2M_POL.png"))]),
        ("Texture_Button_Unit_MANPAD_Strela_2M_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Strela_2M_Para_POL.png"))]),
        ("Texture_Button_Unit_MANPAD_Strela_3_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MANPAD_Strela_3_SOV.png"))]),
        ("Texture_Button_Unit_MAN_Kat_6x6_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MAN_Kat_6x6_RFA.png"))]),
        ("Texture_Button_Unit_MAN_Kat_6x6_trans_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MAN_Kat_6x6_trans_RFA.png"))]),
        ("Texture_Button_Unit_MAN_Z311_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MAN_Z311_BEL.png"))]),
        ("Texture_Button_Unit_MAN_Z311_Mi50_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MAN_Z311_Mi50_BEL.png"))]),
        ("Texture_Button_Unit_MCV_80_Warrior_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MCV_80_Warrior_CMD_UK.png"))]),
        ("Texture_Button_Unit_MCV_80_Warrior_MILAN_ERA_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MCV_80_Warrior_MILAN_ERA_UK.png"))]),
        ("Texture_Button_Unit_MCV_80_Warrior_MILAN_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MCV_80_Warrior_MILAN_UK.png"))]),
        ("Texture_Button_Unit_MCV_80_Warrior_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MCV_80_Warrior_UK.png"))]),
        ("Texture_Button_Unit_MFRW_RM70_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MFRW_RM70_DDR.png"))]),
        ("Texture_Button_Unit_MFRW_RM70_cluster_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MFRW_RM70_DDR.png"))]),
        ("Texture_Button_Unit_MLRS_WP_8z_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MLRS_WP_8z_POL.png"))]),
        ("Texture_Button_Unit_MP_AT_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_AT_NL.png"))]),
        ("Texture_Button_Unit_MP_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_BEL.png"))]),
        ("Texture_Button_Unit_MP_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_CMD_NL.png"))]),
        ("Texture_Button_Unit_MP_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_CMD_US.png"))]),
        ("Texture_Button_Unit_MP_Combat_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MP_Combat_SOV.png"))]),
        ("Texture_Button_Unit_MP_Combat_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_Combat_US.png"))]),
        ("Texture_Button_Unit_MP_Combat_USAF_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_Combat_USAF_US.png"))]),
        ("Texture_Button_Unit_MP_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MP_DDR.png"))]),
        ("Texture_Button_Unit_MP_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_NL.png"))]),
        ("Texture_Button_Unit_MP_Patrol_USAF_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_Patrol_USAF_US.png"))]),
        ("Texture_Button_Unit_MP_RCL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_RCL_US.png"))]),
        ("Texture_Button_Unit_MP_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MP_SOV.png"))]),
        ("Texture_Button_Unit_MP_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MP_US.png"))]),
        ("Texture_Button_Unit_MTLB_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_CMD_DDR.png"))]),
        ("Texture_Button_Unit_MTLB_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_CMD_SOV.png"))]),
        ("Texture_Button_Unit_MTLB_Shturm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_Shturm_DDR.png"))]),
        ("Texture_Button_Unit_MTLB_Shturm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_Shturm_SOV.png"))]),
        ("Texture_Button_Unit_MTLB_Strela10M3_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_Strela10M3_SOV.png"))]),
        ("Texture_Button_Unit_MTLB_Strela10_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_Strela10_DDR.png"))]),
        ("Texture_Button_Unit_MTLB_Strela10_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_Strela10_POL.png"))]),
        ("Texture_Button_Unit_MTLB_Strela10_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_Strela10_SOV.png"))]),
        ("Texture_Button_Unit_MTLB_TRI_Hors_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_TRI_Hors_POL.png"))]),
        ("Texture_Button_Unit_MTLB_Vasilek_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_Vasilek_SOV.png"))]),
        ("Texture_Button_Unit_MTLB_supply_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_supply_DDR.png"))]),
        ("Texture_Button_Unit_MTLB_supply_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_supply_SOV.png"))]),
        ("Texture_Button_Unit_MTLB_trans_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_trans_DDR.png"))]),
        ("Texture_Button_Unit_MTLB_trans_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_trans_POL.png"))]),
        ("Texture_Button_Unit_MTLB_transp_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MTLB_transp_SOV.png"))]),
        ("Texture_Button_Unit_Marder_1A1_MILAN_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_Marder_1A1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_Marder_1A2_MILAN_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marder_1A2_MILAN_RFA.png"))]),
        ("Texture_Button_Unit_Marder_1A2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marder_1A2_RFA.png"))]),
        ("Texture_Button_Unit_Marder_1A3_MILAN_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marder_1A3_MILAN_RFA.png"))]),
        ("Texture_Button_Unit_Marder_1A3_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marder_1A3_RFA.png"))]),
        ("Texture_Button_Unit_Marder_Roland_2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marder_Roland_2_RFA.png"))]),
        ("Texture_Button_Unit_Marder_Roland_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marder_Roland_RFA.png"))]),
        ("Texture_Button_Unit_Marine_Scout_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marine_Scout_NL.png"))]),
        ("Texture_Button_Unit_Marines_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marines_CMD_NL.png"))]),
        ("Texture_Button_Unit_Marines_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Marines_NL.png"))]),
        ("Texture_Button_Unit_Mech_Rifles_AT_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mech_Rifles_AT_BEL.png"))]),
        ("Texture_Button_Unit_Mech_Rifles_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mech_Rifles_CMD_BEL.png"))]),
        ("Texture_Button_Unit_Mech_Rifles_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mech_Rifles_CMD_NL.png"))]),
        ("Texture_Button_Unit_Mech_Rifles_Carl_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mech_Rifles_Carl_NL.png"))]),
        ("Texture_Button_Unit_Mech_Rifles_Dragon_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mech_Rifles_Dragon_NL.png"))]),
        ("Texture_Button_Unit_Mech_Rifles_M72_LAW_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mech_Rifles_M72_LAW_NL.png"))]),
        ("Texture_Button_Unit_Mech_Rifles_MG_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mech_Rifles_MG_BEL.png"))]),
        ("Texture_Button_Unit_Mortier_107mm_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_107mm_Aero_US.png"))]),
        ("Texture_Button_Unit_Mortier_107mm_Airborne_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_107mm_Airborne_US.png"))]),
        ("Texture_Button_Unit_Mortier_107mm_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_107mm_BEL.png"))]),
        ("Texture_Button_Unit_Mortier_107mm_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_107mm_NG_US.png"))]),
        ("Texture_Button_Unit_Mortier_107mm_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_107mm_NL.png"))]),
        ("Texture_Button_Unit_Mortier_107mm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_107mm_US.png"))]),
        ("Texture_Button_Unit_Mortier_107mm_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_107mm_para_BEL.png"))]),
        ("Texture_Button_Unit_Mortier_240mm_M240_Cluster_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_240mm_M240_Cluster_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_240mm_M240_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_240mm_M240_POL.png"))]),
        ("Texture_Button_Unit_Mortier_240mm_M240_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_240mm_M240_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2B14_82mm_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2B14_82mm_DShV_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2B14_82mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2B14_82mm_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2B14_82mm_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2B14_82mm_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2B9_Vasilek_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2B9_Vasilek_Para_POL.png"))]),
        ("Texture_Button_Unit_Mortier_2B9_Vasilek_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2B9_Vasilek_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2B9_Vasilek_nonPara_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2B9_Vasilek_nonPara_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2S12_120mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2S12_120mm_DDR.png"))]),
        ("Texture_Button_Unit_Mortier_2S12_120mm_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2S12_120mm_DShV_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2S12_120mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2S12_120mm_POL.png"))]),
        ("Texture_Button_Unit_Mortier_2S12_120mm_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2S12_120mm_Para_POL.png"))]),
        ("Texture_Button_Unit_Mortier_2S12_120mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2S12_120mm_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2S12_120mm_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2S12_120mm_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_2S12_120mm_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_2S12_120mm_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_81mm_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_81mm_BEL.png"))]),
        ("Texture_Button_Unit_Mortier_81mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_81mm_FR.png"))]),
        ("Texture_Button_Unit_Mortier_81mm_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_81mm_LUX.png"))]),
        ("Texture_Button_Unit_Mortier_81mm_para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_81mm_para_BEL.png"))]),
        ("Texture_Button_Unit_Mortier_M29_81mm_Marines_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_M29_81mm_Marines_NL.png"))]),
        ("Texture_Button_Unit_Mortier_M29_81mm_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_M29_81mm_NL.png"))]),
        ("Texture_Button_Unit_Mortier_M29_81mm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_M29_81mm_US.png"))]),
        ("Texture_Button_Unit_Mortier_M43_160mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_M43_160mm_POL.png"))]),
        ("Texture_Button_Unit_Mortier_M43_82mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_M43_82mm_DDR.png"))]),
        ("Texture_Button_Unit_Mortier_M43_82mm_FJ_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_M43_82mm_FJ_DDR.png"))]),
        ("Texture_Button_Unit_Mortier_M43_82mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_M43_82mm_POL.png"))]),
        ("Texture_Button_Unit_Mortier_M43_82mm_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_M43_82mm_Para_POL.png"))]),
        ("Texture_Button_Unit_Mortier_MORT61_120mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_MORT61_120mm_FR.png"))]),
        ("Texture_Button_Unit_Mortier_MORT61_120mm_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_MORT61_120mm_NL.png"))]),
        ("Texture_Button_Unit_Mortier_MORT61_120mm_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_MORT61_120mm_para_FR.png"))]),
        ("Texture_Button_Unit_Mortier_Nona_K_120mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_Nona_K_120mm_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_PM43_120mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_PM43_120mm_DDR.png"))]),
        ("Texture_Button_Unit_Mortier_PM43_120mm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_PM43_120mm_POL.png"))]),
        ("Texture_Button_Unit_Mortier_PM43_120mm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mortier_PM43_120mm_SOV.png"))]),
        ("Texture_Button_Unit_Mortier_Tampella_120mm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_Tampella_120mm_RFA.png"))]),
        ("Texture_Button_Unit_Mortier_Tampella_120mm_para_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mortier_Tampella_120mm_para_RFA.png"))]),
        ("Texture_Button_Unit_MotRifles_BTR_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_BTR_DDR.png"))]),
        ("Texture_Button_Unit_MotRifles_BTR_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_BTR_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_BTR_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_BTR_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_CMD_DDR.png"))]),
        ("Texture_Button_Unit_MotRifles_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_CMD_POL.png"))]),
        ("Texture_Button_Unit_MotRifles_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_CMD_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_CMD_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_CMD_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_DDR.png"))]),
        ("Texture_Button_Unit_MotRifles_HMG_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_HMG_DDR.png"))]),
        ("Texture_Button_Unit_MotRifles_HMG_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_HMG_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_HMG_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_HMG_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_Metis_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_Metis_DDR.png"))]),
        ("Texture_Button_Unit_MotRifles_Metis_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_Metis_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_Metis_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_Metis_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_POL.png"))]),
        ("Texture_Button_Unit_MotRifles_RPG22_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_RPG22_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_RPG27_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_RPG27_DDR.png"))]),
        ("Texture_Button_Unit_MotRifles_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_SOV.png"))]),
        ("Texture_Button_Unit_MotRifles_SVD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_SVD_DDR.png"))]),
        ("Texture_Button_Unit_MotRifles_SVD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_SVD_POL.png"))]),
        ("Texture_Button_Unit_MotRifles_Strela_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_Strela_DDR.png"))]),
        ("Texture_Button_Unit_MotRifles_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotRifles_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_MotSchutzen_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MotSchutzen_DDR.png"))]),
        ("Texture_Button_Unit_NatGuard_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/NatGuard_CMD_US.png"))]),
        ("Texture_Button_Unit_NatGuard_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/NatGuard_Dragon_US.png"))]),
        ("Texture_Button_Unit_NatGuard_Engineers_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/NatGuard_Engineers_CMD_US.png"))]),
        ("Texture_Button_Unit_NatGuard_Engineers_Flam_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/NatGuard_Engineers_Flam_US.png"))]),
        ("Texture_Button_Unit_NatGuard_Engineers_M67_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/NatGuard_Engineers_M67_US.png"))]),
        ("Texture_Button_Unit_NatGuard_Engineers_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/NatGuard_Engineers_US.png"))]),
        ("Texture_Button_Unit_NatGuard_LAW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/NatGuard_LAW_US.png"))]),
        ("Texture_Button_Unit_NatGuard_M67_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/NatGuard_M67_US.png"))]),
        ("Texture_Button_Unit_Naval_Engineers_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Naval_Engineers_CMD_POL.png"))]),
        ("Texture_Button_Unit_Naval_Engineers_Flam_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Naval_Engineers_Flam_POL.png"))]),
        ("Texture_Button_Unit_Naval_Engineers_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Naval_Engineers_POL.png"))]),
        ("Texture_Button_Unit_Naval_Rifle_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Naval_Rifle_CMD_POL.png"))]),
        ("Texture_Button_Unit_Naval_Rifle_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Naval_Rifle_POL.png"))]),
        ("Texture_Button_Unit_Navy_SEAL_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Navy_SEAL_US.png"))]),
        ("Texture_Button_Unit_OT_62_TOPAS_2AP_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_62_TOPAS_2AP_POL.png"))]),
        ("Texture_Button_Unit_OT_62_TOPAS_JOD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_62_TOPAS_JOD_POL.png"))]),
        ("Texture_Button_Unit_OT_62_TOPAS_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_62_TOPAS_POL.png"))]),
        ("Texture_Button_Unit_OT_62_TOPAS_R3M_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_62_TOPAS_R3M_CMD_POL.png"))]),
        ("Texture_Button_Unit_OT_62_TOPAS_SPG9_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_62_TOPAS_SPG9_POL.png"))]),
        ("Texture_Button_Unit_OT_64_SKOT_2AM_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_64_SKOT_2AM_POL.png"))]),
        ("Texture_Button_Unit_OT_64_SKOT_2A_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_64_SKOT_2A_POL.png"))]),
        ("Texture_Button_Unit_OT_64_SKOT_2P_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_64_SKOT_2P_POL.png"))]),
        ("Texture_Button_Unit_OT_64_SKOT_2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_64_SKOT_2_POL.png"))]),
        ("Texture_Button_Unit_OT_64_SKOT_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_64_SKOT_CMD_POL.png"))]),
        ("Texture_Button_Unit_OT_65_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_OT_65_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_65_DDR.png"))]),
        ("Texture_Button_Unit_OT_65_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/OT_65_POL.png"))]),
        ("Texture_Button_Unit_Obusier_155mm_mle1950_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Obusier_155mm_mle1950_FR.png"))]),
        ("Texture_Button_Unit_Osa_9K33M3_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Osa_9K33M3_DDR.png"))]),
        ("Texture_Button_Unit_Osa_9K33M3_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Osa_9K33M3_POL.png"))]),
        ("Texture_Button_Unit_Osa_9K33M3_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Osa_9K33M3_SOV.png"))]),
        ("Texture_Button_Unit_PSzH_IV_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PSzH_IV_DDR.png"))]),
        ("Texture_Button_Unit_PT76B_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_CMD_DDR.png"))]),
        ("Texture_Button_Unit_PT76B_CMD_Naval_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_CMD_Naval_POL.png"))]),
        ("Texture_Button_Unit_PT76B_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_CMD_POL.png"))]),
        ("Texture_Button_Unit_PT76B_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_DDR.png"))]),
        ("Texture_Button_Unit_PT76B_Naval_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_Naval_POL.png"))]),
        ("Texture_Button_Unit_PT76B_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_POL.png"))]),
        ("Texture_Button_Unit_PT76B_tank_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_tank_DDR.png"))]),
        ("Texture_Button_Unit_PT76B_tank_Naval_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_tank_Naval_POL.png"))]),
        ("Texture_Button_Unit_PT76B_tank_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PT76B_tank_POL.png"))]),
        ("Texture_Button_Unit_PTS_M_supply_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PTS_M_supply_DDR.png"))]),
        ("Texture_Button_Unit_PTS_M_supply_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/PTS_M_supply_POL.png"))]),
        ("Texture_Button_Unit_Panzergrenadier_APC_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Panzergrenadier_APC_RFA.png"))]),
        ("Texture_Button_Unit_Panzergrenadier_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Panzergrenadier_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Panzergrenadier_IFV_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Panzergrenadier_IFV_RFA.png"))]),
        ("Texture_Button_Unit_ParaCmdo_AT_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ParaCmdo_AT_BEL.png"))]),
        ("Texture_Button_Unit_ParaCmdo_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ParaCmdo_BEL.png"))]),
        ("Texture_Button_Unit_ParaCmdo_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ParaCmdo_CMD_BEL.png"))]),
        ("Texture_Button_Unit_ParaCmdo_Pionier_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ParaCmdo_Pionier_BEL.png"))]),
        ("Texture_Button_Unit_ParaCmdo_reserve_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/ParaCmdo_reserve_BEL.png"))]),
        ("Texture_Button_Unit_Para_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Para_CMD_FR.png"))]),
        ("Texture_Button_Unit_Para_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Para_CMD_POL.png"))]),
        ("Texture_Button_Unit_Para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Para_FR.png"))]),
        ("Texture_Button_Unit_Para_HMG_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Para_HMG_POL.png"))]),
        ("Texture_Button_Unit_Para_Legion_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Para_Legion_FR.png"))]),
        ("Texture_Button_Unit_Para_Marine_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Para_Marine_FR.png"))]),
        ("Texture_Button_Unit_Para_Metis_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Para_Metis_POL.png"))]),
        ("Texture_Button_Unit_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Para_POL.png"))]),
        ("Texture_Button_Unit_Para_Sapeurs_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Para_Sapeurs_CMD_FR.png"))]),
        ("Texture_Button_Unit_Para_Sapeurs_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Para_Sapeurs_FR.png"))]),
        ("Texture_Button_Unit_Para_Sapeurs_Flam_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Para_Sapeurs_Flam_FR.png"))]),
        ("Texture_Button_Unit_Para_Security_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Para_Security_POL.png"))]),
        ("Texture_Button_Unit_Paratroopers_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Paratroopers_CMD_UK.png"))]),
        ("Texture_Button_Unit_Paratroopers_Engineers_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Paratroopers_Engineers_CMD_UK.png"))]),
        ("Texture_Button_Unit_Paratroopers_Engineers_CarlG_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Paratroopers_Engineers_CarlG_UK.png"))]),
        ("Texture_Button_Unit_Paratroopers_Engineers_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Paratroopers_Engineers_UK.png"))]),
        ("Texture_Button_Unit_Paratroopers_MILAN_TA_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Paratroopers_MILAN_TA_UK.png"))]),
        ("Texture_Button_Unit_Paratroopers_TA_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Paratroopers_TA_UK.png"))]),
        ("Texture_Button_Unit_Paratroopers_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Paratroopers_UK.png"))]),
        ("Texture_Button_Unit_Partisan_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Partisan_SOV.png"))]),
        ("Texture_Button_Unit_Pathfinder_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Pathfinder_NG_US.png"))]),
        ("Texture_Button_Unit_Pathfinders_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Pathfinders_UK.png"))]),
        ("Texture_Button_Unit_Pioneer_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Pioneer_UK.png"))]),
        ("Texture_Button_Unit_PzGrenadier_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/PzGrenadier_RFA.png"))]),
        ("Texture_Button_Unit_RCL_L6_Wombat_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/RCL_L6_Wombat_UK.png"))]),
        ("Texture_Button_Unit_RM70_85_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/RM70_85_DDR.png"))]),
        ("Texture_Button_Unit_RM70_85_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/RM70_85_POL.png"))]),
        ("Texture_Button_Unit_RMP_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/RMP_UK.png"))]),
        ("Texture_Button_Unit_Ranger_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Ranger_US.png"))]),
        ("Texture_Button_Unit_Ranger_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Ranger_US.png"))]),
        ("Texture_Button_Unit_Ranger_tuto_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Ranger_US.png"))]),
        ("Texture_Button_Unit_Rangers_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rangers_CMD_US.png"))]),
        ("Texture_Button_Unit_Reserve_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Reserve_BEL.png"))]),
        ("Texture_Button_Unit_Reserve_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Reserve_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Reserve_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Reserve_CMD_POL.png"))]),
        ("Texture_Button_Unit_Reserve_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Reserve_CMD_SOV.png"))]),
        ("Texture_Button_Unit_Reserve_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Reserve_DDR.png"))]),
        ("Texture_Button_Unit_Reserve_HMG_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Reserve_HMG_DDR.png"))]),
        ("Texture_Button_Unit_Reserve_HMG_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Reserve_HMG_SOV.png"))]),
        ("Texture_Button_Unit_Reserve_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Reserve_NL.png"))]),
        ("Texture_Button_Unit_Reserve_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Reserve_POL.png"))]),
        ("Texture_Button_Unit_Reserve_Polizei_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Reserve_Polizei_RFA.png"))]),
        ("Texture_Button_Unit_Reserve_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Reserve_SOV.png"))]),
        ("Texture_Button_Unit_Reserviste_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Reserviste_CMD_FR.png"))]),
        ("Texture_Button_Unit_Reserviste_FAMAS_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Reserviste_FAMAS_FR.png"))]),
        ("Texture_Button_Unit_Reserviste_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Reserviste_FR.png"))]),
        ("Texture_Button_Unit_Rifles_APILAS_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_APILAS_FR.png"))]),
        ("Texture_Button_Unit_Rifles_AT_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_AT_BEL.png"))]),
        ("Texture_Button_Unit_Rifles_AT_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_AT_UK.png"))]),
        ("Texture_Button_Unit_Rifles_Aero_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_Aero_CMD_FR.png"))]),
        ("Texture_Button_Unit_Rifles_Aero_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_Aero_FR.png"))]),
        ("Texture_Button_Unit_Rifles_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_BEL.png"))]),
        ("Texture_Button_Unit_Rifles_Berlin_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_Berlin_UK.png"))]),
        ("Texture_Button_Unit_Rifles_CMD_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_CMD_BEL.png"))]),
        ("Texture_Button_Unit_Rifles_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_CMD_FR.png"))]),
        ("Texture_Button_Unit_Rifles_CMD_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_CMD_NL.png"))]),
        ("Texture_Button_Unit_Rifles_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Rifles_CMD_POL.png"))]),
        ("Texture_Button_Unit_Rifles_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_CMD_UK.png"))]),
        ("Texture_Button_Unit_Rifles_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_CMD_US.png"))]),
        ("Texture_Button_Unit_Rifles_Carl_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_Carl_NL.png"))]),
        ("Texture_Button_Unit_Rifles_Cavalry_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/LightRifles_RCL_US.png"))]),
        ("Texture_Button_Unit_Rifles_DMR_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_DMR_FR.png"))]),
        ("Texture_Button_Unit_Rifles_Dragon_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_Dragon_NL.png"))]),
        ("Texture_Button_Unit_Rifles_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_FR.png"))]),
        ("Texture_Button_Unit_Rifles_Gurkhas_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_Gurkhas_CMD_UK.png"))]),
        ("Texture_Button_Unit_Rifles_Gurkhas_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_Gurkhas_UK.png"))]),
        ("Texture_Button_Unit_Rifles_HMG_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Rifles_HMG_POL.png"))]),
        ("Texture_Button_Unit_Rifles_HMG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_HMG_US.png"))]),
        ("Texture_Button_Unit_Rifles_LAW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_LAW_US.png"))]),
        ("Texture_Button_Unit_Rifles_M72_LAW_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_M72_LAW_NL.png"))]),
        ("Texture_Button_Unit_Rifles_Mot_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_Mot_UK.png"))]),
        ("Texture_Button_Unit_Rifles_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Rifles_POL.png"))]),
        ("Texture_Button_Unit_Rifles_RAF_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_RAF_UK.png"))]),
        ("Texture_Button_Unit_Rifles_RIMa_APILAS_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_RIMa_APILAS_FR.png"))]),
        ("Texture_Button_Unit_Rifles_RIMa_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_RIMa_CMD_FR.png"))]),
        ("Texture_Button_Unit_Rifles_RIMa_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_RIMa_FR.png"))]),
        ("Texture_Button_Unit_Rifles_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_UK.png"))]),
        ("Texture_Button_Unit_Rifles_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_US.png"))]),
        ("Texture_Button_Unit_Rifles_half_AT4_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_half_Dragon_US.png"))]),
        ("Texture_Button_Unit_Rifles_half_CMD_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_half_CMD_NG_US.png"))]),
        ("Texture_Button_Unit_Rifles_half_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_CMD_US.png"))]),
        ("Texture_Button_Unit_Rifles_half_Dragon_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_half_Dragon_NG_US.png"))]),
        ("Texture_Button_Unit_Rifles_half_Dragon_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_half_Dragon_US.png"))]),
        ("Texture_Button_Unit_Rifles_half_LAW_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_half_LAW_NG_US.png"))]),
        ("Texture_Button_Unit_Rifles_half_LAW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rifles_half_LAW_US.png"))]),
        ("Texture_Button_Unit_Roland_2_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Roland_2_FR.png"))]),
        ("Texture_Button_Unit_Roland_3_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Roland_3_FR.png"))]),
        ("Texture_Button_Unit_Rover_101FC_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rover_101FC_LUX.png"))]),
        ("Texture_Button_Unit_Rover_101FC_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rover_101FC_UK.png"))]),
        ("Texture_Button_Unit_Rover_101FC_supply_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Rover_101FC_supply_UK.png"))]),
        ("Texture_Button_Unit_SAS_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/SAS_FR.png"))]),
        ("Texture_Button_Unit_SAS_Sniper_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/SAS_Sniper_FR.png"))]),
        ("Texture_Button_Unit_SAS_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/SAS_UK.png"))]),
        ("Texture_Button_Unit_SEK_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/SEK_RFA.png"))]),
        ("Texture_Button_Unit_SPW_152K_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/SPW_152K_DDR.png"))]),
        ("Texture_Button_Unit_Sapeurs_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sapeurs_CMD_FR.png"))]),
        ("Texture_Button_Unit_Sapeurs_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sapeurs_FR.png"))]),
        ("Texture_Button_Unit_Sapeurs_Flam_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sapeurs_Flam_FR.png"))]),
        ("Texture_Button_Unit_Saxon_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Saxon_CMD_UK.png"))]),
        ("Texture_Button_Unit_Saxon_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Saxon_UK.png"))]),
        ("Texture_Button_Unit_Scout_AT_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_AT_BEL.png"))]),
        ("Texture_Button_Unit_Scout_AT_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_AT_NL.png"))]),
        ("Texture_Button_Unit_Scout_AT_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_AT_UK.png"))]),
        ("Texture_Button_Unit_Scout_Aero_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_Aero_US.png"))]),
        ("Texture_Button_Unit_Scout_Airmobile_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_Airmobile_UK.png"))]),
        ("Texture_Button_Unit_Scout_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_BEL.png"))]),
        ("Texture_Button_Unit_Scout_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_DDR.png"))]),
        ("Texture_Button_Unit_Scout_DShV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_DShV_SOV.png"))]),
        ("Texture_Button_Unit_Scout_FJ_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_FJ_DDR.png"))]),
        ("Texture_Button_Unit_Scout_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_FR.png"))]),
        ("Texture_Button_Unit_Scout_KdA_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_KdA_DDR.png"))]),
        ("Texture_Button_Unit_Scout_LRRP_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_LRRP_DDR.png"))]),
        ("Texture_Button_Unit_Scout_LRRP_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_LRRP_POL.png"))]),
        ("Texture_Button_Unit_Scout_LRRP_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_LRRP_Para_POL.png"))]),
        ("Texture_Button_Unit_Scout_LRRP_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_LRRP_SOV.png"))]),
        ("Texture_Button_Unit_Scout_LUX", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_LUX.png"))]),
        ("Texture_Button_Unit_Scout_Motorized_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_Motorized_UK.png"))]),
        ("Texture_Button_Unit_Scout_NG_Alaska_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_NG_Alaska_US.png"))]),
        ("Texture_Button_Unit_Scout_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_NG_US.png"))]),
        ("Texture_Button_Unit_Scout_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_NL.png"))]),
        ("Texture_Button_Unit_Scout_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_POL.png"))]),
        ("Texture_Button_Unit_Scout_ParaCmdo_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_ParaCmdo_BEL.png"))]),
        ("Texture_Button_Unit_Scout_ParaCmdo_Mech_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_ParaCmdo_Mech_BEL.png"))]),
        ("Texture_Button_Unit_Scout_Para_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_Para_UK.png"))]),
        ("Texture_Button_Unit_Scout_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_RFA.png"))]),
        ("Texture_Button_Unit_Scout_RIMa_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_RIMa_FR.png"))]),
        ("Texture_Button_Unit_Scout_Reserve_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_Reserve_DDR.png"))]),
        ("Texture_Button_Unit_Scout_Reserviste_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_Reserviste_FR.png"))]),
        ("Texture_Button_Unit_Scout_SF_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_SF_POL.png"))]),
        ("Texture_Button_Unit_Scout_SIGINT_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_SIGINT_SOV.png"))]),
        ("Texture_Button_Unit_Scout_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_SOV.png"))]),
        ("Texture_Button_Unit_Scout_SpetsnazGRU_Stinger_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_SpetsnazGRU_Stinger_SOV.png"))]),
        ("Texture_Button_Unit_Scout_Spetsnaz_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_Spetsnaz_SOV.png"))]),
        ("Texture_Button_Unit_Scout_Spetsnaz_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_Spetsnaz_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Scout_TA_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_TA_UK.png"))]),
        ("Texture_Button_Unit_Scout_TTsko_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_TTsko_SOV.png"))]),
        ("Texture_Button_Unit_Scout_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_UK.png"))]),
        ("Texture_Button_Unit_Scout_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_US.png"))]),
        ("Texture_Button_Unit_Scout_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_VDV_SOV.png"))]),
        ("Texture_Button_Unit_Scout_Wach_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_Wach_DDR.png"))]),
        ("Texture_Button_Unit_Scout_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Scout_para_FR.png"))]),
        ("Texture_Button_Unit_Scout_para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Scout_para_POL.png"))]),
        ("Texture_Button_Unit_Security_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Security_DDR.png"))]),
        ("Texture_Button_Unit_Security_Mobile_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Security_Mobile_NL.png"))]),
        ("Texture_Button_Unit_Security_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Security_NL.png"))]),
        ("Texture_Button_Unit_Security_Naval_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Security_Naval_SOV.png"))]),
        ("Texture_Button_Unit_Security_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Security_RFA.png"))]),
        ("Texture_Button_Unit_Security_Reserve_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Security_Reserve_SOV.png"))]),
        ("Texture_Button_Unit_Security_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Security_SOV.png"))]),
        ("Texture_Button_Unit_Security_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Security_UK.png"))]),
        ("Texture_Button_Unit_Security_USMC_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Security_USMC_US.png"))]),
        ("Texture_Button_Unit_Sniper_ESR_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sniper_ESR_BEL.png"))]),
        ("Texture_Button_Unit_Sniper_FJ_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Sniper_FJ_DDR.png"))]),
        ("Texture_Button_Unit_Sniper_Fern_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sniper_Fern_RFA.png"))]),
        ("Texture_Button_Unit_Sniper_M82_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sniper_M82_US.png"))]),
        ("Texture_Button_Unit_Sniper_NG_Alaska_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sniper_NG_Alaska_US.png"))]),
        ("Texture_Button_Unit_Sniper_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Sniper_POL.png"))]),
        ("Texture_Button_Unit_Sniper_RIMa_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sniper_RIMa_FR.png"))]),
        ("Texture_Button_Unit_Sniper_Spetsnaz_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Sniper_Spetsnaz_SOV.png"))]),
        ("Texture_Button_Unit_Sniper_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sniper_UK.png"))]),
        ("Texture_Button_Unit_Sniper_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sniper_US.png"))]),
        ("Texture_Button_Unit_Sniper_paras_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Sniper_paras_POL.png"))]),
        ("Texture_Button_Unit_Sonderwagen_4_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sonderwagen_4_RFA.png"))]),
        ("Texture_Button_Unit_Sonderwagen_4_recon_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Sonderwagen_4_recon_RFA.png"))]),
        ("Texture_Button_Unit_Spetsnaz_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Spetsnaz_CMD_SOV.png"))]),
        ("Texture_Button_Unit_Spetsnaz_FireSupport_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Spetsnaz_FireSupport_SOV.png"))]),
        ("Texture_Button_Unit_Spetsnaz_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Spetsnaz_SOV.png"))]),
        ("Texture_Button_Unit_Spetsnaz_Vympel_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Spetsnaz_Vympel_SOV.png"))]),
        ("Texture_Button_Unit_Star_266_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Star_266_POL.png"))]),
        ("Texture_Button_Unit_Star_266_supply_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Star_266_supply_POL.png"))]),
        ("Texture_Button_Unit_Supacat_ATMP_Javelin_LML_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Supacat_ATMP_Javelin_LML_UK.png"))]),
        ("Texture_Button_Unit_Supacat_ATMP_MILAN_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Supacat_ATMP_MILAN_UK.png"))]),
        ("Texture_Button_Unit_Supacat_ATMP_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Supacat_ATMP_UK.png"))]),
        ("Texture_Button_Unit_Supacat_ATMP_supply_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Supacat_ATMP_supply_UK.png"))]),
        ("Texture_Button_Unit_T34_85M_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_T34_85M_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T34_85M_DDR.png"))]),
        ("Texture_Button_Unit_T34_85M_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_T54B_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T54B_CMD_DDR.png"))]),
        ("Texture_Button_Unit_T54B_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_T54B_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T54B_DDR.png"))]),
        ("Texture_Button_Unit_T54B_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_T54B_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T54B_SOV.png"))]),
        ("Texture_Button_Unit_T55AM2B_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AM2B_DDR.png"))]),
        ("Texture_Button_Unit_T55AM2_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AM2_CMD_DDR.png"))]),
        ("Texture_Button_Unit_T55AM2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AM2_DDR.png"))]),
        ("Texture_Button_Unit_T55AMS_Merida_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AMS_Merida_POL.png"))]),
        ("Texture_Button_Unit_T55AM_1_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AM_1_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T55AM_1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AM_1_SOV.png"))]),
        ("Texture_Button_Unit_T55AM_Merida_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AM_Merida_CMD_POL.png"))]),
        ("Texture_Button_Unit_T55AM_Merida_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AM_Merida_POL.png"))]),
        ("Texture_Button_Unit_T55AS_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55AS_POL.png"))]),
        ("Texture_Button_Unit_T55A_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55A_CMD_DDR.png"))]),
        ("Texture_Button_Unit_T55A_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55A_CMD_POL.png"))]),
        ("Texture_Button_Unit_T55A_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55A_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T55A_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55A_DDR.png"))]),
        ("Texture_Button_Unit_T55A_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55A_POL.png"))]),
        ("Texture_Button_Unit_T55A_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55A_SOV.png"))]),
        ("Texture_Button_Unit_T55A_obr81_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T55A_obr81_SOV.png"))]),
        ("Texture_Button_Unit_T62M1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T62M1_SOV.png"))]),
        ("Texture_Button_Unit_T62MD1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T62MD1_SOV.png"))]),
        ("Texture_Button_Unit_T62MD_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T62MD_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T62MD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T62MD_SOV.png"))]),
        ("Texture_Button_Unit_T62MV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T62MV_SOV.png"))]),
        ("Texture_Button_Unit_T62M_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T62M_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T62M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T62M_SOV.png"))]),
        ("Texture_Button_Unit_T64AM_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64AM_SOV.png"))]),
        ("Texture_Button_Unit_T64AV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64AV_SOV.png"))]),
        ("Texture_Button_Unit_T64A_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64A_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T64A_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64A_SOV.png"))]),
        ("Texture_Button_Unit_T64B1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64B1_SOV.png"))]),
        ("Texture_Button_Unit_T64B1_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64B1_reco_SOV.png"))]),
        ("Texture_Button_Unit_T64BV1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64BV1_SOV.png"))]),
        ("Texture_Button_Unit_T64BV_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64BV_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T64BV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64BV_SOV.png"))]),
        ("Texture_Button_Unit_T64B_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64B_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T64B_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T64B_SOV.png"))]),
        ("Texture_Button_Unit_T72M1_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M1_CMD_DDR.png"))]),
        ("Texture_Button_Unit_T72M1_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M1_CMD_POL.png"))]),
        ("Texture_Button_Unit_T72M1_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M1_DDR.png"))]),
        ("Texture_Button_Unit_T72M1_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M1_POL.png"))]),
        ("Texture_Button_Unit_T72M1_Wilk_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M1_Wilk_POL.png"))]),
        ("Texture_Button_Unit_T72MUV2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72MUV2_DDR.png"))]),
        ("Texture_Button_Unit_T72M_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M_CMD_DDR.png"))]),
        ("Texture_Button_Unit_T72M_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M_CMD_POL.png"))]),
        ("Texture_Button_Unit_T72M_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M_DDR.png"))]),
        ("Texture_Button_Unit_T72M_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72M_POL.png"))]),
        ("Texture_Button_Unit_T72S_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72S_DDR.png"))]),
        ("Texture_Button_Unit_T72_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72_CMD_DDR.png"))]),
        ("Texture_Button_Unit_T72_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T72_DDR.png"))]),
        ("Texture_Button_Unit_T80BV_Beast_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T80BV_Beast_SOV.png"))]),
        ("Texture_Button_Unit_T80BV_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T80BV_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T80BV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T80BV_SOV.png"))]),
        ("Texture_Button_Unit_T80B_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T80B_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T80B_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T80B_SOV.png"))]),
        ("Texture_Button_Unit_T80UD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T80UD_SOV.png"))]),
        ("Texture_Button_Unit_T80U_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T80U_CMD_SOV.png"))]),
        ("Texture_Button_Unit_T80U_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T80U_SOV.png"))]),
        ("Texture_Button_Unit_T813_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T813_DDR.png"))]),
        ("Texture_Button_Unit_T813_trans_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T813_DDR.png"))]),
        ("Texture_Button_Unit_T815_supply_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/T815_supply_DDR.png"))]),
        ("Texture_Button_Unit_TOS1_Buratino_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/TOS1_Buratino_SOV.png"))]),
        ("Texture_Button_Unit_TO_55_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/TO_55_DDR.png"))]),
        ("Texture_Button_Unit_TO_55_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/TO_55_SOV.png"))]),
        ("Texture_Button_Unit_TPZ_Fuchs_1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TPZ_Fuchs_1_RFA.png"))]),
        ("Texture_Button_Unit_TPZ_Fuchs_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TPZ_Fuchs_CMD_RFA.png"))]),
        ("Texture_Button_Unit_TPZ_Fuchs_MILAN_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TPZ_Fuchs_MILAN_RFA.png"))]),
        ("Texture_Button_Unit_TPZ_Fuchs_RASIT_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TPZ_Fuchs_RASIT_RFA.png"))]),
        ("Texture_Button_Unit_TRM_10000_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TRM_10000_FR.png"))]),
        ("Texture_Button_Unit_TRM_10000_supply_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TRM_10000_supply_FR.png"))]),
        ("Texture_Button_Unit_TRM_2000_20mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TRM_2000_20mm_FR.png"))]),
        ("Texture_Button_Unit_TRM_2000_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TRM_2000_FR.png"))]),
        ("Texture_Button_Unit_TRM_2000_supply_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/TRM_2000_supply_FR.png"))]),
        ("Texture_Button_Unit_TUTO_M1025_Humvee_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/M1025_Humvee_TOW_US.png"))]),
        ("Texture_Button_Unit_Territorial_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Territorial_CMD_UK.png"))]),
        ("Texture_Button_Unit_Territorial_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Territorial_UK.png"))]),
        ("Texture_Button_Unit_Tor_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Tor_SOV.png"))]),
        ("Texture_Button_Unit_Tracked_Rapier_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tracked_Rapier_UK.png"))]),
        ("Texture_Button_Unit_Tunguska_2K22_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Tunguska_2K22_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_AGL_Grenzer_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_AGL_Grenzer_DDR.png"))]),
        ("Texture_Button_Unit_UAZ_469_AGL_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_AGL_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_AGL_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_AGL_VDV_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_CMD_DDR.png"))]),
        ("Texture_Button_Unit_UAZ_469_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_CMD_POL.png"))]),
        ("Texture_Button_Unit_UAZ_469_CMD_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_CMD_Para_POL.png"))]),
        ("Texture_Button_Unit_UAZ_469_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_CMD_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_CMD_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_CMD_VDV_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_Fagot_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_Fagot_DDR.png"))]),
        ("Texture_Button_Unit_UAZ_469_Fagot_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_Fagot_POL.png"))]),
        ("Texture_Button_Unit_UAZ_469_Fagot_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_Fagot_Para_POL.png"))]),
        ("Texture_Button_Unit_UAZ_469_Konkurs_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_Konkurs_VDV_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_MP_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_MP_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_Reco_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_Reco_DDR.png"))]),
        ("Texture_Button_Unit_UAZ_469_Reco_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_Reco_POL.png"))]),
        ("Texture_Button_Unit_UAZ_469_Reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_Reco_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_CMD_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_SPG9_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_SPG9_DDR.png"))]),
        ("Texture_Button_Unit_UAZ_469_SPG9_FJ_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_SPG9_FJ_DDR.png"))]),
        ("Texture_Button_Unit_UAZ_469_SPG9_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_SPG9_Para_POL.png"))]),
        ("Texture_Button_Unit_UAZ_469_SPG9_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_SPG9_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_SPG9_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_SPG9_VDV_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_supply_Para_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_supply_Para_POL.png"))]),
        ("Texture_Button_Unit_UAZ_469_supply_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_supply_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_supply_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_supply_VDV_SOV.png"))]),
        ("Texture_Button_Unit_UAZ_469_trans_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_trans_DDR.png"))]),
        ("Texture_Button_Unit_UAZ_469_trans_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/UAZ_469_trans_POL.png"))]),
        ("Texture_Button_Unit_Unimog_S_404_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Unimog_S_404_RFA.png"))]),
        ("Texture_Button_Unit_Unimog_U1350L_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Unimog_U1350L_BEL.png"))]),
        ("Texture_Button_Unit_Unimog_U1350L_Para_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Unimog_U1350L_Para_BEL.png"))]),
        ("Texture_Button_Unit_Unimog_U1350L_supply_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Unimog_U1350L_supply_BEL.png"))]),
        ("Texture_Button_Unit_Unimog_supply_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Unimog_supply_BEL.png"))]),
        ("Texture_Button_Unit_Unimog_trans_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Unimog_trans_BEL.png"))]),
        ("Texture_Button_Unit_Unimog_trans_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Unimog_trans_RFA.png"))]),
        ("Texture_Button_Unit_Ural_4320_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Ural_4320_DDR.png"))]),
        ("Texture_Button_Unit_Ural_4320_Metla_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Ural_4320_Metla_SOV.png"))]),
        ("Texture_Button_Unit_Ural_4320_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Ural_4320_SOV.png"))]),
        ("Texture_Button_Unit_Ural_4320_ZPU_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Ural_4320_ZPU_SOV.png"))]),
        ("Texture_Button_Unit_Ural_4320_ZU_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Ural_4320_ZU_SOV.png"))]),
        ("Texture_Button_Unit_Ural_4320_trans_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Ural_4320_trans_SOV.png"))]),
        ("Texture_Button_Unit_VAB_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VAB_CMD_FR.png"))]),
        ("Texture_Button_Unit_VAB_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VAB_FR.png"))]),
        ("Texture_Button_Unit_VAB_HOT_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VAB_HOT_FR.png"))]),
        ("Texture_Button_Unit_VAB_MILAN_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VAB_MILAN_FR.png"))]),
        ("Texture_Button_Unit_VAB_Mortar_81_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VAB_Mortar_81_FR.png"))]),
        ("Texture_Button_Unit_VAB_RASIT_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VAB_RASIT_FR.png"))]),
        ("Texture_Button_Unit_VAB_Reserve_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VAB_Reserve_FR.png"))]),
        ("Texture_Button_Unit_VAB_T20_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VAB_T20_FR.png"))]),
        ("Texture_Button_Unit_VBL_MILAN_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VBL_MILAN_FR.png"))]),
        ("Texture_Button_Unit_VBL_PC_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VBL_PC_FR.png"))]),
        ("Texture_Button_Unit_VBL_Reco_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VBL_Reco_FR.png"))]),
        ("Texture_Button_Unit_VDV_Afgantsy_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/VDV_Afgantsy_SOV.png"))]),
        ("Texture_Button_Unit_VDV_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/VDV_CMD_SOV.png"))]),
        ("Texture_Button_Unit_VDV_Combine_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/VDV_Combine_SOV.png"))]),
        ("Texture_Button_Unit_VDV_HMG_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/VDV_HMG_SOV.png"))]),
        ("Texture_Button_Unit_VDV_Mech_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/VDV_Mech_SOV.png"))]),
        ("Texture_Button_Unit_VDV_Metis_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/VDV_Metis_SOV.png"))]),
        ("Texture_Button_Unit_VDV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/VDV_SOV.png"))]),
        ("Texture_Button_Unit_VIB_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VIB_FR.png"))]),
        ("Texture_Button_Unit_VLRA_20mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLRA_20mm_FR.png"))]),
        ("Texture_Button_Unit_VLRA_HMG_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLRA_HMG_FR.png"))]),
        ("Texture_Button_Unit_VLRA_MILAN_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLRA_MILAN_FR.png"))]),
        ("Texture_Button_Unit_VLRA_Mistral_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLRA_Mistral_FR.png"))]),
        ("Texture_Button_Unit_VLRA_Mortier81_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLRA_Mortier81_FR.png"))]),
        ("Texture_Button_Unit_VLRA_supply_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLRA_supply_FR.png"))]),
        ("Texture_Button_Unit_VLRA_trans_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLRA_trans_FR.png"))]),
        ("Texture_Button_Unit_VLTT_P4_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLTT_P4_FR.png"))]),
        ("Texture_Button_Unit_VLTT_P4_MILAN_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLTT_P4_MILAN_FR.png"))]),
        ("Texture_Button_Unit_VLTT_P4_MILAN_para_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLTT_P4_MILAN_para_FR.png"))]),
        ("Texture_Button_Unit_VLTT_P4_PC_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/VLTT_P4_PC_FR.png"))]),
        ("Texture_Button_Unit_Volkspolizei_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Volkspolizei_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Volkspolizei_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Volkspolizei_DDR.png"))]),
        ("Texture_Button_Unit_Volvo_N10_supply_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Volvo_N10_supply_BEL.png"))]),
        ("Texture_Button_Unit_Volvo_N10_trans_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Volvo_N10_trans_BEL.png"))]),
        ("Texture_Button_Unit_W50_LA_A_25mm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/W50_LA_A_25mm_DDR.png"))]),
        ("Texture_Button_Unit_W50_LA_A_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/W50_LA_A_DDR.png"))]),
        ("Texture_Button_Unit_WSW_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/WSW_POL.png"))]),
        ("Texture_Button_Unit_Wachregiment_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Wachregiment_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Wachregiment_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Wachregiment_DDR.png"))]),
        ("Texture_Button_Unit_Wachregiment_RPG_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Wachregiment_RPG_DDR.png"))]),
        ("Texture_Button_Unit_Wachregiment_SMG_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Wachregiment_SMG_DDR.png"))]),
        ("Texture_Button_Unit_Wiesel_20mm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Wiesel_20mm_RFA.png"))]),
        ("Texture_Button_Unit_Wiesel_TOW_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Wiesel_TOW_RFA.png"))]),
        ("Texture_Button_Unit_ZSU_23_Shilka_Afghan_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ZSU_23_Shilka_Afghan_SOV.png"))]),
        ("Texture_Button_Unit_ZSU_23_Shilka_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ZSU_23_Shilka_DDR.png"))]),
        ("Texture_Button_Unit_ZSU_23_Shilka_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ZSU_23_Shilka_POL.png"))]),
        ("Texture_Button_Unit_ZSU_23_Shilka_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ZSU_23_Shilka_SOV.png"))]),
        ("Texture_Button_Unit_ZSU_23_Shilka_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ZSU_23_Shilka_reco_SOV.png"))]),
        ("Texture_Button_Unit_ZSU_57_2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/ZSU_57_2_DDR.png"))]),

        ("Texture_Button_Unit_A109BA_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A109BA_BEL.png"))]),
        ("Texture_Button_Unit_A109BA_TOW_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A109BA_TOW_BEL.png"))]),
        ("Texture_Button_Unit_A109BA_TOW_twin_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A109BA_TOW_twin_BEL.png"))]),
        ("Texture_Button_Unit_AH1E_Cobra_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH1E_Cobra_US.png"))]),
        ("Texture_Button_Unit_AH1F_ATAS_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH1F_ATAS_US.png"))]),
        ("Texture_Button_Unit_AH1F_CNITE_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH1F_CNITE_US.png"))]),
        ("Texture_Button_Unit_AH1F_Cobra_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH1F_Cobra_NG_US.png"))]),
        ("Texture_Button_Unit_AH1F_Cobra_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH1F_Cobra_US.png"))]),
        ("Texture_Button_Unit_AH1F_HeavyHog_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH1F_HeavyHog_US.png"))]),
        ("Texture_Button_Unit_AH1F_Hog_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH1F_Hog_US.png"))]),
        ("Texture_Button_Unit_AH1S_Cobra_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH1S_Cobra_US.png"))]),
        ("Texture_Button_Unit_AH64_Apache_ATAS_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH64_Apache_ATAS_US.png"))]),
        ("Texture_Button_Unit_AH64_Apache_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH64_Apache_NG_US.png"))]),
        ("Texture_Button_Unit_AH64_Apache_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH64_Apache_US.png"))]),
        ("Texture_Button_Unit_AH64_Apache_emp1_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH64_Apache_emp2_US.png"))]),
        ("Texture_Button_Unit_AH64_Apache_emp2_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH64_Apache_emp1_US.png"))]),
        ("Texture_Button_Unit_AH6C_Little_Bird_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH6C_Little_Bird_US.png"))]),
        ("Texture_Button_Unit_AH6G_Little_Bird_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/AH6G_Little_Bird_US.png"))]),
        ("Texture_Button_Unit_Alouette_III_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_III_FR.png"))]),
        ("Texture_Button_Unit_Alouette_III_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_III_NL.png"))]),
        ("Texture_Button_Unit_Alouette_III_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Alouette_III_SOV.png"))]),
        ("Texture_Button_Unit_Alouette_III_SS11_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_III_SS11_FR.png"))]),
        ("Texture_Button_Unit_Alouette_III_reco_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_III_reco_FR.png"))]),
        ("Texture_Button_Unit_Alouette_III_trans_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_III_trans_NL.png"))]),
        ("Texture_Button_Unit_Alouette_II_CMD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_II_CMD_FR.png"))]),
        ("Texture_Button_Unit_Alouette_II_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_II_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Alouette_II_reco_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_II_reco_BEL.png"))]),
        ("Texture_Button_Unit_Alouette_II_reco_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_II_reco_RFA.png"))]),
        ("Texture_Button_Unit_Alouette_II_trans_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_II_trans_BEL.png"))]),
        ("Texture_Button_Unit_Alouette_II_trans_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alouette_II_trans_FR.png"))]),
        ("Texture_Button_Unit_Bo_105_CB_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bo_105_CB_NL.png"))]),
        ("Texture_Button_Unit_Bo_105_CMD_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bo_105_CMD_RFA.png"))]),
        ("Texture_Button_Unit_Bo_105_PAH_1A1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bo_105_PAH_1A1_RFA.png"))]),
        ("Texture_Button_Unit_Bo_105_PAH_1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bo_105_PAH_1_RFA.png"))]),
        ("Texture_Button_Unit_Bo_105_reco_ESP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_Bo_105_reco_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bo_105_reco_RFA.png"))]),
        ("Texture_Button_Unit_Bo_105_trans_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Bo_105_trans_RFA.png"))]),
        ("Texture_Button_Unit_CH47D_Chinook_supply_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CH47D_Chinook_supply_UK.png"))]),
        ("Texture_Button_Unit_CH47_Chinook_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CH47_Chinook_UK.png"))]),
        ("Texture_Button_Unit_CH47_Chinook_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CH47_Chinook_US.png"))]),
        ("Texture_Button_Unit_CH47_Super_Chinook_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CH47_Super_Chinook_US.png"))]),
        ("Texture_Button_Unit_CH53G_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CH53G_RFA.png"))]),
        ("Texture_Button_Unit_CH53G_trans_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CH53G_trans_RFA.png"))]),
        ("Texture_Button_Unit_CH54B_Tarhe_supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CH54B_Tarhe_supply_US.png"))]),
        ("Texture_Button_Unit_CH54B_Tarhe_trans_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CH54B_Tarhe_trans_US.png"))]),
        ("Texture_Button_Unit_EH60A_EW_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/EH60A_EW_US.png"))]),
        ("Texture_Button_Unit_Ecureuil_20mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Ecureuil_20mm_FR.png"))]),
        ("Texture_Button_Unit_Ecureuil_reco_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Ecureuil_reco_FR.png"))]),
        ("Texture_Button_Unit_Gazelle_20mm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_20mm_FR.png"))]),
        ("Texture_Button_Unit_Gazelle_20mm_reco_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_20mm_reco_FR.png"))]),
        ("Texture_Button_Unit_Gazelle_CMD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_CMD_UK.png"))]),
        ("Texture_Button_Unit_Gazelle_HOT_2_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_HOT_2_FR.png"))]),
        ("Texture_Button_Unit_Gazelle_HOT_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_HOT_FR.png"))]),
        ("Texture_Button_Unit_Gazelle_Mistral_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_Mistral_FR.png"))]),
        ("Texture_Button_Unit_Gazelle_SNEB_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_SNEB_UK.png"))]),
        ("Texture_Button_Unit_Gazelle_SNEB_reco_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_SNEB_reco_UK.png"))]),
        ("Texture_Button_Unit_Gazelle_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_UK.png"))]),
        ("Texture_Button_Unit_Gazelle_reco_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_reco_FR.png"))]),
        ("Texture_Button_Unit_Gazelle_trans_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Gazelle_trans_UK.png"))]),
        ("Texture_Button_Unit_JOH_58C_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/JOH_58C_US.png"))]),
        ("Texture_Button_Unit_Ka_50_AA_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Ka_50_AA_SOV.png"))]),
        ("Texture_Button_Unit_Ka_50_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Ka_50_SOV.png"))]),
        ("Texture_Button_Unit_Lynx_AH_Mk1_LBH_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Lynx_AH_Mk1_LBH_UK.png"))]),
        ("Texture_Button_Unit_Lynx_AH_Mk1_TOW_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Lynx_AH_Mk1_TOW_UK.png"))]),
        ("Texture_Button_Unit_Lynx_AH_Mk1_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Lynx_AH_Mk1_UK.png"))]),
        ("Texture_Button_Unit_Lynx_AH_Mk7_Chancellor_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Lynx_AH_Mk7_Chancellor_UK.png"))]),
        ("Texture_Button_Unit_Lynx_AH_Mk7_I_TOW2_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Lynx_AH_Mk7_I_TOW2_UK.png"))]),
        ("Texture_Button_Unit_Lynx_AH_Mk7_I_TOW_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Lynx_AH_Mk7_I_TOW_UK.png"))]),
        ("Texture_Button_Unit_Lynx_AH_Mk7_SNEB_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Lynx_AH_Mk7_SNEB_UK.png"))]),
        ("Texture_Button_Unit_MH47D_Super_Chinook_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MH47D_Super_Chinook_US.png"))]),
        ("Texture_Button_Unit_MH_60A_DAP_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MH_60A_DAP_US.png"))]),
        ("Texture_Button_Unit_Mi_14PL_AT_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_14PL_AT_DDR.png"))]),
        ("Texture_Button_Unit_Mi_14PL_recon_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_14PL_recon_DDR.png"))]),
        ("Texture_Button_Unit_Mi_24D_AA_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24D_AA_DDR.png"))]),
        ("Texture_Button_Unit_Mi_24D_Desant_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24D_Desant_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24D_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24D_POL.png"))]),
        ("Texture_Button_Unit_Mi_24D_s5_AT_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24D_s5_AT_DDR.png"))]),
        ("Texture_Button_Unit_Mi_24D_s5_AT_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24D_s5_AT_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24D_s8_AT_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24D_s8_AT_DDR.png"))]),
        ("Texture_Button_Unit_Mi_24D_s8_AT_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24D_s8_AT_POL.png"))]),
        ("Texture_Button_Unit_Mi_24D_s8_AT_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24D_s8_AT_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24K_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24K_reco_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24P_AA_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24P_AA_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24P_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24P_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24P_s8_AT2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24P_s8_AT2_DDR.png"))]),
        ("Texture_Button_Unit_Mi_24P_s8_AT_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24P_s8_AT_DDR.png"))]),
        ("Texture_Button_Unit_Mi_24VP_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24VP_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24V_AA_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24V_AA_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24V_AT_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24V_AT_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24V_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24V_POL.png"))]),
        ("Texture_Button_Unit_Mi_24V_RKT2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24V_RKT2_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24V_RKT_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24V_RKT_SOV.png"))]),
        ("Texture_Button_Unit_Mi_24V_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_24V_SOV.png"))]),
        ("Texture_Button_Unit_Mi_26_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_26_SOV.png"))]),
        ("Texture_Button_Unit_Mi_2Ro_reco_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2Ro_reco_POL.png"))]),
        ("Texture_Button_Unit_Mi_2_AA_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_AA_POL.png"))]),
        ("Texture_Button_Unit_Mi_2_ATGM_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_ATGM_POL.png"))]),
        ("Texture_Button_Unit_Mi_2_CMD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_CMD_DDR.png"))]),
        ("Texture_Button_Unit_Mi_2_CMD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_CMD_POL.png"))]),
        ("Texture_Button_Unit_Mi_2_gunship_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_gunship_DDR.png"))]),
        ("Texture_Button_Unit_Mi_2_gunship_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_gunship_POL.png"))]),
        ("Texture_Button_Unit_Mi_2_reco_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_reco_DDR.png"))]),
        ("Texture_Button_Unit_Mi_2_reco_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_reco_SOV.png"))]),
        ("Texture_Button_Unit_Mi_2_rocket_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_rocket_DDR.png"))]),
        ("Texture_Button_Unit_Mi_2_rocket_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_rocket_POL.png"))]),
        ("Texture_Button_Unit_Mi_2_trans_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_trans_DDR.png"))]),
        ("Texture_Button_Unit_Mi_2_trans_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_trans_POL.png"))]),
        ("Texture_Button_Unit_Mi_2_trans_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_2_trans_SOV.png"))]),
        ("Texture_Button_Unit_Mi_6_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_6_POL.png"))]),
        ("Texture_Button_Unit_Mi_6_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_6_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8K_CMD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8K_CMD_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8MTPI_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8MTPI_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8MTV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8MTV_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8MT_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_Mi_8PPA_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8PPA_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8R_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8R_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TB_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TB_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8TB_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TB_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TB_reco_Marine_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TB_reco_Marine_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8TV_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8TV_Gunship_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_Gunship_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TV_PodGatling_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_PodGatling_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8TV_PodGatling_PodAGL_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_PodGatling_PodAGL_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TV_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TV_UPK_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_UPK_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8TV_non_arme_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_non_arme_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TV_s57_16_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_s57_16_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TV_s57_32_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_s57_32_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8TV_s57_32_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_s57_32_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TV_s80_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TV_s80_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8TZ_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8TZ_SOV.png"))]),
        ("Texture_Button_Unit_Mi_8T_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8T_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8T_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8T_POL.png"))]),
        ("Texture_Button_Unit_Mi_8T_non_arme_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8T_non_arme_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8T_non_arme_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8T_non_arme_POL.png"))]),
        ("Texture_Button_Unit_Mi_8_supply_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8_supply_DDR.png"))]),
        ("Texture_Button_Unit_Mi_8_supply_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_8_supply_POL.png"))]),
        ("Texture_Button_Unit_Mi_9_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Mi_9_DDR.png"))]),
        ("Texture_Button_Unit_OH58A_reco_NG_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/OH58A_reco_NG_US.png"))]),
        ("Texture_Button_Unit_OH58C_CMD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/OH58C_CMD_US.png"))]),
        ("Texture_Button_Unit_OH58C_Scout_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/OH58C_Scout_US.png"))]),
        ("Texture_Button_Unit_OH58D_Combat_Scout_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/OH58D_Combat_Scout_US.png"))]),
        ("Texture_Button_Unit_OH58D_Kiowa_Warrior_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/OH58D_Kiowa_Warrior_US.png"))]),
        ("Texture_Button_Unit_OH58_CS_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/OH58_CS_US.png"))]),
        ("Texture_Button_Unit_Puma_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Puma_FR.png"))]),
        ("Texture_Button_Unit_Puma_HET_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Puma_HET_FR.png"))]),
        ("Texture_Button_Unit_Puma_PC_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Puma_PC_FR.png"))]),
        ("Texture_Button_Unit_Puma_Pirate_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Puma_Pirate_FR.png"))]),
        ("Texture_Button_Unit_Puma_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Puma_UK.png"))]),
        ("Texture_Button_Unit_Super_Puma_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Super_Puma_FR.png"))]),
        ("Texture_Button_Unit_UH1A_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH1A_US.png"))]),
        ("Texture_Button_Unit_UH1D_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH1D_NL.png"))]),
        ("Texture_Button_Unit_UH1D_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH1D_RFA.png"))]),
        ("Texture_Button_Unit_UH1D_Supply_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH1D_Supply_RFA.png"))]),
        ("Texture_Button_Unit_UH1H_CMD_ESP", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_UH1H_Huey_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH1H_Huey_US.png"))]),
        ("Texture_Button_Unit_UH1H_supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH1H_supply_US.png"))]),
        ("Texture_Button_Unit_UH1M_gunship_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH1M_gunship_US.png"))]),
        ("Texture_Button_Unit_UH60A_Black_Hawk_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH60A_Black_Hawk_US.png"))]),
        ("Texture_Button_Unit_UH60A_CO_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH60A_CO_US.png"))]),
        ("Texture_Button_Unit_UH60A_Supply_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/UH60A_Supply_US.png"))]),
        ("Texture_Button_Unit_W3RR_Procjon_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/W3RR_Procjon_POL.png"))]),
        ("Texture_Button_Unit_W3W_Sokol_AA_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/W3W_Sokol_AA_POL.png"))]),
        ("Texture_Button_Unit_W3W_Sokol_RKT_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/W3W_Sokol_RKT_POL.png"))]),
        ("Texture_Button_Unit_W3_Sokol_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/W3_Sokol_POL.png"))]),
        ("Texture_Button_Unit_Westland_Scout_SS11_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Westland_Scout_SS11_UK.png"))]),

        ("Texture_Button_Unit_A10_Thunderbolt_II_ATGM_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A10_Thunderbolt_II_ATGM.png"))]),
        ("Texture_Button_Unit_A10_Thunderbolt_II_Rkt_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A10_Thunderbolt_II_Rkt_US.png"))]),
        ("Texture_Button_Unit_A10_Thunderbolt_II_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A10_Thunderbolt_II_US.png"))]),
        ("Texture_Button_Unit_A37B_Dragonfly_HE_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A37B_Dragonfly_HE_US.png"))]),
        ("Texture_Button_Unit_A37B_Dragonfly_NPLM_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A37B_Dragonfly_NPLM_US.png"))]),
        ("Texture_Button_Unit_A37B_Dragonfly_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A37B_Dragonfly_US.png"))]),
        ("Texture_Button_Unit_A6E_Intruder_SEAD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A6E_Intruder_SEAD_US.png"))]),
        ("Texture_Button_Unit_A6E_Intruder_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A6E_Intruder_US.png"))]),
        ("Texture_Button_Unit_A7D_Corsair_II_AT_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A7D_Corsair_II_AT_US.png"))]),
        ("Texture_Button_Unit_A7D_Corsair_II_CLU_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A7D_Corsair_II_CLU_US.png"))]),
        ("Texture_Button_Unit_A7D_Corsair_II_RKT_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A7D_Corsair_II_RKT_US.png"))]),
        ("Texture_Button_Unit_A7D_Corsair_II_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/A7D_Corsair_II_US.png"))]),
        ("Texture_Button_Unit_Alpha_Jet_A_clu_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alpha_Jet_A_clu_RFA.png"))]),
        ("Texture_Button_Unit_Alpha_Jet_A_he_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alpha_Jet_A_he_RFA.png"))]),
        ("Texture_Button_Unit_Alpha_Jet_A_nplm_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alpha_Jet_A_nplm_RFA.png"))]),
        ("Texture_Button_Unit_Alpha_Jet_A_rkt_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alpha_Jet_A_rkt_RFA.png"))]),
        ("Texture_Button_Unit_Alpha_Jet_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alpha_Jet_BEL.png"))]),
        ("Texture_Button_Unit_Alpha_Jet_E_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alpha_Jet_E_FR.png"))]),
        ("Texture_Button_Unit_Alpha_Jet_E_NPLM_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alpha_Jet_E_NPLM_FR.png"))]),
        ("Texture_Button_Unit_Alpha_Jet_HE2_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Alpha_Jet_HE2_BEL.png"))]),
        ("Texture_Button_Unit_Buccaneer_S2B_ATGM_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Buccaneer_S2B_ATGM_UK.png"))]),
        ("Texture_Button_Unit_Buccaneer_S2B_GBU_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Buccaneer_S2B_GBU_UK.png"))]),
        ("Texture_Button_Unit_Buccaneer_S2B_HE_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Buccaneer_S2B_HE_UK.png"))]),
        ("Texture_Button_Unit_Buccaneer_S2B_SEAD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Buccaneer_S2B_SEAD_UK.png"))]),
        ("Texture_Button_Unit_CL_289_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CL_289_RFA.png"))]),
        ("Texture_Button_Unit_CM170_Magister_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CM170_Magister_FR.png"))]),
        ("Texture_Button_Unit_CM170_Magister_SS11_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/CM170_Magister_SS11_FR.png"))]),
        ("Texture_Button_Unit_EA6B_Prowler_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/EA6B_Prowler_US.png"))]),
        ("Texture_Button_Unit_EF111_Raven_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/EF111_Raven_US.png"))]),
        ("Texture_Button_Unit_Epervier_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Epervier_BEL.png"))]),
        ("Texture_Button_Unit_F104G_Starfighter_AT_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F104G_Starfighter_AT_RFA.png"))]),
        ("Texture_Button_Unit_F104G_Starfighter_HE_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F104G_Starfighter_HE_RFA.png"))]),
        ("Texture_Button_Unit_F104G_Starfighter_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F104G_Starfighter_RFA.png"))]),
        ("Texture_Button_Unit_F111E_Aardvark_CBU_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F111E_Aardvark_CBU_US.png"))]),
        ("Texture_Button_Unit_F111E_Aardvark_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F111E_Aardvark_US.png"))]),
        ("Texture_Button_Unit_F111E_Aardvark_napalm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F111E_Aardvark_napalm_US.png"))]),
        ("Texture_Button_Unit_F111F_Aardvark_CBU_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F111F_Aardvark_CBU_US.png"))]),
        ("Texture_Button_Unit_F111F_Aardvark_LGB2_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F111F_Aardvark_LGB2_US.png"))]),
        ("Texture_Button_Unit_F111F_Aardvark_LGB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F111F_Aardvark_LGB_US.png"))]),
        ("Texture_Button_Unit_F111F_Aardvark_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F111F_Aardvark_US.png"))]),
        ("Texture_Button_Unit_F111F_Aardvark_napalm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F111F_Aardvark_napalm_US.png"))]),
        ("Texture_Button_Unit_F117_Nighthawk_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F117_Nighthawk_US.png"))]),
        ("Texture_Button_Unit_F15C_Eagle_AA2_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F15_Eagle.png"))]),
        ("Texture_Button_Unit_F15C_Eagle_AA_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F15_Eagle.png"))]),
        ("Texture_Button_Unit_F15E_StrikeEagle_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F15E_StrikeEagle_US.png"))]),
        ("Texture_Button_Unit_F16A_AA2_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16A_AA2_NL.png"))]),
        ("Texture_Button_Unit_F16A_AA_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16A_AA_BEL.png"))]),
        ("Texture_Button_Unit_F16A_AA_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16A_AA_NL.png"))]),
        ("Texture_Button_Unit_F16A_CBU_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16A_CBU_BEL.png"))]),
        ("Texture_Button_Unit_F16A_CLU_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16A_CLU_NL.png"))]),
        ("Texture_Button_Unit_F16A_HE_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16A_HE_NL.png"))]),
        ("Texture_Button_Unit_F16C_LGB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16C_LGB_US.png"))]),
        ("Texture_Button_Unit_F16E_AA2_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_AA2_US.png"))]),
        ("Texture_Button_Unit_F16E_AA_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_AA_US.png"))]),
        ("Texture_Button_Unit_F16E_AGM_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_AGM_US.png"))]),
        ("Texture_Button_Unit_F16E_CBU_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_CBU_US.png"))]),
        ("Texture_Button_Unit_F16E_HE_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_HE_US.png"))]),
        ("Texture_Button_Unit_F16E_SEAD_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_SEAD_US.png"))]),
        ("Texture_Button_Unit_F16E_TER_CLU_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_TER_CLU_US.png"))]),
        ("Texture_Button_Unit_F16E_TER_HE_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_TER_HE_US.png"))]),
        ("Texture_Button_Unit_F16E_napalm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F16E_napalm_US.png"))]),
        ("Texture_Button_Unit_F4E_Phantom_II_AA_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4E_Phantom_II_AA_US.png"))]),
        ("Texture_Button_Unit_F4E_Phantom_II_AT_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4E_Phantom_II_AT_US.png"))]),
        ("Texture_Button_Unit_F4E_Phantom_II_CBU_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4E_Phantom_II_CBU_US.png"))]),
        ("Texture_Button_Unit_F4E_Phantom_II_HE_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4E_Phantom_II_HE_US.png"))]),
        ("Texture_Button_Unit_F4E_Phantom_II_napalm_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4E_Phantom_II_napalm_US.png"))]),
        ("Texture_Button_Unit_F4F_Phantom_II_AA_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4F_Phantom_II_AA_RFA.png"))]),
        ("Texture_Button_Unit_F4F_Phantom_II_AT_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4F_Phantom_II_AT_RFA.png"))]),
        ("Texture_Button_Unit_F4F_Phantom_II_HE1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4F_Phantom_II_HE1_RFA.png"))]),
        ("Texture_Button_Unit_F4F_Phantom_II_HE2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4F_Phantom_II_HE2_RFA.png"))]),
        ("Texture_Button_Unit_F4F_Phantom_II_RKT2_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4F_Phantom_II_RKT2_RFA.png"))]),
        ("Texture_Button_Unit_F4_Phantom_AA_F3_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4_Phantom_AA_F3.png"))]),
        ("Texture_Button_Unit_F4_Phantom_GR2_HE_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4_Phantom_GR2_HE_UK.png"))]),
        ("Texture_Button_Unit_F4_Phantom_GR2_NPLM_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/default.png"))]),
        ("Texture_Button_Unit_F4_Phantom_GR2_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4_Phantom_GR2_UK.png"))]),
        ("Texture_Button_Unit_F4_Wild_Weasel_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F4_Wild_Weasel_US.png"))]),
        ("Texture_Button_Unit_F5A_FreedomFighter_AA_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F5A_FreedomFighter_AA_NL.png"))]),
        ("Texture_Button_Unit_F5A_FreedomFighter_CLU_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F5A_FreedomFighter_CLU_NL.png"))]),
        ("Texture_Button_Unit_F5A_FreedomFighter_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F5A_FreedomFighter_NL.png"))]),
        ("Texture_Button_Unit_F5A_FreedomFighter_NPLM_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F5A_FreedomFighter_NPLM_NL.png"))]),
        ("Texture_Button_Unit_F5A_FreedomFighter_RKT_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F5A_FreedomFighter_RKT_NL.png"))]),
        ("Texture_Button_Unit_F8P_Crusader_AA2_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F8P_Crusader_AA2_FR.png"))]),
        ("Texture_Button_Unit_F8P_Crusader_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/F8P_Crusader_FR.png"))]),
        ("Texture_Button_Unit_FA16_CAS_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FA16_CAS_US.png"))]),
        ("Texture_Button_Unit_G91_R3_Gina_HE_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/G91_R3_Gina_HE_RFA.png"))]),
        ("Texture_Button_Unit_G91_R3_Gina_NPL_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/G91_R3_Gina_NPL_RFA.png"))]),
        ("Texture_Button_Unit_G91_R3_Gina_RKT_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/G91_R3_Gina_RKT_RFA.png"))]),
        ("Texture_Button_Unit_Harrier_CLU_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Harrier_CLU_UK.png"))]),
        ("Texture_Button_Unit_Harrier_GR5_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Harrier_GR5_UK.png"))]),
        ("Texture_Button_Unit_Harrier_HE1_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Harrier_HE1_UK.png"))]),
        ("Texture_Button_Unit_Harrier_HE2_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Harrier_HE2_UK.png"))]),
        ("Texture_Button_Unit_Harrier_RKT1_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Harrier_RKT1_UK.png"))]),
        ("Texture_Button_Unit_Harrier_RKT2_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Harrier_RKT2_UK.png"))]),
        ("Texture_Button_Unit_Harrier_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Harrier_UK.png"))]),
        ("Texture_Button_Unit_Jaguar_ATGM_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_ATGM_FR.png"))]),
        ("Texture_Button_Unit_Jaguar_CLU_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_CLU_UK.png"))]),
        ("Texture_Button_Unit_Jaguar_HE1_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_HE1_UK.png"))]),
        ("Texture_Button_Unit_Jaguar_HE2_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_HE2_UK.png"))]),
        ("Texture_Button_Unit_Jaguar_HE_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_HE_FR.png"))]),
        ("Texture_Button_Unit_Jaguar_RKT_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_RKT_FR.png"))]),
        ("Texture_Button_Unit_Jaguar_RKT_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_RKT_UK.png"))]),
        ("Texture_Button_Unit_Jaguar_SEAD2_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_SEAD2_FR.png"))]),
        ("Texture_Button_Unit_Jaguar_SEAD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_SEAD_FR.png"))]),
        ("Texture_Button_Unit_Jaguar_clu_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_clu_FR.png"))]),
        ("Texture_Button_Unit_Jaguar_nplm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_nplm_FR.png"))]),
        ("Texture_Button_Unit_Jaguar_overwing_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Jaguar_overwing_UK.png"))]),
        ("Texture_Button_Unit_L39ZO_CLU_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/L39ZO_CLU_DDR.png"))]),
        ("Texture_Button_Unit_L39ZO_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/L39ZO_DDR.png"))]),
        ("Texture_Button_Unit_L39ZO_HE1_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/L39ZO_HE1_DDR.png"))]),
        ("Texture_Button_Unit_L39ZO_HE1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/L39ZO_HE1_SOV.png"))]),
        ("Texture_Button_Unit_L39ZO_NPLM_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/L39ZO_NPLM_SOV.png"))]),
        ("Texture_Button_Unit_MQM_105_Aquila_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/MQM_105_Aquila_US.png"))]),
        ("Texture_Button_Unit_MiG_17PF_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_17PF_POL.png"))]),
        ("Texture_Button_Unit_MiG_21PFM_AA_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21PFM_AA_DDR.png"))]),
        ("Texture_Button_Unit_MiG_21PFM_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21PFM_DDR.png"))]),
        ("Texture_Button_Unit_MiG_21bis_AA2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_AA2_DDR.png"))]),
        ("Texture_Button_Unit_MiG_21bis_AA3_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_AA3_DDR.png"))]),
        ("Texture_Button_Unit_MiG_21bis_AA_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_AA_POL.png"))]),
        ("Texture_Button_Unit_MiG_21bis_CLU_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_CLU_DDR.png"))]),
        ("Texture_Button_Unit_MiG_21bis_HE_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_HE_DDR.png"))]),
        ("Texture_Button_Unit_MiG_21bis_HE_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_HE_POL.png"))]),
        ("Texture_Button_Unit_MiG_21bis_NPLM_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_NPLM_DDR.png"))]),
        ("Texture_Button_Unit_MiG_21bis_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_POL.png"))]),
        ("Texture_Button_Unit_MiG_21bis_RKT2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_RKT2_DDR.png"))]),
        ("Texture_Button_Unit_MiG_21bis_RKT2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_21bis_RKT2_POL.png"))]),
        ("Texture_Button_Unit_MiG_23BN_AT2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23BN_AT2_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23BN_AT_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23BN_AT_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23BN_CLU_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23BN_CLU_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23BN_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23BN_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23BN_KMGU_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23BN_KMGU_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23BN_RKT_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23BN_RKT_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23BN_nplm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23BN_nplm_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23MF_AA2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23MF_AA2_POL.png"))]),
        ("Texture_Button_Unit_MiG_23MF_AA_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23MF_AA_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23MF_AA_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23MF_AA_POL.png"))]),
        ("Texture_Button_Unit_MiG_23MF_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23MF_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23MLD_AA1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23MLD_AA1_SOV.png"))]),
        ("Texture_Button_Unit_MiG_23MLD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23MLD_AA1_SOV.png"))]),
        ("Texture_Button_Unit_MiG_23ML_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23ML_DDR.png"))]),
        ("Texture_Button_Unit_MiG_23ML_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_23ML_SOV.png"))]),
        ("Texture_Button_Unit_MiG_23P_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/default.png"))]),
        ("Texture_Button_Unit_MiG_25BM_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_25BM_SOV.png"))]),
        ("Texture_Button_Unit_MiG_25RBF_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_25RBF_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27K_AT1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27K_AT1_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27K_AT2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27K_AT2_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27K_LGB_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27K_LGB_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27K_SEAD_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27K_SEAD_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27M_CLU_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27M_CLU_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27M_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27M_bombe_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27M_bombe_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27M_napalm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27M_napalm_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27M_rkt_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27M_rkt_SOV.png"))]),
        ("Texture_Button_Unit_MiG_27M_sead_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_27M_sead_SOV.png"))]),
        ("Texture_Button_Unit_MiG_29_AA2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_29_AA2_POL.png"))]),
        ("Texture_Button_Unit_MiG_29_AA2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_29_AA2_SOV.png"))]),
        ("Texture_Button_Unit_MiG_29_AA3_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_29_AA2_SOV.png"))]),
        ("Texture_Button_Unit_MiG_29_AA_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_29_AA_DDR.png"))]),
        ("Texture_Button_Unit_MiG_29_AA_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_29_AA_POL.png"))]),
        ("Texture_Button_Unit_MiG_29_AA_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_29_AA_SOV.png"))]),
        ("Texture_Button_Unit_MiG_31M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_31M_SOV.png"))]),
        ("Texture_Button_Unit_MiG_31_AA1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_31_AA1_SOV.png"))]),
        ("Texture_Button_Unit_MiG_31_AA2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/MiG_31_AA2_SOV.png"))]),
        ("Texture_Button_Unit_Mirage_2000_C_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_2000_C_FR.png"))]),
        ("Texture_Button_Unit_Mirage_5_BA_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_5_BA_BEL.png"))]),
        ("Texture_Button_Unit_Mirage_5_BA_CLU_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_5_BA_CLU_BEL.png"))]),
        ("Texture_Button_Unit_Mirage_5_BA_MIRSIP_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_5_BA_MIRSIP_BEL.png"))]),
        ("Texture_Button_Unit_Mirage_5_BA_NPLM_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_5_BA_NPLM_BEL.png"))]),
        ("Texture_Button_Unit_Mirage_5_BA_RKT_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_5_BA_RKT_BEL.png"))]),
        ("Texture_Button_Unit_Mirage_5_F_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_5_F_FR.png"))]),
        ("Texture_Button_Unit_Mirage_5_F_clu_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_5_F_clu_FR.png"))]),
        ("Texture_Button_Unit_Mirage_5_F_nplm_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_5_F_nplm_FR.png"))]),
        ("Texture_Button_Unit_Mirage_F1_CT_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_F1_CT_FR.png"))]),
        ("Texture_Button_Unit_Mirage_F1_C_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_F1_C_FR.png"))]),
        ("Texture_Button_Unit_Mirage_III_E_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_III_E_FR.png"))]),
        ("Texture_Button_Unit_Mirage_IV_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_IV_FR.png"))]),
        ("Texture_Button_Unit_Mirage_IV_SEAD_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Mirage_IV_SEAD_FR.png"))]),
        ("Texture_Button_Unit_OA10A_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/OA10A_US.png"))]),
        ("Texture_Button_Unit_Pchela_1T_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Pchela_1T_SOV.png"))]),
        ("Texture_Button_Unit_Su_15TM_AA2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_15TM_AA2_SOV.png"))]),
        ("Texture_Button_Unit_Su_15TM_AA_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_15TM_AA_SOV.png"))]),
        ("Texture_Button_Unit_Su_17M4_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_17M4_SOV.png"))]),
        ("Texture_Button_Unit_Su_17_cluster_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_17_cluster_POL.png"))]),
        ("Texture_Button_Unit_Su_22_AT2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_AT2_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_AT_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_AT_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_AT_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_AT_POL.png"))]),
        ("Texture_Button_Unit_Su_22_AT_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_AT_SOV.png"))]),
        ("Texture_Button_Unit_Su_22_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_HE2_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_HE2_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_POL.png"))]),
        ("Texture_Button_Unit_Su_22_RKT2_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_RKT2_POL.png"))]),
        ("Texture_Button_Unit_Su_22_RKT_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_RKT_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_RKT_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_RKT_POL.png"))]),
        ("Texture_Button_Unit_Su_22_SEAD_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_SEAD_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_SEAD_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_SEAD_POL.png"))]),
        ("Texture_Button_Unit_Su_22_UPK_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_UPK_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_clu_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_clu_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_clu_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_clu_POL.png"))]),
        ("Texture_Button_Unit_Su_22_nplm_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_nplm_DDR.png"))]),
        ("Texture_Button_Unit_Su_22_nplm_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_22_nplm_POL.png"))]),
        ("Texture_Button_Unit_Su_24MP_EW_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24MP_EW_SOV.png"))]),
        ("Texture_Button_Unit_Su_24MP_SEAD2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24MP_SEAD2_SOV.png"))]),
        ("Texture_Button_Unit_Su_24MP_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24MP_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_AT1_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_AT1_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_AT2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_AT2_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_LGB2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_LGB2_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_LGB_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_LGB_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_clu2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_clu2_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_clu_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_clu_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_nplm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_nplm_SOV.png"))]),
        ("Texture_Button_Unit_Su_24M_thermo_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_24M_thermo_SOV.png"))]),
        ("Texture_Button_Unit_Su_25T_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_25T_SOV.png"))]),
        ("Texture_Button_Unit_Su_25_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_25_SOV.png"))]),
        ("Texture_Button_Unit_Su_25_clu_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_25_clu_SOV.png"))]),
        ("Texture_Button_Unit_Su_25_he_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_25_he_SOV.png"))]),
        ("Texture_Button_Unit_Su_25_nplm_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_25_nplm_SOV.png"))]),
        ("Texture_Button_Unit_Su_25_rkt2_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_25_rkt2_SOV.png"))]),
        ("Texture_Button_Unit_Su_25_rkt_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_25_rkt_SOV.png"))]),
        ("Texture_Button_Unit_Su_27K_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_27K_SOV.png"))]),
        ("Texture_Button_Unit_Su_27S_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/Su_27S_SOV.png"))]),
        ("Texture_Button_Unit_Super_Etendard_AT_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Super_Etendard_AT_FR.png"))]),
        ("Texture_Button_Unit_Super_Etendard_CLU_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Super_Etendard_CLU_FR.png"))]),
        ("Texture_Button_Unit_Super_Etendard_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Super_Etendard_FR.png"))]),
        ("Texture_Button_Unit_Super_Etendard_HE_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Super_Etendard_HE_FR.png"))]),
        ("Texture_Button_Unit_Tornado_ADV_HE_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tornado_ADV_HE_UK.png"))]),
        ("Texture_Button_Unit_Tornado_ADV_SEAD_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tornado_ADV_SEAD_UK.png"))]),
        ("Texture_Button_Unit_Tornado_ADV_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tornado_ADV_UK.png"))]),
        ("Texture_Button_Unit_Tornado_ADV_clu_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tornado_ADV_clu_UK.png"))]),
        ("Texture_Button_Unit_Tornado_IDS_AT1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tornado_IDS_AT1_RFA.png"))]),
        ("Texture_Button_Unit_Tornado_IDS_CLUS_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tornado_IDS_CLUS_RFA.png"))]),
        ("Texture_Button_Unit_Tornado_IDS_HE1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tornado_IDS_HE1_RFA.png"))]),
        ("Texture_Button_Unit_Tornado_IDS_MW1_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/Tornado_IDS_MW1_RFA.png"))]),

        ("Texture_Button_Unit_FOB_BEL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FOB_US.png"))]),
        ("Texture_Button_Unit_FOB_DDR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/FOB.png"))]),
        ("Texture_Button_Unit_FOB_FR", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FOB_US.png"))]),
        ("Texture_Button_Unit_FOB_NL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FOB_US.png"))]),
        ("Texture_Button_Unit_FOB_POL", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/FOB.png"))]),
        ("Texture_Button_Unit_FOB_RFA", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FOB_US.png"))]),
        ("Texture_Button_Unit_FOB_SOV", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/PACT/FOB.png"))]),
        ("Texture_Button_Unit_FOB_UK", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FOB_US.png"))]),
        ("Texture_Button_Unit_FOB_US", MAP [( ~/ComponentState/Normal, TUIResourceTexture( FileName = "GameData:/Assets/2D/Interface/Common/UnitsIcons/NATO/FOB_US.png"))]),
    ]
)
