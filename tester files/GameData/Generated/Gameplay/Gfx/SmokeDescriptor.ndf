// Ne pas éditer, ce fichier est généré par SmokeDescriptorFileWriter_Specific


export Descriptor_Smoke_Fumi105mm is TEntityDescriptor
(
    World        = WorldIndices_Smokes
    DescriptorId       = GUID:{e5616293-0f25-4b4d-b1f8-29953bd95a92}
    ClassNameForDebug  = 'Smoke_Fumi105mm'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        ~/SmokePositionModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = TTimelyDepictionReceiverFactory( DepictionDescriptor = Template_DescriptorSmoke_Depiction
            (
                Radius  = 88 * 26 * 2.83
                Height  = 88 * 26 * 2.83
                Density = 0.5
            ))
            DefaultVisibility  = True
        ),
        TSmokeModuleDescriptor
        (
            AltitudeGRU       = 88
            TimeToLive     = 60
            RadiusGRU         = 88
            Terrain        = ~/ETerrainType/MediumSmoke
        ),
    ]
)
export Descriptor_Smoke_Fumi120mm is TEntityDescriptor
(
    World        = WorldIndices_Smokes
    DescriptorId       = GUID:{7ba8c9e2-c632-408d-ba0a-f9f457893844}
    ClassNameForDebug  = 'Smoke_Fumi120mm'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        ~/SmokePositionModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = TTimelyDepictionReceiverFactory( DepictionDescriptor = Template_DescriptorSmoke_Depiction
            (
                Radius  = 106 * 26 * 2.83
                Height  = 106 * 26 * 2.83
                Density = 0.5
            ))
            DefaultVisibility  = True
        ),
        TSmokeModuleDescriptor
        (
            AltitudeGRU       = 106
            TimeToLive     = 60
            RadiusGRU         = 106
            Terrain        = ~/ETerrainType/MediumSmoke
        ),
    ]
)
export Descriptor_Smoke_Fumi120mm_mortier is TEntityDescriptor
(
    World        = WorldIndices_Smokes
    DescriptorId       = GUID:{cdb0f2c0-dc16-4584-8192-e4debf9dbd7a}
    ClassNameForDebug  = 'Smoke_Fumi120mm_mortier'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        ~/SmokePositionModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = TTimelyDepictionReceiverFactory( DepictionDescriptor = Template_DescriptorSmoke_Depiction
            (
                Radius  = 106 * 26 * 2.83
                Height  = 106 * 26 * 2.83
                Density = 0.5
            ))
            DefaultVisibility  = True
        ),
        TSmokeModuleDescriptor
        (
            AltitudeGRU       = 106
            TimeToLive     = 45
            RadiusGRU         = 106
            Terrain        = ~/ETerrainType/MediumSmoke
        ),
    ]
)
export Descriptor_Smoke_Fumi152mm is TEntityDescriptor
(
    World        = WorldIndices_Smokes
    DescriptorId       = GUID:{96d317ae-5de8-46ca-a6e0-53fd6c521f60}
    ClassNameForDebug  = 'Smoke_Fumi152mm'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        ~/SmokePositionModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = TTimelyDepictionReceiverFactory( DepictionDescriptor = Template_DescriptorSmoke_Depiction
            (
                Radius  = 124 * 26 * 2.83
                Height  = 124 * 26 * 2.83
                Density = 1.0
            ))
            DefaultVisibility  = True
        ),
        TSmokeModuleDescriptor
        (
            AltitudeGRU       = 124
            TimeToLive     = 60
            RadiusGRU         = 124
            Terrain        = ~/ETerrainType/MediumSmoke
        ),
    ]
)
export Descriptor_Smoke_Fumi203mm is TEntityDescriptor
(
    World        = WorldIndices_Smokes
    DescriptorId       = GUID:{b39be779-537c-48d2-a09c-3a8e0bca6bc3}
    ClassNameForDebug  = 'Smoke_Fumi203mm'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        ~/SmokePositionModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = TTimelyDepictionReceiverFactory( DepictionDescriptor = Template_DescriptorSmoke_Depiction
            (
                Radius  = 177 * 26 * 2.83
                Height  = 124 * 26 * 2.83
                Density = 1.0
            ))
            DefaultVisibility  = True
        ),
        TSmokeModuleDescriptor
        (
            AltitudeGRU       = 124
            TimeToLive     = 60
            RadiusGRU         = 177
            Terrain        = ~/ETerrainType/MediumSmoke
        ),
    ]
)
export Descriptor_Smoke_Fumi60mm is TEntityDescriptor
(
    World        = WorldIndices_Smokes
    DescriptorId       = GUID:{9e5cbce4-64d7-47a6-a968-004e3cf94faf}
    ClassNameForDebug  = 'Smoke_Fumi60mm'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        ~/SmokePositionModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = TTimelyDepictionReceiverFactory( DepictionDescriptor = Template_DescriptorSmoke_Depiction
            (
                Radius  = 53 * 26 * 2.83
                Height  = 53 * 26 * 2.83
                Density = 0.1
            ))
            DefaultVisibility  = True
        ),
        TSmokeModuleDescriptor
        (
            AltitudeGRU       = 53
            TimeToLive     = 60
            RadiusGRU         = 53
            Terrain        = ~/ETerrainType/MediumSmoke
        ),
    ]
)
export Descriptor_Smoke_Fumi81mm is TEntityDescriptor
(
    World        = WorldIndices_Smokes
    DescriptorId       = GUID:{af22ca6f-2562-479a-bcdf-eedab4359bdb}
    ClassNameForDebug  = 'Smoke_Fumi81mm'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        ~/SmokePositionModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = TTimelyDepictionReceiverFactory( DepictionDescriptor = Template_DescriptorSmoke_Depiction
            (
                Radius  = 71 * 26 * 2.83
                Height  = 71 * 26 * 2.83
                Density = 0.1
            ))
            DefaultVisibility  = True
        ),
        TSmokeModuleDescriptor
        (
            AltitudeGRU       = 71
            TimeToLive     = 45
            RadiusGRU         = 71
            Terrain        = ~/ETerrainType/MediumSmoke
        ),
    ]
)
export Descriptor_Smoke_Fumi_smoke_grenade is TEntityDescriptor
(
    World        = WorldIndices_Smokes
    DescriptorId       = GUID:{624a3481-61a2-400f-8f85-0347c6be64bc}
    ClassNameForDebug  = 'Smoke_Fumi_smoke_grenade'
    ModulesDescriptors = [
        ~/SimpleTypeUnitModuleDescriptor,
        ~/EmptyTagsModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        ~/SmokePositionModuleDescriptor,
        TApparenceModuleDescriptor
        (
            ReferenceMesh  = $/GFX/DepictionResources/Rien
            Depiction  = TTimelyDepictionReceiverFactory( DepictionDescriptor = Template_DescriptorSmoke_Depiction
            (
                Radius  = 53 * 26 * 2.83
                Height  = 53 * 26 * 2.83
                Density = 0.1
            ))
            DefaultVisibility  = True
        ),
        TSmokeModuleDescriptor
        (
            AltitudeGRU       = 53
            TimeToLive     = 20
            RadiusGRU         = 53
            Terrain        = ~/ETerrainType/MediumSmoke
        ),
    ]
)
