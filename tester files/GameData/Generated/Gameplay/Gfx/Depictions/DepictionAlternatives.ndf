// Ne pas éditer, ce fichier est généré par DepictionAlternativesFileWriter


Alternatives_2K11_KRUG_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_DDR_LOW ),
]
Alternatives_2K11_KRUG_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_POL_LOW ),
]
Alternatives_2K11_KRUG_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2K11_KRUG_SOV_LOW ),
]
Alternatives_2K12_KUB_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_DDR_LOW ),
]
Alternatives_2K12_KUB_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_POL_LOW ),
]
Alternatives_2K12_KUB_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2K12_KUB_SOV_LOW ),
]
Alternatives_2S19_MstaS_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S19_MstaS_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S19_MstaS_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S19_MstaS_SOV_LOW ),
]
Alternatives_2S1M_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1M_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1M_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1M_POL_LOW ),
]
Alternatives_2S1_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_DDR_LOW ),
]
Alternatives_2S1_Gvozdika_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_Gvozdika_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_Gvozdika_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_Gvozdika_SOV_LOW ),
]
Alternatives_2S1_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S1_POL_LOW ),
]
Alternatives_2S23_Nona_SVK_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S23_Nona_SVK_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S23_Nona_SVK_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_2S23_Nona_SVK_SOV_LOW ),
]
Alternatives_2S3M1_Akatsiya_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3M1_Akatsiya_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3M1_Akatsiya_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3M1_Akatsiya_SOV_LOW ),
]
Alternatives_2S3M_Akatsiya_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3M_Akatsiya_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3M_Akatsiya_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3M_Akatsiya_SOV_LOW ),
]
Alternatives_2S3_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S3_DDR_LOW ),
]
Alternatives_2S5_GiatsintS_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S5_GiatsintS_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S5_GiatsintS_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S5_GiatsintS_SOV_LOW ),
]
Alternatives_2S7M_Malka_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S7M_Malka_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S7M_Malka_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S7M_Malka_SOV_LOW ),
]
Alternatives_2S7_Pion_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S7_Pion_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S7_Pion_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S7_Pion_POL_LOW ),
]
Alternatives_2S9_Nona_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_2S9_Nona_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_2S9_Nona_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_2S9_Nona_SOV_LOW ),
]
Alternatives_81mm_mortar_Aero_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_Aero_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_Aero_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_Aero_US_LOW ),
]
Alternatives_81mm_mortar_CLU_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_CLU_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_CLU_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_CLU_UK_LOW ),
]
Alternatives_81mm_mortar_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_NG_US_LOW ),
]
Alternatives_81mm_mortar_Para_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_Para_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_Para_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_Para_UK_LOW ),
]
Alternatives_81mm_mortar_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_UK_LOW ),
]
Alternatives_81mm_mortar_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_81mm_mortar_US_LOW ),
]
Alternatives_A222_Bereg_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A222_Bereg_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A222_Bereg_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A222_Bereg_SOV_LOW ),
]
Alternatives_AEC_Militant_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AEC_Militant_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AEC_Militant_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AEC_Militant_UK_LOW ),
]
Alternatives_AIFV_B_50_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_50_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_50_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_50_BEL_LOW ),
]
Alternatives_AIFV_B_50_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_50_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_50_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_50_NL_LOW ),
]
Alternatives_AIFV_B_C25_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_C25_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_C25_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_C25_BEL_LOW ),
]
Alternatives_AIFV_B_C25_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_C25_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_C25_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_C25_NL_LOW ),
]
Alternatives_AIFV_B_CMD_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_CMD_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_CMD_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_CMD_BEL_LOW ),
]
Alternatives_AIFV_B_CMD_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_CMD_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_CMD_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_CMD_NL_LOW ),
]
Alternatives_AIFV_B_Cargo_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_Cargo_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_Cargo_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_Cargo_NL_LOW ),
]
Alternatives_AIFV_B_MILAN_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_MILAN_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_MILAN_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_MILAN_BEL_LOW ),
]
Alternatives_AIFV_B_Radar_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_Radar_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_Radar_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_Radar_NL_LOW ),
]
Alternatives_AIFV_B_TOW_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_TOW_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_TOW_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AIFV_B_TOW_NL_LOW ),
]
Alternatives_AML_60_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_60_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_60_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AML_60_FR_LOW ),
]
Alternatives_AML_60_Gendarmerie_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_60_Gendarmerie_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_60_Gendarmerie_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AML_60_Gendarmerie_FR_LOW ),
]
Alternatives_AML_90_CMD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_CMD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_CMD_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_CMD_FR_LOW ),
]
Alternatives_AML_90_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_FR_LOW ),
]
Alternatives_AML_90_Reserve_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_Reserve_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_Reserve_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AML_90_Reserve_FR_LOW ),
]
Alternatives_AMX_10_HOT_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_HOT_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_HOT_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_HOT_FR_LOW ),
]
Alternatives_AMX_10_PC_CMD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_PC_CMD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_PC_CMD_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_PC_CMD_FR_LOW ),
]
Alternatives_AMX_10_P_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_FR_LOW ),
]
Alternatives_AMX_10_P_MILAN_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_MILAN_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_MILAN_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_MILAN_FR_LOW ),
]
Alternatives_AMX_10_P_VOA_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_VOA_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_VOA_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_P_VOA_FR_LOW ),
]
Alternatives_AMX_10_RCR_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_RCR_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_RCR_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_RCR_FR_LOW ),
]
Alternatives_AMX_10_RC_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_RC_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_RC_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_10_RC_FR_LOW ),
]
Alternatives_AMX_13_90mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_90mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_90mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_90mm_FR_LOW ),
]
Alternatives_AMX_13_DCA_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_DCA_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_DCA_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_DCA_FR_LOW ),
]
Alternatives_AMX_13_VCI_12_7mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_VCI_12_7mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_VCI_12_7mm_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_VCI_12_7mm_FR_LOW ),
]
Alternatives_AMX_13_VCI_20mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_VCI_20mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_VCI_20mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_VCI_20mm_FR_LOW ),
]
Alternatives_AMX_13_mod56_CMD_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_CMD_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_CMD_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_CMD_BEL_LOW ),
]
Alternatives_AMX_13_mod56_MILAN_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_MILAN_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_MILAN_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_MILAN_BEL_LOW ),
]
Alternatives_AMX_13_mod56_Mortier_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_Mortier_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_Mortier_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_Mortier_BEL_LOW ),
]
Alternatives_AMX_13_mod56_VCI_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_VCI_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_VCI_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_13_mod56_VCI_BEL_LOW ),
]
Alternatives_AMX_30_AuF1_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_AuF1_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_AuF1_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_AuF1_FR_LOW ),
]
Alternatives_AMX_30_B2_Brennus_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_Brennus_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_Brennus_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_Brennus_FR_LOW ),
]
Alternatives_AMX_30_B2_CMD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_CMD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_CMD_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_CMD_FR_LOW ),
]
Alternatives_AMX_30_B2_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B2_FR_LOW ),
]
Alternatives_AMX_30_B_CMD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B_CMD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B_CMD_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B_CMD_FR_LOW ),
]
Alternatives_AMX_30_B_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_B_FR_LOW ),
]
Alternatives_AMX_30_EBG_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_EBG_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_EBG_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_AMX_30_EBG_FR_LOW ),
]
Alternatives_ASU_85_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ASU_85_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ASU_85_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ASU_85_CMD_POL_LOW ),
]
Alternatives_ASU_85_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ASU_85_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ASU_85_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ASU_85_POL_LOW ),
]
Alternatives_AT_2A45_SprutB_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_2A45_SprutB_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_2A45_SprutB_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_2A45_SprutB_SOV_LOW ),
]
Alternatives_AT_D44_85mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_DDR_LOW ),
]
Alternatives_AT_D44_85mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_POL_LOW ),
]
Alternatives_AT_D44_85mm_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D44_85mm_VDV_SOV_LOW ),
]
Alternatives_AT_D48_85mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D48_85mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D48_85mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_D48_85mm_POL_LOW ),
]
Alternatives_AT_KSM65_100mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_KSM65_100mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_KSM65_100mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_KSM65_100mm_SOV_LOW ),
]
Alternatives_AT_T12R_Ruta_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12R_Ruta_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12R_Ruta_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12R_Ruta_SOV_LOW ),
]
Alternatives_AT_T12_Rapira_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12_Rapira_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12_Rapira_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12_Rapira_DDR_LOW ),
]
Alternatives_AT_T12_Rapira_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12_Rapira_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12_Rapira_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_T12_Rapira_SOV_LOW ),
]
Alternatives_AT_ZiS2_57mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_ZiS2_57mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_ZiS2_57mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_ZiS2_57mm_DDR_LOW ),
]
Alternatives_AT_vz52_85mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_vz52_85mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_vz52_85mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AT_vz52_85mm_DDR_LOW ),
]
Alternatives_ATteam_Fagot_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_DDR_LOW ),
]
Alternatives_ATteam_Fagot_FJ_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_FJ_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_FJ_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_FJ_DDR_LOW ),
]
Alternatives_ATteam_Fagot_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Fagot_SOV_LOW ),
]
Alternatives_ATteam_ITOW_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_NG_US_LOW ),
]
Alternatives_ATteam_ITOW_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_NL_LOW ),
]
Alternatives_ATteam_ITOW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_ITOW_US_LOW ),
]
Alternatives_ATteam_KonkursM_TTsko_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_KonkursM_TTsko_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_KonkursM_TTsko_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_KonkursM_TTsko_SOV_LOW ),
]
Alternatives_ATteam_Konkurs_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_DDR_LOW ),
]
Alternatives_ATteam_Konkurs_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_SOV_LOW ),
]
Alternatives_ATteam_Konkurs_TTsko_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_TTsko_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_TTsko_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Konkurs_TTsko_SOV_LOW ),
]
Alternatives_ATteam_Milan_1_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_BEL_LOW ),
]
Alternatives_ATteam_Milan_1_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_FR_LOW ),
]
Alternatives_ATteam_Milan_1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_RFA_LOW ),
]
Alternatives_ATteam_Milan_1_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_UK_LOW ),
]
Alternatives_ATteam_Milan_1_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_para_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_para_BEL_LOW ),
]
Alternatives_ATteam_Milan_1_para_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_para_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_para_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_1_para_FR_LOW ),
]
Alternatives_ATteam_Milan_2_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_BEL_LOW ),
]
Alternatives_ATteam_Milan_2_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_FR_LOW ),
]
Alternatives_ATteam_Milan_2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_RFA_LOW ),
]
Alternatives_ATteam_Milan_2_RIMa_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_RIMa_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_RIMa_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_RIMa_FR_LOW ),
]
Alternatives_ATteam_Milan_2_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_UK_LOW ),
]
Alternatives_ATteam_Milan_2_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_BEL_LOW ),
]
Alternatives_ATteam_Milan_2_para_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_FR_LOW ),
]
Alternatives_ATteam_Milan_2_para_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_RFA_LOW ),
]
Alternatives_ATteam_Milan_2_para_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_Milan_2_para_UK_LOW ),
]
Alternatives_ATteam_RCL_B11_Reserve_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_B11_Reserve_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_B11_Reserve_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_B11_Reserve_SOV_LOW ),
]
Alternatives_ATteam_RCL_M40A1_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_FR_LOW ),
]
Alternatives_ATteam_RCL_M40A1_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_NG_US_LOW ),
]
Alternatives_ATteam_RCL_M40A1_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_NL_LOW ),
]
Alternatives_ATteam_RCL_M40A1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_M40A1_RFA_LOW ),
]
Alternatives_ATteam_RCL_SPG9_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_DDR_LOW ),
]
Alternatives_ATteam_RCL_SPG9_DShV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_DShV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_DShV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_DShV_SOV_LOW ),
]
Alternatives_ATteam_RCL_SPG9_FJ_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_FJ_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_FJ_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_FJ_DDR_LOW ),
]
Alternatives_ATteam_RCL_SPG9_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_POL_LOW ),
]
Alternatives_ATteam_RCL_SPG9_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_Para_POL_LOW ),
]
Alternatives_ATteam_RCL_SPG9_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_SOV_LOW ),
]
Alternatives_ATteam_RCL_SPG9_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_RCL_SPG9_VDV_SOV_LOW ),
]
Alternatives_ATteam_TOW2A_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2A_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2A_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2A_US_LOW ),
]
Alternatives_ATteam_TOW2_Aero_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_Aero_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_Aero_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_Aero_US_LOW ),
]
Alternatives_ATteam_TOW2_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_NL_LOW ),
]
Alternatives_ATteam_TOW2_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_US_LOW ),
]
Alternatives_ATteam_TOW2_para_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_para_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_para_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW2_para_US_LOW ),
]
Alternatives_ATteam_TOW_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW_NL_LOW ),
]
Alternatives_ATteam_TOW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ATteam_TOW_US_LOW ),
]
Alternatives_Alvis_Stalwart_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alvis_Stalwart_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alvis_Stalwart_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alvis_Stalwart_UK_LOW ),
]
Alternatives_Atteam_Dragon_Marines_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Dragon_Marines_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Dragon_Marines_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Dragon_Marines_NL_LOW ),
]
Alternatives_Atteam_Fagot_DShV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_DShV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_DShV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_DShV_SOV_LOW ),
]
Alternatives_Atteam_Fagot_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_POL_LOW ),
]
Alternatives_Atteam_Fagot_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_Para_POL_LOW ),
]
Alternatives_Atteam_Fagot_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Fagot_VDV_SOV_LOW ),
]
Alternatives_Atteam_Konkurs_DShV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_DShV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_DShV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_DShV_SOV_LOW ),
]
Alternatives_Atteam_Konkurs_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_POL_LOW ),
]
Alternatives_Atteam_Konkurs_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Atteam_Konkurs_VDV_SOV_LOW ),
]
Alternatives_BAV_485_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BAV_485_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BAV_485_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BAV_485_POL_LOW ),
]
Alternatives_BAV_485_Supply_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BAV_485_Supply_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BAV_485_Supply_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BAV_485_Supply_POL_LOW ),
]
Alternatives_BM14M_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM14M_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM14M_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BM14M_POL_LOW ),
]
Alternatives_BM21V_GradV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21V_GradV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21V_GradV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21V_GradV_SOV_LOW ),
]
Alternatives_BM21_Grad_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_DDR_LOW ),
]
Alternatives_BM21_Grad_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_POL_LOW ),
]
Alternatives_BM21_Grad_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BM21_Grad_SOV_LOW ),
]
Alternatives_BM24M_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_DDR_LOW ),
]
Alternatives_BM24M_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_POL_LOW ),
]
Alternatives_BM24M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BM24M_SOV_LOW ),
]
Alternatives_BM27_Uragan_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM27_Uragan_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM27_Uragan_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BM27_Uragan_SOV_LOW ),
]
Alternatives_BM30_Smerch_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BM30_Smerch_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BM30_Smerch_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BM30_Smerch_SOV_LOW ),
]
Alternatives_BMD_1K_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1K_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1K_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1K_CMD_SOV_LOW ),
]
Alternatives_BMD_1P_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1P_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1P_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1P_SOV_LOW ),
]
Alternatives_BMD_1_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_CMD_SOV_LOW ),
]
Alternatives_BMD_1_Reostat_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_Reostat_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_Reostat_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_Reostat_SOV_LOW ),
]
Alternatives_BMD_1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_1_SOV_LOW ),
]
Alternatives_BMD_2_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_2_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_2_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_2_CMD_SOV_LOW ),
]
Alternatives_BMD_2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_2_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_2_SOV_LOW ),
]
Alternatives_BMD_3_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_3_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_3_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_3_SOV_LOW ),
]
Alternatives_BMD_3_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_3_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_3_reco_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMD_3_reco_SOV_LOW ),
]
Alternatives_BMP_1PG_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1PG_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1PG_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1PG_SOV_LOW ),
]
Alternatives_BMP_1P_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_DDR_LOW ),
]
Alternatives_BMP_1P_Konkurs_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_Konkurs_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_Konkurs_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_Konkurs_DDR_LOW ),
]
Alternatives_BMP_1P_Konkurs_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_Konkurs_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_Konkurs_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_Konkurs_SOV_LOW ),
]
Alternatives_BMP_1P_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_SOV_LOW ),
]
Alternatives_BMP_1P_reco_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_DDR_LOW ),
]
Alternatives_BMP_1P_reco_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_POL_LOW ),
]
Alternatives_BMP_1P_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1P_reco_SOV_LOW ),
]
Alternatives_BMP_1_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_DDR_LOW ),
]
Alternatives_BMP_1_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_POL_LOW ),
]
Alternatives_BMP_1_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_CMD_SOV_LOW ),
]
Alternatives_BMP_1_SP1_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP1_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP1_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP1_DDR_LOW ),
]
Alternatives_BMP_1_SP2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_DDR_LOW ),
]
Alternatives_BMP_1_SP2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_POL_LOW ),
]
Alternatives_BMP_1_SP2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_SOV_LOW ),
]
Alternatives_BMP_1_SP2_reco_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_reco_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_reco_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_1_SP2_reco_POL_LOW ),
]
Alternatives_BMP_2AG_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2AG_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2AG_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2AG_SOV_LOW ),
]
Alternatives_BMP_2D_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2D_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2D_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2D_SOV_LOW ),
]
Alternatives_BMP_2D_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2D_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2D_reco_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2D_reco_SOV_LOW ),
]
Alternatives_BMP_2_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_CMD_SOV_LOW ),
]
Alternatives_BMP_2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_DDR_LOW ),
]
Alternatives_BMP_2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_POL_LOW ),
]
Alternatives_BMP_2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_SOV_LOW ),
]
Alternatives_BMP_2_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_reco_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_2_reco_SOV_LOW ),
]
Alternatives_BMP_3_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_3_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_3_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle3" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BMP_3_SOV_LOW ),
]
Alternatives_BRDM_1_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_DDR_LOW ),
]
Alternatives_BRDM_1_DShK_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_DShK_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_DShK_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_DShK_POL_LOW ),
]
Alternatives_BRDM_1_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_POL_LOW ),
]
Alternatives_BRDM_1_PSNR1_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_PSNR1_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_PSNR1_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_1_PSNR1_POL_LOW ),
]
Alternatives_BRDM_2_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_DDR_LOW ),
]
Alternatives_BRDM_2_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_POL_LOW ),
]
Alternatives_BRDM_2_CMD_R5_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_R5_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_R5_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_R5_POL_LOW ),
]
Alternatives_BRDM_2_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_CMD_SOV_LOW ),
]
Alternatives_BRDM_2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_DDR_LOW ),
]
Alternatives_BRDM_2_Konkurs_M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_M_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_M_SOV_LOW ),
]
Alternatives_BRDM_2_Konkurs_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_POL_LOW ),
]
Alternatives_BRDM_2_Konkurs_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Konkurs_SOV_LOW ),
]
Alternatives_BRDM_2_Malyu_P_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_POL_LOW ),
]
Alternatives_BRDM_2_Malyu_P_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_SOV_LOW ),
]
Alternatives_BRDM_2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_POL_LOW ),
]
Alternatives_BRDM_2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_2_SOV_LOW ),
]
Alternatives_BRDM_Konkurs_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Konkurs_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Konkurs_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Konkurs_DDR_LOW ),
]
Alternatives_BRDM_Malyu_P_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Malyu_P_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Malyu_P_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Malyu_P_DDR_LOW ),
]
Alternatives_BRDM_Strela_1_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_DDR_LOW ),
]
Alternatives_BRDM_Strela_1_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_POL_LOW ),
]
Alternatives_BRDM_Strela_1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRDM_Strela_1_SOV_LOW ),
]
Alternatives_BRM_1_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_DDR_LOW ),
]
Alternatives_BRM_1_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_POL_LOW ),
]
Alternatives_BRM_1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BRM_1_SOV_LOW ),
]
Alternatives_BTR_152A_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152A_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152A_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152A_DDR_LOW ),
]
Alternatives_BTR_152A_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152A_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152A_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152A_SOV_LOW ),
]
Alternatives_BTR_152K_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152K_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152K_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152K_SOV_LOW ),
]
Alternatives_BTR_152S_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152S_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152S_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_152S_CMD_SOV_LOW ),
]
Alternatives_BTR_40A_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40A_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40A_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40A_SOV_LOW ),
]
Alternatives_BTR_40B_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40B_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40B_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40B_CMD_SOV_LOW ),
]
Alternatives_BTR_40_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_40_SOV_LOW ),
]
Alternatives_BTR_50_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_CMD_DDR_LOW ),
]
Alternatives_BTR_50_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_DDR_LOW ),
]
Alternatives_BTR_50_MRF_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_MRF_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_MRF_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_50_MRF_DDR_LOW ),
]
Alternatives_BTR_60_CHAIKA_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CHAIKA_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CHAIKA_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CHAIKA_CMD_DDR_LOW ),
]
Alternatives_BTR_60_CHAIKA_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CHAIKA_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CHAIKA_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CHAIKA_CMD_SOV_LOW ),
]
Alternatives_BTR_60_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CMD_DDR_LOW ),
]
Alternatives_BTR_60_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_CMD_SOV_LOW ),
]
Alternatives_BTR_60_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_DDR_LOW ),
]
Alternatives_BTR_60_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_SOV_LOW ),
]
Alternatives_BTR_60_reco_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_reco_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_reco_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_reco_DDR_LOW ),
]
Alternatives_BTR_60_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_reco_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_60_reco_SOV_LOW ),
]
Alternatives_BTR_70D_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70D_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70D_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70D_SOV_LOW ),
]
Alternatives_BTR_70_AGS_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_AGS_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_AGS_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_AGS_SOV_LOW ),
]
Alternatives_BTR_70_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_DDR_LOW ),
]
Alternatives_BTR_70_MP_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_MP_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_MP_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_MP_SOV_LOW ),
]
Alternatives_BTR_70_Rys_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_Rys_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_Rys_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_Rys_SOV_LOW ),
]
Alternatives_BTR_70_S5_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_S5_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_S5_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_S5_SOV_LOW ),
]
Alternatives_BTR_70_S8_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_S8_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_S8_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_S8_SOV_LOW ),
]
Alternatives_BTR_70_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_70_SOV_LOW ),
]
Alternatives_BTR_80_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_80_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_80_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_80_CMD_SOV_LOW ),
]
Alternatives_BTR_80_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_80_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_80_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_80_SOV_LOW ),
]
Alternatives_BTR_D_Robot_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_Robot_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_Robot_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_Robot_SOV_LOW ),
]
Alternatives_BTR_D_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_SOV_LOW ),
]
Alternatives_BTR_D_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_reco_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_D_reco_SOV_LOW ),
]
Alternatives_BTR_ZD_Skrezhet_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_ZD_Skrezhet_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_ZD_Skrezhet_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_BTR_ZD_Skrezhet_SOV_LOW ),
]
Alternatives_Bedford_MJ_4t_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bedford_MJ_4t_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bedford_MJ_4t_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bedford_MJ_4t_UK_LOW ),
]
Alternatives_Bedford_MJ_4t_trans_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bedford_MJ_4t_trans_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bedford_MJ_4t_trans_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bedford_MJ_4t_trans_UK_LOW ),
]
Alternatives_Bofors_40mm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bofors_40mm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bofors_40mm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bofors_40mm_RFA_LOW ),
]
Alternatives_Bofors_40mm_capture_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bofors_40mm_capture_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bofors_40mm_capture_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bofors_40mm_capture_DDR_LOW ),
]
Alternatives_Buk_9K37M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Buk_9K37M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Buk_9K37M_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Buk_9K37M_SOV_LOW ),
]
Alternatives_CGage_Peacekeeper_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CGage_Peacekeeper_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CGage_Peacekeeper_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_CGage_Peacekeeper_US_LOW ),
]
Alternatives_CGage_V150_Commando_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CGage_V150_Commando_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CGage_V150_Commando_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_CGage_V150_Commando_US_LOW ),
]
Alternatives_CUCV_AGL_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_AGL_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_AGL_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_AGL_US_LOW ),
]
Alternatives_CUCV_HMG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_HMG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_HMG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_HMG_US_LOW ),
]
Alternatives_CUCV_Hellfire_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_Hellfire_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_Hellfire_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_Hellfire_US_LOW ),
]
Alternatives_CUCV_MP_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_MP_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_MP_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_MP_US_LOW ),
]
Alternatives_CUCV_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_US_LOW ),
]
Alternatives_CUCV_trans_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_trans_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_trans_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CUCV_trans_US_LOW ),
]
Alternatives_Centurion_AVRE_105_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Centurion_AVRE_105_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Centurion_AVRE_105_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Centurion_AVRE_105_UK_LOW ),
]
Alternatives_Challenger_1_Mk1_CMD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk1_CMD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk1_CMD_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk1_CMD_UK_LOW ),
]
Alternatives_Challenger_1_Mk1_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk1_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk1_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk1_UK_LOW ),
]
Alternatives_Challenger_1_Mk3_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk3_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk3_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Challenger_1_Mk3_UK_LOW ),
]
Alternatives_Crotale_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Crotale_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Crotale_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Crotale_FR_LOW ),
]
Alternatives_DAF_YA_4400_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YA_4400_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YA_4400_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YA_4400_NL_LOW ),
]
Alternatives_DAF_YA_4400_supply_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YA_4400_supply_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YA_4400_supply_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YA_4400_supply_NL_LOW ),
]
Alternatives_DAF_YHZ_2300_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YHZ_2300_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YHZ_2300_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YHZ_2300_NL_LOW ),
]
Alternatives_DAF_YHZ_2300_trans_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YHZ_2300_trans_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YHZ_2300_trans_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DAF_YHZ_2300_trans_NL_LOW ),
]
Alternatives_DANA_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DANA_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DANA_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DANA_POL_LOW ),
]
Alternatives_DCA_53T2_20mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_53T2_20mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_53T2_20mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_53T2_20mm_FR_LOW ),
]
Alternatives_DCA_53T2_20mm_Para_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_53T2_20mm_Para_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_53T2_20mm_Para_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_53T2_20mm_Para_FR_LOW ),
]
Alternatives_DCA_76T2_20mm_CPA_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_76T2_20mm_CPA_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_76T2_20mm_CPA_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_76T2_20mm_CPA_FR_LOW ),
]
Alternatives_DCA_AZP_S60_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_DDR_LOW ),
]
Alternatives_DCA_AZP_S60_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_POL_LOW ),
]
Alternatives_DCA_AZP_S60_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_AZP_S60_SOV_LOW ),
]
Alternatives_DCA_Bofors_L60_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Bofors_L60_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Bofors_L60_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Bofors_L60_FR_LOW ),
]
Alternatives_DCA_Bofors_upgrade_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Bofors_upgrade_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Bofors_upgrade_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Bofors_upgrade_NL_LOW ),
]
Alternatives_DCA_FASTA_4_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FASTA_4_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FASTA_4_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FASTA_4_DDR_LOW ),
]
Alternatives_DCA_FK20_2_20mm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FK20_2_20mm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FK20_2_20mm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FK20_2_20mm_RFA_LOW ),
]
Alternatives_DCA_FK20_2_20mm_Zwillinge_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FK20_2_20mm_Zwillinge_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FK20_2_20mm_Zwillinge_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_FK20_2_20mm_Zwillinge_RFA_LOW ),
]
Alternatives_DCA_I_Hawk_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_BEL_LOW ),
]
Alternatives_DCA_I_Hawk_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_NL_LOW ),
]
Alternatives_DCA_I_Hawk_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_RFA_LOW ),
]
Alternatives_DCA_I_Hawk_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_US_LOW ),
]
Alternatives_DCA_I_Hawk_capture_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_capture_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_capture_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_I_Hawk_capture_DDR_LOW ),
]
Alternatives_DCA_Javelin_LML_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Javelin_LML_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Javelin_LML_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Javelin_LML_UK_LOW ),
]
Alternatives_DCA_KS19_100mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_KS19_100mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_KS19_100mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_KS19_100mm_DDR_LOW ),
]
Alternatives_DCA_KS30_130mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_KS30_130mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_KS30_130mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_KS30_130mm_SOV_LOW ),
]
Alternatives_DCA_M167A2_Vulcan_20mm_Aero_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167A2_Vulcan_20mm_Aero_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167A2_Vulcan_20mm_Aero_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167A2_Vulcan_20mm_Aero_US_LOW ),
]
Alternatives_DCA_M167A2_Vulcan_20mm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167A2_Vulcan_20mm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167A2_Vulcan_20mm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167A2_Vulcan_20mm_US_LOW ),
]
Alternatives_DCA_M167_Vulcan_20mm_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_BEL_LOW ),
]
Alternatives_DCA_M167_Vulcan_20mm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_US_LOW ),
]
Alternatives_DCA_M167_Vulcan_20mm_nonPara_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_nonPara_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_nonPara_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_20mm_nonPara_US_LOW ),
]
Alternatives_DCA_M167_Vulcan_para_20mm_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_para_20mm_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_para_20mm_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M167_Vulcan_para_20mm_BEL_LOW ),
]
Alternatives_DCA_M55_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M55_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M55_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_M55_NL_LOW ),
]
Alternatives_DCA_Oerlikon_GDF_002_35mm_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Oerlikon_GDF_002_35mm_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Oerlikon_GDF_002_35mm_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Oerlikon_GDF_002_35mm_UK_LOW ),
]
Alternatives_DCA_Rapier_Darkfire_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_Darkfire_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_Darkfire_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_Darkfire_UK_LOW ),
]
Alternatives_DCA_Rapier_FSA_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_FSA_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_FSA_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_FSA_UK_LOW ),
]
Alternatives_DCA_Rapier_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_Rapier_UK_LOW ),
]
Alternatives_DCA_XM85_Chaparral_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_XM85_Chaparral_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_XM85_Chaparral_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_XM85_Chaparral_US_LOW ),
]
Alternatives_DCA_XMIM_115A_Roland_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_XMIM_115A_Roland_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_XMIM_115A_Roland_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_XMIM_115A_Roland_US_LOW ),
]
Alternatives_DCA_ZPU4_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZPU4_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZPU4_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZPU4_DDR_LOW ),
]
Alternatives_DCA_ZPU4_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZPU4_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZPU4_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZPU4_POL_LOW ),
]
Alternatives_DCA_ZUR_23_2S_JOD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_POL_LOW ),
]
Alternatives_DCA_ZUR_23_2S_JOD_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_Para_POL_LOW ),
]
Alternatives_DCA_ZU_23_2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_DDR_LOW ),
]
Alternatives_DCA_ZU_23_2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_POL_LOW ),
]
Alternatives_DCA_ZU_23_2_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_Para_POL_LOW ),
]
Alternatives_DCA_ZU_23_2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_SOV_LOW ),
]
Alternatives_DCA_ZU_23_2_TTsko_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_TTsko_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_TTsko_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_TTsko_SOV_LOW ),
]
Alternatives_DCA_ZU_23_2_nonPara_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_nonPara_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_nonPara_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_DCA_ZU_23_2_nonPara_SOV_LOW ),
]
Alternatives_DEP_M109A2 is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_DEP_M109A2 ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_DEP_M109A2_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_DEP_M109A2_LOW ),
]
Alternatives_Dragoon_300_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Dragoon_300_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Dragoon_300_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Dragoon_300_US_LOW ),
]
Alternatives_EBR_90mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_EBR_90mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_EBR_90mm_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_EBR_90mm_FR_LOW ),
]
Alternatives_ERC_90_Sagaie_CMD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_CMD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_CMD_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_CMD_FR_LOW ),
]
Alternatives_ERC_90_Sagaie_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_FR_LOW ),
]
Alternatives_ERC_90_Sagaie_reco_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_reco_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_reco_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_ERC_90_Sagaie_reco_FR_LOW ),
]
Alternatives_FAV_AGL_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_AGL_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_AGL_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_AGL_US_LOW ),
]
Alternatives_FAV_HMG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_HMG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_HMG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_HMG_US_LOW ),
]
Alternatives_FAV_TOW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_TOW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_TOW_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_TOW_US_LOW ),
]
Alternatives_FAV_trans_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_trans_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_trans_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_trans_US_LOW ),
]
Alternatives_FH70_155mm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FH70_155mm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FH70_155mm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FH70_155mm_RFA_LOW ),
]
Alternatives_FH70_155mm_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FH70_155mm_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FH70_155mm_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FH70_155mm_UK_LOW ),
]
Alternatives_FV101_Scorpion_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_BEL_LOW ),
]
Alternatives_FV101_Scorpion_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_UK_LOW ),
]
Alternatives_FV101_Scorpion_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_para_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV101_Scorpion_para_BEL_LOW ),
]
Alternatives_FV102_Striker_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_BEL_LOW ),
]
Alternatives_FV102_Striker_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_UK_LOW ),
]
Alternatives_FV102_Striker_para_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_para_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_para_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV102_Striker_para_UK_LOW ),
]
Alternatives_FV103_Spartan_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_BEL_LOW ),
]
Alternatives_FV103_Spartan_GSR_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_GSR_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_GSR_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_GSR_UK_LOW ),
]
Alternatives_FV103_Spartan_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_UK_LOW ),
]
Alternatives_FV103_Spartan_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_para_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV103_Spartan_para_BEL_LOW ),
]
Alternatives_FV105_Sultan_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_BEL_LOW ),
]
Alternatives_FV105_Sultan_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_UK_LOW ),
]
Alternatives_FV105_Sultan_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_para_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_para_BEL_LOW ),
]
Alternatives_FV105_Sultan_para_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_para_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_para_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV105_Sultan_para_UK_LOW ),
]
Alternatives_FV107_Scimitar_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_BEL_LOW ),
]
Alternatives_FV107_Scimitar_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_UK_LOW ),
]
Alternatives_FV107_Scimitar_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_para_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV107_Scimitar_para_BEL_LOW ),
]
Alternatives_FV120_Spartan_MCT_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV120_Spartan_MCT_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV120_Spartan_MCT_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV120_Spartan_MCT_UK_LOW ),
]
Alternatives_FV4003_Centurion_AVRE_ROMOR_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4003_Centurion_AVRE_ROMOR_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4003_Centurion_AVRE_ROMOR_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV4003_Centurion_AVRE_ROMOR_UK_LOW ),
]
Alternatives_FV4003_Centurion_AVRE_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4003_Centurion_AVRE_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4003_Centurion_AVRE_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV4003_Centurion_AVRE_UK_LOW ),
]
Alternatives_FV4201_Chieftain_CMD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_CMD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_CMD_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_CMD_UK_LOW ),
]
Alternatives_FV4201_Chieftain_Mk11_CMD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk11_CMD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk11_CMD_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk11_CMD_UK_LOW ),
]
Alternatives_FV4201_Chieftain_Mk11_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk11_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk11_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk11_UK_LOW ),
]
Alternatives_FV4201_Chieftain_Mk6_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk6_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk6_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk6_UK_LOW ),
]
Alternatives_FV4201_Chieftain_Mk9_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk9_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk9_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_Mk9_UK_LOW ),
]
Alternatives_FV4201_Chieftain_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV4201_Chieftain_UK_LOW ),
]
Alternatives_FV432_CMD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_CMD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_CMD_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_CMD_UK_LOW ),
]
Alternatives_FV432_MILAN_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_MILAN_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_MILAN_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_MILAN_UK_LOW ),
]
Alternatives_FV432_Mortar_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_Mortar_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_Mortar_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_Mortar_UK_LOW ),
]
Alternatives_FV432_Rarden_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_Rarden_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_Rarden_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_Rarden_UK_LOW ),
]
Alternatives_FV432_SCAT_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_SCAT_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_SCAT_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_SCAT_UK_LOW ),
]
Alternatives_FV432_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_UK_LOW ),
]
Alternatives_FV432_WOMBAT_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_WOMBAT_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_WOMBAT_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle3" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_WOMBAT_UK_LOW ),
]
Alternatives_FV432_supply_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_supply_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_supply_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV432_supply_UK_LOW ),
]
Alternatives_FV433_Abbot_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV433_Abbot_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV433_Abbot_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV433_Abbot_UK_LOW ),
]
Alternatives_FV438_Swingfire_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV438_Swingfire_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV438_Swingfire_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FV438_Swingfire_UK_LOW ),
]
Alternatives_FV601_Saladin_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV601_Saladin_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV601_Saladin_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV601_Saladin_UK_LOW ),
]
Alternatives_FV603_Saracen_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV603_Saracen_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV603_Saracen_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV603_Saracen_UK_LOW ),
]
Alternatives_FV721_Fox_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FV721_Fox_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FV721_Fox_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_FV721_Fox_UK_LOW ),
]
Alternatives_Faun_Kraka_20mm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_20mm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_20mm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_20mm_RFA_LOW ),
]
Alternatives_Faun_Kraka_Log_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_Log_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_Log_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_Log_RFA_LOW ),
]
Alternatives_Faun_Kraka_TOW_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA_LOW ),
]
Alternatives_Faun_kraka_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_kraka_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_kraka_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_kraka_RFA_LOW ),
]
Alternatives_Ferret_Mk2_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ferret_Mk2_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ferret_Mk2_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Ferret_Mk2_UK_LOW ),
]
Alternatives_GAZ_46_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_DDR_LOW ),
]
Alternatives_GAZ_46_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_POL_LOW ),
]
Alternatives_GAZ_46_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_46_SOV_LOW ),
]
Alternatives_GAZ_66B_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_POL_LOW ),
]
Alternatives_GAZ_66B_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_SOV_LOW ),
]
Alternatives_GAZ_66B_ZU_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_ZU_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_ZU_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_ZU_SOV_LOW ),
]
Alternatives_GAZ_66B_supply_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_supply_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_supply_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_supply_POL_LOW ),
]
Alternatives_GAZ_66B_supply_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_supply_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_supply_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66B_supply_SOV_LOW ),
]
Alternatives_GAZ_66_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_POL_LOW ),
]
Alternatives_GAZ_66_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_SOV_LOW ),
]
Alternatives_GAZ_66_supply_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_supply_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_supply_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_supply_SOV_LOW ),
]
Alternatives_GAZ_66_trans_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_trans_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_trans_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GAZ_66_trans_POL_LOW ),
]
Alternatives_GTMU_1D_AGS_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_AGS_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_AGS_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_AGS_SOV_LOW ),
]
Alternatives_GTMU_1D_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_SOV_LOW ),
]
Alternatives_GTMU_1D_SPG9_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_SPG9_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_SPG9_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_SPG9_SOV_LOW ),
]
Alternatives_GTMU_1D_ZU_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_ZU_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_ZU_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_GTMU_1D_ZU_SOV_LOW ),
]
Alternatives_Gama_Goat_supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gama_Goat_supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gama_Goat_supply_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gama_Goat_supply_US_LOW ),
]
Alternatives_Gama_Goat_trans_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gama_Goat_trans_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gama_Goat_trans_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gama_Goat_trans_US_LOW ),
]
Alternatives_Gepard_1A2_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_BEL_LOW ),
]
Alternatives_Gepard_1A2_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_NL_LOW ),
]
Alternatives_Gepard_1A2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gepard_1A2_RFA_LOW ),
]
Alternatives_HEMTT_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HEMTT_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HEMTT_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HEMTT_US_LOW ),
]
Alternatives_HMGteam_AANF1_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_FR_LOW ),
]
Alternatives_HMGteam_AANF1_Reserve_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_Reserve_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_Reserve_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_Reserve_FR_LOW ),
]
Alternatives_HMGteam_AANF1_para_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_para_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_para_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AANF1_para_FR_LOW ),
]
Alternatives_HMGteam_AGS17_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_DDR_LOW ),
]
Alternatives_HMGteam_AGS17_DShV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_DShV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_DShV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_DShV_SOV_LOW ),
]
Alternatives_HMGteam_AGS17_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_POL_LOW ),
]
Alternatives_HMGteam_AGS17_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_SOV_LOW ),
]
Alternatives_HMGteam_AGS17_TTsko_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_TTsko_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_TTsko_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_TTsko_SOV_LOW ),
]
Alternatives_HMGteam_AGS17_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_AGS17_VDV_SOV_LOW ),
]
Alternatives_HMGteam_DShK_AA_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_DShK_AA_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_DShK_AA_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_DShK_AA_SOV_LOW ),
]
Alternatives_HMGteam_DShK_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_DShK_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_DShK_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_DShK_SOV_LOW ),
]
Alternatives_HMGteam_KPVT_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_KPVT_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_KPVT_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_KPVT_SOV_LOW ),
]
Alternatives_HMGteam_M1919A4_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M1919A4_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M1919A4_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M1919A4_NL_LOW ),
]
Alternatives_HMGteam_M2HB_AB_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_AB_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_AB_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_AB_US_LOW ),
]
Alternatives_HMGteam_M2HB_Aero_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_Aero_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_Aero_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_Aero_US_LOW ),
]
Alternatives_HMGteam_M2HB_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_BEL_LOW ),
]
Alternatives_HMGteam_M2HB_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_FR_LOW ),
]
Alternatives_HMGteam_M2HB_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_LUX_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_LUX_LOW ),
]
Alternatives_HMGteam_M2HB_M63_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_M63_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_M63_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_M63_US_LOW ),
]
Alternatives_HMGteam_M2HB_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_NG_US_LOW ),
]
Alternatives_HMGteam_M2HB_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_NL_LOW ),
]
Alternatives_HMGteam_M2HB_RIMa_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_RIMa_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_RIMa_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_RIMa_FR_LOW ),
]
Alternatives_HMGteam_M2HB_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_UK_LOW ),
]
Alternatives_HMGteam_M2HB_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_US_LOW ),
]
Alternatives_HMGteam_M2HB_para_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_para_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_para_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_para_FR_LOW ),
]
Alternatives_HMGteam_M2HB_para_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_para_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_para_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M2HB_para_UK_LOW ),
]
Alternatives_HMGteam_M60_AB_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_AB_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_AB_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_AB_US_LOW ),
]
Alternatives_HMGteam_M60_Aero_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_Aero_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_Aero_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_Aero_US_LOW ),
]
Alternatives_HMGteam_M60_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_NG_US_LOW ),
]
Alternatives_HMGteam_M60_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_M60_US_LOW ),
]
Alternatives_HMGteam_MAG_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_BEL_LOW ),
]
Alternatives_HMGteam_MAG_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_NL_LOW ),
]
Alternatives_HMGteam_MAG_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_UK_LOW ),
]
Alternatives_HMGteam_MAG_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_para_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_para_BEL_LOW ),
]
Alternatives_HMGteam_MAG_para_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_para_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_para_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MAG_para_UK_LOW ),
]
Alternatives_HMGteam_MG3_FJ_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MG3_FJ_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MG3_FJ_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MG3_FJ_RFA_LOW ),
]
Alternatives_HMGteam_MG3_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MG3_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MG3_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_MG3_RFA_LOW ),
]
Alternatives_HMGteam_Maxim_Reserve_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Maxim_Reserve_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Maxim_Reserve_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Maxim_Reserve_SOV_LOW ),
]
Alternatives_HMGteam_Mk19_AB_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Mk19_AB_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Mk19_AB_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Mk19_AB_US_LOW ),
]
Alternatives_HMGteam_Mk19_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Mk19_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Mk19_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_Mk19_US_LOW ),
]
Alternatives_HMGteam_NSV_6U6_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_6U6_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_6U6_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_6U6_VDV_SOV_LOW ),
]
Alternatives_HMGteam_NSV_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_DDR_LOW ),
]
Alternatives_HMGteam_NSV_DShV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_DShV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_DShV_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_DShV_SOV_LOW ),
]
Alternatives_HMGteam_NSV_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_POL_LOW ),
]
Alternatives_HMGteam_NSV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_SOV_LOW ),
]
Alternatives_HMGteam_NSV_TTsko_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_TTsko_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_TTsko_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_TTsko_SOV_LOW ),
]
Alternatives_HMGteam_NSV_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_NSV_VDV_SOV_LOW ),
]
Alternatives_HMGteam_PKM_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_DDR_LOW ),
]
Alternatives_HMGteam_PKM_DShV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_DShV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_DShV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_DShV_SOV_LOW ),
]
Alternatives_HMGteam_PKM_FJ_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_FJ_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_FJ_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_FJ_DDR_LOW ),
]
Alternatives_HMGteam_PKM_Naval_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_Naval_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_Naval_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_Naval_POL_LOW ),
]
Alternatives_HMGteam_PKM_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_POL_LOW ),
]
Alternatives_HMGteam_PKM_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_SOV_LOW ),
]
Alternatives_HMGteam_PKM_TTsko_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_TTsko_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_TTsko_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_TTsko_SOV_LOW ),
]
Alternatives_HMGteam_PKM_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_VDV_SOV_LOW ),
]
Alternatives_HMGteam_PKM_para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HMGteam_PKM_para_POL_LOW ),
]
Alternatives_HS30_Panzermorser_120mm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_HS30_Panzermorser_120mm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_HS30_Panzermorser_120mm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_HS30_Panzermorser_120mm_RFA_LOW ),
]
Alternatives_Hibneryt_KG_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Hibneryt_KG_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Hibneryt_KG_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Hibneryt_KG_POL_LOW ),
]
Alternatives_Hibneryt_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Hibneryt_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Hibneryt_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Hibneryt_POL_LOW ),
]
Alternatives_Honker_4011_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Honker_4011_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Honker_4011_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Honker_4011_POL_LOW ),
]
Alternatives_Honker_RYS_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Honker_RYS_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Honker_RYS_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Honker_RYS_POL_LOW ),
]
Alternatives_Howz_2A36_Giatsint_B_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_2A36_Giatsint_B_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_2A36_Giatsint_B_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_2A36_Giatsint_B_SOV_LOW ),
]
Alternatives_Howz_A19_122mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_A19_122mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_A19_122mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_A19_122mm_POL_LOW ),
]
Alternatives_Howz_B4M_203mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_B4M_203mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_B4M_203mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_B4M_203mm_SOV_LOW ),
]
Alternatives_Howz_BS3_100mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_BS3_100mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_BS3_100mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_BS3_100mm_SOV_LOW ),
]
Alternatives_Howz_Br5M_280mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_Br5M_280mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_Br5M_280mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_Br5M_280mm_SOV_LOW ),
]
Alternatives_Howz_D1_152mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D1_152mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D1_152mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D1_152mm_POL_LOW ),
]
Alternatives_Howz_D1_152mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D1_152mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D1_152mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D1_152mm_SOV_LOW ),
]
Alternatives_Howz_D20_152mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D20_152mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D20_152mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D20_152mm_DDR_LOW ),
]
Alternatives_Howz_D20_152mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D20_152mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D20_152mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D20_152mm_SOV_LOW ),
]
Alternatives_Howz_D30_122mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_DDR_LOW ),
]
Alternatives_Howz_D30_122mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_SOV_LOW ),
]
Alternatives_Howz_D30_122mm_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_D30_122mm_VDV_SOV_LOW ),
]
Alternatives_Howz_L118_105mm_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_L118_105mm_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_L118_105mm_LUX_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_L118_105mm_LUX_LOW ),
]
Alternatives_Howz_L118_105mm_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_L118_105mm_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_L118_105mm_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_L118_105mm_UK_LOW ),
]
Alternatives_Howz_M101_105mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_FR_LOW ),
]
Alternatives_Howz_M101_105mm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_RFA_LOW ),
]
Alternatives_Howz_M101_105mm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_US_LOW ),
]
Alternatives_Howz_M101_105mm_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_para_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M101_105mm_para_BEL_LOW ),
]
Alternatives_Howz_M102_105mm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M102_105mm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M102_105mm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M102_105mm_US_LOW ),
]
Alternatives_Howz_M114_155mm_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M114_155mm_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M114_155mm_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M114_155mm_NL_LOW ),
]
Alternatives_Howz_M114_39_155mm_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M114_39_155mm_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M114_39_155mm_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M114_39_155mm_NL_LOW ),
]
Alternatives_Howz_M119_105mm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M119_105mm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M119_105mm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M119_105mm_US_LOW ),
]
Alternatives_Howz_M198_155mm_Copperhead_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M198_155mm_Copperhead_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M198_155mm_Copperhead_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M198_155mm_Copperhead_US_LOW ),
]
Alternatives_Howz_M198_155mm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M198_155mm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M198_155mm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M198_155mm_US_LOW ),
]
Alternatives_Howz_M30_122mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M30_122mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M30_122mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M30_122mm_DDR_LOW ),
]
Alternatives_Howz_M30_122mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M30_122mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M30_122mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M30_122mm_POL_LOW ),
]
Alternatives_Howz_M46_130mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M46_130mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M46_130mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M46_130mm_DDR_LOW ),
]
Alternatives_Howz_M46_130mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M46_130mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M46_130mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_M46_130mm_POL_LOW ),
]
Alternatives_Howz_ML20_152mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_ML20_152mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_ML20_152mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_ML20_152mm_POL_LOW ),
]
Alternatives_Howz_MstaB_150mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_MstaB_150mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_MstaB_150mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_MstaB_150mm_SOV_LOW ),
]
Alternatives_Howz_ZiS3_76mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_ZiS3_76mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_ZiS3_76mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Howz_ZiS3_76mm_DDR_LOW ),
]
Alternatives_IS2M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_IS2M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_IS2M_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_IS2M_SOV_LOW ),
]
Alternatives_Iltis_CMD_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_CMD_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_CMD_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_CMD_BEL_LOW ),
]
Alternatives_Iltis_HMG_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_HMG_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_HMG_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_HMG_BEL_LOW ),
]
Alternatives_Iltis_MILAN_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_BEL_LOW ),
]
Alternatives_Iltis_MILAN_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_LOW ),
]
Alternatives_Iltis_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_RFA_LOW ),
]
Alternatives_Iltis_para_CMD_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_para_CMD_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_para_CMD_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_para_CMD_BEL_LOW ),
]
Alternatives_Iltis_para_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_para_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_para_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_para_CMD_RFA_LOW ),
]
Alternatives_Iltis_trans_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_trans_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_trans_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_trans_BEL_LOW ),
]
Alternatives_Iltis_trans_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_trans_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_trans_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_trans_RFA_LOW ),
]
Alternatives_Jaguar_1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_1_RFA_LOW ),
]
Alternatives_Jaguar_2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_2_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_2_RFA_LOW ),
]
Alternatives_KanJagdPanzer_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_KanJagdPanzer_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_KanJagdPanzer_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_KanJagdPanzer_BEL_LOW ),
]
Alternatives_KanJagdPanzer_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_KanJagdPanzer_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_KanJagdPanzer_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_KanJagdPanzer_RFA_LOW ),
]
Alternatives_KrAZ_255B_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_POL_LOW ),
]
Alternatives_KrAZ_255B_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_SOV_LOW ),
]
Alternatives_KrAZ_255B_supply_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_DDR_LOW ),
]
Alternatives_KrAZ_255B_supply_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_POL_LOW ),
]
Alternatives_KrAZ_255B_supply_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_KrAZ_255B_supply_SOV_LOW ),
]
Alternatives_LAV_25_M1047_US_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LAV_25_M1047_US_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LAV_25_M1047_US_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_LAV_25_M1047_US_US_LOW ),
]
Alternatives_LO_1800_FASTA_4_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LO_1800_FASTA_4_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LO_1800_FASTA_4_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LO_1800_FASTA_4_DDR_LOW ),
]
Alternatives_LO_1800_ZPU_2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LO_1800_ZPU_2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LO_1800_ZPU_2_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LO_1800_ZPU_2_POL_LOW ),
]
Alternatives_LSV_M2HB_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LSV_M2HB_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LSV_M2HB_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LSV_M2HB_UK_LOW ),
]
Alternatives_LSV_MILAN_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LSV_MILAN_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LSV_MILAN_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LSV_MILAN_UK_LOW ),
]
Alternatives_LUAZ_967M_AGL_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_AGL_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_AGL_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_AGL_SOV_LOW ),
]
Alternatives_LUAZ_967M_AGL_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_AGL_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_AGL_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_AGL_VDV_SOV_LOW ),
]
Alternatives_LUAZ_967M_CMD_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_CMD_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_CMD_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_CMD_VDV_SOV_LOW ),
]
Alternatives_LUAZ_967M_FAO_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_FAO_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_FAO_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_FAO_SOV_LOW ),
]
Alternatives_LUAZ_967M_Fagot_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_SOV_LOW ),
]
Alternatives_LUAZ_967M_Fagot_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_VDV_SOV_LOW ),
]
Alternatives_LUAZ_967M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SOV_LOW ),
]
Alternatives_LUAZ_967M_SPG9_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SPG9_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SPG9_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SPG9_SOV_LOW ),
]
Alternatives_LUAZ_967M_SPG9_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SPG9_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SPG9_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_SPG9_VDV_SOV_LOW ),
]
Alternatives_LUAZ_967M_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_VDV_SOV_LOW ),
]
Alternatives_LUAZ_967M_supply_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_supply_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_supply_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LUAZ_967M_supply_SOV_LOW ),
]
Alternatives_LandRover_CMD_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_NL_LOW ),
]
Alternatives_LandRover_CMD_Para_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_Para_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_Para_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_Para_UK_LOW ),
]
Alternatives_LandRover_CMD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_CMD_UK_LOW ),
]
Alternatives_LandRover_MILAN_Para_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_MILAN_Para_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_MILAN_Para_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_MILAN_Para_UK_LOW ),
]
Alternatives_LandRover_MILAN_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_MILAN_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_MILAN_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_MILAN_UK_LOW ),
]
Alternatives_LandRover_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_NL_LOW ),
]
Alternatives_LandRover_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_UK_LOW ),
]
Alternatives_LandRover_WOMBAT_Gurkhas_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_WOMBAT_Gurkhas_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_WOMBAT_Gurkhas_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_WOMBAT_Gurkhas_UK_LOW ),
]
Alternatives_LandRover_WOMBAT_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_WOMBAT_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_WOMBAT_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LandRover_WOMBAT_UK_LOW ),
]
Alternatives_Lars_2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Lars_2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Lars_2_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Lars_2_RFA_LOW ),
]
Alternatives_Leopard_1A1A1_CMD_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1A1_CMD_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1A1_CMD_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1A1_CMD_NL_LOW ),
]
Alternatives_Leopard_1A1A1_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1A1_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1A1_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1A1_NL_LOW ),
]
Alternatives_Leopard_1A1_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_CMD_RFA_LOW ),
]
Alternatives_Leopard_1A1_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_NL_LOW ),
]
Alternatives_Leopard_1A1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A1_RFA_LOW ),
]
Alternatives_Leopard_1A5_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_BEL_LOW ),
]
Alternatives_Leopard_1A5_CMD_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_CMD_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_CMD_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_CMD_BEL_LOW ),
]
Alternatives_Leopard_1A5_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_CMD_RFA_LOW ),
]
Alternatives_Leopard_1A5_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1A5_RFA_LOW ),
]
Alternatives_Leopard_1BE_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1BE_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1BE_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1BE_BEL_LOW ),
]
Alternatives_Leopard_1BE_CMD_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1BE_CMD_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1BE_CMD_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_1BE_CMD_BEL_LOW ),
]
Alternatives_Leopard_2A1_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_CMD_RFA_LOW ),
]
Alternatives_Leopard_2A1_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_NL_LOW ),
]
Alternatives_Leopard_2A1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A1_RFA_LOW ),
]
Alternatives_Leopard_2A3_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A3_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A3_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A3_CMD_RFA_LOW ),
]
Alternatives_Leopard_2A3_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A3_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A3_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A3_RFA_LOW ),
]
Alternatives_Leopard_2A4B_CMD_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4B_CMD_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4B_CMD_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4B_CMD_NL_LOW ),
]
Alternatives_Leopard_2A4B_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4B_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4B_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4B_NL_LOW ),
]
Alternatives_Leopard_2A4_CMD_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_CMD_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_CMD_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_CMD_NL_LOW ),
]
Alternatives_Leopard_2A4_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_NL_LOW ),
]
Alternatives_Leopard_2A4_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Leopard_2A4_RFA_LOW ),
]
Alternatives_LuAZ_967M_AA_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_LuAZ_967M_AA_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_LuAZ_967M_AA_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_LuAZ_967M_AA_VDV_SOV_LOW ),
]
Alternatives_Luchs_A1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Luchs_A1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Luchs_A1_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Luchs_A1_RFA_LOW ),
]
Alternatives_M1025_Humvee_AGL_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_AGL_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_AGL_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_AGL_US_LOW ),
]
Alternatives_M1025_Humvee_AGL_nonPara_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_AGL_nonPara_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_AGL_nonPara_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_AGL_nonPara_US_LOW ),
]
Alternatives_M1025_Humvee_CMD_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_LUX_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_LUX_LOW ),
]
Alternatives_M1025_Humvee_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_US_LOW ),
]
Alternatives_M1025_Humvee_CMD_para_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_para_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_para_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_CMD_para_US_LOW ),
]
Alternatives_M1025_Humvee_GVLLD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_GVLLD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_GVLLD_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_GVLLD_US_LOW ),
]
Alternatives_M1025_Humvee_HMG_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_HMG_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_HMG_LUX_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_HMG_LUX_LOW ),
]
Alternatives_M1025_Humvee_MP_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_MP_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_MP_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_MP_US_LOW ),
]
Alternatives_M1025_Humvee_TOW_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX_LOW ),
]
Alternatives_M1025_Humvee_TOW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US_LOW ),
]
Alternatives_M1025_Humvee_TOW_para_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US_LOW ),
]
Alternatives_M1025_Humvee_scout_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_scout_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_scout_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_scout_US_LOW ),
]
Alternatives_M1025_Humvee_scout_tuto_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_scout_tuto_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_scout_tuto_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_scout_tuto_US_LOW ),
]
Alternatives_M1038_Humvee_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1038_Humvee_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1038_Humvee_LUX_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M1038_Humvee_LUX_LOW ),
]
Alternatives_M1038_Humvee_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1038_Humvee_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1038_Humvee_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M1038_Humvee_US_LOW ),
]
Alternatives_M106A2_HOWZ_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_HOWZ_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_HOWZ_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_HOWZ_US_LOW ),
]
Alternatives_M106A2_Howz_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_Howz_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_Howz_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_Howz_NG_US_LOW ),
]
Alternatives_M106A2_Mortar_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_Mortar_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_Mortar_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M106A2_Mortar_NL_LOW ),
]
Alternatives_M107A2_175mm_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M107A2_175mm_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M107A2_175mm_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M107A2_175mm_UK_LOW ),
]
Alternatives_M109A2_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_BEL_LOW ),
]
Alternatives_M109A2_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_NG_US_LOW ),
]
Alternatives_M109A2_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_NL_LOW ),
]
Alternatives_M109A2_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A2_UK_LOW ),
]
Alternatives_M109A3G_HOWZ_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A3G_HOWZ_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M109A3G_HOWZ_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M109A3G_HOWZ_RFA_LOW ),
]
Alternatives_M110A2_HOWZ_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_BEL_LOW ),
]
Alternatives_M110A2_HOWZ_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_NL_LOW ),
]
Alternatives_M110A2_HOWZ_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_HOWZ_US_LOW ),
]
Alternatives_M110A2_Howz_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_NG_US_LOW ),
]
Alternatives_M110A2_Howz_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_RFA_LOW ),
]
Alternatives_M110A2_Howz_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M110A2_Howz_UK_LOW ),
]
Alternatives_M113A1B_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_BEL_LOW ),
]
Alternatives_M113A1B_MILAN_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_MILAN_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_MILAN_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_MILAN_BEL_LOW ),
]
Alternatives_M113A1B_Radar_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_Radar_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_Radar_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1B_Radar_BEL_LOW ),
]
Alternatives_M113A1G_MILAN_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA_LOW ),
]
Alternatives_M113A1G_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_RFA_LOW ),
]
Alternatives_M113A1G_reco_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_reco_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_reco_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_reco_RFA_LOW ),
]
Alternatives_M113A1G_supply_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_supply_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_supply_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_supply_RFA_LOW ),
]
Alternatives_M113A1_ACAV_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_ACAV_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_ACAV_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle3" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_ACAV_NG_US_LOW ),
]
Alternatives_M113A1_Dragon_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US_LOW ),
]
Alternatives_M113A1_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_NG_US_LOW ),
]
Alternatives_M113A1_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_NL_LOW ),
]
Alternatives_M113A1_TOW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_TOW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_TOW_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_TOW_US_LOW ),
]
Alternatives_M113A1_reco_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_reco_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_reco_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_reco_NL_LOW ),
]
Alternatives_M113A2_TOW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A2_TOW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A2_TOW_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A2_TOW_US_LOW ),
]
Alternatives_M113A2_supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A2_supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A2_supply_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A2_supply_US_LOW ),
]
Alternatives_M113A3_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A3_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A3_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113A3_US_LOW ),
]
Alternatives_M113_ACAV_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_ACAV_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_ACAV_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle3" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113_ACAV_NG_US_LOW ),
]
Alternatives_M113_ACAV_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_ACAV_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_ACAV_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle3" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113_ACAV_US_LOW ),
]
Alternatives_M113_CV_25mm_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_CV_25mm_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_CV_25mm_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113_CV_25mm_NL_LOW ),
]
Alternatives_M113_Dragon_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_Dragon_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_Dragon_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113_Dragon_US_LOW ),
]
Alternatives_M113_GreenArcher_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_NL_LOW ),
]
Alternatives_M113_GreenArcher_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_RFA_LOW ),
]
Alternatives_M113_GreenArcher_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113_GreenArcher_UK_LOW ),
]
Alternatives_M113_PzMorser_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_PzMorser_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_PzMorser_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M113_PzMorser_RFA_LOW ),
]
Alternatives_M125_HOWZ_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M125_HOWZ_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M125_HOWZ_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M125_HOWZ_NG_US_LOW ),
]
Alternatives_M125_HOWZ_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M125_HOWZ_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M125_HOWZ_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M125_HOWZ_US_LOW ),
]
Alternatives_M151A2_TOW_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M151A2_TOW_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M151A2_TOW_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M151A2_TOW_NG_US_LOW ),
]
Alternatives_M151A2_scout_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M151A2_scout_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M151A2_scout_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M151A2_scout_US_LOW ),
]
Alternatives_M151C_RCL_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M151C_RCL_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M151C_RCL_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M151C_RCL_NG_US_LOW ),
]
Alternatives_M151_MUTT_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_CMD_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_CMD_US_LOW ),
]
Alternatives_M151_MUTT_trans_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_trans_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_trans_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_trans_DDR_LOW ),
]
Alternatives_M151_MUTT_trans_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_trans_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_trans_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M151_MUTT_trans_US_LOW ),
]
Alternatives_M163_CS_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M163_CS_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M163_CS_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M163_CS_US_LOW ),
]
Alternatives_M163_PIVADS_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M163_PIVADS_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M163_PIVADS_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M163_PIVADS_US_LOW ),
]
Alternatives_M1A1HA_Abrams_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1HA_Abrams_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1HA_Abrams_CMD_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1HA_Abrams_CMD_US_LOW ),
]
Alternatives_M1A1HA_Abrams_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1HA_Abrams_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1HA_Abrams_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1HA_Abrams_US_LOW ),
]
Alternatives_M1A1_Abrams_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_CMD_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_CMD_US_LOW ),
]
Alternatives_M1A1_Abrams_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_US_LOW ),
]
Alternatives_M1A1_Abrams_reco_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_reco_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_reco_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1A1_Abrams_reco_US_LOW ),
]
Alternatives_M1IP_Abrams_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1IP_Abrams_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1IP_Abrams_CMD_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1IP_Abrams_CMD_US_LOW ),
]
Alternatives_M1IP_Abrams_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1IP_Abrams_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1IP_Abrams_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1IP_Abrams_US_LOW ),
]
Alternatives_M1_Abrams_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_CMD_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_CMD_US_LOW ),
]
Alternatives_M1_Abrams_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_NG_US_LOW ),
]
Alternatives_M1_Abrams_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M1_Abrams_US_LOW ),
]
Alternatives_M201_CMD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_CMD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_CMD_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M201_CMD_FR_LOW ),
]
Alternatives_M201_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M201_FR_LOW ),
]
Alternatives_M201_MG_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_MG_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_MG_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M201_MG_FR_LOW ),
]
Alternatives_M201_MILAN_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_MILAN_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_MILAN_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M201_MILAN_FR_LOW ),
]
Alternatives_M270_MLRS_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_RFA_LOW ),
]
Alternatives_M270_MLRS_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_US_LOW ),
]
Alternatives_M270_MLRS_cluster_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_NL_LOW ),
]
Alternatives_M270_MLRS_cluster_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_UK_LOW ),
]
Alternatives_M270_MLRS_cluster_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M270_MLRS_cluster_US_LOW ),
]
Alternatives_M274_Mule_ITOW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_ITOW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_ITOW_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_ITOW_US_LOW ),
]
Alternatives_M274_Mule_M2HB_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_M2HB_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_M2HB_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_M2HB_US_LOW ),
]
Alternatives_M274_Mule_RCL_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_RCL_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_RCL_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_RCL_US_LOW ),
]
Alternatives_M274_Mule_supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_supply_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M274_Mule_supply_US_LOW ),
]
Alternatives_M2A1_Bradley_IFV_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M2A1_Bradley_IFV_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M2A1_Bradley_IFV_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M2A1_Bradley_IFV_US_LOW ),
]
Alternatives_M2A1_Bradley_Leader_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M2A1_Bradley_Leader_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M2A1_Bradley_Leader_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M2A1_Bradley_Leader_US_LOW ),
]
Alternatives_M2A2_Bradley_IFV_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M2A2_Bradley_IFV_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M2A2_Bradley_IFV_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M2A2_Bradley_IFV_US_LOW ),
]
Alternatives_M2A2_Bradley_Leader_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M2A2_Bradley_Leader_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M2A2_Bradley_Leader_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M2A2_Bradley_Leader_US_LOW ),
]
Alternatives_M2_Bradley_IFV_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M2_Bradley_IFV_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M2_Bradley_IFV_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M2_Bradley_IFV_NG_US_LOW ),
]
Alternatives_M35_supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_supply_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_supply_US_LOW ),
]
Alternatives_M35_trans_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_DDR_LOW ),
]
Alternatives_M35_trans_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_US_LOW ),
]
Alternatives_M35_trans_tuto_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_tuto_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_tuto_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M35_trans_tuto_US_LOW ),
]
Alternatives_M38A1_CMD_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_CMD_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_CMD_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_CMD_NL_LOW ),
]
Alternatives_M38A1_MG_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_MG_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_MG_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_MG_NL_LOW ),
]
Alternatives_M38A1_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_NL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_NL_LOW ),
]
Alternatives_M38A1_RCL_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_RCL_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_RCL_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_RCL_NL_LOW ),
]
Alternatives_M38A1_TOW_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_TOW_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_TOW_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M38A1_TOW_NL_LOW ),
]
Alternatives_M3A1_Bradley_CFV_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M3A1_Bradley_CFV_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M3A1_Bradley_CFV_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M3A1_Bradley_CFV_US_LOW ),
]
Alternatives_M3A2_Bradley_CFV_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M3A2_Bradley_CFV_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M3A2_Bradley_CFV_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M3A2_Bradley_CFV_US_LOW ),
]
Alternatives_M42_Duster_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M42_Duster_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M42_Duster_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M42_Duster_US_LOW ),
]
Alternatives_M48A2C_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2C_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2C_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2C_RFA_LOW ),
]
Alternatives_M48A2GA2_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2GA2_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2GA2_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2GA2_CMD_RFA_LOW ),
]
Alternatives_M48A2GA2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2GA2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2GA2_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A2GA2_RFA_LOW ),
]
Alternatives_M48A5_reco_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A5_reco_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M48A5_reco_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle3" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M48A5_reco_NG_US_LOW ),
]
Alternatives_M48_Chaparral_MIM72F_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M48_Chaparral_MIM72F_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M48_Chaparral_MIM72F_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M48_Chaparral_MIM72F_US_LOW ),
]
Alternatives_M548A2_supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M548A2_supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M548A2_supply_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M548A2_supply_US_LOW ),
]
Alternatives_M551A1_ACAV_Sheridan_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_ACAV_Sheridan_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_ACAV_Sheridan_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_ACAV_Sheridan_US_LOW ),
]
Alternatives_M551A1_TTS_Sheridan_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_CMD_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_CMD_US_LOW ),
]
Alternatives_M551A1_TTS_Sheridan_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_US_LOW ),
]
Alternatives_M577_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_NL_LOW ),
]
Alternatives_M577_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_RFA_LOW ),
]
Alternatives_M577_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M577_US_LOW ),
]
Alternatives_M60A1_AVLM_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_AVLM_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_AVLM_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_AVLM_US_LOW ),
]
Alternatives_M60A1_RISE_Passive_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_CMD_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_CMD_US_LOW ),
]
Alternatives_M60A1_RISE_Passive_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_US_LOW ),
]
Alternatives_M60A1_RISE_Passive_reco_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_reco_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_reco_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A1_RISE_Passive_reco_US_LOW ),
]
Alternatives_M60A3_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_CMD_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_CMD_US_LOW ),
]
Alternatives_M60A3_ERA_Patton_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_ERA_Patton_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_ERA_Patton_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_ERA_Patton_US_LOW ),
]
Alternatives_M60A3_Patton_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_Patton_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_Patton_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_Patton_NG_US_LOW ),
]
Alternatives_M60A3_Patton_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_Patton_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_Patton_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M60A3_Patton_US_LOW ),
]
Alternatives_M728_CEV_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M728_CEV_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M728_CEV_NG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M728_CEV_NG_US_LOW ),
]
Alternatives_M728_CEV_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M728_CEV_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M728_CEV_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M728_CEV_US_LOW ),
]
Alternatives_M812_supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M812_supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M812_supply_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M812_supply_US_LOW ),
]
Alternatives_M901A1_ITW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M901A1_ITW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M901A1_ITW_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M901A1_ITW_US_LOW ),
]
Alternatives_M901_TOW_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M901_TOW_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M901_TOW_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M901_TOW_NG_US_LOW ),
]
Alternatives_M901_TOW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M901_TOW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M901_TOW_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M901_TOW_US_LOW ),
]
Alternatives_M981_FISTV_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M981_FISTV_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M981_FISTV_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M981_FISTV_US_LOW ),
]
Alternatives_M998_Avenger_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Avenger_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Avenger_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Avenger_US_LOW ),
]
Alternatives_M998_Avenger_nonPara_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Avenger_nonPara_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Avenger_nonPara_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Avenger_nonPara_US_LOW ),
]
Alternatives_M998_Humvee_AGL_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_AGL_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_AGL_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_AGL_US_LOW ),
]
Alternatives_M998_Humvee_Delta_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_Delta_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_Delta_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_Delta_US_LOW ),
]
Alternatives_M998_Humvee_HMG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_HMG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_HMG_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_HMG_US_LOW ),
]
Alternatives_M998_Humvee_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_LUX_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_LUX_LOW ),
]
Alternatives_M998_Humvee_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_M998_Humvee_US_LOW ),
]
Alternatives_MAN_Kat_6x6_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Kat_6x6_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Kat_6x6_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Kat_6x6_RFA_LOW ),
]
Alternatives_MAN_Kat_6x6_trans_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Kat_6x6_trans_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Kat_6x6_trans_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Kat_6x6_trans_RFA_LOW ),
]
Alternatives_MAN_Z311_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Z311_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Z311_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Z311_BEL_LOW ),
]
Alternatives_MAN_Z311_Mi50_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Z311_Mi50_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Z311_Mi50_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MAN_Z311_Mi50_BEL_LOW ),
]
Alternatives_MCV_80_Warrior_CMD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_CMD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_CMD_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_CMD_UK_LOW ),
]
Alternatives_MCV_80_Warrior_MILAN_ERA_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK_LOW ),
]
Alternatives_MCV_80_Warrior_MILAN_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK_LOW ),
]
Alternatives_MCV_80_Warrior_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_UK_LOW ),
]
Alternatives_MFRW_RM70_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MFRW_RM70_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MFRW_RM70_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MFRW_RM70_DDR_LOW ),
]
Alternatives_MFRW_RM70_cluster_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MFRW_RM70_cluster_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MFRW_RM70_cluster_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MFRW_RM70_cluster_DDR_LOW ),
]
Alternatives_MLRS_WP_8z_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MLRS_WP_8z_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MLRS_WP_8z_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MLRS_WP_8z_POL_LOW ),
]
Alternatives_MTLB_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_CMD_DDR_LOW ),
]
Alternatives_MTLB_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_CMD_SOV_LOW ),
]
Alternatives_MTLB_Shturm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Shturm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Shturm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Shturm_DDR_LOW ),
]
Alternatives_MTLB_Shturm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Shturm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Shturm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Shturm_SOV_LOW ),
]
Alternatives_MTLB_Strela10M3_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10M3_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10M3_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10M3_SOV_LOW ),
]
Alternatives_MTLB_Strela10_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_DDR_LOW ),
]
Alternatives_MTLB_Strela10_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_POL_LOW ),
]
Alternatives_MTLB_Strela10_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Strela10_SOV_LOW ),
]
Alternatives_MTLB_TRI_Hors_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_TRI_Hors_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_TRI_Hors_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_TRI_Hors_POL_LOW ),
]
Alternatives_MTLB_Vasilek_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Vasilek_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Vasilek_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_Vasilek_SOV_LOW ),
]
Alternatives_MTLB_supply_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_supply_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_supply_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_supply_DDR_LOW ),
]
Alternatives_MTLB_supply_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_supply_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_supply_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_supply_SOV_LOW ),
]
Alternatives_MTLB_trans_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_trans_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_trans_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_trans_DDR_LOW ),
]
Alternatives_MTLB_trans_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_trans_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_trans_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_trans_POL_LOW ),
]
Alternatives_MTLB_transp_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_transp_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_transp_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MTLB_transp_SOV_LOW ),
]
Alternatives_Marder_1A2_MILAN_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA_LOW ),
]
Alternatives_Marder_1A2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A2_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A2_RFA_LOW ),
]
Alternatives_Marder_1A3_MILAN_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A3_MILAN_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A3_MILAN_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A3_MILAN_RFA_LOW ),
]
Alternatives_Marder_1A3_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A3_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A3_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_1A3_RFA_LOW ),
]
Alternatives_Marder_Roland_2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_Roland_2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_Roland_2_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_Roland_2_RFA_LOW ),
]
Alternatives_Marder_Roland_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_Roland_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_Roland_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Marder_Roland_RFA_LOW ),
]
Alternatives_Mortier_107mm_Aero_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_Aero_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_Aero_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_Aero_US_LOW ),
]
Alternatives_Mortier_107mm_Airborne_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_Airborne_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_Airborne_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_Airborne_US_LOW ),
]
Alternatives_Mortier_107mm_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_BEL_LOW ),
]
Alternatives_Mortier_107mm_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_NG_US_LOW ),
]
Alternatives_Mortier_107mm_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_NL_LOW ),
]
Alternatives_Mortier_107mm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_US_LOW ),
]
Alternatives_Mortier_107mm_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_para_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_107mm_para_BEL_LOW ),
]
Alternatives_Mortier_240mm_M240_Cluster_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_Cluster_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_Cluster_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_Cluster_SOV_LOW ),
]
Alternatives_Mortier_240mm_M240_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_POL_LOW ),
]
Alternatives_Mortier_240mm_M240_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_240mm_M240_SOV_LOW ),
]
Alternatives_Mortier_2B14_82mm_DShV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_DShV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_DShV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_DShV_SOV_LOW ),
]
Alternatives_Mortier_2B14_82mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_SOV_LOW ),
]
Alternatives_Mortier_2B14_82mm_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B14_82mm_VDV_SOV_LOW ),
]
Alternatives_Mortier_2B9_Vasilek_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_Para_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_Para_POL_LOW ),
]
Alternatives_Mortier_2B9_Vasilek_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_SOV_LOW ),
]
Alternatives_Mortier_2B9_Vasilek_nonPara_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_nonPara_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_nonPara_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2B9_Vasilek_nonPara_SOV_LOW ),
]
Alternatives_Mortier_2S12_120mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_DDR_LOW ),
]
Alternatives_Mortier_2S12_120mm_DShV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_DShV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_DShV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_DShV_SOV_LOW ),
]
Alternatives_Mortier_2S12_120mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_POL_LOW ),
]
Alternatives_Mortier_2S12_120mm_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_Para_POL_LOW ),
]
Alternatives_Mortier_2S12_120mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_SOV_LOW ),
]
Alternatives_Mortier_2S12_120mm_TTsko_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_TTsko_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_TTsko_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_TTsko_SOV_LOW ),
]
Alternatives_Mortier_2S12_120mm_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_2S12_120mm_VDV_SOV_LOW ),
]
Alternatives_Mortier_81mm_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_BEL_LOW ),
]
Alternatives_Mortier_81mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_FR_LOW ),
]
Alternatives_Mortier_81mm_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_LUX_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_LUX_LOW ),
]
Alternatives_Mortier_81mm_para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_para_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_81mm_para_BEL_LOW ),
]
Alternatives_Mortier_M29_81mm_Marines_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_Marines_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_Marines_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_Marines_NL_LOW ),
]
Alternatives_Mortier_M29_81mm_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_NL_LOW ),
]
Alternatives_Mortier_M29_81mm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M29_81mm_US_LOW ),
]
Alternatives_Mortier_M43_160mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_160mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_160mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_160mm_POL_LOW ),
]
Alternatives_Mortier_M43_82mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_DDR_LOW ),
]
Alternatives_Mortier_M43_82mm_FJ_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_FJ_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_FJ_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_FJ_DDR_LOW ),
]
Alternatives_Mortier_M43_82mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_POL_LOW ),
]
Alternatives_Mortier_M43_82mm_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_M43_82mm_Para_POL_LOW ),
]
Alternatives_Mortier_MORT61_120mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_FR_LOW ),
]
Alternatives_Mortier_MORT61_120mm_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_NL_LOW ),
]
Alternatives_Mortier_MORT61_120mm_para_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_para_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_para_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_MORT61_120mm_para_FR_LOW ),
]
Alternatives_Mortier_Nona_K_120mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Nona_K_120mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Nona_K_120mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Nona_K_120mm_SOV_LOW ),
]
Alternatives_Mortier_PM43_120mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_DDR_LOW ),
]
Alternatives_Mortier_PM43_120mm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_POL_LOW ),
]
Alternatives_Mortier_PM43_120mm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_PM43_120mm_SOV_LOW ),
]
Alternatives_Mortier_Tampella_120mm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Tampella_120mm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Tampella_120mm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Tampella_120mm_RFA_LOW ),
]
Alternatives_Mortier_Tampella_120mm_para_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Tampella_120mm_para_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Tampella_120mm_para_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mortier_Tampella_120mm_para_RFA_LOW ),
]
Alternatives_OT_62_TOPAS_2AP_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_2AP_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_2AP_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_2AP_POL_LOW ),
]
Alternatives_OT_62_TOPAS_JOD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_JOD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_JOD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_JOD_POL_LOW ),
]
Alternatives_OT_62_TOPAS_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_POL_LOW ),
]
Alternatives_OT_62_TOPAS_R3M_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_R3M_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_R3M_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_R3M_CMD_POL_LOW ),
]
Alternatives_OT_62_TOPAS_SPG9_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_SPG9_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_SPG9_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_OT_62_TOPAS_SPG9_POL_LOW ),
]
Alternatives_OT_64_SKOT_2AM_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2AM_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2AM_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2AM_POL_LOW ),
]
Alternatives_OT_64_SKOT_2A_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2A_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2A_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2A_POL_LOW ),
]
Alternatives_OT_64_SKOT_2P_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2P_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2P_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2P_POL_LOW ),
]
Alternatives_OT_64_SKOT_2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_2_POL_LOW ),
]
Alternatives_OT_64_SKOT_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_CMD_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_OT_64_SKOT_CMD_POL_LOW ),
]
Alternatives_OT_65_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_CMD_POL_LOW ),
]
Alternatives_OT_65_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_DDR_LOW ),
]
Alternatives_OT_65_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OT_65_POL_LOW ),
]
Alternatives_Obusier_155mm_mle1950_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Obusier_155mm_mle1950_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Obusier_155mm_mle1950_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Obusier_155mm_mle1950_FR_LOW ),
]
Alternatives_Osa_9K33M3_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_DDR_LOW ),
]
Alternatives_Osa_9K33M3_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_POL_LOW ),
]
Alternatives_Osa_9K33M3_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Osa_9K33M3_SOV_LOW ),
]
Alternatives_PSzH_IV_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PSzH_IV_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PSzH_IV_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_PSzH_IV_DDR_LOW ),
]
Alternatives_PT76B_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_DDR_LOW ),
]
Alternatives_PT76B_CMD_Naval_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_Naval_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_Naval_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_Naval_POL_LOW ),
]
Alternatives_PT76B_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_CMD_POL_LOW ),
]
Alternatives_PT76B_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_DDR_LOW ),
]
Alternatives_PT76B_Naval_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_Naval_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_Naval_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_Naval_POL_LOW ),
]
Alternatives_PT76B_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_POL_LOW ),
]
Alternatives_PT76B_tank_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_DDR_LOW ),
]
Alternatives_PT76B_tank_Naval_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_Naval_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_Naval_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_Naval_POL_LOW ),
]
Alternatives_PT76B_tank_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PT76B_tank_POL_LOW ),
]
Alternatives_PTS_M_supply_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PTS_M_supply_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PTS_M_supply_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PTS_M_supply_DDR_LOW ),
]
Alternatives_PTS_M_supply_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_PTS_M_supply_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_PTS_M_supply_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_PTS_M_supply_POL_LOW ),
]
Alternatives_RCL_L6_Wombat_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_RCL_L6_Wombat_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_RCL_L6_Wombat_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_RCL_L6_Wombat_UK_LOW ),
]
Alternatives_RM70_85_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_RM70_85_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_RM70_85_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_RM70_85_DDR_LOW ),
]
Alternatives_RM70_85_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_RM70_85_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_RM70_85_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_RM70_85_POL_LOW ),
]
Alternatives_Roland_2_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Roland_2_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Roland_2_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Roland_2_FR_LOW ),
]
Alternatives_Roland_3_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Roland_3_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Roland_3_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Roland_3_FR_LOW ),
]
Alternatives_Rover_101FC_LUX is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_LUX ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_LUX_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_LUX_LOW ),
]
Alternatives_Rover_101FC_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_UK_LOW ),
]
Alternatives_Rover_101FC_supply_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_supply_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_supply_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Rover_101FC_supply_UK_LOW ),
]
Alternatives_SPW_152K_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_SPW_152K_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_SPW_152K_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_SPW_152K_DDR_LOW ),
]
Alternatives_Saxon_CMD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Saxon_CMD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Saxon_CMD_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Saxon_CMD_UK_LOW ),
]
Alternatives_Saxon_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Saxon_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Saxon_UK_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Saxon_UK_LOW ),
]
Alternatives_Sonderwagen_4_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Sonderwagen_4_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Sonderwagen_4_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Sonderwagen_4_RFA_LOW ),
]
Alternatives_Sonderwagen_4_recon_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Sonderwagen_4_recon_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Sonderwagen_4_recon_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Sonderwagen_4_recon_RFA_LOW ),
]
Alternatives_Star_266_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Star_266_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Star_266_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Star_266_POL_LOW ),
]
Alternatives_Star_266_supply_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Star_266_supply_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Star_266_supply_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Star_266_supply_POL_LOW ),
]
Alternatives_Supacat_ATMP_Javelin_LML_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK_LOW ),
]
Alternatives_Supacat_ATMP_MILAN_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_MILAN_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_MILAN_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_MILAN_UK_LOW ),
]
Alternatives_Supacat_ATMP_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_UK_LOW ),
]
Alternatives_Supacat_ATMP_supply_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_supply_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_supply_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_supply_UK_LOW ),
]
Alternatives_T34_85M_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_CMD_POL_LOW ),
]
Alternatives_T34_85M_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_DDR_LOW ),
]
Alternatives_T34_85M_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T34_85M_POL_LOW ),
]
Alternatives_T54B_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_CMD_DDR_LOW ),
]
Alternatives_T54B_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_CMD_POL_LOW ),
]
Alternatives_T54B_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_DDR_LOW ),
]
Alternatives_T54B_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_POL_LOW ),
]
Alternatives_T54B_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T54B_SOV_LOW ),
]
Alternatives_T55AM2B_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2B_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2B_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2B_DDR_LOW ),
]
Alternatives_T55AM2_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2_CMD_DDR_LOW ),
]
Alternatives_T55AM2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM2_DDR_LOW ),
]
Alternatives_T55AMS_Merida_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AMS_Merida_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AMS_Merida_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55AMS_Merida_POL_LOW ),
]
Alternatives_T55AM_1_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_1_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_1_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_1_CMD_SOV_LOW ),
]
Alternatives_T55AM_1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_1_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_1_SOV_LOW ),
]
Alternatives_T55AM_Merida_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_Merida_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_Merida_CMD_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_Merida_CMD_POL_LOW ),
]
Alternatives_T55AM_Merida_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_Merida_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_Merida_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55AM_Merida_POL_LOW ),
]
Alternatives_T55AS_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AS_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55AS_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55AS_POL_LOW ),
]
Alternatives_T55A_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_DDR_LOW ),
]
Alternatives_T55A_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_POL_LOW ),
]
Alternatives_T55A_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_CMD_SOV_LOW ),
]
Alternatives_T55A_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_DDR_LOW ),
]
Alternatives_T55A_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_POL_LOW ),
]
Alternatives_T55A_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_SOV_LOW ),
]
Alternatives_T55A_obr81_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_obr81_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_obr81_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T55A_obr81_SOV_LOW ),
]
Alternatives_T62M1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M1_SOV_LOW ),
]
Alternatives_T62MD1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD1_SOV_LOW ),
]
Alternatives_T62MD_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD_CMD_SOV_LOW ),
]
Alternatives_T62MD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MD_SOV_LOW ),
]
Alternatives_T62MV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T62MV_SOV_LOW ),
]
Alternatives_T62M_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M_CMD_SOV_LOW ),
]
Alternatives_T62M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T62M_SOV_LOW ),
]
Alternatives_T64AM_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64AM_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64AM_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64AM_SOV_LOW ),
]
Alternatives_T64AV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64AV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64AV_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64AV_SOV_LOW ),
]
Alternatives_T64A_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64A_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64A_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64A_CMD_SOV_LOW ),
]
Alternatives_T64A_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64A_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64A_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64A_SOV_LOW ),
]
Alternatives_T64B1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64B1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64B1_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64B1_SOV_LOW ),
]
Alternatives_T64B1_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64B1_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64B1_reco_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64B1_reco_SOV_LOW ),
]
Alternatives_T64BV1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV1_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV1_SOV_LOW ),
]
Alternatives_T64BV_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV_CMD_SOV_LOW ),
]
Alternatives_T64BV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64BV_SOV_LOW ),
]
Alternatives_T64B_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64B_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64B_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64B_CMD_SOV_LOW ),
]
Alternatives_T64B_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T64B_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T64B_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T64B_SOV_LOW ),
]
Alternatives_T72M1_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_CMD_DDR_LOW ),
]
Alternatives_T72M1_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_CMD_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_CMD_POL_LOW ),
]
Alternatives_T72M1_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_DDR_LOW ),
]
Alternatives_T72M1_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_POL_LOW ),
]
Alternatives_T72M1_Wilk_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_Wilk_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_Wilk_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M1_Wilk_POL_LOW ),
]
Alternatives_T72MUV2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72MUV2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72MUV2_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72MUV2_DDR_LOW ),
]
Alternatives_T72M_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_CMD_DDR_LOW ),
]
Alternatives_T72M_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_CMD_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_CMD_POL_LOW ),
]
Alternatives_T72M_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_DDR_LOW ),
]
Alternatives_T72M_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72M_POL_LOW ),
]
Alternatives_T72S_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72S_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72S_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72S_DDR_LOW ),
]
Alternatives_T72_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72_CMD_DDR_LOW ),
]
Alternatives_T72_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T72_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T72_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T72_DDR_LOW ),
]
Alternatives_T80BV_Beast_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_Beast_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_Beast_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_Beast_SOV_LOW ),
]
Alternatives_T80BV_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_CMD_SOV_LOW ),
]
Alternatives_T80BV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T80BV_SOV_LOW ),
]
Alternatives_T80B_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T80B_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T80B_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T80B_CMD_SOV_LOW ),
]
Alternatives_T80B_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T80B_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T80B_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T80B_SOV_LOW ),
]
Alternatives_T80UD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T80UD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T80UD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T80UD_SOV_LOW ),
]
Alternatives_T80U_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T80U_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T80U_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T80U_CMD_SOV_LOW ),
]
Alternatives_T80U_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T80U_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T80U_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_T80U_SOV_LOW ),
]
Alternatives_T813_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T813_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T813_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T813_DDR_LOW ),
]
Alternatives_T813_trans_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T813_trans_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T813_trans_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T813_trans_DDR_LOW ),
]
Alternatives_T815_supply_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_T815_supply_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_T815_supply_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_T815_supply_DDR_LOW ),
]
Alternatives_TOS1_Buratino_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TOS1_Buratino_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TOS1_Buratino_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_TOS1_Buratino_SOV_LOW ),
]
Alternatives_TO_55_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TO_55_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TO_55_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_TO_55_DDR_LOW ),
]
Alternatives_TO_55_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TO_55_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TO_55_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_TO_55_SOV_LOW ),
]
Alternatives_TPZ_Fuchs_1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_1_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_1_RFA_LOW ),
]
Alternatives_TPZ_Fuchs_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_CMD_RFA_LOW ),
]
Alternatives_TPZ_Fuchs_MILAN_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA_LOW ),
]
Alternatives_TPZ_Fuchs_RASIT_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_RASIT_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_RASIT_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_RASIT_RFA_LOW ),
]
Alternatives_TRM_10000_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_10000_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_10000_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_10000_FR_LOW ),
]
Alternatives_TRM_10000_supply_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_10000_supply_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_10000_supply_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_10000_supply_FR_LOW ),
]
Alternatives_TRM_2000_20mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_20mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_20mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_20mm_FR_LOW ),
]
Alternatives_TRM_2000_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_FR_LOW ),
]
Alternatives_TRM_2000_supply_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_supply_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_supply_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_TRM_2000_supply_FR_LOW ),
]
Alternatives_TUTO_M1025_Humvee_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_TUTO_M1025_Humvee_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_TUTO_M1025_Humvee_US_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_TUTO_M1025_Humvee_US_LOW ),
]
Alternatives_Tor_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tor_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tor_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tor_SOV_LOW ),
]
Alternatives_Tracked_Rapier_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tracked_Rapier_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tracked_Rapier_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tracked_Rapier_UK_LOW ),
]
Alternatives_Tunguska_2K22_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tunguska_2K22_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tunguska_2K22_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tunguska_2K22_SOV_LOW ),
]
Alternatives_UAZ_469_AGL_Grenzer_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_Grenzer_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_Grenzer_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_Grenzer_DDR_LOW ),
]
Alternatives_UAZ_469_AGL_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_SOV_LOW ),
]
Alternatives_UAZ_469_AGL_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_AGL_VDV_SOV_LOW ),
]
Alternatives_UAZ_469_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_DDR_LOW ),
]
Alternatives_UAZ_469_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_POL_LOW ),
]
Alternatives_UAZ_469_CMD_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_Para_POL_LOW ),
]
Alternatives_UAZ_469_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_SOV_LOW ),
]
Alternatives_UAZ_469_CMD_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_CMD_VDV_SOV_LOW ),
]
Alternatives_UAZ_469_Fagot_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_DDR_LOW ),
]
Alternatives_UAZ_469_Fagot_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_POL_LOW ),
]
Alternatives_UAZ_469_Fagot_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Fagot_Para_POL_LOW ),
]
Alternatives_UAZ_469_Konkurs_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Konkurs_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Konkurs_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Konkurs_VDV_SOV_LOW ),
]
Alternatives_UAZ_469_MP_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_MP_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_MP_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_MP_SOV_LOW ),
]
Alternatives_UAZ_469_Reco_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_DDR_LOW ),
]
Alternatives_UAZ_469_Reco_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_POL_LOW ),
]
Alternatives_UAZ_469_Reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_Reco_SOV_LOW ),
]
Alternatives_UAZ_469_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SOV_LOW ),
]
Alternatives_UAZ_469_SPG9_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_DDR_LOW ),
]
Alternatives_UAZ_469_SPG9_FJ_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_FJ_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_FJ_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_FJ_DDR_LOW ),
]
Alternatives_UAZ_469_SPG9_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_Para_POL_LOW ),
]
Alternatives_UAZ_469_SPG9_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_SOV_LOW ),
]
Alternatives_UAZ_469_SPG9_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_SPG9_VDV_SOV_LOW ),
]
Alternatives_UAZ_469_supply_Para_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_Para_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_Para_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_Para_POL_LOW ),
]
Alternatives_UAZ_469_supply_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_SOV_LOW ),
]
Alternatives_UAZ_469_supply_VDV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_VDV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_VDV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_supply_VDV_SOV_LOW ),
]
Alternatives_UAZ_469_trans_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_trans_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_trans_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_trans_DDR_LOW ),
]
Alternatives_UAZ_469_trans_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_trans_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_trans_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UAZ_469_trans_POL_LOW ),
]
Alternatives_Unimog_S_404_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_S_404_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_S_404_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_S_404_RFA_LOW ),
]
Alternatives_Unimog_U1350L_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_BEL_LOW ),
]
Alternatives_Unimog_U1350L_Para_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_Para_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_Para_BEL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_Para_BEL_LOW ),
]
Alternatives_Unimog_U1350L_supply_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_supply_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_supply_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_U1350L_supply_BEL_LOW ),
]
Alternatives_Unimog_supply_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_supply_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_supply_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_supply_BEL_LOW ),
]
Alternatives_Unimog_trans_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_trans_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_trans_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_trans_BEL_LOW ),
]
Alternatives_Unimog_trans_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_trans_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_trans_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Unimog_trans_RFA_LOW ),
]
Alternatives_Ural_4320_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_DDR_LOW ),
]
Alternatives_Ural_4320_Metla_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_Metla_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_Metla_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_Metla_SOV_LOW ),
]
Alternatives_Ural_4320_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_SOV_LOW ),
]
Alternatives_Ural_4320_ZPU_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_ZPU_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_ZPU_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_ZPU_SOV_LOW ),
]
Alternatives_Ural_4320_ZU_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_ZU_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_ZU_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_ZU_SOV_LOW ),
]
Alternatives_Ural_4320_trans_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_trans_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_trans_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ural_4320_trans_SOV_LOW ),
]
Alternatives_VAB_CMD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_CMD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_CMD_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_CMD_FR_LOW ),
]
Alternatives_VAB_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_FR_LOW ),
]
Alternatives_VAB_HOT_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_HOT_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_HOT_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_HOT_FR_LOW ),
]
Alternatives_VAB_MILAN_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_MILAN_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_MILAN_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_MILAN_FR_LOW ),
]
Alternatives_VAB_Mortar_81_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_Mortar_81_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_Mortar_81_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_Mortar_81_FR_LOW ),
]
Alternatives_VAB_RASIT_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_RASIT_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_RASIT_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_RASIT_FR_LOW ),
]
Alternatives_VAB_Reserve_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_Reserve_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_Reserve_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_Reserve_FR_LOW ),
]
Alternatives_VAB_T20_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_T20_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_T20_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_VAB_T20_FR_LOW ),
]
Alternatives_VBL_MILAN_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_MILAN_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_MILAN_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_MILAN_FR_LOW ),
]
Alternatives_VBL_PC_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_PC_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_PC_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_PC_FR_LOW ),
]
Alternatives_VBL_Reco_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_Reco_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_Reco_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VBL_Reco_FR_LOW ),
]
Alternatives_VIB_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VIB_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VIB_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1", "tourelle4" ] MeshDescriptor=$/GFX/DepictionResources/Modele_VIB_FR_LOW ),
]
Alternatives_VLRA_20mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_20mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_20mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_20mm_FR_LOW ),
]
Alternatives_VLRA_HMG_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_HMG_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_HMG_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_HMG_FR_LOW ),
]
Alternatives_VLRA_MILAN_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_MILAN_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_MILAN_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_MILAN_FR_LOW ),
]
Alternatives_VLRA_Mistral_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_Mistral_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_Mistral_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_Mistral_FR_LOW ),
]
Alternatives_VLRA_Mortier81_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_Mortier81_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_Mortier81_FR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_Mortier81_FR_LOW ),
]
Alternatives_VLRA_supply_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_supply_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_supply_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_supply_FR_LOW ),
]
Alternatives_VLRA_trans_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_trans_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_trans_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLRA_trans_FR_LOW ),
]
Alternatives_VLTT_P4_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_FR_LOW ),
]
Alternatives_VLTT_P4_MILAN_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_MILAN_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_MILAN_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_MILAN_FR_LOW ),
]
Alternatives_VLTT_P4_MILAN_para_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_MILAN_para_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_MILAN_para_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_MILAN_para_FR_LOW ),
]
Alternatives_VLTT_P4_PC_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_PC_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_PC_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_VLTT_P4_PC_FR_LOW ),
]
Alternatives_Volvo_N10_supply_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Volvo_N10_supply_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Volvo_N10_supply_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Volvo_N10_supply_BEL_LOW ),
]
Alternatives_Volvo_N10_trans_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Volvo_N10_trans_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Volvo_N10_trans_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Volvo_N10_trans_BEL_LOW ),
]
Alternatives_W50_LA_A_25mm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_W50_LA_A_25mm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_W50_LA_A_25mm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_W50_LA_A_25mm_DDR_LOW ),
]
Alternatives_W50_LA_A_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_W50_LA_A_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_W50_LA_A_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_W50_LA_A_DDR_LOW ),
]
Alternatives_Wiesel_20mm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Wiesel_20mm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Wiesel_20mm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Wiesel_20mm_RFA_LOW ),
]
Alternatives_Wiesel_TOW_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Wiesel_TOW_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Wiesel_TOW_RFA_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle1" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Wiesel_TOW_RFA_LOW ),
]
Alternatives_ZSU_23_Shilka_Afghan_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_Afghan_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_Afghan_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_Afghan_SOV_LOW ),
]
Alternatives_ZSU_23_Shilka_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_DDR_LOW ),
]
Alternatives_ZSU_23_Shilka_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_POL_LOW ),
]
Alternatives_ZSU_23_Shilka_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_SOV_LOW ),
]
Alternatives_ZSU_23_Shilka_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_reco_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_23_Shilka_reco_SOV_LOW ),
]
Alternatives_ZSU_57_2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_57_2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_57_2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_ZSU_57_2_DDR_LOW ),
]

Alternatives_A109BA_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_BEL_LOW ),
]
Alternatives_A109BA_TOW_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_TOW_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_TOW_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_TOW_BEL_LOW ),
]
Alternatives_A109BA_TOW_twin_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_TOW_twin_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_TOW_twin_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A109BA_TOW_twin_BEL_LOW ),
]
Alternatives_A10_Thunderbolt_II_ATGM_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_ATGM_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_ATGM_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_ATGM_US_LOW ),
]
Alternatives_A10_Thunderbolt_II_Rkt_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_Rkt_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_Rkt_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_Rkt_US_LOW ),
]
Alternatives_A10_Thunderbolt_II_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A10_Thunderbolt_II_US_LOW ),
]
Alternatives_A37B_Dragonfly_HE_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_HE_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_HE_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_HE_US_LOW ),
]
Alternatives_A37B_Dragonfly_NPLM_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_NPLM_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_NPLM_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_NPLM_US_LOW ),
]
Alternatives_A37B_Dragonfly_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A37B_Dragonfly_US_LOW ),
]
Alternatives_A6E_Intruder_SEAD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A6E_Intruder_SEAD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A6E_Intruder_SEAD_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A6E_Intruder_SEAD_US_LOW ),
]
Alternatives_A6E_Intruder_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A6E_Intruder_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A6E_Intruder_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A6E_Intruder_US_LOW ),
]
Alternatives_A7D_Corsair_II_AT_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_AT_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_AT_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_AT_US_LOW ),
]
Alternatives_A7D_Corsair_II_CLU_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_CLU_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_CLU_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_CLU_US_LOW ),
]
Alternatives_A7D_Corsair_II_RKT_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_RKT_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_RKT_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_RKT_US_LOW ),
]
Alternatives_A7D_Corsair_II_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_A7D_Corsair_II_US_LOW ),
]
Alternatives_AH1E_Cobra_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1E_Cobra_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1E_Cobra_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1E_Cobra_US_LOW ),
]
Alternatives_AH1F_ATAS_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_ATAS_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_ATAS_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_ATAS_US_LOW ),
]
Alternatives_AH1F_CNITE_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_CNITE_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_CNITE_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_CNITE_US_LOW ),
]
Alternatives_AH1F_Cobra_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Cobra_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Cobra_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Cobra_NG_US_LOW ),
]
Alternatives_AH1F_Cobra_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Cobra_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Cobra_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Cobra_US_LOW ),
]
Alternatives_AH1F_HeavyHog_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_HeavyHog_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_HeavyHog_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_HeavyHog_US_LOW ),
]
Alternatives_AH1F_Hog_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Hog_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Hog_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1F_Hog_US_LOW ),
]
Alternatives_AH1S_Cobra_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1S_Cobra_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1S_Cobra_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH1S_Cobra_US_LOW ),
]
Alternatives_AH64_Apache_ATAS_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_ATAS_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_ATAS_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_ATAS_US_LOW ),
]
Alternatives_AH64_Apache_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_NG_US_LOW ),
]
Alternatives_AH64_Apache_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_US_LOW ),
]
Alternatives_AH64_Apache_emp1_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_emp1_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_emp1_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_emp1_US_LOW ),
]
Alternatives_AH64_Apache_emp2_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_emp2_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_emp2_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH64_Apache_emp2_US_LOW ),
]
Alternatives_AH6C_Little_Bird_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH6C_Little_Bird_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH6C_Little_Bird_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH6C_Little_Bird_US_LOW ),
]
Alternatives_AH6G_Little_Bird_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_AH6G_Little_Bird_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_AH6G_Little_Bird_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_AH6G_Little_Bird_US_LOW ),
]
Alternatives_Alouette_III_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_FR_LOW ),
]
Alternatives_Alouette_III_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_NL_LOW ),
]
Alternatives_Alouette_III_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_SOV_LOW ),
]
Alternatives_Alouette_III_SS11_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_SS11_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_SS11_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_SS11_FR_LOW ),
]
Alternatives_Alouette_III_reco_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_reco_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_reco_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_reco_FR_LOW ),
]
Alternatives_Alouette_III_trans_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_trans_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_trans_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_III_trans_NL_LOW ),
]
Alternatives_Alouette_II_CMD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_CMD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_CMD_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_CMD_FR_LOW ),
]
Alternatives_Alouette_II_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_CMD_RFA_LOW ),
]
Alternatives_Alouette_II_reco_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_reco_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_reco_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_reco_BEL_LOW ),
]
Alternatives_Alouette_II_reco_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_reco_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_reco_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_reco_RFA_LOW ),
]
Alternatives_Alouette_II_trans_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_trans_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_trans_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_trans_BEL_LOW ),
]
Alternatives_Alouette_II_trans_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_trans_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_trans_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alouette_II_trans_FR_LOW ),
]
Alternatives_Alpha_Jet_A_clu_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_clu_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_clu_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_clu_RFA_LOW ),
]
Alternatives_Alpha_Jet_A_he_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_he_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_he_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_he_RFA_LOW ),
]
Alternatives_Alpha_Jet_A_nplm_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_nplm_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_nplm_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_nplm_RFA_LOW ),
]
Alternatives_Alpha_Jet_A_rkt_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_rkt_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_rkt_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_A_rkt_RFA_LOW ),
]
Alternatives_Alpha_Jet_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_BEL_LOW ),
]
Alternatives_Alpha_Jet_E_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_E_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_E_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_E_FR_LOW ),
]
Alternatives_Alpha_Jet_E_NPLM_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_E_NPLM_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_E_NPLM_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_E_NPLM_FR_LOW ),
]
Alternatives_Alpha_Jet_HE2_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_HE2_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_HE2_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Alpha_Jet_HE2_BEL_LOW ),
]
Alternatives_Bo_105_CB_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_CB_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_CB_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_CB_NL_LOW ),
]
Alternatives_Bo_105_CMD_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_CMD_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_CMD_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_CMD_RFA_LOW ),
]
Alternatives_Bo_105_PAH_1A1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_PAH_1A1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_PAH_1A1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_PAH_1A1_RFA_LOW ),
]
Alternatives_Bo_105_PAH_1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_PAH_1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_PAH_1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_PAH_1_RFA_LOW ),
]
Alternatives_Bo_105_reco_ESP is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_reco_ESP ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_reco_ESP_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_reco_ESP_LOW ),
]
Alternatives_Bo_105_reco_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_reco_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_reco_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_reco_RFA_LOW ),
]
Alternatives_Bo_105_trans_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_trans_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_trans_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Bo_105_trans_RFA_LOW ),
]
Alternatives_Buccaneer_S2B_ATGM_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_ATGM_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_ATGM_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_ATGM_UK_LOW ),
]
Alternatives_Buccaneer_S2B_GBU_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_GBU_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_GBU_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_GBU_UK_LOW ),
]
Alternatives_Buccaneer_S2B_HE_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_HE_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_HE_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_HE_UK_LOW ),
]
Alternatives_Buccaneer_S2B_SEAD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_SEAD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_SEAD_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Buccaneer_S2B_SEAD_UK_LOW ),
]
Alternatives_CH47D_Chinook_supply_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47D_Chinook_supply_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47D_Chinook_supply_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47D_Chinook_supply_UK_LOW ),
]
Alternatives_CH47_Chinook_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Chinook_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Chinook_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Chinook_UK_LOW ),
]
Alternatives_CH47_Chinook_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Chinook_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Chinook_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Chinook_US_LOW ),
]
Alternatives_CH47_Super_Chinook_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Super_Chinook_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Super_Chinook_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CH47_Super_Chinook_US_LOW ),
]
Alternatives_CH53G_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CH53G_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CH53G_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CH53G_RFA_LOW ),
]
Alternatives_CH53G_trans_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CH53G_trans_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CH53G_trans_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CH53G_trans_RFA_LOW ),
]
Alternatives_CH54B_Tarhe_supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CH54B_Tarhe_supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CH54B_Tarhe_supply_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CH54B_Tarhe_supply_US_LOW ),
]
Alternatives_CH54B_Tarhe_trans_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CH54B_Tarhe_trans_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CH54B_Tarhe_trans_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CH54B_Tarhe_trans_US_LOW ),
]
Alternatives_CL_289_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CL_289_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CL_289_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CL_289_RFA_LOW ),
]
Alternatives_CM170_Magister_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CM170_Magister_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CM170_Magister_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CM170_Magister_FR_LOW ),
]
Alternatives_CM170_Magister_SS11_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_CM170_Magister_SS11_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_CM170_Magister_SS11_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_CM170_Magister_SS11_FR_LOW ),
]
Alternatives_EA6B_Prowler_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_EA6B_Prowler_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_EA6B_Prowler_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_EA6B_Prowler_US_LOW ),
]
Alternatives_EF111_Raven_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_EF111_Raven_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_EF111_Raven_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_EF111_Raven_US_LOW ),
]
Alternatives_EH60A_EW_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_EH60A_EW_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_EH60A_EW_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_EH60A_EW_US_LOW ),
]
Alternatives_Ecureuil_20mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ecureuil_20mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ecureuil_20mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ecureuil_20mm_FR_LOW ),
]
Alternatives_Ecureuil_reco_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ecureuil_reco_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ecureuil_reco_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ecureuil_reco_FR_LOW ),
]
Alternatives_Epervier_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Epervier_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Epervier_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Epervier_BEL_LOW ),
]
Alternatives_F104G_Starfighter_AT_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_AT_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_AT_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_AT_RFA_LOW ),
]
Alternatives_F104G_Starfighter_HE_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_HE_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_HE_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_HE_RFA_LOW ),
]
Alternatives_F104G_Starfighter_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F104G_Starfighter_RFA_LOW ),
]
Alternatives_F111E_Aardvark_CBU_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_CBU_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_CBU_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_CBU_US_LOW ),
]
Alternatives_F111E_Aardvark_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_US_LOW ),
]
Alternatives_F111E_Aardvark_napalm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_napalm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_napalm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F111E_Aardvark_napalm_US_LOW ),
]
Alternatives_F111F_Aardvark_CBU_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_CBU_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_CBU_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_CBU_US_LOW ),
]
Alternatives_F111F_Aardvark_LGB2_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_LGB2_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_LGB2_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_LGB2_US_LOW ),
]
Alternatives_F111F_Aardvark_LGB_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_LGB_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_LGB_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_LGB_US_LOW ),
]
Alternatives_F111F_Aardvark_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_US_LOW ),
]
Alternatives_F111F_Aardvark_napalm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_napalm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_napalm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F111F_Aardvark_napalm_US_LOW ),
]
Alternatives_F117_Nighthawk_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F117_Nighthawk_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F117_Nighthawk_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F117_Nighthawk_US_LOW ),
]
Alternatives_F15C_Eagle_AA2_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F15C_Eagle_AA2_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F15C_Eagle_AA2_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F15C_Eagle_AA2_US_LOW ),
]
Alternatives_F15C_Eagle_AA_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F15C_Eagle_AA_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F15C_Eagle_AA_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F15C_Eagle_AA_US_LOW ),
]
Alternatives_F15E_StrikeEagle_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F15E_StrikeEagle_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F15E_StrikeEagle_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F15E_StrikeEagle_US_LOW ),
]
Alternatives_F16A_AA2_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA2_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA2_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA2_NL_LOW ),
]
Alternatives_F16A_AA_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA_BEL_LOW ),
]
Alternatives_F16A_AA_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_AA_NL_LOW ),
]
Alternatives_F16A_CBU_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_CBU_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_CBU_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_CBU_BEL_LOW ),
]
Alternatives_F16A_CLU_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_CLU_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_CLU_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_CLU_NL_LOW ),
]
Alternatives_F16A_HE_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_HE_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_HE_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16A_HE_NL_LOW ),
]
Alternatives_F16C_LGB_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16C_LGB_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16C_LGB_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16C_LGB_US_LOW ),
]
Alternatives_F16E_AA2_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AA2_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AA2_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AA2_US_LOW ),
]
Alternatives_F16E_AA_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AA_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AA_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AA_US_LOW ),
]
Alternatives_F16E_AGM_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AGM_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AGM_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_AGM_US_LOW ),
]
Alternatives_F16E_CBU_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_CBU_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_CBU_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_CBU_US_LOW ),
]
Alternatives_F16E_HE_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_HE_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_HE_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_HE_US_LOW ),
]
Alternatives_F16E_SEAD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_SEAD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_SEAD_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_SEAD_US_LOW ),
]
Alternatives_F16E_TER_CLU_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_TER_CLU_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_TER_CLU_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_TER_CLU_US_LOW ),
]
Alternatives_F16E_TER_HE_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_TER_HE_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_TER_HE_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_TER_HE_US_LOW ),
]
Alternatives_F16E_napalm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_napalm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_napalm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F16E_napalm_US_LOW ),
]
Alternatives_F4E_Phantom_II_AA_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_AA_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_AA_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_AA_US_LOW ),
]
Alternatives_F4E_Phantom_II_AT_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_AT_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_AT_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_AT_US_LOW ),
]
Alternatives_F4E_Phantom_II_CBU_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_CBU_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_CBU_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_CBU_US_LOW ),
]
Alternatives_F4E_Phantom_II_HE_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_HE_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_HE_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_HE_US_LOW ),
]
Alternatives_F4E_Phantom_II_napalm_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_napalm_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_napalm_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4E_Phantom_II_napalm_US_LOW ),
]
Alternatives_F4F_Phantom_II_AA_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_AA_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_AA_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_AA_RFA_LOW ),
]
Alternatives_F4F_Phantom_II_AT_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_AT_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_AT_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_AT_RFA_LOW ),
]
Alternatives_F4F_Phantom_II_HE1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_HE1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_HE1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_HE1_RFA_LOW ),
]
Alternatives_F4F_Phantom_II_HE2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_HE2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_HE2_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_HE2_RFA_LOW ),
]
Alternatives_F4F_Phantom_II_RKT2_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_RKT2_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_RKT2_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4F_Phantom_II_RKT2_RFA_LOW ),
]
Alternatives_F4_Phantom_AA_F3_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_AA_F3_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_AA_F3_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_AA_F3_UK_LOW ),
]
Alternatives_F4_Phantom_GR2_HE_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_HE_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_HE_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_HE_UK_LOW ),
]
Alternatives_F4_Phantom_GR2_NPLM_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_NPLM_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_NPLM_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_NPLM_UK_LOW ),
]
Alternatives_F4_Phantom_GR2_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Phantom_GR2_UK_LOW ),
]
Alternatives_F4_Wild_Weasel_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Wild_Weasel_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Wild_Weasel_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F4_Wild_Weasel_US_LOW ),
]
Alternatives_F5A_FreedomFighter_AA_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_AA_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_AA_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_AA_NL_LOW ),
]
Alternatives_F5A_FreedomFighter_CLU_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_CLU_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_CLU_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_CLU_NL_LOW ),
]
Alternatives_F5A_FreedomFighter_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_NL_LOW ),
]
Alternatives_F5A_FreedomFighter_NPLM_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_NPLM_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_NPLM_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_NPLM_NL_LOW ),
]
Alternatives_F5A_FreedomFighter_RKT_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_RKT_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_RKT_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F5A_FreedomFighter_RKT_NL_LOW ),
]
Alternatives_F8P_Crusader_AA2_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F8P_Crusader_AA2_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F8P_Crusader_AA2_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F8P_Crusader_AA2_FR_LOW ),
]
Alternatives_F8P_Crusader_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_F8P_Crusader_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_F8P_Crusader_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_F8P_Crusader_FR_LOW ),
]
Alternatives_FA16_CAS_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_FA16_CAS_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_FA16_CAS_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_FA16_CAS_US_LOW ),
]
Alternatives_G91_R3_Gina_HE_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_HE_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_HE_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_HE_RFA_LOW ),
]
Alternatives_G91_R3_Gina_NPL_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_NPL_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_NPL_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_NPL_RFA_LOW ),
]
Alternatives_G91_R3_Gina_RKT_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_RKT_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_RKT_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_G91_R3_Gina_RKT_RFA_LOW ),
]
Alternatives_Gazelle_20mm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_20mm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_20mm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_20mm_FR_LOW ),
]
Alternatives_Gazelle_20mm_reco_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_20mm_reco_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_20mm_reco_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_20mm_reco_FR_LOW ),
]
Alternatives_Gazelle_CMD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_CMD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_CMD_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_CMD_UK_LOW ),
]
Alternatives_Gazelle_HOT_2_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_HOT_2_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_HOT_2_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_HOT_2_FR_LOW ),
]
Alternatives_Gazelle_HOT_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_HOT_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_HOT_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_HOT_FR_LOW ),
]
Alternatives_Gazelle_Mistral_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_Mistral_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_Mistral_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_Mistral_FR_LOW ),
]
Alternatives_Gazelle_SNEB_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_SNEB_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_SNEB_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_SNEB_UK_LOW ),
]
Alternatives_Gazelle_SNEB_reco_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_SNEB_reco_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_SNEB_reco_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_SNEB_reco_UK_LOW ),
]
Alternatives_Gazelle_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_UK_LOW ),
]
Alternatives_Gazelle_reco_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_reco_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_reco_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_reco_FR_LOW ),
]
Alternatives_Gazelle_trans_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_trans_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_trans_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Gazelle_trans_UK_LOW ),
]
Alternatives_Harrier_CLU_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_CLU_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_CLU_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_CLU_UK_LOW ),
]
Alternatives_Harrier_GR5_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_GR5_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_GR5_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_GR5_UK_LOW ),
]
Alternatives_Harrier_HE1_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_HE1_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_HE1_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_HE1_UK_LOW ),
]
Alternatives_Harrier_HE2_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_HE2_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_HE2_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_HE2_UK_LOW ),
]
Alternatives_Harrier_RKT1_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_RKT1_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_RKT1_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_RKT1_UK_LOW ),
]
Alternatives_Harrier_RKT2_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_RKT2_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_RKT2_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_RKT2_UK_LOW ),
]
Alternatives_Harrier_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Harrier_UK_LOW ),
]
Alternatives_JOH_58C_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_JOH_58C_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_JOH_58C_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_JOH_58C_US_LOW ),
]
Alternatives_Jaguar_ATGM_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_ATGM_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_ATGM_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_ATGM_FR_LOW ),
]
Alternatives_Jaguar_CLU_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_CLU_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_CLU_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_CLU_UK_LOW ),
]
Alternatives_Jaguar_HE1_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE1_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE1_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE1_UK_LOW ),
]
Alternatives_Jaguar_HE2_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE2_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE2_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE2_UK_LOW ),
]
Alternatives_Jaguar_HE_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_HE_FR_LOW ),
]
Alternatives_Jaguar_RKT_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_RKT_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_RKT_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_RKT_FR_LOW ),
]
Alternatives_Jaguar_RKT_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_RKT_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_RKT_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_RKT_UK_LOW ),
]
Alternatives_Jaguar_SEAD2_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_SEAD2_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_SEAD2_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_SEAD2_FR_LOW ),
]
Alternatives_Jaguar_SEAD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_SEAD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_SEAD_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_SEAD_FR_LOW ),
]
Alternatives_Jaguar_clu_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_clu_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_clu_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_clu_FR_LOW ),
]
Alternatives_Jaguar_nplm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_nplm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_nplm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_nplm_FR_LOW ),
]
Alternatives_Jaguar_overwing_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_overwing_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_overwing_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_overwing_UK_LOW ),
]
Alternatives_Ka_50_AA_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ka_50_AA_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ka_50_AA_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ka_50_AA_SOV_LOW ),
]
Alternatives_Ka_50_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Ka_50_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Ka_50_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Ka_50_SOV_LOW ),
]
Alternatives_L39ZO_CLU_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_CLU_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_CLU_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_CLU_DDR_LOW ),
]
Alternatives_L39ZO_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_DDR_LOW ),
]
Alternatives_L39ZO_HE1_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_HE1_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_HE1_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_HE1_DDR_LOW ),
]
Alternatives_L39ZO_HE1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_HE1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_HE1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_HE1_SOV_LOW ),
]
Alternatives_L39ZO_NPLM_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_NPLM_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_NPLM_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_L39ZO_NPLM_SOV_LOW ),
]
Alternatives_Lynx_AH_Mk1_LBH_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_LBH_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_LBH_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_LBH_UK_LOW ),
]
Alternatives_Lynx_AH_Mk1_TOW_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_TOW_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_TOW_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_TOW_UK_LOW ),
]
Alternatives_Lynx_AH_Mk1_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk1_UK_LOW ),
]
Alternatives_Lynx_AH_Mk7_Chancellor_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_Chancellor_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_Chancellor_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_Chancellor_UK_LOW ),
]
Alternatives_Lynx_AH_Mk7_I_TOW2_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW2_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW2_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW2_UK_LOW ),
]
Alternatives_Lynx_AH_Mk7_I_TOW_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW_UK_LOW ),
]
Alternatives_Lynx_AH_Mk7_SNEB_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_SNEB_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_SNEB_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Lynx_AH_Mk7_SNEB_UK_LOW ),
]
Alternatives_MH47D_Super_Chinook_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MH47D_Super_Chinook_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MH47D_Super_Chinook_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MH47D_Super_Chinook_US_LOW ),
]
Alternatives_MH_60A_DAP_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MH_60A_DAP_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MH_60A_DAP_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MH_60A_DAP_US_LOW ),
]
Alternatives_MQM_105_Aquila_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MQM_105_Aquila_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MQM_105_Aquila_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MQM_105_Aquila_US_LOW ),
]
Alternatives_MiG_17PF_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_17PF_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_17PF_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_17PF_POL_LOW ),
]
Alternatives_MiG_21PFM_AA_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21PFM_AA_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21PFM_AA_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21PFM_AA_DDR_LOW ),
]
Alternatives_MiG_21PFM_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21PFM_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21PFM_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21PFM_DDR_LOW ),
]
Alternatives_MiG_21bis_AA2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA2_DDR_LOW ),
]
Alternatives_MiG_21bis_AA3_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA3_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA3_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA3_DDR_LOW ),
]
Alternatives_MiG_21bis_AA_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_AA_POL_LOW ),
]
Alternatives_MiG_21bis_CLU_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_CLU_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_CLU_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_CLU_DDR_LOW ),
]
Alternatives_MiG_21bis_HE_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_HE_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_HE_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_HE_DDR_LOW ),
]
Alternatives_MiG_21bis_HE_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_HE_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_HE_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_HE_POL_LOW ),
]
Alternatives_MiG_21bis_NPLM_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_NPLM_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_NPLM_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_NPLM_DDR_LOW ),
]
Alternatives_MiG_21bis_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_POL_LOW ),
]
Alternatives_MiG_21bis_RKT2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_RKT2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_RKT2_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_RKT2_DDR_LOW ),
]
Alternatives_MiG_21bis_RKT2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_RKT2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_RKT2_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_21bis_RKT2_POL_LOW ),
]
Alternatives_MiG_23BN_AT2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_AT2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_AT2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_AT2_DDR_LOW ),
]
Alternatives_MiG_23BN_AT_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_AT_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_AT_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_AT_DDR_LOW ),
]
Alternatives_MiG_23BN_CLU_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_CLU_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_CLU_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_CLU_DDR_LOW ),
]
Alternatives_MiG_23BN_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_DDR_LOW ),
]
Alternatives_MiG_23BN_KMGU_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_KMGU_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_KMGU_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_KMGU_DDR_LOW ),
]
Alternatives_MiG_23BN_RKT_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_RKT_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_RKT_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_RKT_DDR_LOW ),
]
Alternatives_MiG_23BN_nplm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_nplm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_nplm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23BN_nplm_DDR_LOW ),
]
Alternatives_MiG_23MF_AA2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA2_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA2_POL_LOW ),
]
Alternatives_MiG_23MF_AA_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA_DDR_LOW ),
]
Alternatives_MiG_23MF_AA_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_AA_POL_LOW ),
]
Alternatives_MiG_23MF_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MF_DDR_LOW ),
]
Alternatives_MiG_23MLD_AA1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MLD_AA1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MLD_AA1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MLD_AA1_SOV_LOW ),
]
Alternatives_MiG_23MLD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MLD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MLD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23MLD_SOV_LOW ),
]
Alternatives_MiG_23ML_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23ML_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23ML_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23ML_DDR_LOW ),
]
Alternatives_MiG_23ML_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23ML_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23ML_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23ML_SOV_LOW ),
]
Alternatives_MiG_23P_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23P_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23P_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_23P_SOV_LOW ),
]
Alternatives_MiG_25BM_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_25BM_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_25BM_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_25BM_SOV_LOW ),
]
Alternatives_MiG_25RBF_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_25RBF_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_25RBF_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_25RBF_SOV_LOW ),
]
Alternatives_MiG_27K_AT1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_AT1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_AT1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_AT1_SOV_LOW ),
]
Alternatives_MiG_27K_AT2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_AT2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_AT2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_AT2_SOV_LOW ),
]
Alternatives_MiG_27K_LGB_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_LGB_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_LGB_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_LGB_SOV_LOW ),
]
Alternatives_MiG_27K_SEAD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_SEAD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_SEAD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27K_SEAD_SOV_LOW ),
]
Alternatives_MiG_27M_CLU_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_CLU_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_CLU_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_CLU_SOV_LOW ),
]
Alternatives_MiG_27M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_SOV_LOW ),
]
Alternatives_MiG_27M_bombe_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_bombe_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_bombe_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_bombe_SOV_LOW ),
]
Alternatives_MiG_27M_napalm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_napalm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_napalm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_napalm_SOV_LOW ),
]
Alternatives_MiG_27M_rkt_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_rkt_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_rkt_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_rkt_SOV_LOW ),
]
Alternatives_MiG_27M_sead_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_sead_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_sead_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_27M_sead_SOV_LOW ),
]
Alternatives_MiG_29_AA2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA2_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA2_POL_LOW ),
]
Alternatives_MiG_29_AA2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA2_SOV_LOW ),
]
Alternatives_MiG_29_AA3_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA3_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA3_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA3_SOV_LOW ),
]
Alternatives_MiG_29_AA_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_DDR_LOW ),
]
Alternatives_MiG_29_AA_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_POL_LOW ),
]
Alternatives_MiG_29_AA_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_29_AA_SOV_LOW ),
]
Alternatives_MiG_31M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31M_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31M_SOV_LOW ),
]
Alternatives_MiG_31_AA1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31_AA1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31_AA1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31_AA1_SOV_LOW ),
]
Alternatives_MiG_31_AA2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31_AA2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31_AA2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_MiG_31_AA2_SOV_LOW ),
]
Alternatives_Mi_14PL_AT_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_14PL_AT_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_14PL_AT_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_14PL_AT_DDR_LOW ),
]
Alternatives_Mi_14PL_recon_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_14PL_recon_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_14PL_recon_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_14PL_recon_DDR_LOW ),
]
Alternatives_Mi_24D_AA_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_AA_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_AA_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_AA_DDR_LOW ),
]
Alternatives_Mi_24D_Desant_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_Desant_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_Desant_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_Desant_SOV_LOW ),
]
Alternatives_Mi_24D_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_POL_LOW ),
]
Alternatives_Mi_24D_s5_AT_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s5_AT_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s5_AT_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s5_AT_DDR_LOW ),
]
Alternatives_Mi_24D_s5_AT_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s5_AT_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s5_AT_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s5_AT_SOV_LOW ),
]
Alternatives_Mi_24D_s8_AT_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_DDR_LOW ),
]
Alternatives_Mi_24D_s8_AT_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_POL_LOW ),
]
Alternatives_Mi_24D_s8_AT_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24D_s8_AT_SOV_LOW ),
]
Alternatives_Mi_24K_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24K_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24K_reco_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24K_reco_SOV_LOW ),
]
Alternatives_Mi_24P_AA_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_AA_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_AA_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_AA_SOV_LOW ),
]
Alternatives_Mi_24P_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_SOV_LOW ),
]
Alternatives_Mi_24P_s8_AT2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_s8_AT2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_s8_AT2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_s8_AT2_DDR_LOW ),
]
Alternatives_Mi_24P_s8_AT_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_s8_AT_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_s8_AT_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24P_s8_AT_DDR_LOW ),
]
Alternatives_Mi_24VP_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24VP_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24VP_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24VP_SOV_LOW ),
]
Alternatives_Mi_24V_AA_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_AA_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_AA_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_AA_SOV_LOW ),
]
Alternatives_Mi_24V_AT_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_AT_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_AT_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_AT_SOV_LOW ),
]
Alternatives_Mi_24V_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_POL_LOW ),
]
Alternatives_Mi_24V_RKT2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_RKT2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_RKT2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_RKT2_SOV_LOW ),
]
Alternatives_Mi_24V_RKT_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_RKT_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_RKT_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_RKT_SOV_LOW ),
]
Alternatives_Mi_24V_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_24V_SOV_LOW ),
]
Alternatives_Mi_26_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_26_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_26_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_26_SOV_LOW ),
]
Alternatives_Mi_2Ro_reco_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2Ro_reco_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2Ro_reco_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2Ro_reco_POL_LOW ),
]
Alternatives_Mi_2_AA_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_AA_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_AA_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_AA_POL_LOW ),
]
Alternatives_Mi_2_ATGM_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_ATGM_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_ATGM_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_ATGM_POL_LOW ),
]
Alternatives_Mi_2_CMD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_CMD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_CMD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_CMD_DDR_LOW ),
]
Alternatives_Mi_2_CMD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_CMD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_CMD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_CMD_POL_LOW ),
]
Alternatives_Mi_2_gunship_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_gunship_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_gunship_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_gunship_DDR_LOW ),
]
Alternatives_Mi_2_gunship_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_gunship_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_gunship_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_gunship_POL_LOW ),
]
Alternatives_Mi_2_reco_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_reco_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_reco_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_reco_DDR_LOW ),
]
Alternatives_Mi_2_reco_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_reco_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_reco_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_reco_SOV_LOW ),
]
Alternatives_Mi_2_rocket_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_rocket_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_rocket_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_rocket_DDR_LOW ),
]
Alternatives_Mi_2_rocket_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_rocket_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_rocket_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_rocket_POL_LOW ),
]
Alternatives_Mi_2_trans_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_DDR_LOW ),
]
Alternatives_Mi_2_trans_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_POL_LOW ),
]
Alternatives_Mi_2_trans_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_2_trans_SOV_LOW ),
]
Alternatives_Mi_6_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_6_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_6_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_6_POL_LOW ),
]
Alternatives_Mi_6_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_6_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_6_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_6_SOV_LOW ),
]
Alternatives_Mi_8K_CMD_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8K_CMD_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8K_CMD_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8K_CMD_SOV_LOW ),
]
Alternatives_Mi_8MTPI_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MTPI_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MTPI_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MTPI_SOV_LOW ),
]
Alternatives_Mi_8MTV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MTV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MTV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MTV_SOV_LOW ),
]
Alternatives_Mi_8MT_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MT_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MT_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8MT_POL_LOW ),
]
Alternatives_Mi_8PPA_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8PPA_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8PPA_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8PPA_SOV_LOW ),
]
Alternatives_Mi_8R_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8R_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8R_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8R_SOV_LOW ),
]
Alternatives_Mi_8TB_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_DDR_LOW ),
]
Alternatives_Mi_8TB_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_SOV_LOW ),
]
Alternatives_Mi_8TB_reco_Marine_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_reco_Marine_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_reco_Marine_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TB_reco_Marine_DDR_LOW ),
]
Alternatives_Mi_8TV_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_DDR_LOW ),
]
Alternatives_Mi_8TV_Gunship_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_Gunship_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_Gunship_SOV_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle3" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_Gunship_SOV_LOW ),
]
Alternatives_Mi_8TV_PodGatling_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_DDR_LOW ),
]
Alternatives_Mi_8TV_PodGatling_PodAGL_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_PodAGL_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_PodAGL_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_PodAGL_SOV_LOW ),
]
Alternatives_Mi_8TV_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_SOV_LOW ),
]
Alternatives_Mi_8TV_UPK_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_UPK_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_UPK_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_UPK_DDR_LOW ),
]
Alternatives_Mi_8TV_non_arme_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_non_arme_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_non_arme_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_non_arme_SOV_LOW ),
]
Alternatives_Mi_8TV_s57_16_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_16_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_16_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_16_SOV_LOW ),
]
Alternatives_Mi_8TV_s57_32_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_32_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_32_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_32_DDR_LOW ),
]
Alternatives_Mi_8TV_s57_32_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_32_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_32_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s57_32_SOV_LOW ),
]
Alternatives_Mi_8TV_s80_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s80_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s80_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TV_s80_SOV_LOW ),
]
Alternatives_Mi_8TZ_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TZ_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TZ_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8TZ_SOV_LOW ),
]
Alternatives_Mi_8T_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_DDR_LOW ),
]
Alternatives_Mi_8T_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_POL_LOW ),
]
Alternatives_Mi_8T_non_arme_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_non_arme_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_non_arme_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_non_arme_DDR_LOW ),
]
Alternatives_Mi_8T_non_arme_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_non_arme_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_non_arme_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8T_non_arme_POL_LOW ),
]
Alternatives_Mi_8_supply_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8_supply_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8_supply_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8_supply_DDR_LOW ),
]
Alternatives_Mi_8_supply_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8_supply_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8_supply_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_8_supply_POL_LOW ),
]
Alternatives_Mi_9_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_9_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_9_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mi_9_DDR_LOW ),
]
Alternatives_Mirage_2000_C_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_2000_C_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_2000_C_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_2000_C_FR_LOW ),
]
Alternatives_Mirage_5_BA_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_BEL_LOW ),
]
Alternatives_Mirage_5_BA_CLU_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_CLU_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_CLU_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_CLU_BEL_LOW ),
]
Alternatives_Mirage_5_BA_MIRSIP_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_MIRSIP_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_MIRSIP_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_MIRSIP_BEL_LOW ),
]
Alternatives_Mirage_5_BA_NPLM_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_NPLM_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_NPLM_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_NPLM_BEL_LOW ),
]
Alternatives_Mirage_5_BA_RKT_BEL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_RKT_BEL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_RKT_BEL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_BA_RKT_BEL_LOW ),
]
Alternatives_Mirage_5_F_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_FR_LOW ),
]
Alternatives_Mirage_5_F_clu_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_clu_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_clu_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_clu_FR_LOW ),
]
Alternatives_Mirage_5_F_nplm_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_nplm_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_nplm_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_5_F_nplm_FR_LOW ),
]
Alternatives_Mirage_F1_CT_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_F1_CT_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_F1_CT_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_F1_CT_FR_LOW ),
]
Alternatives_Mirage_F1_C_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_F1_C_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_F1_C_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_F1_C_FR_LOW ),
]
Alternatives_Mirage_III_E_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_III_E_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_III_E_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_III_E_FR_LOW ),
]
Alternatives_Mirage_IV_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_IV_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_IV_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_IV_FR_LOW ),
]
Alternatives_Mirage_IV_SEAD_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_IV_SEAD_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_IV_SEAD_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Mirage_IV_SEAD_FR_LOW ),
]
Alternatives_OA10A_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OA10A_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OA10A_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OA10A_US_LOW ),
]
Alternatives_OH58A_reco_NG_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58A_reco_NG_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58A_reco_NG_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58A_reco_NG_US_LOW ),
]
Alternatives_OH58C_CMD_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58C_CMD_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58C_CMD_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58C_CMD_US_LOW ),
]
Alternatives_OH58C_Scout_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58C_Scout_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58C_Scout_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58C_Scout_US_LOW ),
]
Alternatives_OH58D_Combat_Scout_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58D_Combat_Scout_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58D_Combat_Scout_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58D_Combat_Scout_US_LOW ),
]
Alternatives_OH58D_Kiowa_Warrior_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58D_Kiowa_Warrior_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58D_Kiowa_Warrior_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58D_Kiowa_Warrior_US_LOW ),
]
Alternatives_OH58_CS_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58_CS_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58_CS_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_OH58_CS_US_LOW ),
]
Alternatives_Pchela_1T_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Pchela_1T_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Pchela_1T_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Pchela_1T_SOV_LOW ),
]
Alternatives_Puma_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_FR_LOW ),
]
Alternatives_Puma_HET_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_HET_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_HET_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_HET_FR_LOW ),
]
Alternatives_Puma_PC_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_PC_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_PC_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_PC_FR_LOW ),
]
Alternatives_Puma_Pirate_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_Pirate_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_Pirate_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_Pirate_FR_LOW ),
]
Alternatives_Puma_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Puma_UK_LOW ),
]
Alternatives_Su_15TM_AA2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_15TM_AA2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_15TM_AA2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_15TM_AA2_SOV_LOW ),
]
Alternatives_Su_15TM_AA_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_15TM_AA_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_15TM_AA_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_15TM_AA_SOV_LOW ),
]
Alternatives_Su_17M4_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_17M4_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_17M4_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_17M4_SOV_LOW ),
]
Alternatives_Su_17_cluster_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_17_cluster_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_17_cluster_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_17_cluster_POL_LOW ),
]
Alternatives_Su_22_AT2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT2_DDR_LOW ),
]
Alternatives_Su_22_AT_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_DDR_LOW ),
]
Alternatives_Su_22_AT_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_POL_LOW ),
]
Alternatives_Su_22_AT_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_AT_SOV_LOW ),
]
Alternatives_Su_22_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_DDR_LOW ),
]
Alternatives_Su_22_HE2_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_HE2_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_HE2_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_HE2_DDR_LOW ),
]
Alternatives_Su_22_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_POL_LOW ),
]
Alternatives_Su_22_RKT2_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT2_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT2_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT2_POL_LOW ),
]
Alternatives_Su_22_RKT_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT_DDR_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT_DDR_LOW ),
]
Alternatives_Su_22_RKT_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT_POL_MID ),
    DepictionVisual_LOD_Low( DisabledOperators=[ "tourelle2" ] MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_RKT_POL_LOW ),
]
Alternatives_Su_22_SEAD_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_SEAD_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_SEAD_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_SEAD_DDR_LOW ),
]
Alternatives_Su_22_SEAD_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_SEAD_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_SEAD_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_SEAD_POL_LOW ),
]
Alternatives_Su_22_UPK_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_UPK_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_UPK_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_UPK_DDR_LOW ),
]
Alternatives_Su_22_clu_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_clu_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_clu_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_clu_DDR_LOW ),
]
Alternatives_Su_22_clu_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_clu_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_clu_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_clu_POL_LOW ),
]
Alternatives_Su_22_nplm_DDR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_nplm_DDR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_nplm_DDR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_nplm_DDR_LOW ),
]
Alternatives_Su_22_nplm_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_nplm_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_nplm_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_22_nplm_POL_LOW ),
]
Alternatives_Su_24MP_EW_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_EW_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_EW_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_EW_SOV_LOW ),
]
Alternatives_Su_24MP_SEAD2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_SEAD2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_SEAD2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_SEAD2_SOV_LOW ),
]
Alternatives_Su_24MP_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24MP_SOV_LOW ),
]
Alternatives_Su_24M_AT1_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_AT1_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_AT1_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_AT1_SOV_LOW ),
]
Alternatives_Su_24M_AT2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_AT2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_AT2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_AT2_SOV_LOW ),
]
Alternatives_Su_24M_LGB2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_LGB2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_LGB2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_LGB2_SOV_LOW ),
]
Alternatives_Su_24M_LGB_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_LGB_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_LGB_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_LGB_SOV_LOW ),
]
Alternatives_Su_24M_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_SOV_LOW ),
]
Alternatives_Su_24M_clu2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_clu2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_clu2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_clu2_SOV_LOW ),
]
Alternatives_Su_24M_clu_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_clu_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_clu_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_clu_SOV_LOW ),
]
Alternatives_Su_24M_nplm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_nplm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_nplm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_nplm_SOV_LOW ),
]
Alternatives_Su_24M_thermo_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_thermo_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_thermo_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_24M_thermo_SOV_LOW ),
]
Alternatives_Su_25T_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25T_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25T_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25T_SOV_LOW ),
]
Alternatives_Su_25_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_SOV_LOW ),
]
Alternatives_Su_25_clu_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_clu_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_clu_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_clu_SOV_LOW ),
]
Alternatives_Su_25_he_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_he_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_he_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_he_SOV_LOW ),
]
Alternatives_Su_25_nplm_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_nplm_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_nplm_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_nplm_SOV_LOW ),
]
Alternatives_Su_25_rkt2_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_rkt2_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_rkt2_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_rkt2_SOV_LOW ),
]
Alternatives_Su_25_rkt_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_rkt_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_rkt_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_25_rkt_SOV_LOW ),
]
Alternatives_Su_27K_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_27K_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_27K_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_27K_SOV_LOW ),
]
Alternatives_Su_27S_SOV is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_27S_SOV ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_27S_SOV_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Su_27S_SOV_LOW ),
]
Alternatives_Super_Etendard_AT_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_AT_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_AT_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_AT_FR_LOW ),
]
Alternatives_Super_Etendard_CLU_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_CLU_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_CLU_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_CLU_FR_LOW ),
]
Alternatives_Super_Etendard_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_FR_LOW ),
]
Alternatives_Super_Etendard_HE_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_HE_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_HE_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Etendard_HE_FR_LOW ),
]
Alternatives_Super_Puma_FR is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Puma_FR ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Puma_FR_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Super_Puma_FR_LOW ),
]
Alternatives_Tornado_ADV_HE_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_HE_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_HE_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_HE_UK_LOW ),
]
Alternatives_Tornado_ADV_SEAD_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_SEAD_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_SEAD_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_SEAD_UK_LOW ),
]
Alternatives_Tornado_ADV_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_UK_LOW ),
]
Alternatives_Tornado_ADV_clu_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_clu_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_clu_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_ADV_clu_UK_LOW ),
]
Alternatives_Tornado_IDS_AT1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_AT1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_AT1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_AT1_RFA_LOW ),
]
Alternatives_Tornado_IDS_CLUS_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_CLUS_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_CLUS_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_CLUS_RFA_LOW ),
]
Alternatives_Tornado_IDS_HE1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_HE1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_HE1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_HE1_RFA_LOW ),
]
Alternatives_Tornado_IDS_MW1_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_MW1_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_MW1_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Tornado_IDS_MW1_RFA_LOW ),
]
Alternatives_UH1A_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1A_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1A_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1A_US_LOW ),
]
Alternatives_UH1D_NL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_NL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_NL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_NL_LOW ),
]
Alternatives_UH1D_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_RFA_LOW ),
]
Alternatives_UH1D_Supply_RFA is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_Supply_RFA ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_Supply_RFA_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1D_Supply_RFA_LOW ),
]
Alternatives_UH1H_Huey_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1H_Huey_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1H_Huey_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1H_Huey_US_LOW ),
]
Alternatives_UH1H_supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1H_supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1H_supply_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1H_supply_US_LOW ),
]
Alternatives_UH1M_gunship_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1M_gunship_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1M_gunship_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH1M_gunship_US_LOW ),
]
Alternatives_UH60A_Black_Hawk_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_Black_Hawk_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_Black_Hawk_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_Black_Hawk_US_LOW ),
]
Alternatives_UH60A_CO_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_CO_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_CO_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_CO_US_LOW ),
]
Alternatives_UH60A_Supply_US is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_Supply_US ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_Supply_US_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_UH60A_Supply_US_LOW ),
]
Alternatives_W3RR_Procjon_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_W3RR_Procjon_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_W3RR_Procjon_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_W3RR_Procjon_POL_LOW ),
]
Alternatives_W3W_Sokol_AA_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_W3W_Sokol_AA_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_W3W_Sokol_AA_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_W3W_Sokol_AA_POL_LOW ),
]
Alternatives_W3W_Sokol_RKT_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_W3W_Sokol_RKT_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_W3W_Sokol_RKT_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_W3W_Sokol_RKT_POL_LOW ),
]
Alternatives_W3_Sokol_POL is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_W3_Sokol_POL ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_W3_Sokol_POL_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_W3_Sokol_POL_LOW ),
]
Alternatives_Westland_Scout_SS11_UK is 
[
    DepictionVisual_LOD_High( MeshDescriptor=$/GFX/DepictionResources/Modele_Westland_Scout_SS11_UK ),
    DepictionVisual_LOD_Mid( MeshDescriptor=$/GFX/DepictionResources/Modele_Westland_Scout_SS11_UK_MID ),
    DepictionVisual_LOD_Low( MeshDescriptor=$/GFX/DepictionResources/Modele_Westland_Scout_SS11_UK_LOW ),
]
