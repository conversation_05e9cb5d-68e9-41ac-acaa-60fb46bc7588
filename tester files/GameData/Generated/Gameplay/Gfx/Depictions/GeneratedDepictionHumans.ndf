// Ne pas éditer, ce fichier est généré par DepictionHumanSubDepictionsFileWriter


HumanSubDepictions_81mm_mortar_Aero_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_81mm_mortar_Aero_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_81mm_mortar_CLU_UK is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_81mm_mortar_CLU_UK is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_81mm_mortar_NG_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_81mm_mortar_NG_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_81mm_mortar_Para_UK is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_81mm_mortar_Para_UK is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_81mm_mortar_UK is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_81mm_mortar_UK is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_81mm_mortar_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_81mm_mortar_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_AEC_Militant_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_AEC_Militant_UK is []
HumanSubDepictions_AT_2A45_SprutB_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_AT_2A45_SprutB_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_AT_D44_85mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_AT_D44_85mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_AT_D44_85mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_AT_D44_85mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_AT_D44_85mm_VDV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_AT_D44_85mm_VDV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_AT_D48_85mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_AT_D48_85mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_AT_KSM65_100mm_SOV is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_AT_KSM65_100mm_SOV is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_AT_T12R_Ruta_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_AT_T12R_Ruta_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_AT_T12_Rapira_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_AT_T12_Rapira_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_AT_T12_Rapira_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_AT_T12_Rapira_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_AT_ZiS2_57mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_AT_ZiS2_57mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_AT_vz52_85mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_AT_vz52_85mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_ATteam_Fagot_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Fagot_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_ATteam_Fagot_FJ_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Fagot_FJ_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
    ),
]
HumanSubDepictions_ATteam_Fagot_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Fagot_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_ATteam_ITOW_NG_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_ITOW_NG_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_D_US
    ),
]
HumanSubDepictions_ATteam_ITOW_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_ITOW_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_ATteam_ITOW_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_ITOW_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_D_US
    ),
]
HumanSubDepictions_ATteam_KonkursM_TTsko_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_KonkursM_TTsko_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
    ),
]
HumanSubDepictions_ATteam_Konkurs_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Konkurs_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_ATteam_Konkurs_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Konkurs_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_ATteam_Konkurs_TTsko_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Konkurs_TTsko_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
    ),
]
HumanSubDepictions_ATteam_Milan_1_BEL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_1_BEL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
    ),
]
HumanSubDepictions_ATteam_Milan_1_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_1_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_ATteam_Milan_1_RFA is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_1_RFA is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
    ),
]
HumanSubDepictions_ATteam_Milan_1_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_1_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_ATteam_Milan_1_para_BEL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_1_para_BEL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
    ),
]
HumanSubDepictions_ATteam_Milan_1_para_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_1_para_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
    ),
]
HumanSubDepictions_ATteam_Milan_2_BEL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_BEL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
    ),
]
HumanSubDepictions_ATteam_Milan_2_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_ATteam_Milan_2_RFA is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_RFA is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
    ),
]
HumanSubDepictions_ATteam_Milan_2_RIMa_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_RIMa_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_ATteam_Milan_2_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_ATteam_Milan_2_para_BEL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_para_BEL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
    ),
]
HumanSubDepictions_ATteam_Milan_2_para_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_para_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
    ),
]
HumanSubDepictions_ATteam_Milan_2_para_RFA is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_G_RFA_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_para_RFA is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_G_RFA
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_D_RFA
    ),
]
HumanSubDepictions_ATteam_Milan_2_para_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_Milan_2_para_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_ATteam_RCL_B11_Reserve_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Rezervisti_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Rezervisti_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Rezervisti_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Rezervisti_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_B11_Reserve_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Rezervisti_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Rezervisti_SOV
    ),
]
HumanSubDepictions_ATteam_RCL_M40A1_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_M40A1_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_ATteam_RCL_M40A1_NG_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_M40A1_NG_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_ATteam_RCL_M40A1_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_M40A1_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_ATteam_RCL_M40A1_RFA is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_M40A1_RFA is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
    ),
]
HumanSubDepictions_ATteam_RCL_SPG9_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_SPG9_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
    ),
]
HumanSubDepictions_ATteam_RCL_SPG9_DShV_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_SPG9_DShV_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV
    ),
]
HumanSubDepictions_ATteam_RCL_SPG9_FJ_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_SPG9_FJ_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
    ),
]
HumanSubDepictions_ATteam_RCL_SPG9_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_SPG9_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_ATteam_RCL_SPG9_Para_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_SPG9_Para_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
    ),
]
HumanSubDepictions_ATteam_RCL_SPG9_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_SPG9_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_ATteam_RCL_SPG9_VDV_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_RCL_SPG9_VDV_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV
    ),
]
HumanSubDepictions_ATteam_TOW2A_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_NCO_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_NCO_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_TOW2A_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_NCO_D_US
    ),
]
HumanSubDepictions_ATteam_TOW2_Aero_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_TOW2_Aero_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_ATteam_TOW2_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_TOW2_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_ATteam_TOW2_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_NCO_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_NCO_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_TOW2_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_NCO_D_US
    ),
]
HumanSubDepictions_ATteam_TOW2_para_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_TOW2_para_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_ATteam_TOW_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_TOW_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_ATteam_TOW_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_ATteam_TOW_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_Alvis_Stalwart_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Alvis_Stalwart_UK is []
HumanSubDepictions_Atteam_Dragon_Marines_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Marinier_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Marinier_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Marinier_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Marinier_NL_LOW
    ),
]
HumanSubDepictionsShowroom_Atteam_Dragon_Marines_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Marinier_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Marinier_NL
    ),
]
HumanSubDepictions_Atteam_Fagot_DShV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Atteam_Fagot_DShV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Atteam_Fagot_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Atteam_Fagot_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Atteam_Fagot_Para_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Atteam_Fagot_Para_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
    ),
]
HumanSubDepictions_Atteam_Fagot_VDV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Atteam_Fagot_VDV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Atteam_Konkurs_DShV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Atteam_Konkurs_DShV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
    ),
]
HumanSubDepictions_Atteam_Konkurs_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Atteam_Konkurs_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Atteam_Konkurs_VDV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Atteam_Konkurs_VDV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
    ),
]
HumanSubDepictions_BAV_485_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_BAV_485_POL is []
HumanSubDepictions_BAV_485_Supply_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_BAV_485_Supply_POL is []
HumanSubDepictions_BM14M_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_BM14M_POL is []
HumanSubDepictions_BM21V_GradV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_BM21V_GradV_SOV is []
HumanSubDepictions_BM21_Grad_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_BM21_Grad_POL is []
HumanSubDepictions_BM21_Grad_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_BM21_Grad_SOV is []
HumanSubDepictions_BM24M_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_BM24M_DDR is []
HumanSubDepictions_BM24M_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_BM24M_POL is []
HumanSubDepictions_BM24M_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_BM24M_SOV is []
HumanSubDepictions_BM27_Uragan_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_BM27_Uragan_SOV is []
HumanSubDepictions_BTR_152A_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_BTR_152A_DDR is []
HumanSubDepictions_BTR_152A_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_BTR_152A_SOV is []
HumanSubDepictions_BTR_40A_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_BTR_40A_SOV is []
HumanSubDepictions_BTR_40_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_BTR_40_SOV is []
HumanSubDepictions_BTR_ZD_Skrezhet_SOV is 
[
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_BTR_ZD_Skrezhet_SOV is []
HumanSubDepictions_Bedford_MJ_4t_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Bedford_MJ_4t_UK is []
HumanSubDepictions_Bedford_MJ_4t_trans_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Bedford_MJ_4t_trans_UK is []
HumanSubDepictions_Bofors_40mm_RFA is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Bofors_40mm_RFA is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
    ),
]
HumanSubDepictions_Bofors_40mm_capture_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Bofors_40mm_capture_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
    ),
]
HumanSubDepictions_CGage_Peacekeeper_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_CGage_Peacekeeper_US is []
HumanSubDepictions_CGage_V150_Commando_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_CGage_V150_Commando_US is []
HumanSubDepictions_CUCV_AGL_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_CUCV_AGL_US is []
HumanSubDepictions_CUCV_HMG_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_CUCV_HMG_US is []
HumanSubDepictions_CUCV_Hellfire_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_CUCV_Hellfire_US is []
HumanSubDepictions_CUCV_MP_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_CUCV_MP_US is []
HumanSubDepictions_CUCV_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_CUCV_US is []
HumanSubDepictions_CUCV_trans_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_CUCV_trans_US is []
HumanSubDepictions_DAF_YA_4400_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_DAF_YA_4400_NL is []
HumanSubDepictions_DAF_YA_4400_supply_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_DAF_YA_4400_supply_NL is []
HumanSubDepictions_DAF_YHZ_2300_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_DAF_YHZ_2300_NL is []
HumanSubDepictions_DAF_YHZ_2300_trans_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_DAF_YHZ_2300_trans_NL is []
HumanSubDepictions_DCA_53T2_20mm_FR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_53T2_20mm_FR is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_DCA_53T2_20mm_Para_FR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_53T2_20mm_Para_FR is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
    ),
]
HumanSubDepictions_DCA_76T2_20mm_CPA_FR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_76T2_20mm_CPA_FR is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_DCA_AZP_S60_DDR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_AZP_S60_DDR is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_DCA_AZP_S60_POL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_AZP_S60_POL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_DCA_AZP_S60_SOV is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_AZP_S60_SOV is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_DCA_Bofors_L60_FR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_Bofors_L60_FR is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_DCA_Bofors_upgrade_NL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_Bofors_upgrade_NL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_DCA_FASTA_4_DDR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_GunnerStandingUpIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_FASTA_4_DDR is 
[
    ShowroomSubDepiction_GunnerStandingUpLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_DCA_FK20_2_20mm_RFA is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_RFA_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_RFA_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_FK20_2_20mm_RFA is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_RFA
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_RFA
    ),
]
HumanSubDepictions_DCA_FK20_2_20mm_Zwillinge_RFA is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_spe_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_spe_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_FK20_2_20mm_Zwillinge_RFA is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_spe_D_RFA
    ),
]
HumanSubDepictions_DCA_I_Hawk_BEL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_I_Hawk_BEL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
    ),
]
HumanSubDepictions_DCA_I_Hawk_NL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_I_Hawk_NL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_DCA_I_Hawk_RFA is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_spe_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_spe_G_RFA_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_I_Hawk_RFA is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_spe_G_RFA
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA
    ),
]
HumanSubDepictions_DCA_I_Hawk_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_I_Hawk_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_DCA_I_Hawk_capture_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_I_Hawk_capture_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
    ),
]
HumanSubDepictions_DCA_Javelin_LML_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_Javelin_LML_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_DCA_KS19_100mm_DDR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_KS19_100mm_DDR is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_DCA_KS30_130mm_SOV is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_KS30_130mm_SOV is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_DCA_M167A2_Vulcan_20mm_Aero_US is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_M167A2_Vulcan_20mm_Aero_US is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_DCA_M167A2_Vulcan_20mm_US is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_M167A2_Vulcan_20mm_US is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_DCA_M167_Vulcan_20mm_BEL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_M167_Vulcan_20mm_BEL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
    ),
]
HumanSubDepictions_DCA_M167_Vulcan_20mm_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_M167_Vulcan_20mm_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_DCA_M167_Vulcan_20mm_nonPara_US is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_M167_Vulcan_20mm_nonPara_US is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_DCA_M167_Vulcan_para_20mm_BEL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_M167_Vulcan_para_20mm_BEL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
    ),
]
HumanSubDepictions_DCA_M55_NL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_M55_NL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_DCA_Oerlikon_GDF_002_35mm_UK is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_Oerlikon_GDF_002_35mm_UK is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_DCA_Rapier_Darkfire_UK is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_Rapier_Darkfire_UK is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_DCA_Rapier_FSA_UK is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_Rapier_FSA_UK is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_DCA_Rapier_UK is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_Rapier_UK is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_DCA_XM85_Chaparral_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_XM85_Chaparral_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_DCA_XMIM_115A_Roland_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_XMIM_115A_Roland_US is []
HumanSubDepictions_DCA_ZPU4_DDR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZPU4_DDR is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_DCA_ZPU4_POL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZPU4_POL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_DCA_ZUR_23_2S_JOD_POL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZUR_23_2S_JOD_POL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_DCA_ZUR_23_2S_JOD_Para_POL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZUR_23_2S_JOD_Para_POL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
    ),
]
HumanSubDepictions_DCA_ZU_23_2_DDR is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZU_23_2_DDR is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_DCA_ZU_23_2_POL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZU_23_2_POL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_DCA_ZU_23_2_Para_POL is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZU_23_2_Para_POL is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
    ),
]
HumanSubDepictions_DCA_ZU_23_2_SOV is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZU_23_2_SOV is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_DCA_ZU_23_2_TTsko_SOV is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZU_23_2_TTsko_SOV is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
    ),
]
HumanSubDepictions_DCA_ZU_23_2_nonPara_SOV is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantWalkOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
    SubDepiction_GunnerIdleOnlyRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_DCA_ZU_23_2_nonPara_SOV is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_GunnerRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Dragoon_300_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_Dragoon_300_US is []
HumanSubDepictions_FAV_AGL_US is 
[
    SubDepiction_DriverLSV
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_FAV_AGL_US is []
HumanSubDepictions_FAV_HMG_US is 
[
    SubDepiction_DriverLSV
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_FAV_HMG_US is []
HumanSubDepictions_FAV_TOW_US is 
[
    SubDepiction_DriverLSV
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_FAV_TOW_US is []
HumanSubDepictions_FAV_trans_US is 
[
    SubDepiction_DriverLSV
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_FAV_trans_US is []
HumanSubDepictions_FH70_155mm_RFA is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_FH70_155mm_RFA is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA
    ),
]
HumanSubDepictions_FH70_155mm_UK is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_GunnerIdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_FH70_155mm_UK is 
[
    ShowroomSubDepiction_GunnerLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_Faun_Kraka_20mm_RFA is 
[
    SubDepiction_DriverUndeployed
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA_LOW
    ),
]
HumanSubTurretDepictions_Faun_Kraka_20mm_RFA is 
[
    SubDepiction_GunnerDeployed
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Faun_Kraka_20mm_RFA is []
HumanSubDepictions_Faun_Kraka_Log_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Faun_Kraka_Log_RFA is []
HumanSubDepictions_Faun_Kraka_TOW_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Faun_Kraka_TOW_RFA is []
HumanSubDepictions_Faun_kraka_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Faun_kraka_RFA is []
HumanSubDepictions_GAZ_46_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_46_DDR is []
HumanSubDepictions_GAZ_46_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_46_POL is []
HumanSubDepictions_GAZ_46_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_46_SOV is []
HumanSubDepictions_GAZ_66B_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66B_POL is []
HumanSubDepictions_GAZ_66B_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66B_SOV is []
HumanSubDepictions_GAZ_66B_ZU_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66B_ZU_SOV is []
HumanSubDepictions_GAZ_66B_supply_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66B_supply_POL is []
HumanSubDepictions_GAZ_66B_supply_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66B_supply_SOV is []
HumanSubDepictions_GAZ_66_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66_POL is []
HumanSubDepictions_GAZ_66_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66_SOV is []
HumanSubDepictions_GAZ_66_supply_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66_supply_SOV is []
HumanSubDepictions_GAZ_66_trans_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_GAZ_66_trans_POL is []
HumanSubDepictions_GTMU_1D_ZU_SOV is 
[
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_GTMU_1D_ZU_SOV is []
HumanSubDepictions_Gama_Goat_supply_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
]
HumanSubDepictionsShowroom_Gama_Goat_supply_US is []
HumanSubDepictions_Gama_Goat_trans_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
]
HumanSubDepictionsShowroom_Gama_Goat_trans_US is []
HumanSubDepictions_HEMTT_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_HEMTT_US is []
HumanSubDepictions_HMGteam_AANF1_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AANF1_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_HMGteam_AANF1_Reserve_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AANF1_Reserve_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_HMGteam_AANF1_para_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AANF1_para_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
    ),
]
HumanSubDepictions_HMGteam_AGS17_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AGS17_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_HMGteam_AGS17_DShV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AGS17_DShV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_HMGteam_AGS17_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AGS17_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_HMGteam_AGS17_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AGS17_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_HMGteam_AGS17_TTsko_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AGS17_TTsko_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
    ),
]
HumanSubDepictions_HMGteam_AGS17_VDV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_AGS17_VDV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_HMGteam_DShK_AA_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_DShK_AA_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_HMGteam_DShK_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_DShK_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_HMGteam_KPVT_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_KPVT_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_HMGteam_M1919A4_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M1919A4_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_HMGteam_M2HB_AB_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_AB_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_HMGteam_M2HB_Aero_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_Aero_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_HMGteam_M2HB_BEL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_BEL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
    ),
]
HumanSubDepictions_HMGteam_M2HB_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_HMGteam_M2HB_LUX is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_LUX is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX
    ),
]
HumanSubDepictions_HMGteam_M2HB_M63_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_M63_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_HMGteam_M2HB_NG_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_NG_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_HMGteam_M2HB_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_HMGteam_M2HB_RIMa_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_RIMa_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_HMGteam_M2HB_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_HMGteam_M2HB_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_HMGteam_M2HB_para_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_para_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR
    ),
]
HumanSubDepictions_HMGteam_M2HB_para_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M2HB_para_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_HMGteam_M60_AB_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M60_AB_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_HMGteam_M60_Aero_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M60_Aero_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_HMGteam_M60_NG_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M60_NG_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_HMGteam_M60_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_M60_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_HMGteam_MAG_BEL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_MAG_BEL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
    ),
]
HumanSubDepictions_HMGteam_MAG_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_MAG_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_HMGteam_MAG_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_MAG_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_HMGteam_MAG_para_BEL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_MAG_para_BEL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
    ),
]
HumanSubDepictions_HMGteam_MAG_para_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_MAG_para_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_HMGteam_MG3_FJ_RFA is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_G_RFA_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_MG3_FJ_RFA is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_G_RFA
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_D_RFA
    ),
]
HumanSubDepictions_HMGteam_MG3_RFA is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_MG3_RFA is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
    ),
]
HumanSubDepictions_HMGteam_Maxim_Reserve_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Rezervisti_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Rezervisti_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Rezervisti_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Rezervisti_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_Maxim_Reserve_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Rezervisti_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Rezervisti_SOV
    ),
]
HumanSubDepictions_HMGteam_Mk19_AB_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_Mk19_AB_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_HMGteam_Mk19_US is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_Mk19_US is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_HMGteam_NSV_6U6_VDV_SOV is 
[
    SubDepiction_ServantWalkOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV_LOW
    ),
    SubDepiction_GunnerSittingDown_6U6IdleOnlyLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_NSV_6U6_VDV_SOV is 
[
    ShowroomSubDepiction_GunnerSittingDown_6U6_Left
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
    ),
]
HumanSubDepictions_HMGteam_NSV_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_NSV_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_HMGteam_NSV_DShV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_NSV_DShV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
    ),
]
HumanSubDepictions_HMGteam_NSV_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_NSV_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_HMGteam_NSV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_NSV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_HMGteam_NSV_TTsko_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_NSV_TTsko_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
    ),
]
HumanSubDepictions_HMGteam_NSV_VDV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_NSV_VDV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
    ),
]
HumanSubDepictions_HMGteam_PKM_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_HMGteam_PKM_DShV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_DShV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_HMGteam_PKM_FJ_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_FJ_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
    ),
]
HumanSubDepictions_HMGteam_PKM_Naval_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Naval_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Naval_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Naval_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Naval_POL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_Naval_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Naval_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Naval_POL
    ),
]
HumanSubDepictions_HMGteam_PKM_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_HMGteam_PKM_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_HMGteam_PKM_TTsko_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_TTsko_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
    ),
]
HumanSubDepictions_HMGteam_PKM_VDV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_VDV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_HMGteam_PKM_para_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_HMGteam_PKM_para_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
    ),
]
HumanSubDepictions_Hibneryt_KG_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Hibneryt_KG_POL is []
HumanSubDepictions_Hibneryt_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Hibneryt_POL is []
HumanSubDepictions_Honker_4011_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Honker_4011_POL is []
HumanSubDepictions_Honker_RYS_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Honker_RYS_POL is []
HumanSubDepictions_Howz_2A36_Giatsint_B_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_2A36_Giatsint_B_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Howz_A19_122mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_A19_122mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Howz_B4M_203mm_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_B4M_203mm_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Howz_BS3_100mm_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_BS3_100mm_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Howz_Br5M_280mm_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_Br5M_280mm_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Howz_D1_152mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_D1_152mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Howz_D1_152mm_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_D1_152mm_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Howz_D20_152mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_D20_152mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_Howz_D20_152mm_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_D20_152mm_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Howz_D30_122mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_D30_122mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_Howz_D30_122mm_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_D30_122mm_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Howz_D30_122mm_VDV_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_D30_122mm_VDV_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV
    ),
]
HumanSubDepictions_Howz_L118_105mm_LUX is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_L118_105mm_LUX is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX
    ),
]
HumanSubDepictions_Howz_L118_105mm_UK is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_L118_105mm_UK is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_Howz_M101_105mm_FR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M101_105mm_FR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_Howz_M101_105mm_RFA is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M101_105mm_RFA is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA
    ),
]
HumanSubDepictions_Howz_M101_105mm_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M101_105mm_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_Howz_M101_105mm_para_BEL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M101_105mm_para_BEL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
    ),
]
HumanSubDepictions_Howz_M102_105mm_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M102_105mm_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_Howz_M114_155mm_NL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M114_155mm_NL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_Howz_M114_39_155mm_NL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M114_39_155mm_NL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_Howz_M119_105mm_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M119_105mm_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_Howz_M198_155mm_Copperhead_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M198_155mm_Copperhead_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_Howz_M198_155mm_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M198_155mm_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_US
    ),
]
HumanSubDepictions_Howz_M30_122mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M30_122mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_Howz_M30_122mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M30_122mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Howz_M46_130mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M46_130mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_Howz_M46_130mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_M46_130mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Howz_ML20_152mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_ML20_152mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Howz_MstaB_150mm_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_MstaB_150mm_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Howz_ZiS3_76mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Howz_ZiS3_76mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_Iltis_CMD_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_CMD_BEL is []
HumanSubDepictions_Iltis_HMG_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_HMG_BEL is []
HumanSubDepictions_Iltis_MILAN_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_MILAN_BEL is []
HumanSubDepictions_Iltis_MILAN_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_MILAN_RFA is []
HumanSubDepictions_Iltis_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_RFA is []
HumanSubDepictions_Iltis_para_CMD_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_para_CMD_BEL is []
HumanSubDepictions_Iltis_para_CMD_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_para_CMD_RFA is []
HumanSubDepictions_Iltis_trans_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_trans_BEL is []
HumanSubDepictions_Iltis_trans_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Iltis_trans_RFA is []
HumanSubDepictions_KrAZ_255B_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_KrAZ_255B_POL is []
HumanSubDepictions_KrAZ_255B_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_KrAZ_255B_SOV is []
HumanSubDepictions_KrAZ_255B_supply_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_KrAZ_255B_supply_DDR is []
HumanSubDepictions_KrAZ_255B_supply_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_KrAZ_255B_supply_POL is []
HumanSubDepictions_KrAZ_255B_supply_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_KrAZ_255B_supply_SOV is []
HumanSubDepictions_LO_1800_FASTA_4_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_LO_1800_FASTA_4_DDR is []
HumanSubDepictions_LO_1800_ZPU_2_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
]
HumanSubDepictionsShowroom_LO_1800_ZPU_2_POL is []
HumanSubDepictions_LSV_M2HB_UK is 
[
    SubDepiction_DriverLSV
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LSV_M2HB_UK is []
HumanSubDepictions_LSV_MILAN_UK is 
[
    SubDepiction_DriverLSV
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LSV_MILAN_UK is []
HumanSubDepictions_LUAZ_967M_AGL_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_AGL_SOV is []
HumanSubDepictions_LUAZ_967M_AGL_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_AGL_VDV_SOV is []
HumanSubDepictions_LUAZ_967M_CMD_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_CMD_VDV_SOV is []
HumanSubDepictions_LUAZ_967M_FAO_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_FAO_SOV is []
HumanSubDepictions_LUAZ_967M_Fagot_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_Fagot_SOV is []
HumanSubDepictions_LUAZ_967M_Fagot_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_Fagot_VDV_SOV is []
HumanSubDepictions_LUAZ_967M_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_SOV is []
HumanSubDepictions_LUAZ_967M_SPG9_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_SPG9_SOV is []
HumanSubDepictions_LUAZ_967M_SPG9_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_SPG9_VDV_SOV is []
HumanSubDepictions_LUAZ_967M_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_VDV_SOV is []
HumanSubDepictions_LUAZ_967M_supply_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LUAZ_967M_supply_SOV is []
HumanSubDepictions_LandRover_CMD_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_CMD_NL is []
HumanSubDepictions_LandRover_CMD_Para_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_CMD_Para_UK is []
HumanSubDepictions_LandRover_CMD_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_CMD_UK is []
HumanSubDepictions_LandRover_MILAN_Para_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_MILAN_Para_UK is []
HumanSubDepictions_LandRover_MILAN_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_MILAN_UK is []
HumanSubDepictions_LandRover_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_NL is []
HumanSubDepictions_LandRover_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_UK is []
HumanSubDepictions_LandRover_WOMBAT_Gurkhas_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_WOMBAT_Gurkhas_UK is []
HumanSubDepictions_LandRover_WOMBAT_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_LandRover_WOMBAT_UK is []
HumanSubDepictions_Lars_2_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Lars_2_RFA is []
HumanSubDepictions_LuAZ_967M_AA_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_LuAZ_967M_AA_VDV_SOV is []
HumanSubDepictions_M1025_Humvee_AGL_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_AGL_US is []
HumanSubDepictions_M1025_Humvee_AGL_nonPara_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_AGL_nonPara_US is []
HumanSubDepictions_M1025_Humvee_CMD_LUX is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_CMD_LUX is []
HumanSubDepictions_M1025_Humvee_CMD_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_CMD_US is []
HumanSubDepictions_M1025_Humvee_CMD_para_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_CMD_para_US is []
HumanSubDepictions_M1025_Humvee_GVLLD_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_GVLLD_US is []
HumanSubDepictions_M1025_Humvee_HMG_LUX is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_HMG_LUX is []
HumanSubDepictions_M1025_Humvee_MP_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_MP_US is []
HumanSubDepictions_M1025_Humvee_TOW_LUX is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_TOW_LUX is []
HumanSubDepictions_M1025_Humvee_TOW_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_TOW_US is []
HumanSubDepictions_M1025_Humvee_TOW_para_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_TOW_para_US is []
HumanSubDepictions_M1025_Humvee_scout_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_scout_US is []
HumanSubDepictions_M1025_Humvee_scout_tuto_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1025_Humvee_scout_tuto_US is []
HumanSubDepictions_M1038_Humvee_LUX is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1038_Humvee_LUX is []
HumanSubDepictions_M1038_Humvee_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M1038_Humvee_US is []
HumanSubDepictions_M151A2_TOW_NG_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M151A2_TOW_NG_US is []
HumanSubDepictions_M151A2_scout_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M151A2_scout_US is []
HumanSubDepictions_M151C_RCL_NG_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M151C_RCL_NG_US is []
HumanSubDepictions_M151_MUTT_CMD_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M151_MUTT_CMD_US is []
HumanSubDepictions_M151_MUTT_trans_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M151_MUTT_trans_DDR is []
HumanSubDepictions_M151_MUTT_trans_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M151_MUTT_trans_US is []
HumanSubDepictions_M201_CMD_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_M201_CMD_FR is []
HumanSubDepictions_M201_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_M201_FR is []
HumanSubDepictions_M201_MG_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_M201_MG_FR is []
HumanSubDepictions_M201_MILAN_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_M201_MILAN_FR is []
HumanSubDepictions_M274_Mule_ITOW_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
]
HumanSubDepictionsShowroom_M274_Mule_ITOW_US is []
HumanSubDepictions_M274_Mule_M2HB_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
]
HumanSubDepictionsShowroom_M274_Mule_M2HB_US is []
HumanSubDepictions_M274_Mule_RCL_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
]
HumanSubDepictionsShowroom_M274_Mule_RCL_US is []
HumanSubDepictions_M274_Mule_supply_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
]
HumanSubDepictionsShowroom_M274_Mule_supply_US is []
HumanSubDepictions_M35_supply_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M35_supply_US is []
HumanSubDepictions_M35_trans_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M35_trans_DDR is []
HumanSubDepictions_M35_trans_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M35_trans_US is []
HumanSubDepictions_M35_trans_tuto_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M35_trans_tuto_US is []
HumanSubDepictions_M38A1_CMD_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_M38A1_CMD_NL is []
HumanSubDepictions_M38A1_MG_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_M38A1_MG_NL is []
HumanSubDepictions_M38A1_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_M38A1_NL is []
HumanSubDepictions_M38A1_RCL_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_M38A1_RCL_NL is []
HumanSubDepictions_M38A1_TOW_NL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_NL_LOW
    ),
]
HumanSubDepictionsShowroom_M38A1_TOW_NL is []
HumanSubDepictions_M42_Duster_US is 
[
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
]
HumanSubDepictionsShowroom_M42_Duster_US is []
HumanSubDepictions_M48_Chaparral_MIM72F_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M48_Chaparral_MIM72F_US is []
HumanSubDepictions_M548A2_supply_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M548A2_supply_US is []
HumanSubDepictions_M812_supply_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M812_supply_US is []
HumanSubDepictions_M998_Avenger_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_M998_Avenger_US is []
HumanSubDepictions_M998_Avenger_nonPara_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_M998_Avenger_nonPara_US is []
HumanSubDepictions_M998_Humvee_AGL_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US_LOW
    ),
]
HumanSubDepictionsShowroom_M998_Humvee_AGL_US is []
HumanSubDepictions_M998_Humvee_Delta_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US_LOW
    ),
]
HumanSubDepictionsShowroom_M998_Humvee_Delta_US is []
HumanSubDepictions_M998_Humvee_HMG_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US_LOW
    ),
]
HumanSubDepictionsShowroom_M998_Humvee_HMG_US is []
HumanSubDepictions_M998_Humvee_LUX is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M998_Humvee_LUX is []
HumanSubDepictions_M998_Humvee_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_M998_Humvee_US is []
HumanSubDepictions_MAN_Kat_6x6_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_MAN_Kat_6x6_RFA is []
HumanSubDepictions_MAN_Kat_6x6_trans_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_MAN_Kat_6x6_trans_RFA is []
HumanSubDepictions_MAN_Z311_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_MAN_Z311_BEL is []
HumanSubDepictions_MAN_Z311_Mi50_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_MAN_Z311_Mi50_BEL is []
HumanSubDepictions_MLRS_WP_8z_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_MLRS_WP_8z_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Mortier_107mm_Aero_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_107mm_Aero_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_Mortier_107mm_Airborne_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_107mm_Airborne_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US
    ),
]
HumanSubDepictions_Mortier_107mm_BEL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_107mm_BEL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
    ),
]
HumanSubDepictions_Mortier_107mm_NG_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_107mm_NG_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_Mortier_107mm_NL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_107mm_NL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_Mortier_107mm_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_107mm_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_Mortier_107mm_para_BEL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_107mm_para_BEL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
    ),
]
HumanSubDepictions_Mortier_240mm_M240_Cluster_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_240mm_M240_Cluster_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Mortier_240mm_M240_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_240mm_M240_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Mortier_240mm_M240_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_240mm_M240_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Mortier_2B14_82mm_DShV_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2B14_82mm_DShV_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Mortier_2B14_82mm_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2B14_82mm_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Mortier_2B14_82mm_VDV_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2B14_82mm_VDV_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Mortier_2B9_Vasilek_Para_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2B9_Vasilek_Para_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
    ),
]
HumanSubDepictions_Mortier_2B9_Vasilek_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2B9_Vasilek_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Mortier_2B9_Vasilek_nonPara_SOV is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_MotoStrelki_spe_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_MotoStrelki_spe_G_SOV_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2B9_Vasilek_nonPara_SOV is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_MotoStrelki_spe_G_SOV
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Mortier_2S12_120mm_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2S12_120mm_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_Mortier_2S12_120mm_DShV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2S12_120mm_DShV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Mortier_2S12_120mm_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2S12_120mm_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Mortier_2S12_120mm_Para_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2S12_120mm_Para_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL
    ),
]
HumanSubDepictions_Mortier_2S12_120mm_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2S12_120mm_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Mortier_2S12_120mm_TTsko_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2S12_120mm_TTsko_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko
    ),
]
HumanSubDepictions_Mortier_2S12_120mm_VDV_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_2S12_120mm_VDV_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Mortier_81mm_BEL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_81mm_BEL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL
    ),
]
HumanSubDepictions_Mortier_81mm_FR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_81mm_FR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_Mortier_81mm_LUX is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_81mm_LUX is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX
    ),
]
HumanSubDepictions_Mortier_81mm_para_BEL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_81mm_para_BEL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL
    ),
]
HumanSubDepictions_Mortier_M29_81mm_Marines_NL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Marinier_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Marinier_NL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Marinier_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Marinier_NL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_M29_81mm_Marines_NL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_Marinier_NL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_Marinier_NL
    ),
]
HumanSubDepictions_Mortier_M29_81mm_NL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_M29_81mm_NL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_Mortier_M29_81mm_US is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_M29_81mm_US is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_US
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US
    ),
]
HumanSubDepictions_Mortier_M43_160mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_M43_160mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Mortier_M43_82mm_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_M43_82mm_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_Mortier_M43_82mm_FJ_DDR is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_M43_82mm_FJ_DDR is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR
    ),
]
HumanSubDepictions_Mortier_M43_82mm_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_M43_82mm_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Mortier_M43_82mm_Para_POL is 
[
    SubDepiction_ServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_M43_82mm_Para_POL is 
[
    ShowroomSubDepiction_ServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Mortier_MORT61_120mm_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_MORT61_120mm_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_Mortier_MORT61_120mm_NL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_MORT61_120mm_NL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL
    ),
]
HumanSubDepictions_Mortier_MORT61_120mm_para_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_MORT61_120mm_para_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_Mortier_Nona_K_120mm_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_Nona_K_120mm_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
    ),
]
HumanSubDepictions_Mortier_PM43_120mm_DDR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_PM43_120mm_DDR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
    ),
]
HumanSubDepictions_Mortier_PM43_120mm_POL is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_PM43_120mm_POL is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL
    ),
]
HumanSubDepictions_Mortier_PM43_120mm_SOV is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_PM43_120mm_SOV is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV
    ),
]
HumanSubDepictions_Mortier_Tampella_120mm_RFA is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_Tampella_120mm_RFA is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
    ),
]
HumanSubDepictions_Mortier_Tampella_120mm_para_RFA is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Mortier_Tampella_120mm_para_RFA is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA
    ),
]
HumanSubDepictions_OT_62_TOPAS_JOD_POL is 
[
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_OT_62_TOPAS_JOD_POL is []
HumanSubDepictions_Obusier_155mm_mle1950_FR is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_Obusier_155mm_mle1950_FR is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
    ),
]
HumanSubDepictions_PTS_M_supply_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_PTS_M_supply_DDR is []
HumanSubDepictions_PTS_M_supply_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_PTS_M_supply_POL is []
HumanSubDepictions_RCL_L6_Wombat_UK is 
[
    SubDepiction_ATGMServantLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK_LOW
    ),
    SubDepiction_ATGMServantRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK_LOW
    ),
]
HumanSubDepictionsShowroom_RCL_L6_Wombat_UK is 
[
    ShowroomSubDepiction_ATGMServantLeft
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK
    ),
    ShowroomSubDepiction_ATGMServantRight
    (
        MeshDescriptor = $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK
    ),
]
HumanSubDepictions_RM70_85_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_RM70_85_DDR is []
HumanSubDepictions_RM70_85_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_RM70_85_POL is []
HumanSubDepictions_Rover_101FC_LUX is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_Rover_101FC_LUX is []
HumanSubDepictions_Rover_101FC_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Rover_101FC_UK is []
HumanSubDepictions_Rover_101FC_supply_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Rover_101FC_supply_UK is []
HumanSubDepictions_Sonderwagen_4_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Sonderwagen_4_RFA is []
HumanSubDepictions_Sonderwagen_4_recon_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Sonderwagen_4_recon_RFA is []
HumanSubDepictions_Star_266_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Star_266_POL is []
HumanSubDepictions_Star_266_supply_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_Star_266_supply_POL is []
HumanSubDepictions_Supacat_ATMP_Javelin_LML_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Supacat_ATMP_Javelin_LML_UK is []
HumanSubDepictions_Supacat_ATMP_MILAN_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Supacat_ATMP_MILAN_UK is []
HumanSubDepictions_Supacat_ATMP_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Supacat_ATMP_UK is []
HumanSubDepictions_Supacat_ATMP_supply_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Supacat_ATMP_supply_UK is []
HumanSubDepictions_T813_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_T813_DDR is []
HumanSubDepictions_T813_trans_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_T813_trans_DDR is []
HumanSubDepictions_T815_supply_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_T815_supply_DDR is []
HumanSubDepictions_TPZ_Fuchs_1_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_TPZ_Fuchs_1_RFA is []
HumanSubDepictions_TPZ_Fuchs_CMD_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_TPZ_Fuchs_CMD_RFA is []
HumanSubDepictions_TPZ_Fuchs_MILAN_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_TPZ_Fuchs_MILAN_RFA is []
HumanSubDepictions_TPZ_Fuchs_RASIT_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_TPZ_Fuchs_RASIT_RFA is []
HumanSubDepictions_TRM_10000_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_TRM_10000_FR is []
HumanSubDepictions_TRM_10000_supply_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_TRM_10000_supply_FR is []
HumanSubDepictions_TRM_2000_20mm_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_TRM_2000_20mm_FR is []
HumanSubDepictions_TRM_2000_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_TRM_2000_FR is []
HumanSubDepictions_TRM_2000_supply_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_TRM_2000_supply_FR is []
HumanSubDepictions_TUTO_M1025_Humvee_US is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_US
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_US_LOW
    ),
]
HumanSubDepictionsShowroom_TUTO_M1025_Humvee_US is []
HumanSubDepictions_Tracked_Rapier_UK is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_UK
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_UK_LOW
    ),
]
HumanSubDepictionsShowroom_Tracked_Rapier_UK is []
HumanSubDepictions_UAZ_469_AGL_Grenzer_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_AGL_Grenzer_DDR is []
HumanSubDepictions_UAZ_469_AGL_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_AGL_SOV is []
HumanSubDepictions_UAZ_469_AGL_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_AGL_VDV_SOV is []
HumanSubDepictions_UAZ_469_CMD_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_CMD_DDR is []
HumanSubDepictions_UAZ_469_CMD_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_CMD_POL is []
HumanSubDepictions_UAZ_469_CMD_Para_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_CMD_Para_POL is []
HumanSubDepictions_UAZ_469_CMD_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_CMD_SOV is []
HumanSubDepictions_UAZ_469_CMD_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_CMD_VDV_SOV is []
HumanSubDepictions_UAZ_469_Fagot_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_Fagot_DDR is []
HumanSubDepictions_UAZ_469_Fagot_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_Fagot_POL is []
HumanSubDepictions_UAZ_469_Fagot_Para_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_Fagot_Para_POL is []
HumanSubDepictions_UAZ_469_Konkurs_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_Konkurs_VDV_SOV is []
HumanSubDepictions_UAZ_469_MP_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_MP_SOV is []
HumanSubDepictions_UAZ_469_Reco_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_Reco_DDR is []
HumanSubDepictions_UAZ_469_Reco_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_Reco_POL is []
HumanSubDepictions_UAZ_469_Reco_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_Reco_SOV is []
HumanSubDepictions_UAZ_469_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_SOV is []
HumanSubDepictions_UAZ_469_SPG9_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_SPG9_DDR is []
HumanSubDepictions_UAZ_469_SPG9_FJ_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_SPG9_FJ_DDR is []
HumanSubDepictions_UAZ_469_SPG9_Para_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_SPG9_Para_POL is []
HumanSubDepictions_UAZ_469_SPG9_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_SPG9_SOV is []
HumanSubDepictions_UAZ_469_SPG9_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_SPG9_VDV_SOV is []
HumanSubDepictions_UAZ_469_supply_Para_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_supply_Para_POL is []
HumanSubDepictions_UAZ_469_supply_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_supply_SOV is []
HumanSubDepictions_UAZ_469_supply_VDV_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_supply_VDV_SOV is []
HumanSubDepictions_UAZ_469_trans_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_trans_DDR is []
HumanSubDepictions_UAZ_469_trans_POL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_POL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_POL_LOW
    ),
]
HumanSubDepictionsShowroom_UAZ_469_trans_POL is []
HumanSubDepictions_Unimog_S_404_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Unimog_S_404_RFA is []
HumanSubDepictions_Unimog_U1350L_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Unimog_U1350L_BEL is []
HumanSubDepictions_Unimog_U1350L_Para_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_ParaCmdo_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_ParaCmdo_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Unimog_U1350L_Para_BEL is []
HumanSubDepictions_Unimog_U1350L_supply_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Unimog_U1350L_supply_BEL is []
HumanSubDepictions_Unimog_supply_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Unimog_supply_BEL is []
HumanSubDepictions_Unimog_trans_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Unimog_trans_BEL is []
HumanSubDepictions_Unimog_trans_RFA is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_RFA_LOW
    ),
]
HumanSubDepictionsShowroom_Unimog_trans_RFA is []
HumanSubDepictions_Ural_4320_ZPU_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Ural_4320_ZPU_SOV is []
HumanSubDepictions_Ural_4320_ZU_SOV is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV_LOW
    ),
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV_LOW
    ),
]
HumanSubDepictionsShowroom_Ural_4320_ZU_SOV is []
HumanSubDepictions_VAB_CMD_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VAB_CMD_FR is []
HumanSubDepictions_VAB_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VAB_FR is []
HumanSubDepictions_VAB_HOT_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VAB_HOT_FR is []
HumanSubDepictions_VAB_MILAN_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VAB_MILAN_FR is []
HumanSubDepictions_VAB_Mortar_81_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VAB_Mortar_81_FR is []
HumanSubDepictions_VAB_RASIT_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VAB_RASIT_FR is []
HumanSubDepictions_VAB_Reserve_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VAB_Reserve_FR is []
HumanSubDepictions_VBL_MILAN_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VBL_MILAN_FR is []
HumanSubDepictions_VBL_PC_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VBL_PC_FR is []
HumanSubDepictions_VBL_Reco_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VBL_Reco_FR is []
HumanSubDepictions_VLRA_20mm_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLRA_20mm_FR is []
HumanSubDepictions_VLRA_HMG_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLRA_HMG_FR is []
HumanSubDepictions_VLRA_MILAN_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLRA_MILAN_FR is []
HumanSubDepictions_VLRA_Mistral_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLRA_Mistral_FR is []
HumanSubDepictions_VLRA_Mortier81_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLRA_Mortier81_FR is []
HumanSubDepictions_VLRA_supply_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLRA_supply_FR is []
HumanSubDepictions_VLRA_trans_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLRA_trans_FR is []
HumanSubDepictions_VLTT_P4_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLTT_P4_FR is []
HumanSubDepictions_VLTT_P4_MILAN_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLTT_P4_MILAN_FR is []
HumanSubDepictions_VLTT_P4_MILAN_para_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLTT_P4_MILAN_para_FR is []
HumanSubDepictions_VLTT_P4_PC_FR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_FR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_FR_LOW
    ),
]
HumanSubDepictionsShowroom_VLTT_P4_PC_FR is []
HumanSubDepictions_Volvo_N10_supply_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Volvo_N10_supply_BEL is []
HumanSubDepictions_Volvo_N10_trans_BEL is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_BEL_LOW
    ),
]
HumanSubDepictionsShowroom_Volvo_N10_trans_BEL is []
HumanSubDepictions_W50_LA_A_25mm_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_W50_LA_A_25mm_DDR is []
HumanSubDepictions_W50_LA_A_DDR is 
[
    SubDepiction_Driver
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Driver_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_W50_LA_A_DDR is []
HumanSubDepictions_ZSU_57_2_DDR is 
[
    SubDepiction_GunnerLeft
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR_LOW
    ),
    SubDepiction_GunnerRight
    (
        MeshDescriptorHigh = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR
        MeshDescriptorLow = $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR_LOW
    ),
]
HumanSubDepictionsShowroom_ZSU_57_2_DDR is []
unnamed TTransportedInfantryCatalogEntries
(
    Catalog = $/DepictionCore/TransportedInfantryCatalog
    Entries = 
    [
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "81mm_mortar_Aero_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "81mm_mortar_CLU_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "81mm_mortar_NG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "81mm_mortar_Para_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "81mm_mortar_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "81mm_mortar_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "AEC_Militant_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_2A45_SprutB_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_D44_85mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_D44_85mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_D44_85mm_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_D48_85mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_KSM65_100mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_T12R_Ruta_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_T12_Rapira_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_T12_Rapira_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_ZiS2_57mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "AT_vz52_85mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Fagot_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Fagot_FJ_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Fagot_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_ITOW_NG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_ITOW_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_ITOW_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_KonkursM_TTsko_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Konkurs_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Konkurs_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Konkurs_TTsko_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_1_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_1_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_1_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_1_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_1_para_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_1_para_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_RIMa_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_para_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_para_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_para_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_Milan_2_para_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_B11_Reserve_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Rezervisti_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Rezervisti_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_M40A1_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_M40A1_NG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_M40A1_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_M40A1_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_SPG9_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_SPG9_DShV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_SPG9_FJ_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_SPG9_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_SPG9_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_SPG9_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_RCL_SPG9_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_TOW2A_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_NCO_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_TOW2_Aero_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_TOW2_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_TOW2_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_NCO_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_TOW2_para_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_TOW_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ATteam_TOW_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Alvis_Stalwart_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Atteam_Dragon_Marines_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Marinier_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Marinier_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Atteam_Fagot_DShV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Atteam_Fagot_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Atteam_Fagot_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Atteam_Fagot_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Atteam_Konkurs_DShV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Atteam_Konkurs_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Atteam_Konkurs_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BAV_485_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BAV_485_Supply_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BM14M_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BM21V_GradV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BM21_Grad_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BM21_Grad_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BM24M_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BM24M_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BM24M_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BM27_Uragan_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "BTR_152A_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "BTR_152A_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "BTR_40A_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "BTR_40_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "BTR_ZD_Skrezhet_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Bedford_MJ_4t_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Bedford_MJ_4t_trans_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Bofors_40mm_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Bofors_40mm_capture_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "CGage_Peacekeeper_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "CGage_V150_Commando_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "CUCV_AGL_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "CUCV_HMG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "CUCV_Hellfire_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "CUCV_MP_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "CUCV_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "CUCV_trans_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "DAF_YA_4400_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "DAF_YA_4400_supply_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "DAF_YHZ_2300_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "DAF_YHZ_2300_trans_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_53T2_20mm_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_53T2_20mm_Para_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_76T2_20mm_CPA_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_AZP_S60_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_AZP_S60_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_AZP_S60_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_Bofors_L60_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_Bofors_upgrade_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_FASTA_4_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_FK20_2_20mm_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_FK20_2_20mm_Zwillinge_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_spe_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_I_Hawk_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_I_Hawk_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_I_Hawk_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_spe_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_I_Hawk_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_I_Hawk_capture_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_Javelin_LML_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_KS19_100mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_KS30_130mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_M167A2_Vulcan_20mm_Aero_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_M167A2_Vulcan_20mm_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_M167_Vulcan_20mm_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_M167_Vulcan_20mm_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_M167_Vulcan_20mm_nonPara_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_M167_Vulcan_para_20mm_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_M55_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_Oerlikon_GDF_002_35mm_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_Rapier_Darkfire_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_Rapier_FSA_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_Rapier_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_XM85_Chaparral_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_spe_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "DCA_XMIM_115A_Roland_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZPU4_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZPU4_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZUR_23_2S_JOD_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZUR_23_2S_JOD_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZU_23_2_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZU_23_2_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZU_23_2_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZU_23_2_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZU_23_2_TTsko_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "DCA_ZU_23_2_nonPara_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Dragoon_300_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "FAV_AGL_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "FAV_HMG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "FAV_TOW_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "FAV_trans_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "FH70_155mm_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "FH70_155mm_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Faun_Kraka_20mm_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Faun_Kraka_Log_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Faun_Kraka_TOW_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Faun_kraka_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_46_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_46_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_46_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_66B_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_66B_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 3
            Identifier = "GAZ_66B_ZU_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
            ]
            UniqueCount = 3
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_66B_supply_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_66B_supply_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_66_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_66_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_66_supply_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "GAZ_66_trans_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "GTMU_1D_ZU_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Gama_Goat_supply_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Gama_Goat_trans_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "HEMTT_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AANF1_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AANF1_Reserve_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AANF1_para_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AGS17_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AGS17_DShV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AGS17_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AGS17_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AGS17_TTsko_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_AGS17_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_DShK_AA_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_DShK_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_KPVT_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M1919A4_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_AB_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_Aero_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_M63_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_NG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_RIMa_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_para_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_Sep_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Para_NCO_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M2HB_para_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M60_AB_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M60_Aero_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M60_NG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_M60_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_MAG_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_MAG_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_MAG_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_MAG_para_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_MAG_para_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_MG3_FJ_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Fallschirm_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_MG3_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_Maxim_Reserve_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Rezervisti_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Rezervisti_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_Mk19_AB_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_Mk19_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_NSV_6U6_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_NSV_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_NSV_DShV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_NSV_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_NSV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_NSV_TTsko_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_NSV_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_DShV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_FJ_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_Naval_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Naval_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Naval_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_TTsko_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "HMGteam_PKM_para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Hibneryt_KG_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 3
            Identifier = "Hibneryt_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
            ]
            UniqueCount = 3
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Honker_4011_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Honker_RYS_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_2A36_Giatsint_B_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_A19_122mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_B4M_203mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_BS3_100mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_Br5M_280mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_D1_152mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_D1_152mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_D20_152mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_D20_152mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_D30_122mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_D30_122mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_D30_122mm_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Officer_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_L118_105mm_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_L118_105mm_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M101_105mm_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M101_105mm_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_PzGrenadier_Officer_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M101_105mm_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M101_105mm_para_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M102_105mm_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M114_155mm_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M114_39_155mm_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M119_105mm_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M198_155mm_Copperhead_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M198_155mm_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M30_122mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M30_122mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M46_130mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_M46_130mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_ML20_152mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_MstaB_150mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Howz_ZiS3_76mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_CMD_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_HMG_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_MILAN_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_MILAN_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_para_CMD_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_ParaCmdo_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_para_CMD_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FJ_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_trans_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Iltis_trans_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "KrAZ_255B_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "KrAZ_255B_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "KrAZ_255B_supply_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "KrAZ_255B_supply_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "KrAZ_255B_supply_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LO_1800_FASTA_4_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 3
            Identifier = "LO_1800_ZPU_2_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
            ]
            UniqueCount = 3
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LSV_M2HB_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LSV_MILAN_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_AGL_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_AGL_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_CMD_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_FAO_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_Fagot_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_Fagot_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_SPG9_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_SPG9_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LUAZ_967M_supply_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_CMD_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_CMD_Para_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_CMD_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_MILAN_Para_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_Para_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_MILAN_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_WOMBAT_Gurkhas_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LandRover_WOMBAT_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Lars_2_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "LuAZ_967M_AA_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_AGL_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_AGL_nonPara_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_CMD_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_CMD_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_CMD_para_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_GVLLD_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_HMG_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_MP_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_TOW_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_TOW_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_TOW_para_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_scout_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1025_Humvee_scout_tuto_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1038_Humvee_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M1038_Humvee_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M151A2_TOW_NG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M151A2_scout_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M151C_RCL_NG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M151_MUTT_CMD_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M151_MUTT_trans_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M151_MUTT_trans_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M201_CMD_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M201_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M201_MG_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M201_MILAN_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M274_Mule_ITOW_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M274_Mule_M2HB_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M274_Mule_RCL_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M274_Mule_supply_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M35_supply_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M35_trans_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M35_trans_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M35_trans_tuto_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M38A1_CMD_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M38A1_MG_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M38A1_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M38A1_RCL_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M38A1_TOW_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_NL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "M42_Duster_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M48_Chaparral_MIM72F_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M548A2_supply_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M812_supply_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "M998_Avenger_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "M998_Avenger_nonPara_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M998_Humvee_AGL_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M998_Humvee_Delta_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M998_Humvee_HMG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_Delta_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M998_Humvee_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "M998_Humvee_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "MAN_Kat_6x6_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "MAN_Kat_6x6_trans_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "MAN_Z311_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "MAN_Z311_Mi50_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "MLRS_WP_8z_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_107mm_Aero_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_107mm_Airborne_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Airborne_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_107mm_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_107mm_NG_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_107mm_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_107mm_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_107mm_para_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_240mm_M240_Cluster_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_240mm_M240_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_240mm_M240_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2B14_82mm_DShV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2B14_82mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2B14_82mm_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2B9_Vasilek_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2B9_Vasilek_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2B9_Vasilek_nonPara_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_MotoStrelki_spe_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2S12_120mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2S12_120mm_DShV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2S12_120mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2S12_120mm_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Para_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Para_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2S12_120mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2S12_120mm_TTsko_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_TTsko,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_TTsko,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_2S12_120mm_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_81mm_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_81mm_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_81mm_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_LUX,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_LUX,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_81mm_para_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_ParaCmdo_BEL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_ParaCmdo_BEL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_M29_81mm_Marines_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_Marinier_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_Marinier_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_M29_81mm_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_M29_81mm_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_US,
                $/GFX/DepictionResources/MeshDescriptor_Servant_GI_Officer_D_US,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_M43_160mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_M43_82mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_M43_82mm_FJ_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FJ_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FJ_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_M43_82mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_M43_82mm_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_MORT61_120mm_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_MORT61_120mm_NL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_NL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_NL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_MORT61_120mm_para_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_Nona_K_120mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_PM43_120mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_PM43_120mm_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_POL,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_POL,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_PM43_120mm_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_SOV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_Tampella_120mm_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Mortier_Tampella_120mm_para_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_RFA,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_RFA,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "OT_62_TOPAS_JOD_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Obusier_155mm_mle1950_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "PTS_M_supply_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "PTS_M_supply_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "RCL_L6_Wombat_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_UK,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_UK,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "RM70_85_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "RM70_85_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Rover_101FC_LUX"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Rover_101FC_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Rover_101FC_supply_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Sonderwagen_4_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Sonderwagen_4_recon_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Star_266_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Star_266_supply_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Supacat_ATMP_Javelin_LML_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Supacat_ATMP_MILAN_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Supacat_ATMP_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Supacat_ATMP_supply_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "T813_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "T813_trans_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "T815_supply_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TPZ_Fuchs_1_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TPZ_Fuchs_CMD_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TPZ_Fuchs_MILAN_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TPZ_Fuchs_RASIT_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TRM_10000_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TRM_10000_supply_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "TRM_2000_20mm_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TRM_2000_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TRM_2000_supply_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "TUTO_M1025_Humvee_US"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_US,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Tracked_Rapier_UK"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_UK,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_AGL_Grenzer_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_AGL_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_AGL_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_CMD_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_CMD_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_CMD_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_CMD_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_CMD_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_Fagot_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_Fagot_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_Fagot_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_Konkurs_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_MP_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_Reco_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_Reco_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_Reco_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_SPG9_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_SPG9_FJ_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_SPG9_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_SPG9_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_SPG9_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_supply_Para_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_Para_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_supply_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_supply_VDV_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_trans_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "UAZ_469_trans_POL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_POL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Unimog_S_404_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Unimog_U1350L_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Unimog_U1350L_Para_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_ParaCmdo_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Unimog_U1350L_supply_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Unimog_supply_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Unimog_trans_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Unimog_trans_RFA"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_RFA,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "Ural_4320_ZPU_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_VDV,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 3
            Identifier = "Ural_4320_ZU_SOV"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_VdV_SOV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_Spe_D_VDV,
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_VDV,
            ]
            UniqueCount = 3
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VAB_CMD_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VAB_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VAB_HOT_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VAB_MILAN_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VAB_Mortar_81_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VAB_RASIT_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VAB_Reserve_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VBL_MILAN_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VBL_PC_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VBL_Reco_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "VLRA_20mm_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_FR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLRA_HMG_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLRA_MILAN_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLRA_Mistral_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLRA_Mortier81_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLRA_supply_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLRA_trans_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLTT_P4_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLTT_P4_MILAN_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLTT_P4_MILAN_para_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "VLTT_P4_PC_FR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_FR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Volvo_N10_supply_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "Volvo_N10_trans_BEL"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_BEL,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "W50_LA_A_25mm_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
        TTransportedInfantryEntry
        (
            Count = 1
            Identifier = "W50_LA_A_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Driver_DDR,
            ]
            UniqueCount = 1
        ),
        TTransportedInfantryEntry
        (
            Count = 2
            Identifier = "ZSU_57_2_DDR"
            Meshes = 
            [
                $/GFX/DepictionResources/MeshDescriptor_Servant_G_DDR,
                $/GFX/DepictionResources/MeshDescriptor_Servant_D_DDR,
            ]
            UniqueCount = 2
        ),
    ]
    GfxProperties = ~/GfxProperties
)