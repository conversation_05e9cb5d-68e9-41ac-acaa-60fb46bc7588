// Ne pas éditer, ce fichier est généré par DepictionMimeticGhostsFileWriter



unnamed TMimeticGhostRegistration
(
    GfxProperties = ~/GfxProperties
    MimeticGhost = MAP
    [
        ( '2K11_KRUG_DDR', GhostDepiction_2K11_KRUG_DDR ),
        ( '2K11_KRUG_POL', GhostDepiction_2K11_KRUG_POL ),
        ( '2K11_KRUG_SOV', GhostDepiction_2K11_KRUG_SOV ),
        ( '2K12_KUB_DDR', GhostDepiction_2K12_KUB_DDR ),
        ( '2K12_KUB_POL', GhostDepiction_2K12_KUB_POL ),
        ( '2K12_KUB_SOV', GhostDepiction_2K12_KUB_SOV ),
        ( '2S19_MstaS_SOV', GhostDepiction_2S19_MstaS_SOV ),
        ( '2S1M_POL', GhostDepiction_2S1M_POL ),
        ( '2S1_DDR', GhostDepiction_2S1_DDR ),
        ( '2S1_Gvozdika_SOV', GhostDepiction_2S1_Gvozdika_SOV ),
        ( '2S1_POL', GhostDepiction_2S1_POL ),
        ( '2S23_Nona_SVK_SOV', GhostDepiction_2S23_Nona_SVK_SOV ),
        ( '2S3M1_Akatsiya_SOV', GhostDepiction_2S3M1_Akatsiya_SOV ),
        ( '2S3M_Akatsiya_SOV', GhostDepiction_2S3M_Akatsiya_SOV ),
        ( '2S3_DDR', GhostDepiction_2S3_DDR ),
        ( '2S5_GiatsintS_SOV', GhostDepiction_2S5_GiatsintS_SOV ),
        ( '2S7M_Malka_SOV', GhostDepiction_2S7M_Malka_SOV ),
        ( '2S7_Pion_POL', GhostDepiction_2S7_Pion_POL ),
        ( '2S9_Nona_SOV', GhostDepiction_2S9_Nona_SOV ),
        ( '81mm_mortar_Aero_US', GhostDepiction_81mm_mortar_Aero_US ),
        ( '81mm_mortar_CLU_UK', GhostDepiction_81mm_mortar_CLU_UK ),
        ( '81mm_mortar_NG_US', GhostDepiction_81mm_mortar_NG_US ),
        ( '81mm_mortar_Para_UK', GhostDepiction_81mm_mortar_Para_UK ),
        ( '81mm_mortar_UK', GhostDepiction_81mm_mortar_UK ),
        ( '81mm_mortar_US', GhostDepiction_81mm_mortar_US ),
        ( 'A109BA_BEL', GhostDepiction_A109BA_BEL ),
        ( 'A109BA_TOW_BEL', GhostDepiction_A109BA_TOW_BEL ),
        ( 'A109BA_TOW_twin_BEL', GhostDepiction_A109BA_TOW_twin_BEL ),
        ( 'A10_Thunderbolt_II_ATGM_US', GhostDepiction_A10_Thunderbolt_II_ATGM_US ),
        ( 'A10_Thunderbolt_II_Rkt_US', GhostDepiction_A10_Thunderbolt_II_Rkt_US ),
        ( 'A10_Thunderbolt_II_US', GhostDepiction_A10_Thunderbolt_II_US ),
        ( 'A222_Bereg_SOV', GhostDepiction_A222_Bereg_SOV ),
        ( 'A37B_Dragonfly_HE_US', GhostDepiction_A37B_Dragonfly_HE_US ),
        ( 'A37B_Dragonfly_NPLM_US', GhostDepiction_A37B_Dragonfly_NPLM_US ),
        ( 'A37B_Dragonfly_US', GhostDepiction_A37B_Dragonfly_US ),
        ( 'A6E_Intruder_SEAD_US', GhostDepiction_A6E_Intruder_SEAD_US ),
        ( 'A6E_Intruder_US', GhostDepiction_A6E_Intruder_US ),
        ( 'A7D_Corsair_II_AT_US', GhostDepiction_A7D_Corsair_II_AT_US ),
        ( 'A7D_Corsair_II_CLU_US', GhostDepiction_A7D_Corsair_II_CLU_US ),
        ( 'A7D_Corsair_II_RKT_US', GhostDepiction_A7D_Corsair_II_RKT_US ),
        ( 'A7D_Corsair_II_US', GhostDepiction_A7D_Corsair_II_US ),
        ( 'AEC_Militant_UK', GhostDepiction_AEC_Militant_UK ),
        ( 'AH1E_Cobra_US', GhostDepiction_AH1E_Cobra_US ),
        ( 'AH1F_ATAS_US', GhostDepiction_AH1F_ATAS_US ),
        ( 'AH1F_CNITE_US', GhostDepiction_AH1F_CNITE_US ),
        ( 'AH1F_Cobra_NG_US', GhostDepiction_AH1F_Cobra_NG_US ),
        ( 'AH1F_Cobra_US', GhostDepiction_AH1F_Cobra_US ),
        ( 'AH1F_HeavyHog_US', GhostDepiction_AH1F_HeavyHog_US ),
        ( 'AH1F_Hog_US', GhostDepiction_AH1F_Hog_US ),
        ( 'AH1S_Cobra_US', GhostDepiction_AH1S_Cobra_US ),
        ( 'AH64_Apache_ATAS_US', GhostDepiction_AH64_Apache_ATAS_US ),
        ( 'AH64_Apache_NG_US', GhostDepiction_AH64_Apache_NG_US ),
        ( 'AH64_Apache_US', GhostDepiction_AH64_Apache_US ),
        ( 'AH64_Apache_emp1_US', GhostDepiction_AH64_Apache_emp1_US ),
        ( 'AH64_Apache_emp2_US', GhostDepiction_AH64_Apache_emp2_US ),
        ( 'AH6C_Little_Bird_US', GhostDepiction_AH6C_Little_Bird_US ),
        ( 'AH6G_Little_Bird_US', GhostDepiction_AH6G_Little_Bird_US ),
        ( 'AIFV_B_50_BEL', GhostDepiction_AIFV_B_50_BEL ),
        ( 'AIFV_B_50_NL', GhostDepiction_AIFV_B_50_NL ),
        ( 'AIFV_B_C25_BEL', GhostDepiction_AIFV_B_C25_BEL ),
        ( 'AIFV_B_C25_NL', GhostDepiction_AIFV_B_C25_NL ),
        ( 'AIFV_B_CMD_BEL', GhostDepiction_AIFV_B_CMD_BEL ),
        ( 'AIFV_B_CMD_NL', GhostDepiction_AIFV_B_CMD_NL ),
        ( 'AIFV_B_Cargo_NL', GhostDepiction_AIFV_B_Cargo_NL ),
        ( 'AIFV_B_MILAN_BEL', GhostDepiction_AIFV_B_MILAN_BEL ),
        ( 'AIFV_B_Radar_NL', GhostDepiction_AIFV_B_Radar_NL ),
        ( 'AIFV_B_TOW_NL', GhostDepiction_AIFV_B_TOW_NL ),
        ( 'AML_60_FR', GhostDepiction_AML_60_FR ),
        ( 'AML_60_Gendarmerie_FR', GhostDepiction_AML_60_Gendarmerie_FR ),
        ( 'AML_90_CMD_FR', GhostDepiction_AML_90_CMD_FR ),
        ( 'AML_90_FR', GhostDepiction_AML_90_FR ),
        ( 'AML_90_Reserve_FR', GhostDepiction_AML_90_Reserve_FR ),
        ( 'AMX_10_HOT_FR', GhostDepiction_AMX_10_HOT_FR ),
        ( 'AMX_10_PC_CMD_FR', GhostDepiction_AMX_10_PC_CMD_FR ),
        ( 'AMX_10_P_FR', GhostDepiction_AMX_10_P_FR ),
        ( 'AMX_10_P_MILAN_FR', GhostDepiction_AMX_10_P_MILAN_FR ),
        ( 'AMX_10_P_VOA_FR', GhostDepiction_AMX_10_P_VOA_FR ),
        ( 'AMX_10_RCR_FR', GhostDepiction_AMX_10_RCR_FR ),
        ( 'AMX_10_RC_FR', GhostDepiction_AMX_10_RC_FR ),
        ( 'AMX_13_90mm_FR', GhostDepiction_AMX_13_90mm_FR ),
        ( 'AMX_13_DCA_FR', GhostDepiction_AMX_13_DCA_FR ),
        ( 'AMX_13_VCI_12_7mm_FR', GhostDepiction_AMX_13_VCI_12_7mm_FR ),
        ( 'AMX_13_VCI_20mm_FR', GhostDepiction_AMX_13_VCI_20mm_FR ),
        ( 'AMX_13_mod56_CMD_BEL', GhostDepiction_AMX_13_mod56_CMD_BEL ),
        ( 'AMX_13_mod56_MILAN_BEL', GhostDepiction_AMX_13_mod56_MILAN_BEL ),
        ( 'AMX_13_mod56_Mortier_BEL', GhostDepiction_AMX_13_mod56_Mortier_BEL ),
        ( 'AMX_13_mod56_VCI_BEL', GhostDepiction_AMX_13_mod56_VCI_BEL ),
        ( 'AMX_30_AuF1_FR', GhostDepiction_AMX_30_AuF1_FR ),
        ( 'AMX_30_B2_Brennus_FR', GhostDepiction_AMX_30_B2_Brennus_FR ),
        ( 'AMX_30_B2_CMD_FR', GhostDepiction_AMX_30_B2_CMD_FR ),
        ( 'AMX_30_B2_FR', GhostDepiction_AMX_30_B2_FR ),
        ( 'AMX_30_B_CMD_FR', GhostDepiction_AMX_30_B_CMD_FR ),
        ( 'AMX_30_B_FR', GhostDepiction_AMX_30_B_FR ),
        ( 'AMX_30_EBG_FR', GhostDepiction_AMX_30_EBG_FR ),
        ( 'ASU_85_CMD_POL', GhostDepiction_ASU_85_CMD_POL ),
        ( 'ASU_85_POL', GhostDepiction_ASU_85_POL ),
        ( 'AT_2A45_SprutB_SOV', GhostDepiction_AT_2A45_SprutB_SOV ),
        ( 'AT_D44_85mm_DDR', GhostDepiction_AT_D44_85mm_DDR ),
        ( 'AT_D44_85mm_POL', GhostDepiction_AT_D44_85mm_POL ),
        ( 'AT_D44_85mm_VDV_SOV', GhostDepiction_AT_D44_85mm_VDV_SOV ),
        ( 'AT_D48_85mm_POL', GhostDepiction_AT_D48_85mm_POL ),
        ( 'AT_KSM65_100mm_SOV', GhostDepiction_AT_KSM65_100mm_SOV ),
        ( 'AT_T12R_Ruta_SOV', GhostDepiction_AT_T12R_Ruta_SOV ),
        ( 'AT_T12_Rapira_DDR', GhostDepiction_AT_T12_Rapira_DDR ),
        ( 'AT_T12_Rapira_SOV', GhostDepiction_AT_T12_Rapira_SOV ),
        ( 'AT_ZiS2_57mm_DDR', GhostDepiction_AT_ZiS2_57mm_DDR ),
        ( 'AT_vz52_85mm_DDR', GhostDepiction_AT_vz52_85mm_DDR ),
        ( 'ATteam_Fagot_DDR', GhostDepiction_ATteam_Fagot_DDR ),
        ( 'ATteam_Fagot_FJ_DDR', GhostDepiction_ATteam_Fagot_FJ_DDR ),
        ( 'ATteam_Fagot_SOV', GhostDepiction_ATteam_Fagot_SOV ),
        ( 'ATteam_ITOW_NG_US', GhostDepiction_ATteam_ITOW_NG_US ),
        ( 'ATteam_ITOW_NL', GhostDepiction_ATteam_ITOW_NL ),
        ( 'ATteam_ITOW_US', GhostDepiction_ATteam_ITOW_US ),
        ( 'ATteam_KonkursM_TTsko_SOV', GhostDepiction_ATteam_KonkursM_TTsko_SOV ),
        ( 'ATteam_Konkurs_DDR', GhostDepiction_ATteam_Konkurs_DDR ),
        ( 'ATteam_Konkurs_SOV', GhostDepiction_ATteam_Konkurs_SOV ),
        ( 'ATteam_Konkurs_TTsko_SOV', GhostDepiction_ATteam_Konkurs_TTsko_SOV ),
        ( 'ATteam_Milan_1_BEL', GhostDepiction_ATteam_Milan_1_BEL ),
        ( 'ATteam_Milan_1_FR', GhostDepiction_ATteam_Milan_1_FR ),
        ( 'ATteam_Milan_1_RFA', GhostDepiction_ATteam_Milan_1_RFA ),
        ( 'ATteam_Milan_1_UK', GhostDepiction_ATteam_Milan_1_UK ),
        ( 'ATteam_Milan_1_para_BEL', GhostDepiction_ATteam_Milan_1_para_BEL ),
        ( 'ATteam_Milan_1_para_FR', GhostDepiction_ATteam_Milan_1_para_FR ),
        ( 'ATteam_Milan_2_BEL', GhostDepiction_ATteam_Milan_2_BEL ),
        ( 'ATteam_Milan_2_FR', GhostDepiction_ATteam_Milan_2_FR ),
        ( 'ATteam_Milan_2_RFA', GhostDepiction_ATteam_Milan_2_RFA ),
        ( 'ATteam_Milan_2_RIMa_FR', GhostDepiction_ATteam_Milan_2_RIMa_FR ),
        ( 'ATteam_Milan_2_UK', GhostDepiction_ATteam_Milan_2_UK ),
        ( 'ATteam_Milan_2_para_BEL', GhostDepiction_ATteam_Milan_2_para_BEL ),
        ( 'ATteam_Milan_2_para_FR', GhostDepiction_ATteam_Milan_2_para_FR ),
        ( 'ATteam_Milan_2_para_RFA', GhostDepiction_ATteam_Milan_2_para_RFA ),
        ( 'ATteam_Milan_2_para_UK', GhostDepiction_ATteam_Milan_2_para_UK ),
        ( 'ATteam_RCL_B11_Reserve_SOV', GhostDepiction_ATteam_RCL_B11_Reserve_SOV ),
        ( 'ATteam_RCL_M40A1_FR', GhostDepiction_ATteam_RCL_M40A1_FR ),
        ( 'ATteam_RCL_M40A1_NG_US', GhostDepiction_ATteam_RCL_M40A1_NG_US ),
        ( 'ATteam_RCL_M40A1_NL', GhostDepiction_ATteam_RCL_M40A1_NL ),
        ( 'ATteam_RCL_M40A1_RFA', GhostDepiction_ATteam_RCL_M40A1_RFA ),
        ( 'ATteam_RCL_SPG9_DDR', GhostDepiction_ATteam_RCL_SPG9_DDR ),
        ( 'ATteam_RCL_SPG9_DShV_SOV', GhostDepiction_ATteam_RCL_SPG9_DShV_SOV ),
        ( 'ATteam_RCL_SPG9_FJ_DDR', GhostDepiction_ATteam_RCL_SPG9_FJ_DDR ),
        ( 'ATteam_RCL_SPG9_POL', GhostDepiction_ATteam_RCL_SPG9_POL ),
        ( 'ATteam_RCL_SPG9_Para_POL', GhostDepiction_ATteam_RCL_SPG9_Para_POL ),
        ( 'ATteam_RCL_SPG9_SOV', GhostDepiction_ATteam_RCL_SPG9_SOV ),
        ( 'ATteam_RCL_SPG9_VDV_SOV', GhostDepiction_ATteam_RCL_SPG9_VDV_SOV ),
        ( 'ATteam_TOW2A_US', GhostDepiction_ATteam_TOW2A_US ),
        ( 'ATteam_TOW2_Aero_US', GhostDepiction_ATteam_TOW2_Aero_US ),
        ( 'ATteam_TOW2_NL', GhostDepiction_ATteam_TOW2_NL ),
        ( 'ATteam_TOW2_US', GhostDepiction_ATteam_TOW2_US ),
        ( 'ATteam_TOW2_para_US', GhostDepiction_ATteam_TOW2_para_US ),
        ( 'ATteam_TOW_NL', GhostDepiction_ATteam_TOW_NL ),
        ( 'ATteam_TOW_US', GhostDepiction_ATteam_TOW_US ),
        ( 'Alouette_III_FR', GhostDepiction_Alouette_III_FR ),
        ( 'Alouette_III_NL', GhostDepiction_Alouette_III_NL ),
        ( 'Alouette_III_SOV', GhostDepiction_Alouette_III_SOV ),
        ( 'Alouette_III_SS11_FR', GhostDepiction_Alouette_III_SS11_FR ),
        ( 'Alouette_III_reco_FR', GhostDepiction_Alouette_III_reco_FR ),
        ( 'Alouette_III_trans_NL', GhostDepiction_Alouette_III_trans_NL ),
        ( 'Alouette_II_CMD_FR', GhostDepiction_Alouette_II_CMD_FR ),
        ( 'Alouette_II_CMD_RFA', GhostDepiction_Alouette_II_CMD_RFA ),
        ( 'Alouette_II_reco_BEL', GhostDepiction_Alouette_II_reco_BEL ),
        ( 'Alouette_II_reco_RFA', GhostDepiction_Alouette_II_reco_RFA ),
        ( 'Alouette_II_trans_BEL', GhostDepiction_Alouette_II_trans_BEL ),
        ( 'Alouette_II_trans_FR', GhostDepiction_Alouette_II_trans_FR ),
        ( 'Alpha_Jet_A_clu_RFA', GhostDepiction_Alpha_Jet_A_clu_RFA ),
        ( 'Alpha_Jet_A_he_RFA', GhostDepiction_Alpha_Jet_A_he_RFA ),
        ( 'Alpha_Jet_A_nplm_RFA', GhostDepiction_Alpha_Jet_A_nplm_RFA ),
        ( 'Alpha_Jet_A_rkt_RFA', GhostDepiction_Alpha_Jet_A_rkt_RFA ),
        ( 'Alpha_Jet_BEL', GhostDepiction_Alpha_Jet_BEL ),
        ( 'Alpha_Jet_E_FR', GhostDepiction_Alpha_Jet_E_FR ),
        ( 'Alpha_Jet_E_NPLM_FR', GhostDepiction_Alpha_Jet_E_NPLM_FR ),
        ( 'Alpha_Jet_HE2_BEL', GhostDepiction_Alpha_Jet_HE2_BEL ),
        ( 'Alvis_Stalwart_UK', GhostDepiction_Alvis_Stalwart_UK ),
        ( 'Atteam_Dragon_Marines_NL', GhostDepiction_Atteam_Dragon_Marines_NL ),
        ( 'Atteam_Fagot_DShV_SOV', GhostDepiction_Atteam_Fagot_DShV_SOV ),
        ( 'Atteam_Fagot_POL', GhostDepiction_Atteam_Fagot_POL ),
        ( 'Atteam_Fagot_Para_POL', GhostDepiction_Atteam_Fagot_Para_POL ),
        ( 'Atteam_Fagot_VDV_SOV', GhostDepiction_Atteam_Fagot_VDV_SOV ),
        ( 'Atteam_Konkurs_DShV_SOV', GhostDepiction_Atteam_Konkurs_DShV_SOV ),
        ( 'Atteam_Konkurs_POL', GhostDepiction_Atteam_Konkurs_POL ),
        ( 'Atteam_Konkurs_VDV_SOV', GhostDepiction_Atteam_Konkurs_VDV_SOV ),
        ( 'BAV_485_POL', GhostDepiction_BAV_485_POL ),
        ( 'BAV_485_Supply_POL', GhostDepiction_BAV_485_Supply_POL ),
        ( 'BM14M_POL', GhostDepiction_BM14M_POL ),
        ( 'BM21V_GradV_SOV', GhostDepiction_BM21V_GradV_SOV ),
        ( 'BM21_Grad_DDR', GhostDepiction_BM21_Grad_DDR ),
        ( 'BM21_Grad_POL', GhostDepiction_BM21_Grad_POL ),
        ( 'BM21_Grad_SOV', GhostDepiction_BM21_Grad_SOV ),
        ( 'BM24M_DDR', GhostDepiction_BM24M_DDR ),
        ( 'BM24M_POL', GhostDepiction_BM24M_POL ),
        ( 'BM24M_SOV', GhostDepiction_BM24M_SOV ),
        ( 'BM27_Uragan_SOV', GhostDepiction_BM27_Uragan_SOV ),
        ( 'BM30_Smerch_SOV', GhostDepiction_BM30_Smerch_SOV ),
        ( 'BMD_1K_CMD_SOV', GhostDepiction_BMD_1K_CMD_SOV ),
        ( 'BMD_1P_SOV', GhostDepiction_BMD_1P_SOV ),
        ( 'BMD_1_CMD_SOV', GhostDepiction_BMD_1_CMD_SOV ),
        ( 'BMD_1_Reostat_SOV', GhostDepiction_BMD_1_Reostat_SOV ),
        ( 'BMD_1_SOV', GhostDepiction_BMD_1_SOV ),
        ( 'BMD_2_CMD_SOV', GhostDepiction_BMD_2_CMD_SOV ),
        ( 'BMD_2_SOV', GhostDepiction_BMD_2_SOV ),
        ( 'BMD_3_SOV', GhostDepiction_BMD_3_SOV ),
        ( 'BMD_3_reco_SOV', GhostDepiction_BMD_3_reco_SOV ),
        ( 'BMP_1PG_SOV', GhostDepiction_BMP_1PG_SOV ),
        ( 'BMP_1P_DDR', GhostDepiction_BMP_1P_DDR ),
        ( 'BMP_1P_Konkurs_DDR', GhostDepiction_BMP_1P_Konkurs_DDR ),
        ( 'BMP_1P_Konkurs_SOV', GhostDepiction_BMP_1P_Konkurs_SOV ),
        ( 'BMP_1P_SOV', GhostDepiction_BMP_1P_SOV ),
        ( 'BMP_1P_reco_DDR', GhostDepiction_BMP_1P_reco_DDR ),
        ( 'BMP_1P_reco_POL', GhostDepiction_BMP_1P_reco_POL ),
        ( 'BMP_1P_reco_SOV', GhostDepiction_BMP_1P_reco_SOV ),
        ( 'BMP_1_CMD_DDR', GhostDepiction_BMP_1_CMD_DDR ),
        ( 'BMP_1_CMD_POL', GhostDepiction_BMP_1_CMD_POL ),
        ( 'BMP_1_CMD_SOV', GhostDepiction_BMP_1_CMD_SOV ),
        ( 'BMP_1_SP1_DDR', GhostDepiction_BMP_1_SP1_DDR ),
        ( 'BMP_1_SP2_DDR', GhostDepiction_BMP_1_SP2_DDR ),
        ( 'BMP_1_SP2_POL', GhostDepiction_BMP_1_SP2_POL ),
        ( 'BMP_1_SP2_SOV', GhostDepiction_BMP_1_SP2_SOV ),
        ( 'BMP_1_SP2_reco_POL', GhostDepiction_BMP_1_SP2_reco_POL ),
        ( 'BMP_2AG_SOV', GhostDepiction_BMP_2AG_SOV ),
        ( 'BMP_2D_SOV', GhostDepiction_BMP_2D_SOV ),
        ( 'BMP_2D_reco_SOV', GhostDepiction_BMP_2D_reco_SOV ),
        ( 'BMP_2_CMD_SOV', GhostDepiction_BMP_2_CMD_SOV ),
        ( 'BMP_2_DDR', GhostDepiction_BMP_2_DDR ),
        ( 'BMP_2_POL', GhostDepiction_BMP_2_POL ),
        ( 'BMP_2_SOV', GhostDepiction_BMP_2_SOV ),
        ( 'BMP_2_reco_SOV', GhostDepiction_BMP_2_reco_SOV ),
        ( 'BMP_3_SOV', GhostDepiction_BMP_3_SOV ),
        ( 'BRDM_1_DDR', GhostDepiction_BRDM_1_DDR ),
        ( 'BRDM_1_DShK_POL', GhostDepiction_BRDM_1_DShK_POL ),
        ( 'BRDM_1_POL', GhostDepiction_BRDM_1_POL ),
        ( 'BRDM_1_PSNR1_POL', GhostDepiction_BRDM_1_PSNR1_POL ),
        ( 'BRDM_2_CMD_DDR', GhostDepiction_BRDM_2_CMD_DDR ),
        ( 'BRDM_2_CMD_POL', GhostDepiction_BRDM_2_CMD_POL ),
        ( 'BRDM_2_CMD_R5_POL', GhostDepiction_BRDM_2_CMD_R5_POL ),
        ( 'BRDM_2_CMD_SOV', GhostDepiction_BRDM_2_CMD_SOV ),
        ( 'BRDM_2_DDR', GhostDepiction_BRDM_2_DDR ),
        ( 'BRDM_2_Konkurs_M_SOV', GhostDepiction_BRDM_2_Konkurs_M_SOV ),
        ( 'BRDM_2_Konkurs_POL', GhostDepiction_BRDM_2_Konkurs_POL ),
        ( 'BRDM_2_Konkurs_SOV', GhostDepiction_BRDM_2_Konkurs_SOV ),
        ( 'BRDM_2_Malyu_P_POL', GhostDepiction_BRDM_2_Malyu_P_POL ),
        ( 'BRDM_2_Malyu_P_SOV', GhostDepiction_BRDM_2_Malyu_P_SOV ),
        ( 'BRDM_2_POL', GhostDepiction_BRDM_2_POL ),
        ( 'BRDM_2_SOV', GhostDepiction_BRDM_2_SOV ),
        ( 'BRDM_Konkurs_DDR', GhostDepiction_BRDM_Konkurs_DDR ),
        ( 'BRDM_Malyu_P_DDR', GhostDepiction_BRDM_Malyu_P_DDR ),
        ( 'BRDM_Strela_1_DDR', GhostDepiction_BRDM_Strela_1_DDR ),
        ( 'BRDM_Strela_1_POL', GhostDepiction_BRDM_Strela_1_POL ),
        ( 'BRDM_Strela_1_SOV', GhostDepiction_BRDM_Strela_1_SOV ),
        ( 'BRM_1_DDR', GhostDepiction_BRM_1_DDR ),
        ( 'BRM_1_POL', GhostDepiction_BRM_1_POL ),
        ( 'BRM_1_SOV', GhostDepiction_BRM_1_SOV ),
        ( 'BTR_152A_DDR', GhostDepiction_BTR_152A_DDR ),
        ( 'BTR_152A_SOV', GhostDepiction_BTR_152A_SOV ),
        ( 'BTR_152K_SOV', GhostDepiction_BTR_152K_SOV ),
        ( 'BTR_152S_CMD_SOV', GhostDepiction_BTR_152S_CMD_SOV ),
        ( 'BTR_40A_SOV', GhostDepiction_BTR_40A_SOV ),
        ( 'BTR_40B_CMD_SOV', GhostDepiction_BTR_40B_CMD_SOV ),
        ( 'BTR_40_SOV', GhostDepiction_BTR_40_SOV ),
        ( 'BTR_50_CMD_DDR', GhostDepiction_BTR_50_CMD_DDR ),
        ( 'BTR_50_DDR', GhostDepiction_BTR_50_DDR ),
        ( 'BTR_50_MRF_DDR', GhostDepiction_BTR_50_MRF_DDR ),
        ( 'BTR_60_CHAIKA_CMD_DDR', GhostDepiction_BTR_60_CHAIKA_CMD_DDR ),
        ( 'BTR_60_CHAIKA_CMD_SOV', GhostDepiction_BTR_60_CHAIKA_CMD_SOV ),
        ( 'BTR_60_CMD_DDR', GhostDepiction_BTR_60_CMD_DDR ),
        ( 'BTR_60_CMD_SOV', GhostDepiction_BTR_60_CMD_SOV ),
        ( 'BTR_60_DDR', GhostDepiction_BTR_60_DDR ),
        ( 'BTR_60_SOV', GhostDepiction_BTR_60_SOV ),
        ( 'BTR_60_reco_DDR', GhostDepiction_BTR_60_reco_DDR ),
        ( 'BTR_60_reco_SOV', GhostDepiction_BTR_60_reco_SOV ),
        ( 'BTR_70D_SOV', GhostDepiction_BTR_70D_SOV ),
        ( 'BTR_70_AGS_SOV', GhostDepiction_BTR_70_AGS_SOV ),
        ( 'BTR_70_DDR', GhostDepiction_BTR_70_DDR ),
        ( 'BTR_70_MP_SOV', GhostDepiction_BTR_70_MP_SOV ),
        ( 'BTR_70_Rys_SOV', GhostDepiction_BTR_70_Rys_SOV ),
        ( 'BTR_70_S5_SOV', GhostDepiction_BTR_70_S5_SOV ),
        ( 'BTR_70_S8_SOV', GhostDepiction_BTR_70_S8_SOV ),
        ( 'BTR_70_SOV', GhostDepiction_BTR_70_SOV ),
        ( 'BTR_80_CMD_SOV', GhostDepiction_BTR_80_CMD_SOV ),
        ( 'BTR_80_SOV', GhostDepiction_BTR_80_SOV ),
        ( 'BTR_D_Robot_SOV', GhostDepiction_BTR_D_Robot_SOV ),
        ( 'BTR_D_SOV', GhostDepiction_BTR_D_SOV ),
        ( 'BTR_D_reco_SOV', GhostDepiction_BTR_D_reco_SOV ),
        ( 'BTR_ZD_Skrezhet_SOV', GhostDepiction_BTR_ZD_Skrezhet_SOV ),
        ( 'Bedford_MJ_4t_UK', GhostDepiction_Bedford_MJ_4t_UK ),
        ( 'Bedford_MJ_4t_trans_UK', GhostDepiction_Bedford_MJ_4t_trans_UK ),
        ( 'Bo_105_CB_NL', GhostDepiction_Bo_105_CB_NL ),
        ( 'Bo_105_CMD_RFA', GhostDepiction_Bo_105_CMD_RFA ),
        ( 'Bo_105_PAH_1A1_RFA', GhostDepiction_Bo_105_PAH_1A1_RFA ),
        ( 'Bo_105_PAH_1_RFA', GhostDepiction_Bo_105_PAH_1_RFA ),
        ( 'Bo_105_reco_ESP', GhostDepiction_Bo_105_reco_ESP ),
        ( 'Bo_105_reco_RFA', GhostDepiction_Bo_105_reco_RFA ),
        ( 'Bo_105_trans_RFA', GhostDepiction_Bo_105_trans_RFA ),
        ( 'Bofors_40mm_RFA', GhostDepiction_Bofors_40mm_RFA ),
        ( 'Bofors_40mm_capture_DDR', GhostDepiction_Bofors_40mm_capture_DDR ),
        ( 'Buccaneer_S2B_ATGM_UK', GhostDepiction_Buccaneer_S2B_ATGM_UK ),
        ( 'Buccaneer_S2B_GBU_UK', GhostDepiction_Buccaneer_S2B_GBU_UK ),
        ( 'Buccaneer_S2B_HE_UK', GhostDepiction_Buccaneer_S2B_HE_UK ),
        ( 'Buccaneer_S2B_SEAD_UK', GhostDepiction_Buccaneer_S2B_SEAD_UK ),
        ( 'Buk_9K37M_SOV', GhostDepiction_Buk_9K37M_SOV ),
        ( 'CGage_Peacekeeper_US', GhostDepiction_CGage_Peacekeeper_US ),
        ( 'CGage_V150_Commando_US', GhostDepiction_CGage_V150_Commando_US ),
        ( 'CH47D_Chinook_supply_UK', GhostDepiction_CH47D_Chinook_supply_UK ),
        ( 'CH47_Chinook_UK', GhostDepiction_CH47_Chinook_UK ),
        ( 'CH47_Chinook_US', GhostDepiction_CH47_Chinook_US ),
        ( 'CH47_Super_Chinook_US', GhostDepiction_CH47_Super_Chinook_US ),
        ( 'CH53G_RFA', GhostDepiction_CH53G_RFA ),
        ( 'CH53G_trans_RFA', GhostDepiction_CH53G_trans_RFA ),
        ( 'CH54B_Tarhe_supply_US', GhostDepiction_CH54B_Tarhe_supply_US ),
        ( 'CH54B_Tarhe_trans_US', GhostDepiction_CH54B_Tarhe_trans_US ),
        ( 'CL_289_RFA', GhostDepiction_CL_289_RFA ),
        ( 'CM170_Magister_FR', GhostDepiction_CM170_Magister_FR ),
        ( 'CM170_Magister_SS11_FR', GhostDepiction_CM170_Magister_SS11_FR ),
        ( 'CUCV_AGL_US', GhostDepiction_CUCV_AGL_US ),
        ( 'CUCV_HMG_US', GhostDepiction_CUCV_HMG_US ),
        ( 'CUCV_Hellfire_US', GhostDepiction_CUCV_Hellfire_US ),
        ( 'CUCV_MP_US', GhostDepiction_CUCV_MP_US ),
        ( 'CUCV_US', GhostDepiction_CUCV_US ),
        ( 'CUCV_trans_US', GhostDepiction_CUCV_trans_US ),
        ( 'Centurion_AVRE_105_UK', GhostDepiction_Centurion_AVRE_105_UK ),
        ( 'Challenger_1_Mk1_CMD_UK', GhostDepiction_Challenger_1_Mk1_CMD_UK ),
        ( 'Challenger_1_Mk1_UK', GhostDepiction_Challenger_1_Mk1_UK ),
        ( 'Challenger_1_Mk3_UK', GhostDepiction_Challenger_1_Mk3_UK ),
        ( 'Crotale_FR', GhostDepiction_Crotale_FR ),
        ( 'DAF_YA_4400_NL', GhostDepiction_DAF_YA_4400_NL ),
        ( 'DAF_YA_4400_supply_NL', GhostDepiction_DAF_YA_4400_supply_NL ),
        ( 'DAF_YHZ_2300_NL', GhostDepiction_DAF_YHZ_2300_NL ),
        ( 'DAF_YHZ_2300_trans_NL', GhostDepiction_DAF_YHZ_2300_trans_NL ),
        ( 'DANA_POL', GhostDepiction_DANA_POL ),
        ( 'DCA_53T2_20mm_FR', GhostDepiction_DCA_53T2_20mm_FR ),
        ( 'DCA_53T2_20mm_Para_FR', GhostDepiction_DCA_53T2_20mm_Para_FR ),
        ( 'DCA_76T2_20mm_CPA_FR', GhostDepiction_DCA_76T2_20mm_CPA_FR ),
        ( 'DCA_AZP_S60_DDR', GhostDepiction_DCA_AZP_S60_DDR ),
        ( 'DCA_AZP_S60_POL', GhostDepiction_DCA_AZP_S60_POL ),
        ( 'DCA_AZP_S60_SOV', GhostDepiction_DCA_AZP_S60_SOV ),
        ( 'DCA_Bofors_L60_FR', GhostDepiction_DCA_Bofors_L60_FR ),
        ( 'DCA_Bofors_upgrade_NL', GhostDepiction_DCA_Bofors_upgrade_NL ),
        ( 'DCA_FASTA_4_DDR', GhostDepiction_DCA_FASTA_4_DDR ),
        ( 'DCA_FK20_2_20mm_RFA', GhostDepiction_DCA_FK20_2_20mm_RFA ),
        ( 'DCA_FK20_2_20mm_Zwillinge_RFA', GhostDepiction_DCA_FK20_2_20mm_Zwillinge_RFA ),
        ( 'DCA_I_Hawk_BEL', GhostDepiction_DCA_I_Hawk_BEL ),
        ( 'DCA_I_Hawk_NL', GhostDepiction_DCA_I_Hawk_NL ),
        ( 'DCA_I_Hawk_RFA', GhostDepiction_DCA_I_Hawk_RFA ),
        ( 'DCA_I_Hawk_US', GhostDepiction_DCA_I_Hawk_US ),
        ( 'DCA_I_Hawk_capture_DDR', GhostDepiction_DCA_I_Hawk_capture_DDR ),
        ( 'DCA_Javelin_LML_UK', GhostDepiction_DCA_Javelin_LML_UK ),
        ( 'DCA_KS19_100mm_DDR', GhostDepiction_DCA_KS19_100mm_DDR ),
        ( 'DCA_KS30_130mm_SOV', GhostDepiction_DCA_KS30_130mm_SOV ),
        ( 'DCA_M167A2_Vulcan_20mm_Aero_US', GhostDepiction_DCA_M167A2_Vulcan_20mm_Aero_US ),
        ( 'DCA_M167A2_Vulcan_20mm_US', GhostDepiction_DCA_M167A2_Vulcan_20mm_US ),
        ( 'DCA_M167_Vulcan_20mm_BEL', GhostDepiction_DCA_M167_Vulcan_20mm_BEL ),
        ( 'DCA_M167_Vulcan_20mm_US', GhostDepiction_DCA_M167_Vulcan_20mm_US ),
        ( 'DCA_M167_Vulcan_20mm_nonPara_US', GhostDepiction_DCA_M167_Vulcan_20mm_nonPara_US ),
        ( 'DCA_M167_Vulcan_para_20mm_BEL', GhostDepiction_DCA_M167_Vulcan_para_20mm_BEL ),
        ( 'DCA_M55_NL', GhostDepiction_DCA_M55_NL ),
        ( 'DCA_Oerlikon_GDF_002_35mm_UK', GhostDepiction_DCA_Oerlikon_GDF_002_35mm_UK ),
        ( 'DCA_Rapier_Darkfire_UK', GhostDepiction_DCA_Rapier_Darkfire_UK ),
        ( 'DCA_Rapier_FSA_UK', GhostDepiction_DCA_Rapier_FSA_UK ),
        ( 'DCA_Rapier_UK', GhostDepiction_DCA_Rapier_UK ),
        ( 'DCA_XM85_Chaparral_US', GhostDepiction_DCA_XM85_Chaparral_US ),
        ( 'DCA_XMIM_115A_Roland_US', GhostDepiction_DCA_XMIM_115A_Roland_US ),
        ( 'DCA_ZPU4_DDR', GhostDepiction_DCA_ZPU4_DDR ),
        ( 'DCA_ZPU4_POL', GhostDepiction_DCA_ZPU4_POL ),
        ( 'DCA_ZUR_23_2S_JOD_POL', GhostDepiction_DCA_ZUR_23_2S_JOD_POL ),
        ( 'DCA_ZUR_23_2S_JOD_Para_POL', GhostDepiction_DCA_ZUR_23_2S_JOD_Para_POL ),
        ( 'DCA_ZU_23_2_DDR', GhostDepiction_DCA_ZU_23_2_DDR ),
        ( 'DCA_ZU_23_2_POL', GhostDepiction_DCA_ZU_23_2_POL ),
        ( 'DCA_ZU_23_2_Para_POL', GhostDepiction_DCA_ZU_23_2_Para_POL ),
        ( 'DCA_ZU_23_2_SOV', GhostDepiction_DCA_ZU_23_2_SOV ),
        ( 'DCA_ZU_23_2_TTsko_SOV', GhostDepiction_DCA_ZU_23_2_TTsko_SOV ),
        ( 'DCA_ZU_23_2_nonPara_SOV', GhostDepiction_DCA_ZU_23_2_nonPara_SOV ),
        ( 'DEP_M109A2', GhostDepiction_DEP_M109A2 ),
        ( 'Dragoon_300_US', GhostDepiction_Dragoon_300_US ),
        ( 'EA6B_Prowler_US', GhostDepiction_EA6B_Prowler_US ),
        ( 'EBR_90mm_FR', GhostDepiction_EBR_90mm_FR ),
        ( 'EF111_Raven_US', GhostDepiction_EF111_Raven_US ),
        ( 'EH60A_EW_US', GhostDepiction_EH60A_EW_US ),
        ( 'ERC_90_Sagaie_CMD_FR', GhostDepiction_ERC_90_Sagaie_CMD_FR ),
        ( 'ERC_90_Sagaie_FR', GhostDepiction_ERC_90_Sagaie_FR ),
        ( 'ERC_90_Sagaie_reco_FR', GhostDepiction_ERC_90_Sagaie_reco_FR ),
        ( 'Ecureuil_20mm_FR', GhostDepiction_Ecureuil_20mm_FR ),
        ( 'Ecureuil_reco_FR', GhostDepiction_Ecureuil_reco_FR ),
        ( 'Epervier_BEL', GhostDepiction_Epervier_BEL ),
        ( 'F104G_Starfighter_AT_RFA', GhostDepiction_F104G_Starfighter_AT_RFA ),
        ( 'F104G_Starfighter_HE_RFA', GhostDepiction_F104G_Starfighter_HE_RFA ),
        ( 'F104G_Starfighter_RFA', GhostDepiction_F104G_Starfighter_RFA ),
        ( 'F111E_Aardvark_CBU_US', GhostDepiction_F111E_Aardvark_CBU_US ),
        ( 'F111E_Aardvark_US', GhostDepiction_F111E_Aardvark_US ),
        ( 'F111E_Aardvark_napalm_US', GhostDepiction_F111E_Aardvark_napalm_US ),
        ( 'F111F_Aardvark_CBU_US', GhostDepiction_F111F_Aardvark_CBU_US ),
        ( 'F111F_Aardvark_LGB2_US', GhostDepiction_F111F_Aardvark_LGB2_US ),
        ( 'F111F_Aardvark_LGB_US', GhostDepiction_F111F_Aardvark_LGB_US ),
        ( 'F111F_Aardvark_US', GhostDepiction_F111F_Aardvark_US ),
        ( 'F111F_Aardvark_napalm_US', GhostDepiction_F111F_Aardvark_napalm_US ),
        ( 'F117_Nighthawk_US', GhostDepiction_F117_Nighthawk_US ),
        ( 'F15C_Eagle_AA2_US', GhostDepiction_F15C_Eagle_AA2_US ),
        ( 'F15C_Eagle_AA_US', GhostDepiction_F15C_Eagle_AA_US ),
        ( 'F15E_StrikeEagle_US', GhostDepiction_F15E_StrikeEagle_US ),
        ( 'F16A_AA2_NL', GhostDepiction_F16A_AA2_NL ),
        ( 'F16A_AA_BEL', GhostDepiction_F16A_AA_BEL ),
        ( 'F16A_AA_NL', GhostDepiction_F16A_AA_NL ),
        ( 'F16A_CBU_BEL', GhostDepiction_F16A_CBU_BEL ),
        ( 'F16A_CLU_NL', GhostDepiction_F16A_CLU_NL ),
        ( 'F16A_HE_NL', GhostDepiction_F16A_HE_NL ),
        ( 'F16C_LGB_US', GhostDepiction_F16C_LGB_US ),
        ( 'F16E_AA2_US', GhostDepiction_F16E_AA2_US ),
        ( 'F16E_AA_US', GhostDepiction_F16E_AA_US ),
        ( 'F16E_AGM_US', GhostDepiction_F16E_AGM_US ),
        ( 'F16E_CBU_US', GhostDepiction_F16E_CBU_US ),
        ( 'F16E_HE_US', GhostDepiction_F16E_HE_US ),
        ( 'F16E_SEAD_US', GhostDepiction_F16E_SEAD_US ),
        ( 'F16E_TER_CLU_US', GhostDepiction_F16E_TER_CLU_US ),
        ( 'F16E_TER_HE_US', GhostDepiction_F16E_TER_HE_US ),
        ( 'F16E_napalm_US', GhostDepiction_F16E_napalm_US ),
        ( 'F4E_Phantom_II_AA_US', GhostDepiction_F4E_Phantom_II_AA_US ),
        ( 'F4E_Phantom_II_AT_US', GhostDepiction_F4E_Phantom_II_AT_US ),
        ( 'F4E_Phantom_II_CBU_US', GhostDepiction_F4E_Phantom_II_CBU_US ),
        ( 'F4E_Phantom_II_HE_US', GhostDepiction_F4E_Phantom_II_HE_US ),
        ( 'F4E_Phantom_II_napalm_US', GhostDepiction_F4E_Phantom_II_napalm_US ),
        ( 'F4F_Phantom_II_AA_RFA', GhostDepiction_F4F_Phantom_II_AA_RFA ),
        ( 'F4F_Phantom_II_AT_RFA', GhostDepiction_F4F_Phantom_II_AT_RFA ),
        ( 'F4F_Phantom_II_HE1_RFA', GhostDepiction_F4F_Phantom_II_HE1_RFA ),
        ( 'F4F_Phantom_II_HE2_RFA', GhostDepiction_F4F_Phantom_II_HE2_RFA ),
        ( 'F4F_Phantom_II_RKT2_RFA', GhostDepiction_F4F_Phantom_II_RKT2_RFA ),
        ( 'F4_Phantom_AA_F3_UK', GhostDepiction_F4_Phantom_AA_F3_UK ),
        ( 'F4_Phantom_GR2_HE_UK', GhostDepiction_F4_Phantom_GR2_HE_UK ),
        ( 'F4_Phantom_GR2_NPLM_UK', GhostDepiction_F4_Phantom_GR2_NPLM_UK ),
        ( 'F4_Phantom_GR2_UK', GhostDepiction_F4_Phantom_GR2_UK ),
        ( 'F4_Wild_Weasel_US', GhostDepiction_F4_Wild_Weasel_US ),
        ( 'F5A_FreedomFighter_AA_NL', GhostDepiction_F5A_FreedomFighter_AA_NL ),
        ( 'F5A_FreedomFighter_CLU_NL', GhostDepiction_F5A_FreedomFighter_CLU_NL ),
        ( 'F5A_FreedomFighter_NL', GhostDepiction_F5A_FreedomFighter_NL ),
        ( 'F5A_FreedomFighter_NPLM_NL', GhostDepiction_F5A_FreedomFighter_NPLM_NL ),
        ( 'F5A_FreedomFighter_RKT_NL', GhostDepiction_F5A_FreedomFighter_RKT_NL ),
        ( 'F8P_Crusader_AA2_FR', GhostDepiction_F8P_Crusader_AA2_FR ),
        ( 'F8P_Crusader_FR', GhostDepiction_F8P_Crusader_FR ),
        ( 'FA16_CAS_US', GhostDepiction_FA16_CAS_US ),
        ( 'FAV_AGL_US', GhostDepiction_FAV_AGL_US ),
        ( 'FAV_HMG_US', GhostDepiction_FAV_HMG_US ),
        ( 'FAV_TOW_US', GhostDepiction_FAV_TOW_US ),
        ( 'FAV_trans_US', GhostDepiction_FAV_trans_US ),
        ( 'FH70_155mm_RFA', GhostDepiction_FH70_155mm_RFA ),
        ( 'FH70_155mm_UK', GhostDepiction_FH70_155mm_UK ),
        ( 'FV101_Scorpion_BEL', GhostDepiction_FV101_Scorpion_BEL ),
        ( 'FV101_Scorpion_UK', GhostDepiction_FV101_Scorpion_UK ),
        ( 'FV101_Scorpion_para_BEL', GhostDepiction_FV101_Scorpion_para_BEL ),
        ( 'FV102_Striker_BEL', GhostDepiction_FV102_Striker_BEL ),
        ( 'FV102_Striker_UK', GhostDepiction_FV102_Striker_UK ),
        ( 'FV102_Striker_para_UK', GhostDepiction_FV102_Striker_para_UK ),
        ( 'FV103_Spartan_BEL', GhostDepiction_FV103_Spartan_BEL ),
        ( 'FV103_Spartan_GSR_UK', GhostDepiction_FV103_Spartan_GSR_UK ),
        ( 'FV103_Spartan_UK', GhostDepiction_FV103_Spartan_UK ),
        ( 'FV103_Spartan_para_BEL', GhostDepiction_FV103_Spartan_para_BEL ),
        ( 'FV105_Sultan_BEL', GhostDepiction_FV105_Sultan_BEL ),
        ( 'FV105_Sultan_UK', GhostDepiction_FV105_Sultan_UK ),
        ( 'FV105_Sultan_para_BEL', GhostDepiction_FV105_Sultan_para_BEL ),
        ( 'FV105_Sultan_para_UK', GhostDepiction_FV105_Sultan_para_UK ),
        ( 'FV107_Scimitar_BEL', GhostDepiction_FV107_Scimitar_BEL ),
        ( 'FV107_Scimitar_UK', GhostDepiction_FV107_Scimitar_UK ),
        ( 'FV107_Scimitar_para_BEL', GhostDepiction_FV107_Scimitar_para_BEL ),
        ( 'FV120_Spartan_MCT_UK', GhostDepiction_FV120_Spartan_MCT_UK ),
        ( 'FV4003_Centurion_AVRE_ROMOR_UK', GhostDepiction_FV4003_Centurion_AVRE_ROMOR_UK ),
        ( 'FV4003_Centurion_AVRE_UK', GhostDepiction_FV4003_Centurion_AVRE_UK ),
        ( 'FV4201_Chieftain_CMD_UK', GhostDepiction_FV4201_Chieftain_CMD_UK ),
        ( 'FV4201_Chieftain_Mk11_CMD_UK', GhostDepiction_FV4201_Chieftain_Mk11_CMD_UK ),
        ( 'FV4201_Chieftain_Mk11_UK', GhostDepiction_FV4201_Chieftain_Mk11_UK ),
        ( 'FV4201_Chieftain_Mk6_UK', GhostDepiction_FV4201_Chieftain_Mk6_UK ),
        ( 'FV4201_Chieftain_Mk9_UK', GhostDepiction_FV4201_Chieftain_Mk9_UK ),
        ( 'FV4201_Chieftain_UK', GhostDepiction_FV4201_Chieftain_UK ),
        ( 'FV432_CMD_UK', GhostDepiction_FV432_CMD_UK ),
        ( 'FV432_MILAN_UK', GhostDepiction_FV432_MILAN_UK ),
        ( 'FV432_Mortar_UK', GhostDepiction_FV432_Mortar_UK ),
        ( 'FV432_Rarden_UK', GhostDepiction_FV432_Rarden_UK ),
        ( 'FV432_SCAT_UK', GhostDepiction_FV432_SCAT_UK ),
        ( 'FV432_UK', GhostDepiction_FV432_UK ),
        ( 'FV432_WOMBAT_UK', GhostDepiction_FV432_WOMBAT_UK ),
        ( 'FV432_supply_UK', GhostDepiction_FV432_supply_UK ),
        ( 'FV433_Abbot_UK', GhostDepiction_FV433_Abbot_UK ),
        ( 'FV438_Swingfire_UK', GhostDepiction_FV438_Swingfire_UK ),
        ( 'FV601_Saladin_UK', GhostDepiction_FV601_Saladin_UK ),
        ( 'FV603_Saracen_UK', GhostDepiction_FV603_Saracen_UK ),
        ( 'FV721_Fox_UK', GhostDepiction_FV721_Fox_UK ),
        ( 'Faun_Kraka_20mm_RFA', GhostDepiction_Faun_Kraka_20mm_RFA ),
        ( 'Faun_Kraka_Log_RFA', GhostDepiction_Faun_Kraka_Log_RFA ),
        ( 'Faun_Kraka_TOW_RFA', GhostDepiction_Faun_Kraka_TOW_RFA ),
        ( 'Faun_kraka_RFA', GhostDepiction_Faun_kraka_RFA ),
        ( 'Ferret_Mk2_UK', GhostDepiction_Ferret_Mk2_UK ),
        ( 'G91_R3_Gina_HE_RFA', GhostDepiction_G91_R3_Gina_HE_RFA ),
        ( 'G91_R3_Gina_NPL_RFA', GhostDepiction_G91_R3_Gina_NPL_RFA ),
        ( 'G91_R3_Gina_RKT_RFA', GhostDepiction_G91_R3_Gina_RKT_RFA ),
        ( 'GAZ_46_DDR', GhostDepiction_GAZ_46_DDR ),
        ( 'GAZ_46_POL', GhostDepiction_GAZ_46_POL ),
        ( 'GAZ_46_SOV', GhostDepiction_GAZ_46_SOV ),
        ( 'GAZ_66B_POL', GhostDepiction_GAZ_66B_POL ),
        ( 'GAZ_66B_SOV', GhostDepiction_GAZ_66B_SOV ),
        ( 'GAZ_66B_ZU_SOV', GhostDepiction_GAZ_66B_ZU_SOV ),
        ( 'GAZ_66B_supply_POL', GhostDepiction_GAZ_66B_supply_POL ),
        ( 'GAZ_66B_supply_SOV', GhostDepiction_GAZ_66B_supply_SOV ),
        ( 'GAZ_66_POL', GhostDepiction_GAZ_66_POL ),
        ( 'GAZ_66_SOV', GhostDepiction_GAZ_66_SOV ),
        ( 'GAZ_66_supply_SOV', GhostDepiction_GAZ_66_supply_SOV ),
        ( 'GAZ_66_trans_POL', GhostDepiction_GAZ_66_trans_POL ),
        ( 'GTMU_1D_AGS_SOV', GhostDepiction_GTMU_1D_AGS_SOV ),
        ( 'GTMU_1D_SOV', GhostDepiction_GTMU_1D_SOV ),
        ( 'GTMU_1D_SPG9_SOV', GhostDepiction_GTMU_1D_SPG9_SOV ),
        ( 'GTMU_1D_ZU_SOV', GhostDepiction_GTMU_1D_ZU_SOV ),
        ( 'Gama_Goat_supply_US', GhostDepiction_Gama_Goat_supply_US ),
        ( 'Gama_Goat_trans_US', GhostDepiction_Gama_Goat_trans_US ),
        ( 'Gazelle_20mm_FR', GhostDepiction_Gazelle_20mm_FR ),
        ( 'Gazelle_20mm_reco_FR', GhostDepiction_Gazelle_20mm_reco_FR ),
        ( 'Gazelle_CMD_UK', GhostDepiction_Gazelle_CMD_UK ),
        ( 'Gazelle_HOT_2_FR', GhostDepiction_Gazelle_HOT_2_FR ),
        ( 'Gazelle_HOT_FR', GhostDepiction_Gazelle_HOT_FR ),
        ( 'Gazelle_Mistral_FR', GhostDepiction_Gazelle_Mistral_FR ),
        ( 'Gazelle_SNEB_UK', GhostDepiction_Gazelle_SNEB_UK ),
        ( 'Gazelle_SNEB_reco_UK', GhostDepiction_Gazelle_SNEB_reco_UK ),
        ( 'Gazelle_UK', GhostDepiction_Gazelle_UK ),
        ( 'Gazelle_reco_FR', GhostDepiction_Gazelle_reco_FR ),
        ( 'Gazelle_trans_UK', GhostDepiction_Gazelle_trans_UK ),
        ( 'Gepard_1A2_BEL', GhostDepiction_Gepard_1A2_BEL ),
        ( 'Gepard_1A2_NL', GhostDepiction_Gepard_1A2_NL ),
        ( 'Gepard_1A2_RFA', GhostDepiction_Gepard_1A2_RFA ),
        ( 'HEMTT_US', GhostDepiction_HEMTT_US ),
        ( 'HMGteam_AANF1_FR', GhostDepiction_HMGteam_AANF1_FR ),
        ( 'HMGteam_AANF1_Reserve_FR', GhostDepiction_HMGteam_AANF1_Reserve_FR ),
        ( 'HMGteam_AANF1_para_FR', GhostDepiction_HMGteam_AANF1_para_FR ),
        ( 'HMGteam_AGS17_DDR', GhostDepiction_HMGteam_AGS17_DDR ),
        ( 'HMGteam_AGS17_DShV_SOV', GhostDepiction_HMGteam_AGS17_DShV_SOV ),
        ( 'HMGteam_AGS17_POL', GhostDepiction_HMGteam_AGS17_POL ),
        ( 'HMGteam_AGS17_SOV', GhostDepiction_HMGteam_AGS17_SOV ),
        ( 'HMGteam_AGS17_TTsko_SOV', GhostDepiction_HMGteam_AGS17_TTsko_SOV ),
        ( 'HMGteam_AGS17_VDV_SOV', GhostDepiction_HMGteam_AGS17_VDV_SOV ),
        ( 'HMGteam_DShK_AA_SOV', GhostDepiction_HMGteam_DShK_AA_SOV ),
        ( 'HMGteam_DShK_SOV', GhostDepiction_HMGteam_DShK_SOV ),
        ( 'HMGteam_KPVT_SOV', GhostDepiction_HMGteam_KPVT_SOV ),
        ( 'HMGteam_M1919A4_NL', GhostDepiction_HMGteam_M1919A4_NL ),
        ( 'HMGteam_M2HB_AB_US', GhostDepiction_HMGteam_M2HB_AB_US ),
        ( 'HMGteam_M2HB_Aero_US', GhostDepiction_HMGteam_M2HB_Aero_US ),
        ( 'HMGteam_M2HB_BEL', GhostDepiction_HMGteam_M2HB_BEL ),
        ( 'HMGteam_M2HB_FR', GhostDepiction_HMGteam_M2HB_FR ),
        ( 'HMGteam_M2HB_LUX', GhostDepiction_HMGteam_M2HB_LUX ),
        ( 'HMGteam_M2HB_M63_US', GhostDepiction_HMGteam_M2HB_M63_US ),
        ( 'HMGteam_M2HB_NG_US', GhostDepiction_HMGteam_M2HB_NG_US ),
        ( 'HMGteam_M2HB_NL', GhostDepiction_HMGteam_M2HB_NL ),
        ( 'HMGteam_M2HB_RIMa_FR', GhostDepiction_HMGteam_M2HB_RIMa_FR ),
        ( 'HMGteam_M2HB_UK', GhostDepiction_HMGteam_M2HB_UK ),
        ( 'HMGteam_M2HB_US', GhostDepiction_HMGteam_M2HB_US ),
        ( 'HMGteam_M2HB_para_FR', GhostDepiction_HMGteam_M2HB_para_FR ),
        ( 'HMGteam_M2HB_para_UK', GhostDepiction_HMGteam_M2HB_para_UK ),
        ( 'HMGteam_M60_AB_US', GhostDepiction_HMGteam_M60_AB_US ),
        ( 'HMGteam_M60_Aero_US', GhostDepiction_HMGteam_M60_Aero_US ),
        ( 'HMGteam_M60_NG_US', GhostDepiction_HMGteam_M60_NG_US ),
        ( 'HMGteam_M60_US', GhostDepiction_HMGteam_M60_US ),
        ( 'HMGteam_MAG_BEL', GhostDepiction_HMGteam_MAG_BEL ),
        ( 'HMGteam_MAG_NL', GhostDepiction_HMGteam_MAG_NL ),
        ( 'HMGteam_MAG_UK', GhostDepiction_HMGteam_MAG_UK ),
        ( 'HMGteam_MAG_para_BEL', GhostDepiction_HMGteam_MAG_para_BEL ),
        ( 'HMGteam_MAG_para_UK', GhostDepiction_HMGteam_MAG_para_UK ),
        ( 'HMGteam_MG3_FJ_RFA', GhostDepiction_HMGteam_MG3_FJ_RFA ),
        ( 'HMGteam_MG3_RFA', GhostDepiction_HMGteam_MG3_RFA ),
        ( 'HMGteam_Maxim_Reserve_SOV', GhostDepiction_HMGteam_Maxim_Reserve_SOV ),
        ( 'HMGteam_Mk19_AB_US', GhostDepiction_HMGteam_Mk19_AB_US ),
        ( 'HMGteam_Mk19_US', GhostDepiction_HMGteam_Mk19_US ),
        ( 'HMGteam_NSV_6U6_VDV_SOV', GhostDepiction_HMGteam_NSV_6U6_VDV_SOV ),
        ( 'HMGteam_NSV_DDR', GhostDepiction_HMGteam_NSV_DDR ),
        ( 'HMGteam_NSV_DShV_SOV', GhostDepiction_HMGteam_NSV_DShV_SOV ),
        ( 'HMGteam_NSV_POL', GhostDepiction_HMGteam_NSV_POL ),
        ( 'HMGteam_NSV_SOV', GhostDepiction_HMGteam_NSV_SOV ),
        ( 'HMGteam_NSV_TTsko_SOV', GhostDepiction_HMGteam_NSV_TTsko_SOV ),
        ( 'HMGteam_NSV_VDV_SOV', GhostDepiction_HMGteam_NSV_VDV_SOV ),
        ( 'HMGteam_PKM_DDR', GhostDepiction_HMGteam_PKM_DDR ),
        ( 'HMGteam_PKM_DShV_SOV', GhostDepiction_HMGteam_PKM_DShV_SOV ),
        ( 'HMGteam_PKM_FJ_DDR', GhostDepiction_HMGteam_PKM_FJ_DDR ),
        ( 'HMGteam_PKM_Naval_POL', GhostDepiction_HMGteam_PKM_Naval_POL ),
        ( 'HMGteam_PKM_POL', GhostDepiction_HMGteam_PKM_POL ),
        ( 'HMGteam_PKM_SOV', GhostDepiction_HMGteam_PKM_SOV ),
        ( 'HMGteam_PKM_TTsko_SOV', GhostDepiction_HMGteam_PKM_TTsko_SOV ),
        ( 'HMGteam_PKM_VDV_SOV', GhostDepiction_HMGteam_PKM_VDV_SOV ),
        ( 'HMGteam_PKM_para_POL', GhostDepiction_HMGteam_PKM_para_POL ),
        ( 'HS30_Panzermorser_120mm_RFA', GhostDepiction_HS30_Panzermorser_120mm_RFA ),
        ( 'Harrier_CLU_UK', GhostDepiction_Harrier_CLU_UK ),
        ( 'Harrier_GR5_UK', GhostDepiction_Harrier_GR5_UK ),
        ( 'Harrier_HE1_UK', GhostDepiction_Harrier_HE1_UK ),
        ( 'Harrier_HE2_UK', GhostDepiction_Harrier_HE2_UK ),
        ( 'Harrier_RKT1_UK', GhostDepiction_Harrier_RKT1_UK ),
        ( 'Harrier_RKT2_UK', GhostDepiction_Harrier_RKT2_UK ),
        ( 'Harrier_UK', GhostDepiction_Harrier_UK ),
        ( 'Hibneryt_KG_POL', GhostDepiction_Hibneryt_KG_POL ),
        ( 'Hibneryt_POL', GhostDepiction_Hibneryt_POL ),
        ( 'Honker_4011_POL', GhostDepiction_Honker_4011_POL ),
        ( 'Honker_RYS_POL', GhostDepiction_Honker_RYS_POL ),
        ( 'Howz_2A36_Giatsint_B_SOV', GhostDepiction_Howz_2A36_Giatsint_B_SOV ),
        ( 'Howz_A19_122mm_POL', GhostDepiction_Howz_A19_122mm_POL ),
        ( 'Howz_B4M_203mm_SOV', GhostDepiction_Howz_B4M_203mm_SOV ),
        ( 'Howz_BS3_100mm_SOV', GhostDepiction_Howz_BS3_100mm_SOV ),
        ( 'Howz_Br5M_280mm_SOV', GhostDepiction_Howz_Br5M_280mm_SOV ),
        ( 'Howz_D1_152mm_POL', GhostDepiction_Howz_D1_152mm_POL ),
        ( 'Howz_D1_152mm_SOV', GhostDepiction_Howz_D1_152mm_SOV ),
        ( 'Howz_D20_152mm_DDR', GhostDepiction_Howz_D20_152mm_DDR ),
        ( 'Howz_D20_152mm_SOV', GhostDepiction_Howz_D20_152mm_SOV ),
        ( 'Howz_D30_122mm_DDR', GhostDepiction_Howz_D30_122mm_DDR ),
        ( 'Howz_D30_122mm_SOV', GhostDepiction_Howz_D30_122mm_SOV ),
        ( 'Howz_D30_122mm_VDV_SOV', GhostDepiction_Howz_D30_122mm_VDV_SOV ),
        ( 'Howz_L118_105mm_LUX', GhostDepiction_Howz_L118_105mm_LUX ),
        ( 'Howz_L118_105mm_UK', GhostDepiction_Howz_L118_105mm_UK ),
        ( 'Howz_M101_105mm_FR', GhostDepiction_Howz_M101_105mm_FR ),
        ( 'Howz_M101_105mm_RFA', GhostDepiction_Howz_M101_105mm_RFA ),
        ( 'Howz_M101_105mm_US', GhostDepiction_Howz_M101_105mm_US ),
        ( 'Howz_M101_105mm_para_BEL', GhostDepiction_Howz_M101_105mm_para_BEL ),
        ( 'Howz_M102_105mm_US', GhostDepiction_Howz_M102_105mm_US ),
        ( 'Howz_M114_155mm_NL', GhostDepiction_Howz_M114_155mm_NL ),
        ( 'Howz_M114_39_155mm_NL', GhostDepiction_Howz_M114_39_155mm_NL ),
        ( 'Howz_M119_105mm_US', GhostDepiction_Howz_M119_105mm_US ),
        ( 'Howz_M198_155mm_Copperhead_US', GhostDepiction_Howz_M198_155mm_Copperhead_US ),
        ( 'Howz_M198_155mm_US', GhostDepiction_Howz_M198_155mm_US ),
        ( 'Howz_M30_122mm_DDR', GhostDepiction_Howz_M30_122mm_DDR ),
        ( 'Howz_M30_122mm_POL', GhostDepiction_Howz_M30_122mm_POL ),
        ( 'Howz_M46_130mm_DDR', GhostDepiction_Howz_M46_130mm_DDR ),
        ( 'Howz_M46_130mm_POL', GhostDepiction_Howz_M46_130mm_POL ),
        ( 'Howz_ML20_152mm_POL', GhostDepiction_Howz_ML20_152mm_POL ),
        ( 'Howz_MstaB_150mm_SOV', GhostDepiction_Howz_MstaB_150mm_SOV ),
        ( 'Howz_ZiS3_76mm_DDR', GhostDepiction_Howz_ZiS3_76mm_DDR ),
        ( 'IS2M_SOV', GhostDepiction_IS2M_SOV ),
        ( 'Iltis_CMD_BEL', GhostDepiction_Iltis_CMD_BEL ),
        ( 'Iltis_HMG_BEL', GhostDepiction_Iltis_HMG_BEL ),
        ( 'Iltis_MILAN_BEL', GhostDepiction_Iltis_MILAN_BEL ),
        ( 'Iltis_MILAN_RFA', GhostDepiction_Iltis_MILAN_RFA ),
        ( 'Iltis_RFA', GhostDepiction_Iltis_RFA ),
        ( 'Iltis_para_CMD_BEL', GhostDepiction_Iltis_para_CMD_BEL ),
        ( 'Iltis_para_CMD_RFA', GhostDepiction_Iltis_para_CMD_RFA ),
        ( 'Iltis_trans_BEL', GhostDepiction_Iltis_trans_BEL ),
        ( 'Iltis_trans_RFA', GhostDepiction_Iltis_trans_RFA ),
        ( 'JOH_58C_US', GhostDepiction_JOH_58C_US ),
        ( 'Jaguar_1_RFA', GhostDepiction_Jaguar_1_RFA ),
        ( 'Jaguar_2_RFA', GhostDepiction_Jaguar_2_RFA ),
        ( 'Jaguar_ATGM_FR', GhostDepiction_Jaguar_ATGM_FR ),
        ( 'Jaguar_CLU_UK', GhostDepiction_Jaguar_CLU_UK ),
        ( 'Jaguar_HE1_UK', GhostDepiction_Jaguar_HE1_UK ),
        ( 'Jaguar_HE2_UK', GhostDepiction_Jaguar_HE2_UK ),
        ( 'Jaguar_HE_FR', GhostDepiction_Jaguar_HE_FR ),
        ( 'Jaguar_RKT_FR', GhostDepiction_Jaguar_RKT_FR ),
        ( 'Jaguar_RKT_UK', GhostDepiction_Jaguar_RKT_UK ),
        ( 'Jaguar_SEAD2_FR', GhostDepiction_Jaguar_SEAD2_FR ),
        ( 'Jaguar_SEAD_FR', GhostDepiction_Jaguar_SEAD_FR ),
        ( 'Jaguar_clu_FR', GhostDepiction_Jaguar_clu_FR ),
        ( 'Jaguar_nplm_FR', GhostDepiction_Jaguar_nplm_FR ),
        ( 'Jaguar_overwing_UK', GhostDepiction_Jaguar_overwing_UK ),
        ( 'Ka_50_AA_SOV', GhostDepiction_Ka_50_AA_SOV ),
        ( 'Ka_50_SOV', GhostDepiction_Ka_50_SOV ),
        ( 'KanJagdPanzer_BEL', GhostDepiction_KanJagdPanzer_BEL ),
        ( 'KanJagdPanzer_RFA', GhostDepiction_KanJagdPanzer_RFA ),
        ( 'KrAZ_255B_POL', GhostDepiction_KrAZ_255B_POL ),
        ( 'KrAZ_255B_SOV', GhostDepiction_KrAZ_255B_SOV ),
        ( 'KrAZ_255B_supply_DDR', GhostDepiction_KrAZ_255B_supply_DDR ),
        ( 'KrAZ_255B_supply_POL', GhostDepiction_KrAZ_255B_supply_POL ),
        ( 'KrAZ_255B_supply_SOV', GhostDepiction_KrAZ_255B_supply_SOV ),
        ( 'L39ZO_CLU_DDR', GhostDepiction_L39ZO_CLU_DDR ),
        ( 'L39ZO_DDR', GhostDepiction_L39ZO_DDR ),
        ( 'L39ZO_HE1_DDR', GhostDepiction_L39ZO_HE1_DDR ),
        ( 'L39ZO_HE1_SOV', GhostDepiction_L39ZO_HE1_SOV ),
        ( 'L39ZO_NPLM_SOV', GhostDepiction_L39ZO_NPLM_SOV ),
        ( 'LAV_25_M1047_US_US', GhostDepiction_LAV_25_M1047_US_US ),
        ( 'LO_1800_FASTA_4_DDR', GhostDepiction_LO_1800_FASTA_4_DDR ),
        ( 'LO_1800_ZPU_2_POL', GhostDepiction_LO_1800_ZPU_2_POL ),
        ( 'LSV_M2HB_UK', GhostDepiction_LSV_M2HB_UK ),
        ( 'LSV_MILAN_UK', GhostDepiction_LSV_MILAN_UK ),
        ( 'LUAZ_967M_AGL_SOV', GhostDepiction_LUAZ_967M_AGL_SOV ),
        ( 'LUAZ_967M_AGL_VDV_SOV', GhostDepiction_LUAZ_967M_AGL_VDV_SOV ),
        ( 'LUAZ_967M_CMD_VDV_SOV', GhostDepiction_LUAZ_967M_CMD_VDV_SOV ),
        ( 'LUAZ_967M_FAO_SOV', GhostDepiction_LUAZ_967M_FAO_SOV ),
        ( 'LUAZ_967M_Fagot_SOV', GhostDepiction_LUAZ_967M_Fagot_SOV ),
        ( 'LUAZ_967M_Fagot_VDV_SOV', GhostDepiction_LUAZ_967M_Fagot_VDV_SOV ),
        ( 'LUAZ_967M_SOV', GhostDepiction_LUAZ_967M_SOV ),
        ( 'LUAZ_967M_SPG9_SOV', GhostDepiction_LUAZ_967M_SPG9_SOV ),
        ( 'LUAZ_967M_SPG9_VDV_SOV', GhostDepiction_LUAZ_967M_SPG9_VDV_SOV ),
        ( 'LUAZ_967M_VDV_SOV', GhostDepiction_LUAZ_967M_VDV_SOV ),
        ( 'LUAZ_967M_supply_SOV', GhostDepiction_LUAZ_967M_supply_SOV ),
        ( 'LandRover_CMD_NL', GhostDepiction_LandRover_CMD_NL ),
        ( 'LandRover_CMD_Para_UK', GhostDepiction_LandRover_CMD_Para_UK ),
        ( 'LandRover_CMD_UK', GhostDepiction_LandRover_CMD_UK ),
        ( 'LandRover_MILAN_Para_UK', GhostDepiction_LandRover_MILAN_Para_UK ),
        ( 'LandRover_MILAN_UK', GhostDepiction_LandRover_MILAN_UK ),
        ( 'LandRover_NL', GhostDepiction_LandRover_NL ),
        ( 'LandRover_UK', GhostDepiction_LandRover_UK ),
        ( 'LandRover_WOMBAT_Gurkhas_UK', GhostDepiction_LandRover_WOMBAT_Gurkhas_UK ),
        ( 'LandRover_WOMBAT_UK', GhostDepiction_LandRover_WOMBAT_UK ),
        ( 'Lars_2_RFA', GhostDepiction_Lars_2_RFA ),
        ( 'Leopard_1A1A1_CMD_NL', GhostDepiction_Leopard_1A1A1_CMD_NL ),
        ( 'Leopard_1A1A1_NL', GhostDepiction_Leopard_1A1A1_NL ),
        ( 'Leopard_1A1_CMD_RFA', GhostDepiction_Leopard_1A1_CMD_RFA ),
        ( 'Leopard_1A1_NL', GhostDepiction_Leopard_1A1_NL ),
        ( 'Leopard_1A1_RFA', GhostDepiction_Leopard_1A1_RFA ),
        ( 'Leopard_1A5_BEL', GhostDepiction_Leopard_1A5_BEL ),
        ( 'Leopard_1A5_CMD_BEL', GhostDepiction_Leopard_1A5_CMD_BEL ),
        ( 'Leopard_1A5_CMD_RFA', GhostDepiction_Leopard_1A5_CMD_RFA ),
        ( 'Leopard_1A5_RFA', GhostDepiction_Leopard_1A5_RFA ),
        ( 'Leopard_1BE_BEL', GhostDepiction_Leopard_1BE_BEL ),
        ( 'Leopard_1BE_CMD_BEL', GhostDepiction_Leopard_1BE_CMD_BEL ),
        ( 'Leopard_2A1_CMD_RFA', GhostDepiction_Leopard_2A1_CMD_RFA ),
        ( 'Leopard_2A1_NL', GhostDepiction_Leopard_2A1_NL ),
        ( 'Leopard_2A1_RFA', GhostDepiction_Leopard_2A1_RFA ),
        ( 'Leopard_2A3_CMD_RFA', GhostDepiction_Leopard_2A3_CMD_RFA ),
        ( 'Leopard_2A3_RFA', GhostDepiction_Leopard_2A3_RFA ),
        ( 'Leopard_2A4B_CMD_NL', GhostDepiction_Leopard_2A4B_CMD_NL ),
        ( 'Leopard_2A4B_NL', GhostDepiction_Leopard_2A4B_NL ),
        ( 'Leopard_2A4_CMD_NL', GhostDepiction_Leopard_2A4_CMD_NL ),
        ( 'Leopard_2A4_NL', GhostDepiction_Leopard_2A4_NL ),
        ( 'Leopard_2A4_RFA', GhostDepiction_Leopard_2A4_RFA ),
        ( 'LuAZ_967M_AA_VDV_SOV', GhostDepiction_LuAZ_967M_AA_VDV_SOV ),
        ( 'Luchs_A1_RFA', GhostDepiction_Luchs_A1_RFA ),
        ( 'Lynx_AH_Mk1_LBH_UK', GhostDepiction_Lynx_AH_Mk1_LBH_UK ),
        ( 'Lynx_AH_Mk1_TOW_UK', GhostDepiction_Lynx_AH_Mk1_TOW_UK ),
        ( 'Lynx_AH_Mk1_UK', GhostDepiction_Lynx_AH_Mk1_UK ),
        ( 'Lynx_AH_Mk7_Chancellor_UK', GhostDepiction_Lynx_AH_Mk7_Chancellor_UK ),
        ( 'Lynx_AH_Mk7_I_TOW2_UK', GhostDepiction_Lynx_AH_Mk7_I_TOW2_UK ),
        ( 'Lynx_AH_Mk7_I_TOW_UK', GhostDepiction_Lynx_AH_Mk7_I_TOW_UK ),
        ( 'Lynx_AH_Mk7_SNEB_UK', GhostDepiction_Lynx_AH_Mk7_SNEB_UK ),
        ( 'M1025_Humvee_AGL_US', GhostDepiction_M1025_Humvee_AGL_US ),
        ( 'M1025_Humvee_AGL_nonPara_US', GhostDepiction_M1025_Humvee_AGL_nonPara_US ),
        ( 'M1025_Humvee_CMD_LUX', GhostDepiction_M1025_Humvee_CMD_LUX ),
        ( 'M1025_Humvee_CMD_US', GhostDepiction_M1025_Humvee_CMD_US ),
        ( 'M1025_Humvee_CMD_para_US', GhostDepiction_M1025_Humvee_CMD_para_US ),
        ( 'M1025_Humvee_GVLLD_US', GhostDepiction_M1025_Humvee_GVLLD_US ),
        ( 'M1025_Humvee_HMG_LUX', GhostDepiction_M1025_Humvee_HMG_LUX ),
        ( 'M1025_Humvee_MP_US', GhostDepiction_M1025_Humvee_MP_US ),
        ( 'M1025_Humvee_TOW_LUX', GhostDepiction_M1025_Humvee_TOW_LUX ),
        ( 'M1025_Humvee_TOW_US', GhostDepiction_M1025_Humvee_TOW_US ),
        ( 'M1025_Humvee_TOW_para_US', GhostDepiction_M1025_Humvee_TOW_para_US ),
        ( 'M1025_Humvee_scout_US', GhostDepiction_M1025_Humvee_scout_US ),
        ( 'M1025_Humvee_scout_tuto_US', GhostDepiction_M1025_Humvee_scout_tuto_US ),
        ( 'M1038_Humvee_LUX', GhostDepiction_M1038_Humvee_LUX ),
        ( 'M1038_Humvee_US', GhostDepiction_M1038_Humvee_US ),
        ( 'M106A2_HOWZ_US', GhostDepiction_M106A2_HOWZ_US ),
        ( 'M106A2_Howz_NG_US', GhostDepiction_M106A2_Howz_NG_US ),
        ( 'M106A2_Mortar_NL', GhostDepiction_M106A2_Mortar_NL ),
        ( 'M107A2_175mm_UK', GhostDepiction_M107A2_175mm_UK ),
        ( 'M109A2_BEL', GhostDepiction_M109A2_BEL ),
        ( 'M109A2_NG_US', GhostDepiction_M109A2_NG_US ),
        ( 'M109A2_NL', GhostDepiction_M109A2_NL ),
        ( 'M109A2_UK', GhostDepiction_M109A2_UK ),
        ( 'M109A3G_HOWZ_RFA', GhostDepiction_M109A3G_HOWZ_RFA ),
        ( 'M110A2_HOWZ_BEL', GhostDepiction_M110A2_HOWZ_BEL ),
        ( 'M110A2_HOWZ_NL', GhostDepiction_M110A2_HOWZ_NL ),
        ( 'M110A2_HOWZ_US', GhostDepiction_M110A2_HOWZ_US ),
        ( 'M110A2_Howz_NG_US', GhostDepiction_M110A2_Howz_NG_US ),
        ( 'M110A2_Howz_RFA', GhostDepiction_M110A2_Howz_RFA ),
        ( 'M110A2_Howz_UK', GhostDepiction_M110A2_Howz_UK ),
        ( 'M113A1B_BEL', GhostDepiction_M113A1B_BEL ),
        ( 'M113A1B_MILAN_BEL', GhostDepiction_M113A1B_MILAN_BEL ),
        ( 'M113A1B_Radar_BEL', GhostDepiction_M113A1B_Radar_BEL ),
        ( 'M113A1G_MILAN_RFA', GhostDepiction_M113A1G_MILAN_RFA ),
        ( 'M113A1G_RFA', GhostDepiction_M113A1G_RFA ),
        ( 'M113A1G_reco_RFA', GhostDepiction_M113A1G_reco_RFA ),
        ( 'M113A1G_supply_RFA', GhostDepiction_M113A1G_supply_RFA ),
        ( 'M113A1_ACAV_NG_US', GhostDepiction_M113A1_ACAV_NG_US ),
        ( 'M113A1_Dragon_NG_US', GhostDepiction_M113A1_Dragon_NG_US ),
        ( 'M113A1_NG_US', GhostDepiction_M113A1_NG_US ),
        ( 'M113A1_NL', GhostDepiction_M113A1_NL ),
        ( 'M113A1_TOW_US', GhostDepiction_M113A1_TOW_US ),
        ( 'M113A1_reco_NL', GhostDepiction_M113A1_reco_NL ),
        ( 'M113A2_TOW_US', GhostDepiction_M113A2_TOW_US ),
        ( 'M113A2_supply_US', GhostDepiction_M113A2_supply_US ),
        ( 'M113A3_US', GhostDepiction_M113A3_US ),
        ( 'M113_ACAV_NG_US', GhostDepiction_M113_ACAV_NG_US ),
        ( 'M113_ACAV_US', GhostDepiction_M113_ACAV_US ),
        ( 'M113_CV_25mm_NL', GhostDepiction_M113_CV_25mm_NL ),
        ( 'M113_Dragon_US', GhostDepiction_M113_Dragon_US ),
        ( 'M113_GreenArcher_NL', GhostDepiction_M113_GreenArcher_NL ),
        ( 'M113_GreenArcher_RFA', GhostDepiction_M113_GreenArcher_RFA ),
        ( 'M113_GreenArcher_UK', GhostDepiction_M113_GreenArcher_UK ),
        ( 'M113_PzMorser_RFA', GhostDepiction_M113_PzMorser_RFA ),
        ( 'M125_HOWZ_NG_US', GhostDepiction_M125_HOWZ_NG_US ),
        ( 'M125_HOWZ_US', GhostDepiction_M125_HOWZ_US ),
        ( 'M151A2_TOW_NG_US', GhostDepiction_M151A2_TOW_NG_US ),
        ( 'M151A2_scout_US', GhostDepiction_M151A2_scout_US ),
        ( 'M151C_RCL_NG_US', GhostDepiction_M151C_RCL_NG_US ),
        ( 'M151_MUTT_CMD_US', GhostDepiction_M151_MUTT_CMD_US ),
        ( 'M151_MUTT_trans_DDR', GhostDepiction_M151_MUTT_trans_DDR ),
        ( 'M151_MUTT_trans_US', GhostDepiction_M151_MUTT_trans_US ),
        ( 'M163_CS_US', GhostDepiction_M163_CS_US ),
        ( 'M163_PIVADS_US', GhostDepiction_M163_PIVADS_US ),
        ( 'M1A1HA_Abrams_CMD_US', GhostDepiction_M1A1HA_Abrams_CMD_US ),
        ( 'M1A1HA_Abrams_US', GhostDepiction_M1A1HA_Abrams_US ),
        ( 'M1A1_Abrams_CMD_US', GhostDepiction_M1A1_Abrams_CMD_US ),
        ( 'M1A1_Abrams_US', GhostDepiction_M1A1_Abrams_US ),
        ( 'M1A1_Abrams_reco_US', GhostDepiction_M1A1_Abrams_reco_US ),
        ( 'M1IP_Abrams_CMD_US', GhostDepiction_M1IP_Abrams_CMD_US ),
        ( 'M1IP_Abrams_US', GhostDepiction_M1IP_Abrams_US ),
        ( 'M1_Abrams_CMD_US', GhostDepiction_M1_Abrams_CMD_US ),
        ( 'M1_Abrams_NG_US', GhostDepiction_M1_Abrams_NG_US ),
        ( 'M1_Abrams_US', GhostDepiction_M1_Abrams_US ),
        ( 'M201_CMD_FR', GhostDepiction_M201_CMD_FR ),
        ( 'M201_FR', GhostDepiction_M201_FR ),
        ( 'M201_MG_FR', GhostDepiction_M201_MG_FR ),
        ( 'M201_MILAN_FR', GhostDepiction_M201_MILAN_FR ),
        ( 'M270_MLRS_RFA', GhostDepiction_M270_MLRS_RFA ),
        ( 'M270_MLRS_US', GhostDepiction_M270_MLRS_US ),
        ( 'M270_MLRS_cluster_NL', GhostDepiction_M270_MLRS_cluster_NL ),
        ( 'M270_MLRS_cluster_UK', GhostDepiction_M270_MLRS_cluster_UK ),
        ( 'M270_MLRS_cluster_US', GhostDepiction_M270_MLRS_cluster_US ),
        ( 'M274_Mule_ITOW_US', GhostDepiction_M274_Mule_ITOW_US ),
        ( 'M274_Mule_M2HB_US', GhostDepiction_M274_Mule_M2HB_US ),
        ( 'M274_Mule_RCL_US', GhostDepiction_M274_Mule_RCL_US ),
        ( 'M274_Mule_supply_US', GhostDepiction_M274_Mule_supply_US ),
        ( 'M2A1_Bradley_IFV_US', GhostDepiction_M2A1_Bradley_IFV_US ),
        ( 'M2A1_Bradley_Leader_US', GhostDepiction_M2A1_Bradley_Leader_US ),
        ( 'M2A2_Bradley_IFV_US', GhostDepiction_M2A2_Bradley_IFV_US ),
        ( 'M2A2_Bradley_Leader_US', GhostDepiction_M2A2_Bradley_Leader_US ),
        ( 'M2_Bradley_IFV_NG_US', GhostDepiction_M2_Bradley_IFV_NG_US ),
        ( 'M35_supply_US', GhostDepiction_M35_supply_US ),
        ( 'M35_trans_DDR', GhostDepiction_M35_trans_DDR ),
        ( 'M35_trans_US', GhostDepiction_M35_trans_US ),
        ( 'M35_trans_tuto_US', GhostDepiction_M35_trans_tuto_US ),
        ( 'M38A1_CMD_NL', GhostDepiction_M38A1_CMD_NL ),
        ( 'M38A1_MG_NL', GhostDepiction_M38A1_MG_NL ),
        ( 'M38A1_NL', GhostDepiction_M38A1_NL ),
        ( 'M38A1_RCL_NL', GhostDepiction_M38A1_RCL_NL ),
        ( 'M38A1_TOW_NL', GhostDepiction_M38A1_TOW_NL ),
        ( 'M3A1_Bradley_CFV_US', GhostDepiction_M3A1_Bradley_CFV_US ),
        ( 'M3A2_Bradley_CFV_US', GhostDepiction_M3A2_Bradley_CFV_US ),
        ( 'M42_Duster_US', GhostDepiction_M42_Duster_US ),
        ( 'M48A2C_RFA', GhostDepiction_M48A2C_RFA ),
        ( 'M48A2GA2_CMD_RFA', GhostDepiction_M48A2GA2_CMD_RFA ),
        ( 'M48A2GA2_RFA', GhostDepiction_M48A2GA2_RFA ),
        ( 'M48A5_reco_NG_US', GhostDepiction_M48A5_reco_NG_US ),
        ( 'M48_Chaparral_MIM72F_US', GhostDepiction_M48_Chaparral_MIM72F_US ),
        ( 'M548A2_supply_US', GhostDepiction_M548A2_supply_US ),
        ( 'M551A1_ACAV_Sheridan_US', GhostDepiction_M551A1_ACAV_Sheridan_US ),
        ( 'M551A1_TTS_Sheridan_CMD_US', GhostDepiction_M551A1_TTS_Sheridan_CMD_US ),
        ( 'M551A1_TTS_Sheridan_US', GhostDepiction_M551A1_TTS_Sheridan_US ),
        ( 'M577_NL', GhostDepiction_M577_NL ),
        ( 'M577_RFA', GhostDepiction_M577_RFA ),
        ( 'M577_US', GhostDepiction_M577_US ),
        ( 'M60A1_AVLM_US', GhostDepiction_M60A1_AVLM_US ),
        ( 'M60A1_RISE_Passive_CMD_US', GhostDepiction_M60A1_RISE_Passive_CMD_US ),
        ( 'M60A1_RISE_Passive_US', GhostDepiction_M60A1_RISE_Passive_US ),
        ( 'M60A1_RISE_Passive_reco_US', GhostDepiction_M60A1_RISE_Passive_reco_US ),
        ( 'M60A3_CMD_US', GhostDepiction_M60A3_CMD_US ),
        ( 'M60A3_ERA_Patton_US', GhostDepiction_M60A3_ERA_Patton_US ),
        ( 'M60A3_Patton_NG_US', GhostDepiction_M60A3_Patton_NG_US ),
        ( 'M60A3_Patton_US', GhostDepiction_M60A3_Patton_US ),
        ( 'M728_CEV_NG_US', GhostDepiction_M728_CEV_NG_US ),
        ( 'M728_CEV_US', GhostDepiction_M728_CEV_US ),
        ( 'M812_supply_US', GhostDepiction_M812_supply_US ),
        ( 'M901A1_ITW_US', GhostDepiction_M901A1_ITW_US ),
        ( 'M901_TOW_NG_US', GhostDepiction_M901_TOW_NG_US ),
        ( 'M901_TOW_US', GhostDepiction_M901_TOW_US ),
        ( 'M981_FISTV_US', GhostDepiction_M981_FISTV_US ),
        ( 'M998_Avenger_US', GhostDepiction_M998_Avenger_US ),
        ( 'M998_Avenger_nonPara_US', GhostDepiction_M998_Avenger_nonPara_US ),
        ( 'M998_Humvee_AGL_US', GhostDepiction_M998_Humvee_AGL_US ),
        ( 'M998_Humvee_Delta_US', GhostDepiction_M998_Humvee_Delta_US ),
        ( 'M998_Humvee_HMG_US', GhostDepiction_M998_Humvee_HMG_US ),
        ( 'M998_Humvee_LUX', GhostDepiction_M998_Humvee_LUX ),
        ( 'M998_Humvee_US', GhostDepiction_M998_Humvee_US ),
        ( 'MAN_Kat_6x6_RFA', GhostDepiction_MAN_Kat_6x6_RFA ),
        ( 'MAN_Kat_6x6_trans_RFA', GhostDepiction_MAN_Kat_6x6_trans_RFA ),
        ( 'MAN_Z311_BEL', GhostDepiction_MAN_Z311_BEL ),
        ( 'MAN_Z311_Mi50_BEL', GhostDepiction_MAN_Z311_Mi50_BEL ),
        ( 'MCV_80_Warrior_CMD_UK', GhostDepiction_MCV_80_Warrior_CMD_UK ),
        ( 'MCV_80_Warrior_MILAN_ERA_UK', GhostDepiction_MCV_80_Warrior_MILAN_ERA_UK ),
        ( 'MCV_80_Warrior_MILAN_UK', GhostDepiction_MCV_80_Warrior_MILAN_UK ),
        ( 'MCV_80_Warrior_UK', GhostDepiction_MCV_80_Warrior_UK ),
        ( 'MFRW_RM70_DDR', GhostDepiction_MFRW_RM70_DDR ),
        ( 'MFRW_RM70_cluster_DDR', GhostDepiction_MFRW_RM70_cluster_DDR ),
        ( 'MH47D_Super_Chinook_US', GhostDepiction_MH47D_Super_Chinook_US ),
        ( 'MH_60A_DAP_US', GhostDepiction_MH_60A_DAP_US ),
        ( 'MLRS_WP_8z_POL', GhostDepiction_MLRS_WP_8z_POL ),
        ( 'MQM_105_Aquila_US', GhostDepiction_MQM_105_Aquila_US ),
        ( 'MTLB_CMD_DDR', GhostDepiction_MTLB_CMD_DDR ),
        ( 'MTLB_CMD_SOV', GhostDepiction_MTLB_CMD_SOV ),
        ( 'MTLB_Shturm_DDR', GhostDepiction_MTLB_Shturm_DDR ),
        ( 'MTLB_Shturm_SOV', GhostDepiction_MTLB_Shturm_SOV ),
        ( 'MTLB_Strela10M3_SOV', GhostDepiction_MTLB_Strela10M3_SOV ),
        ( 'MTLB_Strela10_DDR', GhostDepiction_MTLB_Strela10_DDR ),
        ( 'MTLB_Strela10_POL', GhostDepiction_MTLB_Strela10_POL ),
        ( 'MTLB_Strela10_SOV', GhostDepiction_MTLB_Strela10_SOV ),
        ( 'MTLB_TRI_Hors_POL', GhostDepiction_MTLB_TRI_Hors_POL ),
        ( 'MTLB_Vasilek_SOV', GhostDepiction_MTLB_Vasilek_SOV ),
        ( 'MTLB_supply_DDR', GhostDepiction_MTLB_supply_DDR ),
        ( 'MTLB_supply_SOV', GhostDepiction_MTLB_supply_SOV ),
        ( 'MTLB_trans_DDR', GhostDepiction_MTLB_trans_DDR ),
        ( 'MTLB_trans_POL', GhostDepiction_MTLB_trans_POL ),
        ( 'MTLB_transp_SOV', GhostDepiction_MTLB_transp_SOV ),
        ( 'Marder_1A2_MILAN_RFA', GhostDepiction_Marder_1A2_MILAN_RFA ),
        ( 'Marder_1A2_RFA', GhostDepiction_Marder_1A2_RFA ),
        ( 'Marder_1A3_MILAN_RFA', GhostDepiction_Marder_1A3_MILAN_RFA ),
        ( 'Marder_1A3_RFA', GhostDepiction_Marder_1A3_RFA ),
        ( 'Marder_Roland_2_RFA', GhostDepiction_Marder_Roland_2_RFA ),
        ( 'Marder_Roland_RFA', GhostDepiction_Marder_Roland_RFA ),
        ( 'MiG_17PF_POL', GhostDepiction_MiG_17PF_POL ),
        ( 'MiG_21PFM_AA_DDR', GhostDepiction_MiG_21PFM_AA_DDR ),
        ( 'MiG_21PFM_DDR', GhostDepiction_MiG_21PFM_DDR ),
        ( 'MiG_21bis_AA2_DDR', GhostDepiction_MiG_21bis_AA2_DDR ),
        ( 'MiG_21bis_AA3_DDR', GhostDepiction_MiG_21bis_AA3_DDR ),
        ( 'MiG_21bis_AA_POL', GhostDepiction_MiG_21bis_AA_POL ),
        ( 'MiG_21bis_CLU_DDR', GhostDepiction_MiG_21bis_CLU_DDR ),
        ( 'MiG_21bis_HE_DDR', GhostDepiction_MiG_21bis_HE_DDR ),
        ( 'MiG_21bis_HE_POL', GhostDepiction_MiG_21bis_HE_POL ),
        ( 'MiG_21bis_NPLM_DDR', GhostDepiction_MiG_21bis_NPLM_DDR ),
        ( 'MiG_21bis_POL', GhostDepiction_MiG_21bis_POL ),
        ( 'MiG_21bis_RKT2_DDR', GhostDepiction_MiG_21bis_RKT2_DDR ),
        ( 'MiG_21bis_RKT2_POL', GhostDepiction_MiG_21bis_RKT2_POL ),
        ( 'MiG_23BN_AT2_DDR', GhostDepiction_MiG_23BN_AT2_DDR ),
        ( 'MiG_23BN_AT_DDR', GhostDepiction_MiG_23BN_AT_DDR ),
        ( 'MiG_23BN_CLU_DDR', GhostDepiction_MiG_23BN_CLU_DDR ),
        ( 'MiG_23BN_DDR', GhostDepiction_MiG_23BN_DDR ),
        ( 'MiG_23BN_KMGU_DDR', GhostDepiction_MiG_23BN_KMGU_DDR ),
        ( 'MiG_23BN_RKT_DDR', GhostDepiction_MiG_23BN_RKT_DDR ),
        ( 'MiG_23BN_nplm_DDR', GhostDepiction_MiG_23BN_nplm_DDR ),
        ( 'MiG_23MF_AA2_POL', GhostDepiction_MiG_23MF_AA2_POL ),
        ( 'MiG_23MF_AA_DDR', GhostDepiction_MiG_23MF_AA_DDR ),
        ( 'MiG_23MF_AA_POL', GhostDepiction_MiG_23MF_AA_POL ),
        ( 'MiG_23MF_DDR', GhostDepiction_MiG_23MF_DDR ),
        ( 'MiG_23MLD_AA1_SOV', GhostDepiction_MiG_23MLD_AA1_SOV ),
        ( 'MiG_23MLD_SOV', GhostDepiction_MiG_23MLD_SOV ),
        ( 'MiG_23ML_DDR', GhostDepiction_MiG_23ML_DDR ),
        ( 'MiG_23ML_SOV', GhostDepiction_MiG_23ML_SOV ),
        ( 'MiG_23P_SOV', GhostDepiction_MiG_23P_SOV ),
        ( 'MiG_25BM_SOV', GhostDepiction_MiG_25BM_SOV ),
        ( 'MiG_25RBF_SOV', GhostDepiction_MiG_25RBF_SOV ),
        ( 'MiG_27K_AT1_SOV', GhostDepiction_MiG_27K_AT1_SOV ),
        ( 'MiG_27K_AT2_SOV', GhostDepiction_MiG_27K_AT2_SOV ),
        ( 'MiG_27K_LGB_SOV', GhostDepiction_MiG_27K_LGB_SOV ),
        ( 'MiG_27K_SEAD_SOV', GhostDepiction_MiG_27K_SEAD_SOV ),
        ( 'MiG_27M_CLU_SOV', GhostDepiction_MiG_27M_CLU_SOV ),
        ( 'MiG_27M_SOV', GhostDepiction_MiG_27M_SOV ),
        ( 'MiG_27M_bombe_SOV', GhostDepiction_MiG_27M_bombe_SOV ),
        ( 'MiG_27M_napalm_SOV', GhostDepiction_MiG_27M_napalm_SOV ),
        ( 'MiG_27M_rkt_SOV', GhostDepiction_MiG_27M_rkt_SOV ),
        ( 'MiG_27M_sead_SOV', GhostDepiction_MiG_27M_sead_SOV ),
        ( 'MiG_29_AA2_POL', GhostDepiction_MiG_29_AA2_POL ),
        ( 'MiG_29_AA2_SOV', GhostDepiction_MiG_29_AA2_SOV ),
        ( 'MiG_29_AA3_SOV', GhostDepiction_MiG_29_AA3_SOV ),
        ( 'MiG_29_AA_DDR', GhostDepiction_MiG_29_AA_DDR ),
        ( 'MiG_29_AA_POL', GhostDepiction_MiG_29_AA_POL ),
        ( 'MiG_29_AA_SOV', GhostDepiction_MiG_29_AA_SOV ),
        ( 'MiG_31M_SOV', GhostDepiction_MiG_31M_SOV ),
        ( 'MiG_31_AA1_SOV', GhostDepiction_MiG_31_AA1_SOV ),
        ( 'MiG_31_AA2_SOV', GhostDepiction_MiG_31_AA2_SOV ),
        ( 'Mi_14PL_AT_DDR', GhostDepiction_Mi_14PL_AT_DDR ),
        ( 'Mi_14PL_recon_DDR', GhostDepiction_Mi_14PL_recon_DDR ),
        ( 'Mi_24D_AA_DDR', GhostDepiction_Mi_24D_AA_DDR ),
        ( 'Mi_24D_Desant_SOV', GhostDepiction_Mi_24D_Desant_SOV ),
        ( 'Mi_24D_POL', GhostDepiction_Mi_24D_POL ),
        ( 'Mi_24D_s5_AT_DDR', GhostDepiction_Mi_24D_s5_AT_DDR ),
        ( 'Mi_24D_s5_AT_SOV', GhostDepiction_Mi_24D_s5_AT_SOV ),
        ( 'Mi_24D_s8_AT_DDR', GhostDepiction_Mi_24D_s8_AT_DDR ),
        ( 'Mi_24D_s8_AT_POL', GhostDepiction_Mi_24D_s8_AT_POL ),
        ( 'Mi_24D_s8_AT_SOV', GhostDepiction_Mi_24D_s8_AT_SOV ),
        ( 'Mi_24K_reco_SOV', GhostDepiction_Mi_24K_reco_SOV ),
        ( 'Mi_24P_AA_SOV', GhostDepiction_Mi_24P_AA_SOV ),
        ( 'Mi_24P_SOV', GhostDepiction_Mi_24P_SOV ),
        ( 'Mi_24P_s8_AT2_DDR', GhostDepiction_Mi_24P_s8_AT2_DDR ),
        ( 'Mi_24P_s8_AT_DDR', GhostDepiction_Mi_24P_s8_AT_DDR ),
        ( 'Mi_24VP_SOV', GhostDepiction_Mi_24VP_SOV ),
        ( 'Mi_24V_AA_SOV', GhostDepiction_Mi_24V_AA_SOV ),
        ( 'Mi_24V_AT_SOV', GhostDepiction_Mi_24V_AT_SOV ),
        ( 'Mi_24V_POL', GhostDepiction_Mi_24V_POL ),
        ( 'Mi_24V_RKT2_SOV', GhostDepiction_Mi_24V_RKT2_SOV ),
        ( 'Mi_24V_RKT_SOV', GhostDepiction_Mi_24V_RKT_SOV ),
        ( 'Mi_24V_SOV', GhostDepiction_Mi_24V_SOV ),
        ( 'Mi_26_SOV', GhostDepiction_Mi_26_SOV ),
        ( 'Mi_2Ro_reco_POL', GhostDepiction_Mi_2Ro_reco_POL ),
        ( 'Mi_2_AA_POL', GhostDepiction_Mi_2_AA_POL ),
        ( 'Mi_2_ATGM_POL', GhostDepiction_Mi_2_ATGM_POL ),
        ( 'Mi_2_CMD_DDR', GhostDepiction_Mi_2_CMD_DDR ),
        ( 'Mi_2_CMD_POL', GhostDepiction_Mi_2_CMD_POL ),
        ( 'Mi_2_gunship_DDR', GhostDepiction_Mi_2_gunship_DDR ),
        ( 'Mi_2_gunship_POL', GhostDepiction_Mi_2_gunship_POL ),
        ( 'Mi_2_reco_DDR', GhostDepiction_Mi_2_reco_DDR ),
        ( 'Mi_2_reco_SOV', GhostDepiction_Mi_2_reco_SOV ),
        ( 'Mi_2_rocket_DDR', GhostDepiction_Mi_2_rocket_DDR ),
        ( 'Mi_2_rocket_POL', GhostDepiction_Mi_2_rocket_POL ),
        ( 'Mi_2_trans_DDR', GhostDepiction_Mi_2_trans_DDR ),
        ( 'Mi_2_trans_POL', GhostDepiction_Mi_2_trans_POL ),
        ( 'Mi_2_trans_SOV', GhostDepiction_Mi_2_trans_SOV ),
        ( 'Mi_6_POL', GhostDepiction_Mi_6_POL ),
        ( 'Mi_6_SOV', GhostDepiction_Mi_6_SOV ),
        ( 'Mi_8K_CMD_SOV', GhostDepiction_Mi_8K_CMD_SOV ),
        ( 'Mi_8MTPI_SOV', GhostDepiction_Mi_8MTPI_SOV ),
        ( 'Mi_8MTV_SOV', GhostDepiction_Mi_8MTV_SOV ),
        ( 'Mi_8MT_POL', GhostDepiction_Mi_8MT_POL ),
        ( 'Mi_8PPA_SOV', GhostDepiction_Mi_8PPA_SOV ),
        ( 'Mi_8R_SOV', GhostDepiction_Mi_8R_SOV ),
        ( 'Mi_8TB_DDR', GhostDepiction_Mi_8TB_DDR ),
        ( 'Mi_8TB_SOV', GhostDepiction_Mi_8TB_SOV ),
        ( 'Mi_8TB_reco_Marine_DDR', GhostDepiction_Mi_8TB_reco_Marine_DDR ),
        ( 'Mi_8TV_DDR', GhostDepiction_Mi_8TV_DDR ),
        ( 'Mi_8TV_Gunship_SOV', GhostDepiction_Mi_8TV_Gunship_SOV ),
        ( 'Mi_8TV_PodGatling_DDR', GhostDepiction_Mi_8TV_PodGatling_DDR ),
        ( 'Mi_8TV_PodGatling_PodAGL_SOV', GhostDepiction_Mi_8TV_PodGatling_PodAGL_SOV ),
        ( 'Mi_8TV_SOV', GhostDepiction_Mi_8TV_SOV ),
        ( 'Mi_8TV_UPK_DDR', GhostDepiction_Mi_8TV_UPK_DDR ),
        ( 'Mi_8TV_non_arme_SOV', GhostDepiction_Mi_8TV_non_arme_SOV ),
        ( 'Mi_8TV_s57_16_SOV', GhostDepiction_Mi_8TV_s57_16_SOV ),
        ( 'Mi_8TV_s57_32_DDR', GhostDepiction_Mi_8TV_s57_32_DDR ),
        ( 'Mi_8TV_s57_32_SOV', GhostDepiction_Mi_8TV_s57_32_SOV ),
        ( 'Mi_8TV_s80_SOV', GhostDepiction_Mi_8TV_s80_SOV ),
        ( 'Mi_8TZ_SOV', GhostDepiction_Mi_8TZ_SOV ),
        ( 'Mi_8T_DDR', GhostDepiction_Mi_8T_DDR ),
        ( 'Mi_8T_POL', GhostDepiction_Mi_8T_POL ),
        ( 'Mi_8T_non_arme_DDR', GhostDepiction_Mi_8T_non_arme_DDR ),
        ( 'Mi_8T_non_arme_POL', GhostDepiction_Mi_8T_non_arme_POL ),
        ( 'Mi_8_supply_DDR', GhostDepiction_Mi_8_supply_DDR ),
        ( 'Mi_8_supply_POL', GhostDepiction_Mi_8_supply_POL ),
        ( 'Mi_9_DDR', GhostDepiction_Mi_9_DDR ),
        ( 'Mirage_2000_C_FR', GhostDepiction_Mirage_2000_C_FR ),
        ( 'Mirage_5_BA_BEL', GhostDepiction_Mirage_5_BA_BEL ),
        ( 'Mirage_5_BA_CLU_BEL', GhostDepiction_Mirage_5_BA_CLU_BEL ),
        ( 'Mirage_5_BA_MIRSIP_BEL', GhostDepiction_Mirage_5_BA_MIRSIP_BEL ),
        ( 'Mirage_5_BA_NPLM_BEL', GhostDepiction_Mirage_5_BA_NPLM_BEL ),
        ( 'Mirage_5_BA_RKT_BEL', GhostDepiction_Mirage_5_BA_RKT_BEL ),
        ( 'Mirage_5_F_FR', GhostDepiction_Mirage_5_F_FR ),
        ( 'Mirage_5_F_clu_FR', GhostDepiction_Mirage_5_F_clu_FR ),
        ( 'Mirage_5_F_nplm_FR', GhostDepiction_Mirage_5_F_nplm_FR ),
        ( 'Mirage_F1_CT_FR', GhostDepiction_Mirage_F1_CT_FR ),
        ( 'Mirage_F1_C_FR', GhostDepiction_Mirage_F1_C_FR ),
        ( 'Mirage_III_E_FR', GhostDepiction_Mirage_III_E_FR ),
        ( 'Mirage_IV_FR', GhostDepiction_Mirage_IV_FR ),
        ( 'Mirage_IV_SEAD_FR', GhostDepiction_Mirage_IV_SEAD_FR ),
        ( 'Mortier_107mm_Aero_US', GhostDepiction_Mortier_107mm_Aero_US ),
        ( 'Mortier_107mm_Airborne_US', GhostDepiction_Mortier_107mm_Airborne_US ),
        ( 'Mortier_107mm_BEL', GhostDepiction_Mortier_107mm_BEL ),
        ( 'Mortier_107mm_NG_US', GhostDepiction_Mortier_107mm_NG_US ),
        ( 'Mortier_107mm_NL', GhostDepiction_Mortier_107mm_NL ),
        ( 'Mortier_107mm_US', GhostDepiction_Mortier_107mm_US ),
        ( 'Mortier_107mm_para_BEL', GhostDepiction_Mortier_107mm_para_BEL ),
        ( 'Mortier_240mm_M240_Cluster_SOV', GhostDepiction_Mortier_240mm_M240_Cluster_SOV ),
        ( 'Mortier_240mm_M240_POL', GhostDepiction_Mortier_240mm_M240_POL ),
        ( 'Mortier_240mm_M240_SOV', GhostDepiction_Mortier_240mm_M240_SOV ),
        ( 'Mortier_2B14_82mm_DShV_SOV', GhostDepiction_Mortier_2B14_82mm_DShV_SOV ),
        ( 'Mortier_2B14_82mm_SOV', GhostDepiction_Mortier_2B14_82mm_SOV ),
        ( 'Mortier_2B14_82mm_VDV_SOV', GhostDepiction_Mortier_2B14_82mm_VDV_SOV ),
        ( 'Mortier_2B9_Vasilek_Para_POL', GhostDepiction_Mortier_2B9_Vasilek_Para_POL ),
        ( 'Mortier_2B9_Vasilek_SOV', GhostDepiction_Mortier_2B9_Vasilek_SOV ),
        ( 'Mortier_2B9_Vasilek_nonPara_SOV', GhostDepiction_Mortier_2B9_Vasilek_nonPara_SOV ),
        ( 'Mortier_2S12_120mm_DDR', GhostDepiction_Mortier_2S12_120mm_DDR ),
        ( 'Mortier_2S12_120mm_DShV_SOV', GhostDepiction_Mortier_2S12_120mm_DShV_SOV ),
        ( 'Mortier_2S12_120mm_POL', GhostDepiction_Mortier_2S12_120mm_POL ),
        ( 'Mortier_2S12_120mm_Para_POL', GhostDepiction_Mortier_2S12_120mm_Para_POL ),
        ( 'Mortier_2S12_120mm_SOV', GhostDepiction_Mortier_2S12_120mm_SOV ),
        ( 'Mortier_2S12_120mm_TTsko_SOV', GhostDepiction_Mortier_2S12_120mm_TTsko_SOV ),
        ( 'Mortier_2S12_120mm_VDV_SOV', GhostDepiction_Mortier_2S12_120mm_VDV_SOV ),
        ( 'Mortier_81mm_BEL', GhostDepiction_Mortier_81mm_BEL ),
        ( 'Mortier_81mm_FR', GhostDepiction_Mortier_81mm_FR ),
        ( 'Mortier_81mm_LUX', GhostDepiction_Mortier_81mm_LUX ),
        ( 'Mortier_81mm_para_BEL', GhostDepiction_Mortier_81mm_para_BEL ),
        ( 'Mortier_M29_81mm_Marines_NL', GhostDepiction_Mortier_M29_81mm_Marines_NL ),
        ( 'Mortier_M29_81mm_NL', GhostDepiction_Mortier_M29_81mm_NL ),
        ( 'Mortier_M29_81mm_US', GhostDepiction_Mortier_M29_81mm_US ),
        ( 'Mortier_M43_160mm_POL', GhostDepiction_Mortier_M43_160mm_POL ),
        ( 'Mortier_M43_82mm_DDR', GhostDepiction_Mortier_M43_82mm_DDR ),
        ( 'Mortier_M43_82mm_FJ_DDR', GhostDepiction_Mortier_M43_82mm_FJ_DDR ),
        ( 'Mortier_M43_82mm_POL', GhostDepiction_Mortier_M43_82mm_POL ),
        ( 'Mortier_M43_82mm_Para_POL', GhostDepiction_Mortier_M43_82mm_Para_POL ),
        ( 'Mortier_MORT61_120mm_FR', GhostDepiction_Mortier_MORT61_120mm_FR ),
        ( 'Mortier_MORT61_120mm_NL', GhostDepiction_Mortier_MORT61_120mm_NL ),
        ( 'Mortier_MORT61_120mm_para_FR', GhostDepiction_Mortier_MORT61_120mm_para_FR ),
        ( 'Mortier_Nona_K_120mm_SOV', GhostDepiction_Mortier_Nona_K_120mm_SOV ),
        ( 'Mortier_PM43_120mm_DDR', GhostDepiction_Mortier_PM43_120mm_DDR ),
        ( 'Mortier_PM43_120mm_POL', GhostDepiction_Mortier_PM43_120mm_POL ),
        ( 'Mortier_PM43_120mm_SOV', GhostDepiction_Mortier_PM43_120mm_SOV ),
        ( 'Mortier_Tampella_120mm_RFA', GhostDepiction_Mortier_Tampella_120mm_RFA ),
        ( 'Mortier_Tampella_120mm_para_RFA', GhostDepiction_Mortier_Tampella_120mm_para_RFA ),
        ( 'OA10A_US', GhostDepiction_OA10A_US ),
        ( 'OH58A_reco_NG_US', GhostDepiction_OH58A_reco_NG_US ),
        ( 'OH58C_CMD_US', GhostDepiction_OH58C_CMD_US ),
        ( 'OH58C_Scout_US', GhostDepiction_OH58C_Scout_US ),
        ( 'OH58D_Combat_Scout_US', GhostDepiction_OH58D_Combat_Scout_US ),
        ( 'OH58D_Kiowa_Warrior_US', GhostDepiction_OH58D_Kiowa_Warrior_US ),
        ( 'OH58_CS_US', GhostDepiction_OH58_CS_US ),
        ( 'OT_62_TOPAS_2AP_POL', GhostDepiction_OT_62_TOPAS_2AP_POL ),
        ( 'OT_62_TOPAS_JOD_POL', GhostDepiction_OT_62_TOPAS_JOD_POL ),
        ( 'OT_62_TOPAS_POL', GhostDepiction_OT_62_TOPAS_POL ),
        ( 'OT_62_TOPAS_R3M_CMD_POL', GhostDepiction_OT_62_TOPAS_R3M_CMD_POL ),
        ( 'OT_62_TOPAS_SPG9_POL', GhostDepiction_OT_62_TOPAS_SPG9_POL ),
        ( 'OT_64_SKOT_2AM_POL', GhostDepiction_OT_64_SKOT_2AM_POL ),
        ( 'OT_64_SKOT_2A_POL', GhostDepiction_OT_64_SKOT_2A_POL ),
        ( 'OT_64_SKOT_2P_POL', GhostDepiction_OT_64_SKOT_2P_POL ),
        ( 'OT_64_SKOT_2_POL', GhostDepiction_OT_64_SKOT_2_POL ),
        ( 'OT_64_SKOT_CMD_POL', GhostDepiction_OT_64_SKOT_CMD_POL ),
        ( 'OT_65_CMD_POL', GhostDepiction_OT_65_CMD_POL ),
        ( 'OT_65_DDR', GhostDepiction_OT_65_DDR ),
        ( 'OT_65_POL', GhostDepiction_OT_65_POL ),
        ( 'Obusier_155mm_mle1950_FR', GhostDepiction_Obusier_155mm_mle1950_FR ),
        ( 'Osa_9K33M3_DDR', GhostDepiction_Osa_9K33M3_DDR ),
        ( 'Osa_9K33M3_POL', GhostDepiction_Osa_9K33M3_POL ),
        ( 'Osa_9K33M3_SOV', GhostDepiction_Osa_9K33M3_SOV ),
        ( 'PSzH_IV_DDR', GhostDepiction_PSzH_IV_DDR ),
        ( 'PT76B_CMD_DDR', GhostDepiction_PT76B_CMD_DDR ),
        ( 'PT76B_CMD_Naval_POL', GhostDepiction_PT76B_CMD_Naval_POL ),
        ( 'PT76B_CMD_POL', GhostDepiction_PT76B_CMD_POL ),
        ( 'PT76B_DDR', GhostDepiction_PT76B_DDR ),
        ( 'PT76B_Naval_POL', GhostDepiction_PT76B_Naval_POL ),
        ( 'PT76B_POL', GhostDepiction_PT76B_POL ),
        ( 'PT76B_tank_DDR', GhostDepiction_PT76B_tank_DDR ),
        ( 'PT76B_tank_Naval_POL', GhostDepiction_PT76B_tank_Naval_POL ),
        ( 'PT76B_tank_POL', GhostDepiction_PT76B_tank_POL ),
        ( 'PTS_M_supply_DDR', GhostDepiction_PTS_M_supply_DDR ),
        ( 'PTS_M_supply_POL', GhostDepiction_PTS_M_supply_POL ),
        ( 'Pchela_1T_SOV', GhostDepiction_Pchela_1T_SOV ),
        ( 'Puma_FR', GhostDepiction_Puma_FR ),
        ( 'Puma_HET_FR', GhostDepiction_Puma_HET_FR ),
        ( 'Puma_PC_FR', GhostDepiction_Puma_PC_FR ),
        ( 'Puma_Pirate_FR', GhostDepiction_Puma_Pirate_FR ),
        ( 'Puma_UK', GhostDepiction_Puma_UK ),
        ( 'RCL_L6_Wombat_UK', GhostDepiction_RCL_L6_Wombat_UK ),
        ( 'RM70_85_DDR', GhostDepiction_RM70_85_DDR ),
        ( 'RM70_85_POL', GhostDepiction_RM70_85_POL ),
        ( 'Roland_2_FR', GhostDepiction_Roland_2_FR ),
        ( 'Roland_3_FR', GhostDepiction_Roland_3_FR ),
        ( 'Rover_101FC_LUX', GhostDepiction_Rover_101FC_LUX ),
        ( 'Rover_101FC_UK', GhostDepiction_Rover_101FC_UK ),
        ( 'Rover_101FC_supply_UK', GhostDepiction_Rover_101FC_supply_UK ),
        ( 'SPW_152K_DDR', GhostDepiction_SPW_152K_DDR ),
        ( 'Saxon_CMD_UK', GhostDepiction_Saxon_CMD_UK ),
        ( 'Saxon_UK', GhostDepiction_Saxon_UK ),
        ( 'Sonderwagen_4_RFA', GhostDepiction_Sonderwagen_4_RFA ),
        ( 'Sonderwagen_4_recon_RFA', GhostDepiction_Sonderwagen_4_recon_RFA ),
        ( 'Star_266_POL', GhostDepiction_Star_266_POL ),
        ( 'Star_266_supply_POL', GhostDepiction_Star_266_supply_POL ),
        ( 'Su_15TM_AA2_SOV', GhostDepiction_Su_15TM_AA2_SOV ),
        ( 'Su_15TM_AA_SOV', GhostDepiction_Su_15TM_AA_SOV ),
        ( 'Su_17M4_SOV', GhostDepiction_Su_17M4_SOV ),
        ( 'Su_17_cluster_POL', GhostDepiction_Su_17_cluster_POL ),
        ( 'Su_22_AT2_DDR', GhostDepiction_Su_22_AT2_DDR ),
        ( 'Su_22_AT_DDR', GhostDepiction_Su_22_AT_DDR ),
        ( 'Su_22_AT_POL', GhostDepiction_Su_22_AT_POL ),
        ( 'Su_22_AT_SOV', GhostDepiction_Su_22_AT_SOV ),
        ( 'Su_22_DDR', GhostDepiction_Su_22_DDR ),
        ( 'Su_22_HE2_DDR', GhostDepiction_Su_22_HE2_DDR ),
        ( 'Su_22_POL', GhostDepiction_Su_22_POL ),
        ( 'Su_22_RKT2_POL', GhostDepiction_Su_22_RKT2_POL ),
        ( 'Su_22_RKT_DDR', GhostDepiction_Su_22_RKT_DDR ),
        ( 'Su_22_RKT_POL', GhostDepiction_Su_22_RKT_POL ),
        ( 'Su_22_SEAD_DDR', GhostDepiction_Su_22_SEAD_DDR ),
        ( 'Su_22_SEAD_POL', GhostDepiction_Su_22_SEAD_POL ),
        ( 'Su_22_UPK_DDR', GhostDepiction_Su_22_UPK_DDR ),
        ( 'Su_22_clu_DDR', GhostDepiction_Su_22_clu_DDR ),
        ( 'Su_22_clu_POL', GhostDepiction_Su_22_clu_POL ),
        ( 'Su_22_nplm_DDR', GhostDepiction_Su_22_nplm_DDR ),
        ( 'Su_22_nplm_POL', GhostDepiction_Su_22_nplm_POL ),
        ( 'Su_24MP_EW_SOV', GhostDepiction_Su_24MP_EW_SOV ),
        ( 'Su_24MP_SEAD2_SOV', GhostDepiction_Su_24MP_SEAD2_SOV ),
        ( 'Su_24MP_SOV', GhostDepiction_Su_24MP_SOV ),
        ( 'Su_24M_AT1_SOV', GhostDepiction_Su_24M_AT1_SOV ),
        ( 'Su_24M_AT2_SOV', GhostDepiction_Su_24M_AT2_SOV ),
        ( 'Su_24M_LGB2_SOV', GhostDepiction_Su_24M_LGB2_SOV ),
        ( 'Su_24M_LGB_SOV', GhostDepiction_Su_24M_LGB_SOV ),
        ( 'Su_24M_SOV', GhostDepiction_Su_24M_SOV ),
        ( 'Su_24M_clu2_SOV', GhostDepiction_Su_24M_clu2_SOV ),
        ( 'Su_24M_clu_SOV', GhostDepiction_Su_24M_clu_SOV ),
        ( 'Su_24M_nplm_SOV', GhostDepiction_Su_24M_nplm_SOV ),
        ( 'Su_24M_thermo_SOV', GhostDepiction_Su_24M_thermo_SOV ),
        ( 'Su_25T_SOV', GhostDepiction_Su_25T_SOV ),
        ( 'Su_25_SOV', GhostDepiction_Su_25_SOV ),
        ( 'Su_25_clu_SOV', GhostDepiction_Su_25_clu_SOV ),
        ( 'Su_25_he_SOV', GhostDepiction_Su_25_he_SOV ),
        ( 'Su_25_nplm_SOV', GhostDepiction_Su_25_nplm_SOV ),
        ( 'Su_25_rkt2_SOV', GhostDepiction_Su_25_rkt2_SOV ),
        ( 'Su_25_rkt_SOV', GhostDepiction_Su_25_rkt_SOV ),
        ( 'Su_27K_SOV', GhostDepiction_Su_27K_SOV ),
        ( 'Su_27S_SOV', GhostDepiction_Su_27S_SOV ),
        ( 'Supacat_ATMP_Javelin_LML_UK', GhostDepiction_Supacat_ATMP_Javelin_LML_UK ),
        ( 'Supacat_ATMP_MILAN_UK', GhostDepiction_Supacat_ATMP_MILAN_UK ),
        ( 'Supacat_ATMP_UK', GhostDepiction_Supacat_ATMP_UK ),
        ( 'Supacat_ATMP_supply_UK', GhostDepiction_Supacat_ATMP_supply_UK ),
        ( 'Super_Etendard_AT_FR', GhostDepiction_Super_Etendard_AT_FR ),
        ( 'Super_Etendard_CLU_FR', GhostDepiction_Super_Etendard_CLU_FR ),
        ( 'Super_Etendard_FR', GhostDepiction_Super_Etendard_FR ),
        ( 'Super_Etendard_HE_FR', GhostDepiction_Super_Etendard_HE_FR ),
        ( 'Super_Puma_FR', GhostDepiction_Super_Puma_FR ),
        ( 'T34_85M_CMD_POL', GhostDepiction_T34_85M_CMD_POL ),
        ( 'T34_85M_DDR', GhostDepiction_T34_85M_DDR ),
        ( 'T34_85M_POL', GhostDepiction_T34_85M_POL ),
        ( 'T54B_CMD_DDR', GhostDepiction_T54B_CMD_DDR ),
        ( 'T54B_CMD_POL', GhostDepiction_T54B_CMD_POL ),
        ( 'T54B_DDR', GhostDepiction_T54B_DDR ),
        ( 'T54B_POL', GhostDepiction_T54B_POL ),
        ( 'T54B_SOV', GhostDepiction_T54B_SOV ),
        ( 'T55AM2B_DDR', GhostDepiction_T55AM2B_DDR ),
        ( 'T55AM2_CMD_DDR', GhostDepiction_T55AM2_CMD_DDR ),
        ( 'T55AM2_DDR', GhostDepiction_T55AM2_DDR ),
        ( 'T55AMS_Merida_POL', GhostDepiction_T55AMS_Merida_POL ),
        ( 'T55AM_1_CMD_SOV', GhostDepiction_T55AM_1_CMD_SOV ),
        ( 'T55AM_1_SOV', GhostDepiction_T55AM_1_SOV ),
        ( 'T55AM_Merida_CMD_POL', GhostDepiction_T55AM_Merida_CMD_POL ),
        ( 'T55AM_Merida_POL', GhostDepiction_T55AM_Merida_POL ),
        ( 'T55AS_POL', GhostDepiction_T55AS_POL ),
        ( 'T55A_CMD_DDR', GhostDepiction_T55A_CMD_DDR ),
        ( 'T55A_CMD_POL', GhostDepiction_T55A_CMD_POL ),
        ( 'T55A_CMD_SOV', GhostDepiction_T55A_CMD_SOV ),
        ( 'T55A_DDR', GhostDepiction_T55A_DDR ),
        ( 'T55A_POL', GhostDepiction_T55A_POL ),
        ( 'T55A_SOV', GhostDepiction_T55A_SOV ),
        ( 'T55A_obr81_SOV', GhostDepiction_T55A_obr81_SOV ),
        ( 'T62M1_SOV', GhostDepiction_T62M1_SOV ),
        ( 'T62MD1_SOV', GhostDepiction_T62MD1_SOV ),
        ( 'T62MD_CMD_SOV', GhostDepiction_T62MD_CMD_SOV ),
        ( 'T62MD_SOV', GhostDepiction_T62MD_SOV ),
        ( 'T62MV_SOV', GhostDepiction_T62MV_SOV ),
        ( 'T62M_CMD_SOV', GhostDepiction_T62M_CMD_SOV ),
        ( 'T62M_SOV', GhostDepiction_T62M_SOV ),
        ( 'T64AM_SOV', GhostDepiction_T64AM_SOV ),
        ( 'T64AV_SOV', GhostDepiction_T64AV_SOV ),
        ( 'T64A_CMD_SOV', GhostDepiction_T64A_CMD_SOV ),
        ( 'T64A_SOV', GhostDepiction_T64A_SOV ),
        ( 'T64B1_SOV', GhostDepiction_T64B1_SOV ),
        ( 'T64B1_reco_SOV', GhostDepiction_T64B1_reco_SOV ),
        ( 'T64BV1_SOV', GhostDepiction_T64BV1_SOV ),
        ( 'T64BV_CMD_SOV', GhostDepiction_T64BV_CMD_SOV ),
        ( 'T64BV_SOV', GhostDepiction_T64BV_SOV ),
        ( 'T64B_CMD_SOV', GhostDepiction_T64B_CMD_SOV ),
        ( 'T64B_SOV', GhostDepiction_T64B_SOV ),
        ( 'T72M1_CMD_DDR', GhostDepiction_T72M1_CMD_DDR ),
        ( 'T72M1_CMD_POL', GhostDepiction_T72M1_CMD_POL ),
        ( 'T72M1_DDR', GhostDepiction_T72M1_DDR ),
        ( 'T72M1_POL', GhostDepiction_T72M1_POL ),
        ( 'T72M1_Wilk_POL', GhostDepiction_T72M1_Wilk_POL ),
        ( 'T72MUV2_DDR', GhostDepiction_T72MUV2_DDR ),
        ( 'T72M_CMD_DDR', GhostDepiction_T72M_CMD_DDR ),
        ( 'T72M_CMD_POL', GhostDepiction_T72M_CMD_POL ),
        ( 'T72M_DDR', GhostDepiction_T72M_DDR ),
        ( 'T72M_POL', GhostDepiction_T72M_POL ),
        ( 'T72S_DDR', GhostDepiction_T72S_DDR ),
        ( 'T72_CMD_DDR', GhostDepiction_T72_CMD_DDR ),
        ( 'T72_DDR', GhostDepiction_T72_DDR ),
        ( 'T80BV_Beast_SOV', GhostDepiction_T80BV_Beast_SOV ),
        ( 'T80BV_CMD_SOV', GhostDepiction_T80BV_CMD_SOV ),
        ( 'T80BV_SOV', GhostDepiction_T80BV_SOV ),
        ( 'T80B_CMD_SOV', GhostDepiction_T80B_CMD_SOV ),
        ( 'T80B_SOV', GhostDepiction_T80B_SOV ),
        ( 'T80UD_SOV', GhostDepiction_T80UD_SOV ),
        ( 'T80U_CMD_SOV', GhostDepiction_T80U_CMD_SOV ),
        ( 'T80U_SOV', GhostDepiction_T80U_SOV ),
        ( 'T813_DDR', GhostDepiction_T813_DDR ),
        ( 'T813_trans_DDR', GhostDepiction_T813_trans_DDR ),
        ( 'T815_supply_DDR', GhostDepiction_T815_supply_DDR ),
        ( 'TOS1_Buratino_SOV', GhostDepiction_TOS1_Buratino_SOV ),
        ( 'TO_55_DDR', GhostDepiction_TO_55_DDR ),
        ( 'TO_55_SOV', GhostDepiction_TO_55_SOV ),
        ( 'TPZ_Fuchs_1_RFA', GhostDepiction_TPZ_Fuchs_1_RFA ),
        ( 'TPZ_Fuchs_CMD_RFA', GhostDepiction_TPZ_Fuchs_CMD_RFA ),
        ( 'TPZ_Fuchs_MILAN_RFA', GhostDepiction_TPZ_Fuchs_MILAN_RFA ),
        ( 'TPZ_Fuchs_RASIT_RFA', GhostDepiction_TPZ_Fuchs_RASIT_RFA ),
        ( 'TRM_10000_FR', GhostDepiction_TRM_10000_FR ),
        ( 'TRM_10000_supply_FR', GhostDepiction_TRM_10000_supply_FR ),
        ( 'TRM_2000_20mm_FR', GhostDepiction_TRM_2000_20mm_FR ),
        ( 'TRM_2000_FR', GhostDepiction_TRM_2000_FR ),
        ( 'TRM_2000_supply_FR', GhostDepiction_TRM_2000_supply_FR ),
        ( 'TUTO_M1025_Humvee_US', GhostDepiction_TUTO_M1025_Humvee_US ),
        ( 'Tor_SOV', GhostDepiction_Tor_SOV ),
        ( 'Tornado_ADV_HE_UK', GhostDepiction_Tornado_ADV_HE_UK ),
        ( 'Tornado_ADV_SEAD_UK', GhostDepiction_Tornado_ADV_SEAD_UK ),
        ( 'Tornado_ADV_UK', GhostDepiction_Tornado_ADV_UK ),
        ( 'Tornado_ADV_clu_UK', GhostDepiction_Tornado_ADV_clu_UK ),
        ( 'Tornado_IDS_AT1_RFA', GhostDepiction_Tornado_IDS_AT1_RFA ),
        ( 'Tornado_IDS_CLUS_RFA', GhostDepiction_Tornado_IDS_CLUS_RFA ),
        ( 'Tornado_IDS_HE1_RFA', GhostDepiction_Tornado_IDS_HE1_RFA ),
        ( 'Tornado_IDS_MW1_RFA', GhostDepiction_Tornado_IDS_MW1_RFA ),
        ( 'Tracked_Rapier_UK', GhostDepiction_Tracked_Rapier_UK ),
        ( 'Tunguska_2K22_SOV', GhostDepiction_Tunguska_2K22_SOV ),
        ( 'UAZ_469_AGL_Grenzer_DDR', GhostDepiction_UAZ_469_AGL_Grenzer_DDR ),
        ( 'UAZ_469_AGL_SOV', GhostDepiction_UAZ_469_AGL_SOV ),
        ( 'UAZ_469_AGL_VDV_SOV', GhostDepiction_UAZ_469_AGL_VDV_SOV ),
        ( 'UAZ_469_CMD_DDR', GhostDepiction_UAZ_469_CMD_DDR ),
        ( 'UAZ_469_CMD_POL', GhostDepiction_UAZ_469_CMD_POL ),
        ( 'UAZ_469_CMD_Para_POL', GhostDepiction_UAZ_469_CMD_Para_POL ),
        ( 'UAZ_469_CMD_SOV', GhostDepiction_UAZ_469_CMD_SOV ),
        ( 'UAZ_469_CMD_VDV_SOV', GhostDepiction_UAZ_469_CMD_VDV_SOV ),
        ( 'UAZ_469_Fagot_DDR', GhostDepiction_UAZ_469_Fagot_DDR ),
        ( 'UAZ_469_Fagot_POL', GhostDepiction_UAZ_469_Fagot_POL ),
        ( 'UAZ_469_Fagot_Para_POL', GhostDepiction_UAZ_469_Fagot_Para_POL ),
        ( 'UAZ_469_Konkurs_VDV_SOV', GhostDepiction_UAZ_469_Konkurs_VDV_SOV ),
        ( 'UAZ_469_MP_SOV', GhostDepiction_UAZ_469_MP_SOV ),
        ( 'UAZ_469_Reco_DDR', GhostDepiction_UAZ_469_Reco_DDR ),
        ( 'UAZ_469_Reco_POL', GhostDepiction_UAZ_469_Reco_POL ),
        ( 'UAZ_469_Reco_SOV', GhostDepiction_UAZ_469_Reco_SOV ),
        ( 'UAZ_469_SOV', GhostDepiction_UAZ_469_SOV ),
        ( 'UAZ_469_SPG9_DDR', GhostDepiction_UAZ_469_SPG9_DDR ),
        ( 'UAZ_469_SPG9_FJ_DDR', GhostDepiction_UAZ_469_SPG9_FJ_DDR ),
        ( 'UAZ_469_SPG9_Para_POL', GhostDepiction_UAZ_469_SPG9_Para_POL ),
        ( 'UAZ_469_SPG9_SOV', GhostDepiction_UAZ_469_SPG9_SOV ),
        ( 'UAZ_469_SPG9_VDV_SOV', GhostDepiction_UAZ_469_SPG9_VDV_SOV ),
        ( 'UAZ_469_supply_Para_POL', GhostDepiction_UAZ_469_supply_Para_POL ),
        ( 'UAZ_469_supply_SOV', GhostDepiction_UAZ_469_supply_SOV ),
        ( 'UAZ_469_supply_VDV_SOV', GhostDepiction_UAZ_469_supply_VDV_SOV ),
        ( 'UAZ_469_trans_DDR', GhostDepiction_UAZ_469_trans_DDR ),
        ( 'UAZ_469_trans_POL', GhostDepiction_UAZ_469_trans_POL ),
        ( 'UH1A_US', GhostDepiction_UH1A_US ),
        ( 'UH1D_NL', GhostDepiction_UH1D_NL ),
        ( 'UH1D_RFA', GhostDepiction_UH1D_RFA ),
        ( 'UH1D_Supply_RFA', GhostDepiction_UH1D_Supply_RFA ),
        ( 'UH1H_Huey_US', GhostDepiction_UH1H_Huey_US ),
        ( 'UH1H_supply_US', GhostDepiction_UH1H_supply_US ),
        ( 'UH1M_gunship_US', GhostDepiction_UH1M_gunship_US ),
        ( 'UH60A_Black_Hawk_US', GhostDepiction_UH60A_Black_Hawk_US ),
        ( 'UH60A_CO_US', GhostDepiction_UH60A_CO_US ),
        ( 'UH60A_Supply_US', GhostDepiction_UH60A_Supply_US ),
        ( 'Unimog_S_404_RFA', GhostDepiction_Unimog_S_404_RFA ),
        ( 'Unimog_U1350L_BEL', GhostDepiction_Unimog_U1350L_BEL ),
        ( 'Unimog_U1350L_Para_BEL', GhostDepiction_Unimog_U1350L_Para_BEL ),
        ( 'Unimog_U1350L_supply_BEL', GhostDepiction_Unimog_U1350L_supply_BEL ),
        ( 'Unimog_supply_BEL', GhostDepiction_Unimog_supply_BEL ),
        ( 'Unimog_trans_BEL', GhostDepiction_Unimog_trans_BEL ),
        ( 'Unimog_trans_RFA', GhostDepiction_Unimog_trans_RFA ),
        ( 'Ural_4320_DDR', GhostDepiction_Ural_4320_DDR ),
        ( 'Ural_4320_Metla_SOV', GhostDepiction_Ural_4320_Metla_SOV ),
        ( 'Ural_4320_SOV', GhostDepiction_Ural_4320_SOV ),
        ( 'Ural_4320_ZPU_SOV', GhostDepiction_Ural_4320_ZPU_SOV ),
        ( 'Ural_4320_ZU_SOV', GhostDepiction_Ural_4320_ZU_SOV ),
        ( 'Ural_4320_trans_SOV', GhostDepiction_Ural_4320_trans_SOV ),
        ( 'VAB_CMD_FR', GhostDepiction_VAB_CMD_FR ),
        ( 'VAB_FR', GhostDepiction_VAB_FR ),
        ( 'VAB_HOT_FR', GhostDepiction_VAB_HOT_FR ),
        ( 'VAB_MILAN_FR', GhostDepiction_VAB_MILAN_FR ),
        ( 'VAB_Mortar_81_FR', GhostDepiction_VAB_Mortar_81_FR ),
        ( 'VAB_RASIT_FR', GhostDepiction_VAB_RASIT_FR ),
        ( 'VAB_Reserve_FR', GhostDepiction_VAB_Reserve_FR ),
        ( 'VAB_T20_FR', GhostDepiction_VAB_T20_FR ),
        ( 'VBL_MILAN_FR', GhostDepiction_VBL_MILAN_FR ),
        ( 'VBL_PC_FR', GhostDepiction_VBL_PC_FR ),
        ( 'VBL_Reco_FR', GhostDepiction_VBL_Reco_FR ),
        ( 'VIB_FR', GhostDepiction_VIB_FR ),
        ( 'VLRA_20mm_FR', GhostDepiction_VLRA_20mm_FR ),
        ( 'VLRA_HMG_FR', GhostDepiction_VLRA_HMG_FR ),
        ( 'VLRA_MILAN_FR', GhostDepiction_VLRA_MILAN_FR ),
        ( 'VLRA_Mistral_FR', GhostDepiction_VLRA_Mistral_FR ),
        ( 'VLRA_Mortier81_FR', GhostDepiction_VLRA_Mortier81_FR ),
        ( 'VLRA_supply_FR', GhostDepiction_VLRA_supply_FR ),
        ( 'VLRA_trans_FR', GhostDepiction_VLRA_trans_FR ),
        ( 'VLTT_P4_FR', GhostDepiction_VLTT_P4_FR ),
        ( 'VLTT_P4_MILAN_FR', GhostDepiction_VLTT_P4_MILAN_FR ),
        ( 'VLTT_P4_MILAN_para_FR', GhostDepiction_VLTT_P4_MILAN_para_FR ),
        ( 'VLTT_P4_PC_FR', GhostDepiction_VLTT_P4_PC_FR ),
        ( 'Volvo_N10_supply_BEL', GhostDepiction_Volvo_N10_supply_BEL ),
        ( 'Volvo_N10_trans_BEL', GhostDepiction_Volvo_N10_trans_BEL ),
        ( 'W3RR_Procjon_POL', GhostDepiction_W3RR_Procjon_POL ),
        ( 'W3W_Sokol_AA_POL', GhostDepiction_W3W_Sokol_AA_POL ),
        ( 'W3W_Sokol_RKT_POL', GhostDepiction_W3W_Sokol_RKT_POL ),
        ( 'W3_Sokol_POL', GhostDepiction_W3_Sokol_POL ),
        ( 'W50_LA_A_25mm_DDR', GhostDepiction_W50_LA_A_25mm_DDR ),
        ( 'W50_LA_A_DDR', GhostDepiction_W50_LA_A_DDR ),
        ( 'Westland_Scout_SS11_UK', GhostDepiction_Westland_Scout_SS11_UK ),
        ( 'Wiesel_20mm_RFA', GhostDepiction_Wiesel_20mm_RFA ),
        ( 'Wiesel_TOW_RFA', GhostDepiction_Wiesel_TOW_RFA ),
        ( 'ZSU_23_Shilka_Afghan_SOV', GhostDepiction_ZSU_23_Shilka_Afghan_SOV ),
        ( 'ZSU_23_Shilka_DDR', GhostDepiction_ZSU_23_Shilka_DDR ),
        ( 'ZSU_23_Shilka_POL', GhostDepiction_ZSU_23_Shilka_POL ),
        ( 'ZSU_23_Shilka_SOV', GhostDepiction_ZSU_23_Shilka_SOV ),
        ( 'ZSU_23_Shilka_reco_SOV', GhostDepiction_ZSU_23_Shilka_reco_SOV ),
        ( 'ZSU_57_2_DDR', GhostDepiction_ZSU_57_2_DDR ),
    ]
)

