// Ne pas éditer, ce fichier est généré par DepictionVehiclesFileWriter_Specific_Showroom


export Gfx_2K11_KRUG_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2K11_KRUG_DDR
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_2K11_KRUG_DDR ]
)
export Gfx_2K11_KRUG_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2K11_KRUG_POL
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_2K11_KRUG_POL ]
)
export Gfx_2K11_KRUG_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2K11_KRUG_SOV
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_2K11_KRUG_SOV ]
)
export Gfx_2K12_KUB_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2K12_KUB_DDR
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_2K12_KUB_DDR ]
)
export Gfx_2K12_KUB_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2K12_KUB_POL
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_2K12_KUB_POL ]
)
export Gfx_2K12_KUB_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2K12_KUB_SOV
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_2K12_KUB_SOV ]
)
export Gfx_2S19_MstaS_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S19_MstaS_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S1M_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S1M_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S1_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S1_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S1_Gvozdika_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S1_Gvozdika_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S1_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S1_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S23_Nona_SVK_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S23_Nona_SVK_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S3M1_Akatsiya_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S3M1_Akatsiya_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S3M_Akatsiya_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S3M_Akatsiya_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S3_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S3_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S5_GiatsintS_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S5_GiatsintS_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S7M_Malka_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S7M_Malka_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S7_Pion_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S7_Pion_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_2S9_Nona_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_2S9_Nona_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_81mm_mortar_Aero_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_81mm_mortar_Aero_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_81mm_mortar_Aero_US
)
export Gfx_81mm_mortar_CLU_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_81mm_mortar_CLU_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_81mm_mortar_CLU_UK
)
export Gfx_81mm_mortar_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_81mm_mortar_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_81mm_mortar_NG_US
)
export Gfx_81mm_mortar_Para_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_81mm_mortar_Para_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_81mm_mortar_Para_UK
)
export Gfx_81mm_mortar_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_81mm_mortar_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_81mm_mortar_UK
)
export Gfx_81mm_mortar_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_81mm_mortar_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_81mm_mortar_US
)
export Gfx_A222_Bereg_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_A222_Bereg_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AEC_Militant_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AEC_Militant_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AEC_Militant_UK
)
export Gfx_AIFV_B_50_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_50_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AIFV_B_50_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_50_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AIFV_B_C25_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_C25_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AIFV_B_C25_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_C25_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AIFV_B_CMD_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_CMD_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AIFV_B_CMD_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_CMD_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AIFV_B_Cargo_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_Cargo_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AIFV_B_MILAN_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_MILAN_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AIFV_B_Radar_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_Radar_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_AIFV_B_Radar_NL_Tourelle_01
            )
        ),
    ]
)
export Gfx_AIFV_B_TOW_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AIFV_B_TOW_NL
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_AIFV_B_TOW_NL ]
)
export Gfx_AML_60_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AML_60_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AML_60_Gendarmerie_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AML_60_Gendarmerie_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AML_90_CMD_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AML_90_CMD_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AML_90_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AML_90_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AML_90_Reserve_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AML_90_Reserve_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_10_HOT_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_10_HOT_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_10_PC_CMD_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_10_PC_CMD_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_10_P_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_10_P_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_10_P_MILAN_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_10_P_MILAN_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_10_P_VOA_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_10_P_VOA_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_10_RCR_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_10_RCR_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_10_RC_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_10_RC_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_10_RC_FR_Tourelle_02
            )
        ),
    ]
)
export Gfx_AMX_13_90mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_13_90mm_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_13_DCA_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_13_DCA_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_13_VCI_12_7mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_13_VCI_12_7mm_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_13_VCI_12_7mm_FR_Tourelle_01
            )
        ),
    ]
)
export Gfx_AMX_13_VCI_20mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_13_VCI_20mm_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_13_mod56_CMD_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_13_mod56_CMD_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_13_mod56_MILAN_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_13_mod56_MILAN_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_13_mod56_Mortier_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_13_mod56_Mortier_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_13_mod56_VCI_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_13_mod56_VCI_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_30_AuF1_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_30_AuF1_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_30_B2_Brennus_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_30_B2_Brennus_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_30_B2_CMD_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_30_B2_CMD_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_30_B2_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_30_B2_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_30_B_CMD_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_30_B_CMD_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_30_B_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_30_B_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AMX_30_EBG_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AMX_30_EBG_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ASU_85_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ASU_85_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ASU_85_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ASU_85_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_AT_2A45_SprutB_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_2A45_SprutB_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_2A45_SprutB_SOV
)
export Gfx_AT_D44_85mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_D44_85mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_D44_85mm_DDR
)
export Gfx_AT_D44_85mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_D44_85mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_D44_85mm_POL
)
export Gfx_AT_D44_85mm_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_D44_85mm_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_D44_85mm_VDV_SOV
)
export Gfx_AT_D48_85mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_D48_85mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_D48_85mm_POL
)
export Gfx_AT_KSM65_100mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_KSM65_100mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_KSM65_100mm_SOV
)
export Gfx_AT_T12R_Ruta_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_T12R_Ruta_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_T12R_Ruta_SOV
)
export Gfx_AT_T12_Rapira_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_T12_Rapira_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_T12_Rapira_DDR
)
export Gfx_AT_T12_Rapira_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_T12_Rapira_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_T12_Rapira_SOV
)
export Gfx_AT_ZiS2_57mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_ZiS2_57mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_ZiS2_57mm_DDR
)
export Gfx_AT_vz52_85mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_AT_vz52_85mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_AT_vz52_85mm_DDR
)
export Gfx_ATteam_Fagot_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Fagot_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Fagot_DDR
)
export Gfx_ATteam_Fagot_FJ_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Fagot_FJ_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Fagot_FJ_DDR
)
export Gfx_ATteam_Fagot_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Fagot_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Fagot_SOV
)
export Gfx_ATteam_ITOW_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_ITOW_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_ITOW_NG_US
)
export Gfx_ATteam_ITOW_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_ITOW_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_ITOW_NL
)
export Gfx_ATteam_ITOW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_ITOW_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_ITOW_US
)
export Gfx_ATteam_KonkursM_TTsko_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_KonkursM_TTsko_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_KonkursM_TTsko_SOV
)
export Gfx_ATteam_Konkurs_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Konkurs_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Konkurs_DDR
)
export Gfx_ATteam_Konkurs_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Konkurs_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Konkurs_SOV
)
export Gfx_ATteam_Konkurs_TTsko_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Konkurs_TTsko_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Konkurs_TTsko_SOV
)
export Gfx_ATteam_Milan_1_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_1_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_1_BEL
)
export Gfx_ATteam_Milan_1_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_1_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_1_FR
)
export Gfx_ATteam_Milan_1_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_1_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_1_RFA
)
export Gfx_ATteam_Milan_1_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_1_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_1_UK
)
export Gfx_ATteam_Milan_1_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_1_para_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_1_para_BEL
)
export Gfx_ATteam_Milan_1_para_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_1_para_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_1_para_FR
)
export Gfx_ATteam_Milan_2_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_BEL
)
export Gfx_ATteam_Milan_2_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_FR
)
export Gfx_ATteam_Milan_2_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_RFA
)
export Gfx_ATteam_Milan_2_RIMa_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_RIMa_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_RIMa_FR
)
export Gfx_ATteam_Milan_2_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_UK
)
export Gfx_ATteam_Milan_2_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_para_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_para_BEL
)
export Gfx_ATteam_Milan_2_para_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_para_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_para_FR
)
export Gfx_ATteam_Milan_2_para_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_para_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_para_RFA
)
export Gfx_ATteam_Milan_2_para_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_Milan_2_para_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_Milan_2_para_UK
)
export Gfx_ATteam_RCL_B11_Reserve_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_B11_Reserve_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_B11_Reserve_SOV
)
export Gfx_ATteam_RCL_M40A1_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_M40A1_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_M40A1_FR
)
export Gfx_ATteam_RCL_M40A1_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_M40A1_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_M40A1_NG_US
)
export Gfx_ATteam_RCL_M40A1_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_M40A1_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_M40A1_NL
)
export Gfx_ATteam_RCL_M40A1_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_M40A1_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_M40A1_RFA
)
export Gfx_ATteam_RCL_SPG9_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_SPG9_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_SPG9_DDR
)
export Gfx_ATteam_RCL_SPG9_DShV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_SPG9_DShV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_SPG9_DShV_SOV
)
export Gfx_ATteam_RCL_SPG9_FJ_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_SPG9_FJ_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_SPG9_FJ_DDR
)
export Gfx_ATteam_RCL_SPG9_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_SPG9_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_SPG9_POL
)
export Gfx_ATteam_RCL_SPG9_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_SPG9_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_SPG9_Para_POL
)
export Gfx_ATteam_RCL_SPG9_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_SPG9_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_SPG9_SOV
)
export Gfx_ATteam_RCL_SPG9_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_RCL_SPG9_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_RCL_SPG9_VDV_SOV
)
export Gfx_ATteam_TOW2A_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_TOW2A_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_TOW2A_US
)
export Gfx_ATteam_TOW2_Aero_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_TOW2_Aero_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_TOW2_Aero_US
)
export Gfx_ATteam_TOW2_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_TOW2_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_TOW2_NL
)
export Gfx_ATteam_TOW2_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_TOW2_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_TOW2_US
)
export Gfx_ATteam_TOW2_para_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_TOW2_para_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_TOW2_para_US
)
export Gfx_ATteam_TOW_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_TOW_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_TOW_NL
)
export Gfx_ATteam_TOW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ATteam_TOW_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ATteam_TOW_US
)
export Gfx_Alvis_Stalwart_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Alvis_Stalwart_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Alvis_Stalwart_UK
)
export Gfx_Atteam_Dragon_Marines_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Atteam_Dragon_Marines_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Atteam_Dragon_Marines_NL
)
export Gfx_Atteam_Fagot_DShV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Atteam_Fagot_DShV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Atteam_Fagot_DShV_SOV
)
export Gfx_Atteam_Fagot_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Atteam_Fagot_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Atteam_Fagot_POL
)
export Gfx_Atteam_Fagot_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Atteam_Fagot_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Atteam_Fagot_Para_POL
)
export Gfx_Atteam_Fagot_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Atteam_Fagot_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Atteam_Fagot_VDV_SOV
)
export Gfx_Atteam_Konkurs_DShV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Atteam_Konkurs_DShV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Atteam_Konkurs_DShV_SOV
)
export Gfx_Atteam_Konkurs_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Atteam_Konkurs_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Atteam_Konkurs_POL
)
export Gfx_Atteam_Konkurs_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Atteam_Konkurs_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Atteam_Konkurs_VDV_SOV
)
export Gfx_BAV_485_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BAV_485_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BAV_485_POL
)
export Gfx_BAV_485_Supply_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BAV_485_Supply_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BAV_485_Supply_POL
)
export Gfx_BM14M_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM14M_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BM14M_POL
)
export Gfx_BM21V_GradV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM21V_GradV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BM21V_GradV_SOV
)
export Gfx_BM21_Grad_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM21_Grad_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BM21_Grad_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM21_Grad_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BM21_Grad_POL
)
export Gfx_BM21_Grad_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM21_Grad_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BM21_Grad_SOV
)
export Gfx_BM24M_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM24M_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BM24M_DDR

    SubDepictionGenerators = [ ShowroomRocketSubDepictionTemplate(
            UnitMeshDescriptor = $/GFX/DepictionResources/Modele_BM24M_DDR
            RocketMeshDescriptor = $/GFX/DepictionResources/Modele_Missile_MD_24F
            RocketCount = 12
        ) ]
)
export Gfx_BM24M_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM24M_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BM24M_POL

    SubDepictionGenerators = [ ShowroomRocketSubDepictionTemplate(
            UnitMeshDescriptor = $/GFX/DepictionResources/Modele_BM24M_POL
            RocketMeshDescriptor = $/GFX/DepictionResources/Modele_Missile_MD_24F
            RocketCount = 12
        ) ]
)
export Gfx_BM24M_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM24M_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BM24M_SOV

    SubDepictionGenerators = [ ShowroomRocketSubDepictionTemplate(
            UnitMeshDescriptor = $/GFX/DepictionResources/Modele_BM24M_SOV
            RocketMeshDescriptor = $/GFX/DepictionResources/Modele_Missile_MD_24F
            RocketCount = 12
        ) ]
)
export Gfx_BM27_Uragan_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM27_Uragan_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BM27_Uragan_SOV
)
export Gfx_BM30_Smerch_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BM30_Smerch_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_1K_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_1K_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_1P_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_1P_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_1_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_1_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_1_Reostat_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_1_Reostat_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_1_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_1_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_2_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_2_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_2_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_2_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_3_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_3_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMD_3_reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMD_3_reco_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1PG_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1PG_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1P_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1P_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1P_Konkurs_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1P_Konkurs_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1P_Konkurs_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1P_Konkurs_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1P_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1P_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1P_reco_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1P_reco_DDR
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BMP_1P_reco_DDR ]
)
export Gfx_BMP_1P_reco_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1P_reco_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1P_reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1P_reco_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1_CMD_DDR
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BMP_1_CMD_DDR ]
)
export Gfx_BMP_1_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1_CMD_POL
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BMP_1_CMD_POL ]
)
export Gfx_BMP_1_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1_CMD_SOV
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BMP_1_CMD_SOV ]
)
export Gfx_BMP_1_SP1_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1_SP1_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_1_SP2_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1_SP2_DDR
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BMP_1_SP2_DDR ]
)
export Gfx_BMP_1_SP2_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1_SP2_POL
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BMP_1_SP2_POL ]
)
export Gfx_BMP_1_SP2_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1_SP2_SOV
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BMP_1_SP2_SOV ]
)
export Gfx_BMP_1_SP2_reco_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_1_SP2_reco_POL
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BMP_1_SP2_reco_POL ]
)
export Gfx_BMP_2AG_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_2AG_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_2D_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_2D_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_2D_reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_2D_reco_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_2_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_2_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_2_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_2_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_2_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_2_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_2_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_2_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_2_reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_2_reco_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BMP_3_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BMP_3_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_1_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_1_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_1_DDR_Tourelle_01
            )
        ),
    ]
)
export Gfx_BRDM_1_DShK_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_1_DShK_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_1_DShK_POL_Tourelle_01
            )
        ),
    ]
)
export Gfx_BRDM_1_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_1_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_1_POL_Tourelle_01
            )
        ),
    ]
)
export Gfx_BRDM_1_PSNR1_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_1_PSNR1_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_1_PSNR1_POL_Tourelle_01
            )
        ),
    ]
)
export Gfx_BRDM_2_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_CMD_R5_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_CMD_R5_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_Konkurs_M_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_Konkurs_M_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_Konkurs_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_Konkurs_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_Konkurs_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_Konkurs_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_Malyu_P_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_Malyu_P_POL
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BRDM_2_Malyu_P_POL ]
)
export Gfx_BRDM_2_Malyu_P_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_Malyu_P_SOV
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BRDM_2_Malyu_P_SOV ]
)
export Gfx_BRDM_2_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_2_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_2_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_Konkurs_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_Konkurs_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_Malyu_P_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_Malyu_P_DDR
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_BRDM_Malyu_P_DDR ]
)
export Gfx_BRDM_Strela_1_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_Strela_1_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_Strela_1_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_Strela_1_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRDM_Strela_1_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRDM_Strela_1_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRM_1_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRM_1_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRM_1_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRM_1_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BRM_1_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BRM_1_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_152A_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_152A_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BTR_152A_DDR
)
export Gfx_BTR_152A_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_152A_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BTR_152A_SOV
)
export Gfx_BTR_152K_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_152K_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_152K_SOV_Tourelle_01
            )
        ),
    ]
)
export Gfx_BTR_152S_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_152S_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_40A_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_40A_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BTR_40A_SOV
)
export Gfx_BTR_40B_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_40B_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_40_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_40_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BTR_40_SOV
)
export Gfx_BTR_50_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_50_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_50_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_50_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_50_MRF_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_50_MRF_DDR
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ ShowroomRocketSubDepictionTemplate(
            UnitMeshDescriptor = $/GFX/DepictionResources/Modele_BTR_50_MRF_DDR
            RocketMeshDescriptor = $/GFX/DepictionResources/Modele_Missile_PW_LWD
            RocketCount = 2
        ) ]
)
export Gfx_BTR_60_CHAIKA_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_60_CHAIKA_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_60_CHAIKA_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_60_CHAIKA_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_60_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_60_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_60_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_60_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_60_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_60_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_60_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_60_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_60_reco_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_60_reco_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_60_reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_60_reco_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_70D_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_70D_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_70_AGS_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_70_AGS_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_70_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_70_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_70_MP_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_70_MP_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_70_Rys_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_70_Rys_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_70_S5_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_70_S5_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_70_S8_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_70_S8_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_70_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_70_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_80_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_80_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_80_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_80_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_D_Robot_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_D_Robot_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_D_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_D_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_D_reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_D_reco_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_BTR_ZD_Skrezhet_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_BTR_ZD_Skrezhet_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_BTR_ZD_Skrezhet_SOV
)
export Gfx_Bedford_MJ_4t_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Bedford_MJ_4t_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Bedford_MJ_4t_UK
)
export Gfx_Bedford_MJ_4t_trans_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Bedford_MJ_4t_trans_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Bedford_MJ_4t_trans_UK
)
export Gfx_Bofors_40mm_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Bofors_40mm_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Bofors_40mm_RFA
)
export Gfx_Bofors_40mm_capture_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Bofors_40mm_capture_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Bofors_40mm_capture_DDR
)
export Gfx_Buk_9K37M_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Buk_9K37M_SOV
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_Buk_9K37M_SOV ]
)
export Gfx_CGage_Peacekeeper_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_CGage_Peacekeeper_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_CGage_Peacekeeper_US
)
export Gfx_CGage_V150_Commando_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_CGage_V150_Commando_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_CGage_V150_Commando_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_CGage_V150_Commando_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_CUCV_AGL_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_CUCV_AGL_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_CUCV_AGL_US
)
export Gfx_CUCV_HMG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_CUCV_HMG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_CUCV_HMG_US
)
export Gfx_CUCV_Hellfire_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_CUCV_Hellfire_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_CUCV_Hellfire_US

    SubDepictionGenerators = [ SubGenerators_Showroom_CUCV_Hellfire_US ]
)
export Gfx_CUCV_MP_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_CUCV_MP_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_CUCV_MP_US
)
export Gfx_CUCV_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_CUCV_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_CUCV_US
)
export Gfx_CUCV_trans_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_CUCV_trans_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_CUCV_trans_US
)
export Gfx_Centurion_AVRE_105_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Centurion_AVRE_105_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Challenger_1_Mk1_CMD_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Challenger_1_Mk1_CMD_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Challenger_1_Mk1_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Challenger_1_Mk1_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Challenger_1_Mk3_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Challenger_1_Mk3_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Crotale_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Crotale_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_DAF_YA_4400_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DAF_YA_4400_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DAF_YA_4400_NL
)
export Gfx_DAF_YA_4400_supply_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DAF_YA_4400_supply_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DAF_YA_4400_supply_NL
)
export Gfx_DAF_YHZ_2300_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DAF_YHZ_2300_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DAF_YHZ_2300_NL
)
export Gfx_DAF_YHZ_2300_trans_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DAF_YHZ_2300_trans_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DAF_YHZ_2300_trans_NL
)
export Gfx_DANA_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DANA_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_DCA_53T2_20mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_53T2_20mm_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_53T2_20mm_FR
)
export Gfx_DCA_53T2_20mm_Para_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_53T2_20mm_Para_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_53T2_20mm_Para_FR
)
export Gfx_DCA_76T2_20mm_CPA_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_76T2_20mm_CPA_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_76T2_20mm_CPA_FR
)
export Gfx_DCA_AZP_S60_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_AZP_S60_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_AZP_S60_DDR
)
export Gfx_DCA_AZP_S60_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_AZP_S60_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_AZP_S60_POL
)
export Gfx_DCA_AZP_S60_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_AZP_S60_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_AZP_S60_SOV
)
export Gfx_DCA_Bofors_L60_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_Bofors_L60_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_Bofors_L60_FR
)
export Gfx_DCA_Bofors_upgrade_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_Bofors_upgrade_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_Bofors_upgrade_NL
)
export Gfx_DCA_FASTA_4_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_FASTA_4_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_FASTA_4_DDR
)
export Gfx_DCA_FK20_2_20mm_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_FK20_2_20mm_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_FK20_2_20mm_RFA
)
export Gfx_DCA_FK20_2_20mm_Zwillinge_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_FK20_2_20mm_Zwillinge_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_FK20_2_20mm_Zwillinge_RFA
)
export Gfx_DCA_I_Hawk_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_I_Hawk_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_I_Hawk_BEL

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_I_Hawk_BEL ]
)
export Gfx_DCA_I_Hawk_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_I_Hawk_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_I_Hawk_NL

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_I_Hawk_NL ]
)
export Gfx_DCA_I_Hawk_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_I_Hawk_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_I_Hawk_RFA

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_I_Hawk_RFA ]
)
export Gfx_DCA_I_Hawk_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_I_Hawk_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_I_Hawk_US

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_I_Hawk_US ]
)
export Gfx_DCA_I_Hawk_capture_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_I_Hawk_capture_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_I_Hawk_capture_DDR

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_I_Hawk_capture_DDR ]
)
export Gfx_DCA_Javelin_LML_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_Javelin_LML_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_Javelin_LML_UK
)
export Gfx_DCA_KS19_100mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_KS19_100mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_KS19_100mm_DDR
)
export Gfx_DCA_KS30_130mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_KS30_130mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_KS30_130mm_SOV
)
export Gfx_DCA_M167A2_Vulcan_20mm_Aero_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_M167A2_Vulcan_20mm_Aero_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_M167A2_Vulcan_20mm_Aero_US
)
export Gfx_DCA_M167A2_Vulcan_20mm_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_M167A2_Vulcan_20mm_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_M167A2_Vulcan_20mm_US
)
export Gfx_DCA_M167_Vulcan_20mm_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_M167_Vulcan_20mm_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_M167_Vulcan_20mm_BEL
)
export Gfx_DCA_M167_Vulcan_20mm_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_M167_Vulcan_20mm_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_M167_Vulcan_20mm_US
)
export Gfx_DCA_M167_Vulcan_20mm_nonPara_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_M167_Vulcan_20mm_nonPara_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_M167_Vulcan_20mm_nonPara_US
)
export Gfx_DCA_M167_Vulcan_para_20mm_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_M167_Vulcan_para_20mm_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_M167_Vulcan_para_20mm_BEL
)
export Gfx_DCA_M55_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_M55_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_M55_NL
)
export Gfx_DCA_Oerlikon_GDF_002_35mm_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_Oerlikon_GDF_002_35mm_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_Oerlikon_GDF_002_35mm_UK
)
export Gfx_DCA_Rapier_Darkfire_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_Rapier_Darkfire_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_Rapier_Darkfire_UK

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_Rapier_Darkfire_UK ]
)
export Gfx_DCA_Rapier_FSA_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_Rapier_FSA_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_Rapier_FSA_UK

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_Rapier_FSA_UK ]
)
export Gfx_DCA_Rapier_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_Rapier_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_Rapier_UK

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_Rapier_UK ]
)
export Gfx_DCA_XM85_Chaparral_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_XM85_Chaparral_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_XM85_Chaparral_US

    SubDepictionGenerators = [ SubGenerators_Showroom_DCA_XM85_Chaparral_US ]
)
export Gfx_DCA_XMIM_115A_Roland_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_XMIM_115A_Roland_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_XMIM_115A_Roland_US
)
export Gfx_DCA_ZPU4_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZPU4_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZPU4_DDR
)
export Gfx_DCA_ZPU4_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZPU4_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZPU4_POL
)
export Gfx_DCA_ZUR_23_2S_JOD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZUR_23_2S_JOD_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZUR_23_2S_JOD_POL
)
export Gfx_DCA_ZUR_23_2S_JOD_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZUR_23_2S_JOD_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZUR_23_2S_JOD_Para_POL
)
export Gfx_DCA_ZU_23_2_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZU_23_2_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZU_23_2_DDR
)
export Gfx_DCA_ZU_23_2_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZU_23_2_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZU_23_2_POL
)
export Gfx_DCA_ZU_23_2_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZU_23_2_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZU_23_2_Para_POL
)
export Gfx_DCA_ZU_23_2_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZU_23_2_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZU_23_2_SOV
)
export Gfx_DCA_ZU_23_2_TTsko_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZU_23_2_TTsko_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZU_23_2_TTsko_SOV
)
export Gfx_DCA_ZU_23_2_nonPara_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DCA_ZU_23_2_nonPara_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_DCA_ZU_23_2_nonPara_SOV
)
export Gfx_DEP_M109A2_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_DEP_M109A2
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_DEP_M109A2_Tourelle_02
            )
        ),
    ]
)
export Gfx_Dragoon_300_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Dragoon_300_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Dragoon_300_US
)
export Gfx_EBR_90mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_EBR_90mm_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ERC_90_Sagaie_CMD_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ERC_90_Sagaie_CMD_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ERC_90_Sagaie_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ERC_90_Sagaie_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ERC_90_Sagaie_reco_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ERC_90_Sagaie_reco_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FAV_AGL_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FAV_AGL_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_FAV_AGL_US
)
export Gfx_FAV_HMG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FAV_HMG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_FAV_HMG_US
)
export Gfx_FAV_TOW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FAV_TOW_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_FAV_TOW_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_FAV_TOW_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_FAV_trans_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FAV_trans_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_FAV_trans_US
)
export Gfx_FH70_155mm_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FH70_155mm_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_FH70_155mm_RFA
)
export Gfx_FH70_155mm_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FH70_155mm_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_FH70_155mm_UK
)
export Gfx_FV101_Scorpion_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV101_Scorpion_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV101_Scorpion_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV101_Scorpion_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV101_Scorpion_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV101_Scorpion_para_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV102_Striker_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV102_Striker_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV102_Striker_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV102_Striker_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV102_Striker_para_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV102_Striker_para_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV103_Spartan_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV103_Spartan_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_FV103_Spartan_BEL_Tourelle_01
            )
        ),
    ]
)
export Gfx_FV103_Spartan_GSR_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV103_Spartan_GSR_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_FV103_Spartan_GSR_UK_Tourelle_01
            )
        ),
    ]
)
export Gfx_FV103_Spartan_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV103_Spartan_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_FV103_Spartan_UK_Tourelle_01
            )
        ),
    ]
)
export Gfx_FV103_Spartan_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV103_Spartan_para_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_FV103_Spartan_para_BEL_Tourelle_01
            )
        ),
    ]
)
export Gfx_FV105_Sultan_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV105_Sultan_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV105_Sultan_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV105_Sultan_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV105_Sultan_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV105_Sultan_para_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV105_Sultan_para_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV105_Sultan_para_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV107_Scimitar_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV107_Scimitar_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV107_Scimitar_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV107_Scimitar_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV107_Scimitar_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV107_Scimitar_para_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV120_Spartan_MCT_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV120_Spartan_MCT_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV4003_Centurion_AVRE_ROMOR_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV4003_Centurion_AVRE_ROMOR_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV4003_Centurion_AVRE_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV4003_Centurion_AVRE_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV4201_Chieftain_CMD_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV4201_Chieftain_CMD_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV4201_Chieftain_Mk11_CMD_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV4201_Chieftain_Mk11_CMD_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV4201_Chieftain_Mk11_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV4201_Chieftain_Mk11_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV4201_Chieftain_Mk6_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV4201_Chieftain_Mk6_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV4201_Chieftain_Mk9_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV4201_Chieftain_Mk9_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV4201_Chieftain_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV4201_Chieftain_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV432_CMD_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV432_CMD_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV432_MILAN_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV432_MILAN_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV432_Mortar_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV432_Mortar_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_FV432_Mortar_UK_Tourelle_02
            )
        ),
    ]
)
export Gfx_FV432_Rarden_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV432_Rarden_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV432_SCAT_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV432_SCAT_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV432_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV432_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_FV432_UK_Tourelle_01
            )
        ),
    ]
)
export Gfx_FV432_WOMBAT_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV432_WOMBAT_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV432_supply_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV432_supply_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV433_Abbot_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV433_Abbot_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV438_Swingfire_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV438_Swingfire_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV601_Saladin_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV601_Saladin_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV603_Saracen_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV603_Saracen_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_FV721_Fox_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_FV721_Fox_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Faun_Kraka_20mm_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Faun_Kraka_20mm_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Faun_Kraka_20mm_RFA + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Faun_Kraka_20mm_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_Faun_Kraka_Log_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Faun_Kraka_Log_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Faun_Kraka_Log_RFA
)
export Gfx_Faun_Kraka_TOW_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Faun_Kraka_TOW_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Faun_Kraka_TOW_RFA + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_Faun_kraka_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Faun_kraka_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Faun_kraka_RFA
)
export Gfx_Ferret_Mk2_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Ferret_Mk2_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_GAZ_46_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_46_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_46_DDR
)
export Gfx_GAZ_46_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_46_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_46_POL
)
export Gfx_GAZ_46_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_46_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_46_SOV
)
export Gfx_GAZ_66B_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66B_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66B_POL
)
export Gfx_GAZ_66B_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66B_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66B_SOV
)
export Gfx_GAZ_66B_ZU_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66B_ZU_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66B_ZU_SOV
)
export Gfx_GAZ_66B_supply_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66B_supply_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66B_supply_POL
)
export Gfx_GAZ_66B_supply_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66B_supply_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66B_supply_SOV
)
export Gfx_GAZ_66_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66_POL
)
export Gfx_GAZ_66_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66_SOV
)
export Gfx_GAZ_66_supply_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66_supply_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66_supply_SOV
)
export Gfx_GAZ_66_trans_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GAZ_66_trans_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GAZ_66_trans_POL
)
export Gfx_GTMU_1D_AGS_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GTMU_1D_AGS_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_GTMU_1D_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GTMU_1D_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_GTMU_1D_SPG9_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GTMU_1D_SPG9_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_GTMU_1D_ZU_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_GTMU_1D_ZU_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_GTMU_1D_ZU_SOV
)
export Gfx_Gama_Goat_supply_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Gama_Goat_supply_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Gama_Goat_supply_US
)
export Gfx_Gama_Goat_trans_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Gama_Goat_trans_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Gama_Goat_trans_US
)
export Gfx_Gepard_1A2_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Gepard_1A2_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Gepard_1A2_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Gepard_1A2_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Gepard_1A2_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Gepard_1A2_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_HEMTT_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HEMTT_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HEMTT_US
)
export Gfx_HMGteam_AANF1_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AANF1_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AANF1_FR
)
export Gfx_HMGteam_AANF1_Reserve_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AANF1_Reserve_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AANF1_Reserve_FR
)
export Gfx_HMGteam_AANF1_para_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AANF1_para_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AANF1_para_FR
)
export Gfx_HMGteam_AGS17_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AGS17_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AGS17_DDR
)
export Gfx_HMGteam_AGS17_DShV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AGS17_DShV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AGS17_DShV_SOV
)
export Gfx_HMGteam_AGS17_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AGS17_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AGS17_POL
)
export Gfx_HMGteam_AGS17_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AGS17_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AGS17_SOV
)
export Gfx_HMGteam_AGS17_TTsko_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AGS17_TTsko_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AGS17_TTsko_SOV
)
export Gfx_HMGteam_AGS17_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_AGS17_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_AGS17_VDV_SOV
)
export Gfx_HMGteam_DShK_AA_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_DShK_AA_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_DShK_AA_SOV
)
export Gfx_HMGteam_DShK_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_DShK_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_DShK_SOV
)
export Gfx_HMGteam_KPVT_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_KPVT_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_KPVT_SOV
)
export Gfx_HMGteam_M1919A4_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M1919A4_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M1919A4_NL
)
export Gfx_HMGteam_M2HB_AB_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_AB_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_AB_US
)
export Gfx_HMGteam_M2HB_Aero_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_Aero_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_Aero_US
)
export Gfx_HMGteam_M2HB_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_BEL
)
export Gfx_HMGteam_M2HB_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_FR
)
export Gfx_HMGteam_M2HB_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_LUX
)
export Gfx_HMGteam_M2HB_M63_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_M63_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_M63_US
)
export Gfx_HMGteam_M2HB_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_NG_US
)
export Gfx_HMGteam_M2HB_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_NL
)
export Gfx_HMGteam_M2HB_RIMa_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_RIMa_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_RIMa_FR
)
export Gfx_HMGteam_M2HB_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_UK
)
export Gfx_HMGteam_M2HB_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_US
)
export Gfx_HMGteam_M2HB_para_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_para_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_para_FR
)
export Gfx_HMGteam_M2HB_para_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M2HB_para_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M2HB_para_UK
)
export Gfx_HMGteam_M60_AB_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M60_AB_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M60_AB_US
)
export Gfx_HMGteam_M60_Aero_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M60_Aero_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M60_Aero_US
)
export Gfx_HMGteam_M60_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M60_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M60_NG_US
)
export Gfx_HMGteam_M60_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_M60_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_M60_US
)
export Gfx_HMGteam_MAG_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_MAG_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_MAG_BEL
)
export Gfx_HMGteam_MAG_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_MAG_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_MAG_NL
)
export Gfx_HMGteam_MAG_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_MAG_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_MAG_UK
)
export Gfx_HMGteam_MAG_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_MAG_para_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_MAG_para_BEL
)
export Gfx_HMGteam_MAG_para_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_MAG_para_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_MAG_para_UK
)
export Gfx_HMGteam_MG3_FJ_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_MG3_FJ_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_MG3_FJ_RFA
)
export Gfx_HMGteam_MG3_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_MG3_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_MG3_RFA
)
export Gfx_HMGteam_Maxim_Reserve_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_Maxim_Reserve_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_Maxim_Reserve_SOV
)
export Gfx_HMGteam_Mk19_AB_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_Mk19_AB_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_Mk19_AB_US
)
export Gfx_HMGteam_Mk19_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_Mk19_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_Mk19_US
)
export Gfx_HMGteam_NSV_6U6_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_NSV_6U6_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_NSV_6U6_VDV_SOV
)
export Gfx_HMGteam_NSV_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_NSV_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_NSV_DDR
)
export Gfx_HMGteam_NSV_DShV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_NSV_DShV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_NSV_DShV_SOV
)
export Gfx_HMGteam_NSV_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_NSV_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_NSV_POL
)
export Gfx_HMGteam_NSV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_NSV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_NSV_SOV
)
export Gfx_HMGteam_NSV_TTsko_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_NSV_TTsko_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_NSV_TTsko_SOV
)
export Gfx_HMGteam_NSV_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_NSV_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_NSV_VDV_SOV
)
export Gfx_HMGteam_PKM_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_DDR
)
export Gfx_HMGteam_PKM_DShV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_DShV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_DShV_SOV
)
export Gfx_HMGteam_PKM_FJ_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_FJ_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_FJ_DDR
)
export Gfx_HMGteam_PKM_Naval_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_Naval_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_Naval_POL
)
export Gfx_HMGteam_PKM_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_POL
)
export Gfx_HMGteam_PKM_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_SOV
)
export Gfx_HMGteam_PKM_TTsko_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_TTsko_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_TTsko_SOV
)
export Gfx_HMGteam_PKM_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_VDV_SOV
)
export Gfx_HMGteam_PKM_para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HMGteam_PKM_para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_HMGteam_PKM_para_POL
)
export Gfx_HS30_Panzermorser_120mm_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_HS30_Panzermorser_120mm_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Hibneryt_KG_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Hibneryt_KG_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Hibneryt_KG_POL
)
export Gfx_Hibneryt_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Hibneryt_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Hibneryt_POL
)
export Gfx_Honker_4011_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Honker_4011_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Honker_4011_POL
)
export Gfx_Honker_RYS_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Honker_RYS_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Honker_RYS_POL + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Honker_RYS_POL_Tourelle_01
            )
        ),
    ]
)
export Gfx_Howz_2A36_Giatsint_B_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_2A36_Giatsint_B_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_2A36_Giatsint_B_SOV
)
export Gfx_Howz_A19_122mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_A19_122mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_A19_122mm_POL
)
export Gfx_Howz_B4M_203mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_B4M_203mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_B4M_203mm_SOV
)
export Gfx_Howz_BS3_100mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_BS3_100mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_BS3_100mm_SOV
)
export Gfx_Howz_Br5M_280mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_Br5M_280mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_Br5M_280mm_SOV
)
export Gfx_Howz_D1_152mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_D1_152mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_D1_152mm_POL
)
export Gfx_Howz_D1_152mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_D1_152mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_D1_152mm_SOV
)
export Gfx_Howz_D20_152mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_D20_152mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_D20_152mm_DDR
)
export Gfx_Howz_D20_152mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_D20_152mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_D20_152mm_SOV
)
export Gfx_Howz_D30_122mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_D30_122mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_D30_122mm_DDR
)
export Gfx_Howz_D30_122mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_D30_122mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_D30_122mm_SOV
)
export Gfx_Howz_D30_122mm_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_D30_122mm_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_D30_122mm_VDV_SOV
)
export Gfx_Howz_L118_105mm_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_L118_105mm_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_L118_105mm_LUX
)
export Gfx_Howz_L118_105mm_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_L118_105mm_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_L118_105mm_UK
)
export Gfx_Howz_M101_105mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M101_105mm_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M101_105mm_FR
)
export Gfx_Howz_M101_105mm_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M101_105mm_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M101_105mm_RFA
)
export Gfx_Howz_M101_105mm_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M101_105mm_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M101_105mm_US
)
export Gfx_Howz_M101_105mm_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M101_105mm_para_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M101_105mm_para_BEL
)
export Gfx_Howz_M102_105mm_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M102_105mm_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M102_105mm_US
)
export Gfx_Howz_M114_155mm_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M114_155mm_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M114_155mm_NL
)
export Gfx_Howz_M114_39_155mm_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M114_39_155mm_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M114_39_155mm_NL
)
export Gfx_Howz_M119_105mm_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M119_105mm_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M119_105mm_US
)
export Gfx_Howz_M198_155mm_Copperhead_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M198_155mm_Copperhead_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M198_155mm_Copperhead_US
)
export Gfx_Howz_M198_155mm_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M198_155mm_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M198_155mm_US
)
export Gfx_Howz_M30_122mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M30_122mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M30_122mm_DDR
)
export Gfx_Howz_M30_122mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M30_122mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M30_122mm_POL
)
export Gfx_Howz_M46_130mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M46_130mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M46_130mm_DDR
)
export Gfx_Howz_M46_130mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_M46_130mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_M46_130mm_POL
)
export Gfx_Howz_ML20_152mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_ML20_152mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_ML20_152mm_POL
)
export Gfx_Howz_MstaB_150mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_MstaB_150mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_MstaB_150mm_SOV
)
export Gfx_Howz_ZiS3_76mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Howz_ZiS3_76mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Howz_ZiS3_76mm_DDR
)
export Gfx_IS2M_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_IS2M_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Iltis_CMD_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_CMD_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_CMD_BEL
)
export Gfx_Iltis_HMG_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_HMG_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_HMG_BEL + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_HMG_BEL_Tourelle_01
            )
        ),
    ]
)
export Gfx_Iltis_MILAN_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_MILAN_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_MILAN_BEL + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_BEL_Tourelle_01
            )
        ),
    ]
)
export Gfx_Iltis_MILAN_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_MILAN_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_MILAN_RFA + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_Iltis_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_RFA
)
export Gfx_Iltis_para_CMD_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_para_CMD_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_para_CMD_BEL
)
export Gfx_Iltis_para_CMD_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_para_CMD_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_para_CMD_RFA
)
export Gfx_Iltis_trans_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_trans_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_trans_BEL
)
export Gfx_Iltis_trans_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Iltis_trans_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Iltis_trans_RFA
)
export Gfx_Jaguar_1_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Jaguar_1_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_1_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_Jaguar_2_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Jaguar_2_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_2_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_KanJagdPanzer_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_KanJagdPanzer_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_KanJagdPanzer_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_KanJagdPanzer_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_KrAZ_255B_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_KrAZ_255B_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_KrAZ_255B_POL
)
export Gfx_KrAZ_255B_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_KrAZ_255B_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_KrAZ_255B_SOV
)
export Gfx_KrAZ_255B_supply_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_KrAZ_255B_supply_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_KrAZ_255B_supply_DDR
)
export Gfx_KrAZ_255B_supply_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_KrAZ_255B_supply_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_KrAZ_255B_supply_POL
)
export Gfx_KrAZ_255B_supply_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_KrAZ_255B_supply_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_KrAZ_255B_supply_SOV
)
export Gfx_LAV_25_M1047_US_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LAV_25_M1047_US_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_LO_1800_FASTA_4_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LO_1800_FASTA_4_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LO_1800_FASTA_4_DDR
)
export Gfx_LO_1800_ZPU_2_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LO_1800_ZPU_2_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LO_1800_ZPU_2_POL
)
export Gfx_LSV_M2HB_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LSV_M2HB_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LSV_M2HB_UK
)
export Gfx_LSV_MILAN_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LSV_MILAN_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LSV_MILAN_UK
)
export Gfx_LUAZ_967M_AGL_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_AGL_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_AGL_SOV
)
export Gfx_LUAZ_967M_AGL_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_AGL_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_AGL_VDV_SOV
)
export Gfx_LUAZ_967M_CMD_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_CMD_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_CMD_VDV_SOV
)
export Gfx_LUAZ_967M_FAO_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_FAO_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_FAO_SOV
)
export Gfx_LUAZ_967M_Fagot_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_Fagot_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_Fagot_SOV
)
export Gfx_LUAZ_967M_Fagot_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_Fagot_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_Fagot_VDV_SOV
)
export Gfx_LUAZ_967M_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_SOV
)
export Gfx_LUAZ_967M_SPG9_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_SPG9_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_SPG9_SOV
)
export Gfx_LUAZ_967M_SPG9_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_SPG9_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_SPG9_VDV_SOV
)
export Gfx_LUAZ_967M_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_VDV_SOV
)
export Gfx_LUAZ_967M_supply_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LUAZ_967M_supply_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LUAZ_967M_supply_SOV
)
export Gfx_LandRover_CMD_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_CMD_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_CMD_NL
)
export Gfx_LandRover_CMD_Para_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_CMD_Para_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_CMD_Para_UK
)
export Gfx_LandRover_CMD_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_CMD_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_CMD_UK
)
export Gfx_LandRover_MILAN_Para_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_MILAN_Para_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_MILAN_Para_UK
)
export Gfx_LandRover_MILAN_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_MILAN_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_MILAN_UK
)
export Gfx_LandRover_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_NL
)
export Gfx_LandRover_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_UK
)
export Gfx_LandRover_WOMBAT_Gurkhas_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_WOMBAT_Gurkhas_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_WOMBAT_Gurkhas_UK
)
export Gfx_LandRover_WOMBAT_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LandRover_WOMBAT_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LandRover_WOMBAT_UK
)
export Gfx_Lars_2_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Lars_2_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Lars_2_RFA + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Lars_2_RFA_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A1A1_CMD_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A1A1_CMD_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A1A1_CMD_NL_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A1A1_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A1A1_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A1A1_NL_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A1_CMD_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A1_CMD_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A1_CMD_RFA_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A1_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A1_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A1_NL_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A1_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A1_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A1_RFA_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A5_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A5_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A5_BEL_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A5_CMD_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A5_CMD_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A5_CMD_BEL_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A5_CMD_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A5_CMD_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A5_CMD_RFA_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1A5_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1A5_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1A5_RFA_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1BE_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1BE_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1BE_BEL_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_1BE_CMD_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_1BE_CMD_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Leopard_1BE_CMD_BEL_Tourelle_02
            )
        ),
    ]
)
export Gfx_Leopard_2A1_CMD_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A1_CMD_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A1_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A1_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A1_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A1_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A3_CMD_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A3_CMD_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A3_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A3_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A4B_CMD_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A4B_CMD_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A4B_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A4B_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A4_CMD_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A4_CMD_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A4_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A4_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Leopard_2A4_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Leopard_2A4_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_LuAZ_967M_AA_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_LuAZ_967M_AA_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_LuAZ_967M_AA_VDV_SOV
)
export Gfx_Luchs_A1_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Luchs_A1_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1025_Humvee_AGL_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_AGL_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_AGL_US
)
export Gfx_M1025_Humvee_AGL_nonPara_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_AGL_nonPara_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_AGL_nonPara_US
)
export Gfx_M1025_Humvee_CMD_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_CMD_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_CMD_LUX
)
export Gfx_M1025_Humvee_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_CMD_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_CMD_US
)
export Gfx_M1025_Humvee_CMD_para_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_CMD_para_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_CMD_para_US
)
export Gfx_M1025_Humvee_GVLLD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_GVLLD_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_GVLLD_US
)
export Gfx_M1025_Humvee_HMG_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_HMG_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_HMG_LUX
)
export Gfx_M1025_Humvee_MP_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_MP_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_MP_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_MP_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_M1025_Humvee_TOW_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_TOW_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_TOW_LUX + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX_Tourelle_01
            )
        ),
    ]
)
export Gfx_M1025_Humvee_TOW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_TOW_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_TOW_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_M1025_Humvee_TOW_para_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_TOW_para_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_TOW_para_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_M1025_Humvee_scout_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_scout_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_scout_US
)
export Gfx_M1025_Humvee_scout_tuto_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1025_Humvee_scout_tuto_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1025_Humvee_scout_tuto_US
)
export Gfx_M1038_Humvee_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1038_Humvee_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1038_Humvee_LUX
)
export Gfx_M1038_Humvee_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1038_Humvee_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M1038_Humvee_US
)
export Gfx_M106A2_HOWZ_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M106A2_HOWZ_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M106A2_HOWZ_US_Tourelle_02
            )
        ),
    ]
)
export Gfx_M106A2_Howz_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M106A2_Howz_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M106A2_Howz_NG_US_Tourelle_02
            )
        ),
    ]
)
export Gfx_M106A2_Mortar_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M106A2_Mortar_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M106A2_Mortar_NL_Tourelle_02
            )
        ),
    ]
)
export Gfx_M107A2_175mm_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M107A2_175mm_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M109A2_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M109A2_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M109A2_BEL_Tourelle_02
            )
        ),
    ]
)
export Gfx_M109A2_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M109A2_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M109A2_NG_US_Tourelle_02
            )
        ),
    ]
)
export Gfx_M109A2_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M109A2_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M109A2_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M109A2_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M109A3G_HOWZ_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M109A3G_HOWZ_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M109A3G_HOWZ_RFA_Tourelle_02
            )
        ),
    ]
)
export Gfx_M110A2_HOWZ_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M110A2_HOWZ_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M110A2_HOWZ_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M110A2_HOWZ_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M110A2_HOWZ_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M110A2_HOWZ_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M110A2_Howz_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M110A2_Howz_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M110A2_Howz_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M110A2_Howz_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M110A2_Howz_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M110A2_Howz_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1B_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1B_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1B_MILAN_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1B_MILAN_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1B_Radar_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1B_Radar_BEL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1G_MILAN_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1G_MILAN_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_M113A1G_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1G_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1G_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_M113A1G_reco_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1G_reco_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1G_reco_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_M113A1G_supply_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1G_supply_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1_ACAV_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1_ACAV_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1_Dragon_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1_Dragon_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US_Tourelle_02
            )
        ),
    ]
)
export Gfx_M113A1_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1_TOW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1_TOW_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A1_reco_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A1_reco_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A2_TOW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A2_TOW_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A2_supply_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A2_supply_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113A3_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113A3_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113_ACAV_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113_ACAV_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113_ACAV_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113_ACAV_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113_CV_25mm_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113_CV_25mm_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113_Dragon_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113_Dragon_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M113_Dragon_US_Tourelle_02
            )
        ),
    ]
)
export Gfx_M113_GreenArcher_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113_GreenArcher_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M113_GreenArcher_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113_GreenArcher_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M113_GreenArcher_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_M113_GreenArcher_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113_GreenArcher_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M113_GreenArcher_UK_Tourelle_01
            )
        ),
    ]
)
export Gfx_M113_PzMorser_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M113_PzMorser_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M113_PzMorser_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_M125_HOWZ_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M125_HOWZ_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M125_HOWZ_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M125_HOWZ_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M151A2_TOW_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M151A2_TOW_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M151A2_TOW_NG_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M151A2_TOW_NG_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_M151A2_scout_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M151A2_scout_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M151A2_scout_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M151A2_scout_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_M151C_RCL_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M151C_RCL_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M151C_RCL_NG_US
)
export Gfx_M151_MUTT_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M151_MUTT_CMD_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M151_MUTT_CMD_US
)
export Gfx_M151_MUTT_trans_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M151_MUTT_trans_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M151_MUTT_trans_DDR
)
export Gfx_M151_MUTT_trans_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M151_MUTT_trans_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M151_MUTT_trans_US
)
export Gfx_M163_CS_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M163_CS_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M163_PIVADS_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M163_PIVADS_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1A1HA_Abrams_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1A1HA_Abrams_CMD_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1A1HA_Abrams_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1A1HA_Abrams_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1A1_Abrams_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1A1_Abrams_CMD_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1A1_Abrams_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1A1_Abrams_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1A1_Abrams_reco_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1A1_Abrams_reco_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1IP_Abrams_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1IP_Abrams_CMD_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1IP_Abrams_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1IP_Abrams_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1_Abrams_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1_Abrams_CMD_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1_Abrams_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1_Abrams_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M1_Abrams_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M1_Abrams_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M201_CMD_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M201_CMD_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M201_CMD_FR
)
export Gfx_M201_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M201_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M201_FR
)
export Gfx_M201_MG_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M201_MG_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M201_MG_FR
)
export Gfx_M201_MILAN_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M201_MILAN_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M201_MILAN_FR
)
export Gfx_M270_MLRS_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M270_MLRS_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M270_MLRS_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M270_MLRS_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M270_MLRS_cluster_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M270_MLRS_cluster_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M270_MLRS_cluster_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M270_MLRS_cluster_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M270_MLRS_cluster_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M270_MLRS_cluster_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M274_Mule_ITOW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M274_Mule_ITOW_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M274_Mule_ITOW_US
)
export Gfx_M274_Mule_M2HB_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M274_Mule_M2HB_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M274_Mule_M2HB_US
)
export Gfx_M274_Mule_RCL_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M274_Mule_RCL_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M274_Mule_RCL_US
)
export Gfx_M274_Mule_supply_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M274_Mule_supply_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M274_Mule_supply_US
)
export Gfx_M2A1_Bradley_IFV_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M2A1_Bradley_IFV_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M2A1_Bradley_Leader_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M2A1_Bradley_Leader_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M2A2_Bradley_IFV_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M2A2_Bradley_IFV_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M2A2_Bradley_Leader_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M2A2_Bradley_Leader_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M2_Bradley_IFV_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M2_Bradley_IFV_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M35_supply_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M35_supply_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M35_supply_US
)
export Gfx_M35_trans_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M35_trans_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M35_trans_DDR
)
export Gfx_M35_trans_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M35_trans_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M35_trans_US
)
export Gfx_M35_trans_tuto_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M35_trans_tuto_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M35_trans_tuto_US
)
export Gfx_M38A1_CMD_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M38A1_CMD_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M38A1_CMD_NL
)
export Gfx_M38A1_MG_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M38A1_MG_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M38A1_MG_NL
)
export Gfx_M38A1_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M38A1_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M38A1_NL
)
export Gfx_M38A1_RCL_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M38A1_RCL_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M38A1_RCL_NL
)
export Gfx_M38A1_TOW_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M38A1_TOW_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M38A1_TOW_NL
)
export Gfx_M3A1_Bradley_CFV_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M3A1_Bradley_CFV_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M3A2_Bradley_CFV_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M3A2_Bradley_CFV_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M42_Duster_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M42_Duster_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M42_Duster_US
)
export Gfx_M48A2C_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M48A2C_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M48A2GA2_CMD_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M48A2GA2_CMD_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M48A2GA2_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M48A2GA2_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M48A5_reco_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M48A5_reco_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M48A5_reco_NG_US_Tourelle_02
            )
        ),
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_03",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M48A5_reco_NG_US_Tourelle_03
            )
        ),
    ]
)
export Gfx_M48_Chaparral_MIM72F_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M48_Chaparral_MIM72F_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M48_Chaparral_MIM72F_US

    SubDepictionGenerators = [ SubGenerators_Showroom_M48_Chaparral_MIM72F_US ]
)
export Gfx_M548A2_supply_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M548A2_supply_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M548A2_supply_US
)
export Gfx_M551A1_ACAV_Sheridan_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M551A1_ACAV_Sheridan_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M551A1_TTS_Sheridan_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M551A1_TTS_Sheridan_CMD_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M551A1_TTS_Sheridan_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M551A1_TTS_Sheridan_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M577_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M577_NL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M577_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M577_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M577_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M577_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M60A1_AVLM_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M60A1_AVLM_US
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ ShowroomRocketSubDepictionTemplate(
            UnitMeshDescriptor = $/GFX/DepictionResources/Modele_M60A1_AVLM_US
            RocketMeshDescriptor = $/GFX/DepictionResources/Modele_Missile_MK22_Mod_2_Rocket
            RocketCount = 2
        ) ]
)
export Gfx_M60A1_RISE_Passive_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M60A1_RISE_Passive_CMD_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M60A1_RISE_Passive_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M60A1_RISE_Passive_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M60A1_RISE_Passive_reco_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M60A1_RISE_Passive_reco_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M60A3_CMD_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M60A3_CMD_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M60A3_ERA_Patton_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M60A3_ERA_Patton_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M60A3_Patton_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M60A3_Patton_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M60A3_Patton_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M60A3_Patton_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M728_CEV_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M728_CEV_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M728_CEV_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M728_CEV_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M812_supply_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M812_supply_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M812_supply_US
)
export Gfx_M901A1_ITW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M901A1_ITW_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M901_TOW_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M901_TOW_NG_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M901_TOW_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M901_TOW_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M981_FISTV_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M981_FISTV_US
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_M998_Avenger_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M998_Avenger_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M998_Avenger_US
)
export Gfx_M998_Avenger_nonPara_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M998_Avenger_nonPara_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M998_Avenger_nonPara_US
)
export Gfx_M998_Humvee_AGL_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M998_Humvee_AGL_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M998_Humvee_AGL_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Humvee_AGL_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_M998_Humvee_Delta_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M998_Humvee_Delta_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M998_Humvee_Delta_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Humvee_Delta_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_M998_Humvee_HMG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M998_Humvee_HMG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M998_Humvee_HMG_US + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Humvee_HMG_US_Tourelle_01
            )
        ),
    ]
)
export Gfx_M998_Humvee_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M998_Humvee_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M998_Humvee_LUX
)
export Gfx_M998_Humvee_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_M998_Humvee_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_M998_Humvee_US
)
export Gfx_MAN_Kat_6x6_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MAN_Kat_6x6_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_MAN_Kat_6x6_RFA
)
export Gfx_MAN_Kat_6x6_trans_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MAN_Kat_6x6_trans_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_MAN_Kat_6x6_trans_RFA
)
export Gfx_MAN_Z311_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MAN_Z311_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_MAN_Z311_BEL
)
export Gfx_MAN_Z311_Mi50_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MAN_Z311_Mi50_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_MAN_Z311_Mi50_BEL
)
export Gfx_MCV_80_Warrior_CMD_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MCV_80_Warrior_CMD_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MCV_80_Warrior_MILAN_ERA_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MCV_80_Warrior_MILAN_ERA_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK_Tourelle_02
            )
        ),
    ]
)
export Gfx_MCV_80_Warrior_MILAN_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MCV_80_Warrior_MILAN_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK_Tourelle_02
            )
        ),
    ]
)
export Gfx_MCV_80_Warrior_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MCV_80_Warrior_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MFRW_RM70_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MFRW_RM70_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MFRW_RM70_cluster_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MFRW_RM70_cluster_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MLRS_WP_8z_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MLRS_WP_8z_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_MLRS_WP_8z_POL
)
export Gfx_MTLB_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_Shturm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_Shturm_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_Shturm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_Shturm_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_Strela10M3_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_Strela10M3_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_Strela10_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_Strela10_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_Strela10_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_Strela10_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_Strela10_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_Strela10_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_TRI_Hors_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_TRI_Hors_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_Vasilek_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_Vasilek_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Vasilek_SOV_Tourelle_01
            )
        ),
    ]
)
export Gfx_MTLB_supply_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_supply_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_supply_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_supply_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_trans_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_trans_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_trans_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_trans_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_MTLB_transp_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_MTLB_transp_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Marder_1A2_MILAN_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Marder_1A2_MILAN_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Marder_1A2_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Marder_1A2_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Marder_1A3_MILAN_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Marder_1A3_MILAN_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Marder_1A3_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Marder_1A3_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Marder_Roland_2_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Marder_Roland_2_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Marder_Roland_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Marder_Roland_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Mortier_107mm_Aero_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_107mm_Aero_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_107mm_Aero_US
)
export Gfx_Mortier_107mm_Airborne_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_107mm_Airborne_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_107mm_Airborne_US
)
export Gfx_Mortier_107mm_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_107mm_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_107mm_BEL
)
export Gfx_Mortier_107mm_NG_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_107mm_NG_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_107mm_NG_US
)
export Gfx_Mortier_107mm_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_107mm_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_107mm_NL
)
export Gfx_Mortier_107mm_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_107mm_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_107mm_US
)
export Gfx_Mortier_107mm_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_107mm_para_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_107mm_para_BEL
)
export Gfx_Mortier_240mm_M240_Cluster_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_240mm_M240_Cluster_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_240mm_M240_Cluster_SOV
)
export Gfx_Mortier_240mm_M240_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_240mm_M240_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_240mm_M240_POL
)
export Gfx_Mortier_240mm_M240_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_240mm_M240_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_240mm_M240_SOV
)
export Gfx_Mortier_2B14_82mm_DShV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2B14_82mm_DShV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2B14_82mm_DShV_SOV
)
export Gfx_Mortier_2B14_82mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2B14_82mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2B14_82mm_SOV
)
export Gfx_Mortier_2B14_82mm_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2B14_82mm_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2B14_82mm_VDV_SOV
)
export Gfx_Mortier_2B9_Vasilek_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2B9_Vasilek_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2B9_Vasilek_Para_POL
)
export Gfx_Mortier_2B9_Vasilek_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2B9_Vasilek_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2B9_Vasilek_SOV
)
export Gfx_Mortier_2B9_Vasilek_nonPara_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2B9_Vasilek_nonPara_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2B9_Vasilek_nonPara_SOV
)
export Gfx_Mortier_2S12_120mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2S12_120mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2S12_120mm_DDR
)
export Gfx_Mortier_2S12_120mm_DShV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2S12_120mm_DShV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2S12_120mm_DShV_SOV
)
export Gfx_Mortier_2S12_120mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2S12_120mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2S12_120mm_POL
)
export Gfx_Mortier_2S12_120mm_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2S12_120mm_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2S12_120mm_Para_POL
)
export Gfx_Mortier_2S12_120mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2S12_120mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2S12_120mm_SOV
)
export Gfx_Mortier_2S12_120mm_TTsko_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2S12_120mm_TTsko_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2S12_120mm_TTsko_SOV
)
export Gfx_Mortier_2S12_120mm_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_2S12_120mm_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_2S12_120mm_VDV_SOV
)
export Gfx_Mortier_81mm_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_81mm_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_81mm_BEL
)
export Gfx_Mortier_81mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_81mm_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_81mm_FR
)
export Gfx_Mortier_81mm_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_81mm_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_81mm_LUX
)
export Gfx_Mortier_81mm_para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_81mm_para_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_81mm_para_BEL
)
export Gfx_Mortier_M29_81mm_Marines_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_M29_81mm_Marines_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_M29_81mm_Marines_NL
)
export Gfx_Mortier_M29_81mm_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_M29_81mm_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_M29_81mm_NL
)
export Gfx_Mortier_M29_81mm_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_M29_81mm_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_M29_81mm_US
)
export Gfx_Mortier_M43_160mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_M43_160mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_M43_160mm_POL
)
export Gfx_Mortier_M43_82mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_M43_82mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_M43_82mm_DDR
)
export Gfx_Mortier_M43_82mm_FJ_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_M43_82mm_FJ_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_M43_82mm_FJ_DDR
)
export Gfx_Mortier_M43_82mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_M43_82mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_M43_82mm_POL
)
export Gfx_Mortier_M43_82mm_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_M43_82mm_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_M43_82mm_Para_POL
)
export Gfx_Mortier_MORT61_120mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_MORT61_120mm_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_MORT61_120mm_FR
)
export Gfx_Mortier_MORT61_120mm_NL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_MORT61_120mm_NL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_MORT61_120mm_NL
)
export Gfx_Mortier_MORT61_120mm_para_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_MORT61_120mm_para_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_MORT61_120mm_para_FR
)
export Gfx_Mortier_Nona_K_120mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_Nona_K_120mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_Nona_K_120mm_SOV
)
export Gfx_Mortier_PM43_120mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_PM43_120mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_PM43_120mm_DDR
)
export Gfx_Mortier_PM43_120mm_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_PM43_120mm_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_PM43_120mm_POL
)
export Gfx_Mortier_PM43_120mm_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_PM43_120mm_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_PM43_120mm_SOV
)
export Gfx_Mortier_Tampella_120mm_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_Tampella_120mm_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_Tampella_120mm_RFA
)
export Gfx_Mortier_Tampella_120mm_para_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Mortier_Tampella_120mm_para_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Mortier_Tampella_120mm_para_RFA
)
export Gfx_OT_62_TOPAS_2AP_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_62_TOPAS_2AP_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_62_TOPAS_JOD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_62_TOPAS_JOD_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_OT_62_TOPAS_JOD_POL
)
export Gfx_OT_62_TOPAS_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_62_TOPAS_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_62_TOPAS_R3M_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_62_TOPAS_R3M_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_62_TOPAS_SPG9_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_62_TOPAS_SPG9_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_64_SKOT_2AM_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_64_SKOT_2AM_POL
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_OT_64_SKOT_2AM_POL ]
)
export Gfx_OT_64_SKOT_2A_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_64_SKOT_2A_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_64_SKOT_2P_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_64_SKOT_2P_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_64_SKOT_2_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_64_SKOT_2_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_64_SKOT_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_64_SKOT_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_65_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_65_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_65_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_65_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_OT_65_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_OT_65_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Obusier_155mm_mle1950_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Obusier_155mm_mle1950_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Obusier_155mm_mle1950_FR
)
export Gfx_Osa_9K33M3_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Osa_9K33M3_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Osa_9K33M3_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Osa_9K33M3_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Osa_9K33M3_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Osa_9K33M3_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PSzH_IV_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PSzH_IV_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_CMD_Naval_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_CMD_Naval_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_Naval_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_Naval_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_tank_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_tank_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_tank_Naval_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_tank_Naval_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PT76B_tank_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PT76B_tank_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_PTS_M_supply_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PTS_M_supply_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_PTS_M_supply_DDR
)
export Gfx_PTS_M_supply_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_PTS_M_supply_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_PTS_M_supply_POL
)
export Gfx_RCL_L6_Wombat_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_RCL_L6_Wombat_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_RCL_L6_Wombat_UK
)
export Gfx_RM70_85_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_RM70_85_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_RM70_85_DDR
)
export Gfx_RM70_85_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_RM70_85_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_RM70_85_POL
)
export Gfx_Roland_2_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Roland_2_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Roland_3_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Roland_3_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Rover_101FC_LUX_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Rover_101FC_LUX
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Rover_101FC_LUX
)
export Gfx_Rover_101FC_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Rover_101FC_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Rover_101FC_UK
)
export Gfx_Rover_101FC_supply_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Rover_101FC_supply_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Rover_101FC_supply_UK
)
export Gfx_SPW_152K_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_SPW_152K_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_SPW_152K_DDR_Tourelle_01
            )
        ),
    ]
)
export Gfx_Saxon_CMD_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Saxon_CMD_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Saxon_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Saxon_UK
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Sonderwagen_4_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Sonderwagen_4_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Sonderwagen_4_RFA
)
export Gfx_Sonderwagen_4_recon_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Sonderwagen_4_recon_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Sonderwagen_4_recon_RFA
)
export Gfx_Star_266_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Star_266_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Star_266_POL
)
export Gfx_Star_266_supply_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Star_266_supply_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Star_266_supply_POL
)
export Gfx_Supacat_ATMP_Javelin_LML_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Supacat_ATMP_Javelin_LML_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Supacat_ATMP_Javelin_LML_UK + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK_Tourelle_01
            )
        ),
    ]
)
export Gfx_Supacat_ATMP_MILAN_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Supacat_ATMP_MILAN_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Supacat_ATMP_MILAN_UK
)
export Gfx_Supacat_ATMP_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Supacat_ATMP_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Supacat_ATMP_UK
)
export Gfx_Supacat_ATMP_supply_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Supacat_ATMP_supply_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Supacat_ATMP_supply_UK
)
export Gfx_T34_85M_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T34_85M_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T34_85M_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T34_85M_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T34_85M_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T34_85M_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T54B_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T54B_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T54B_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T54B_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T54B_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T54B_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T54B_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T54B_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T54B_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T54B_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T55AM2B_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AM2B_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM2B_DDR_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55AM2_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AM2_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM2_CMD_DDR_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55AM2_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AM2_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM2_DDR_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55AMS_Merida_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AMS_Merida_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AMS_Merida_POL_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55AM_1_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AM_1_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM_1_CMD_SOV_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55AM_1_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AM_1_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM_1_SOV_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55AM_Merida_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AM_Merida_CMD_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM_Merida_CMD_POL_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55AM_Merida_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AM_Merida_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM_Merida_POL_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55AS_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55AS_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55AS_POL_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55A_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55A_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55A_CMD_DDR_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55A_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55A_CMD_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55A_CMD_POL_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55A_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55A_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55A_CMD_SOV_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55A_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55A_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55A_DDR_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55A_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55A_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55A_POL_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55A_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55A_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55A_SOV_Tourelle_02
            )
        ),
    ]
)
export Gfx_T55A_obr81_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T55A_obr81_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_T55A_obr81_SOV_Tourelle_02
            )
        ),
    ]
)
export Gfx_T62M1_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T62M1_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T62MD1_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T62MD1_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T62MD_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T62MD_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T62MD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T62MD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T62MV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T62MV_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T62M_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T62M_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T62M_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T62M_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64AM_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64AM_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64AV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64AV_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64A_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64A_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64A_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64A_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64B1_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64B1_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64B1_reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64B1_reco_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64BV1_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64BV1_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64BV_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64BV_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64BV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64BV_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64B_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64B_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T64B_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T64B_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M1_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M1_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M1_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M1_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M1_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M1_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M1_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M1_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M1_Wilk_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M1_Wilk_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72MUV2_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72MUV2_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M_CMD_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72M_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72M_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72S_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72S_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T72_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T72_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T80BV_Beast_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T80BV_Beast_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T80BV_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T80BV_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T80BV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T80BV_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T80B_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T80B_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T80B_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T80B_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T80UD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T80UD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T80U_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T80U_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T80U_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T80U_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_T813_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T813_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_T813_DDR
)
export Gfx_T813_trans_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T813_trans_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_T813_trans_DDR
)
export Gfx_T815_supply_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_T815_supply_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_T815_supply_DDR
)
export Gfx_TOS1_Buratino_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TOS1_Buratino_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_TO_55_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TO_55_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_TO_55_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TO_55_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_TPZ_Fuchs_1_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TPZ_Fuchs_1_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TPZ_Fuchs_1_RFA + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_1_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_TPZ_Fuchs_CMD_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TPZ_Fuchs_CMD_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TPZ_Fuchs_CMD_RFA + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_CMD_RFA_Tourelle_01
            )
        ),
    ]
)
export Gfx_TPZ_Fuchs_MILAN_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TPZ_Fuchs_MILAN_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TPZ_Fuchs_MILAN_RFA + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA_Tourelle_01
            )
        ),
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA_Tourelle_02
            )
        ),
    ]
)
export Gfx_TPZ_Fuchs_RASIT_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TPZ_Fuchs_RASIT_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TPZ_Fuchs_RASIT_RFA
)
export Gfx_TRM_10000_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TRM_10000_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TRM_10000_FR
)
export Gfx_TRM_10000_supply_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TRM_10000_supply_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TRM_10000_supply_FR
)
export Gfx_TRM_2000_20mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TRM_2000_20mm_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TRM_2000_20mm_FR
)
export Gfx_TRM_2000_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TRM_2000_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TRM_2000_FR
)
export Gfx_TRM_2000_supply_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TRM_2000_supply_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TRM_2000_supply_FR
)
export Gfx_TUTO_M1025_Humvee_US_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_TUTO_M1025_Humvee_US
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_TUTO_M1025_Humvee_US
)
export Gfx_Tor_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Tor_SOV
    Selector = SpecificVehicleDepictionSelector

    SubDepictionGenerators = [ SubGenerators_Showroom_Tor_SOV ]
)
export Gfx_Tracked_Rapier_UK_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Tracked_Rapier_UK
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Tracked_Rapier_UK

    SubDepictionGenerators = [ SubGenerators_Showroom_Tracked_Rapier_UK ]
)
export Gfx_Tunguska_2K22_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Tunguska_2K22_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_UAZ_469_AGL_Grenzer_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_AGL_Grenzer_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_AGL_Grenzer_DDR
)
export Gfx_UAZ_469_AGL_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_AGL_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_AGL_SOV
)
export Gfx_UAZ_469_AGL_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_AGL_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_AGL_VDV_SOV
)
export Gfx_UAZ_469_CMD_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_CMD_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_CMD_DDR
)
export Gfx_UAZ_469_CMD_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_CMD_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_CMD_POL
)
export Gfx_UAZ_469_CMD_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_CMD_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_CMD_Para_POL
)
export Gfx_UAZ_469_CMD_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_CMD_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_CMD_SOV
)
export Gfx_UAZ_469_CMD_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_CMD_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_CMD_VDV_SOV
)
export Gfx_UAZ_469_Fagot_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_Fagot_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_Fagot_DDR
)
export Gfx_UAZ_469_Fagot_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_Fagot_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_Fagot_POL
)
export Gfx_UAZ_469_Fagot_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_Fagot_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_Fagot_Para_POL
)
export Gfx_UAZ_469_Konkurs_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_Konkurs_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_Konkurs_VDV_SOV
)
export Gfx_UAZ_469_MP_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_MP_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_MP_SOV
)
export Gfx_UAZ_469_Reco_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_Reco_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_Reco_DDR + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Reco_DDR_Tourelle_01
            )
        ),
    ]
)
export Gfx_UAZ_469_Reco_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_Reco_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_Reco_POL + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Reco_POL_Tourelle_01
            )
        ),
    ]
)
export Gfx_UAZ_469_Reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_Reco_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_Reco_SOV + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Reco_SOV_Tourelle_01
            )
        ),
    ]
)
export Gfx_UAZ_469_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_SOV
)
export Gfx_UAZ_469_SPG9_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_SPG9_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_SPG9_DDR
)
export Gfx_UAZ_469_SPG9_FJ_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_SPG9_FJ_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_SPG9_FJ_DDR
)
export Gfx_UAZ_469_SPG9_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_SPG9_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_SPG9_Para_POL
)
export Gfx_UAZ_469_SPG9_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_SPG9_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_SPG9_SOV
)
export Gfx_UAZ_469_SPG9_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_SPG9_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_SPG9_VDV_SOV
)
export Gfx_UAZ_469_supply_Para_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_supply_Para_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_supply_Para_POL
)
export Gfx_UAZ_469_supply_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_supply_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_supply_SOV
)
export Gfx_UAZ_469_supply_VDV_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_supply_VDV_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_supply_VDV_SOV
)
export Gfx_UAZ_469_trans_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_trans_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_trans_DDR
)
export Gfx_UAZ_469_trans_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_UAZ_469_trans_POL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_UAZ_469_trans_POL
)
export Gfx_Unimog_S_404_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Unimog_S_404_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Unimog_S_404_RFA
)
export Gfx_Unimog_U1350L_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Unimog_U1350L_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Unimog_U1350L_BEL
)
export Gfx_Unimog_U1350L_Para_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Unimog_U1350L_Para_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Unimog_U1350L_Para_BEL + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_Unimog_U1350L_Para_BEL_Tourelle_01
            )
        ),
    ]
)
export Gfx_Unimog_U1350L_supply_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Unimog_U1350L_supply_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Unimog_U1350L_supply_BEL
)
export Gfx_Unimog_supply_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Unimog_supply_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Unimog_supply_BEL
)
export Gfx_Unimog_trans_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Unimog_trans_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Unimog_trans_BEL
)
export Gfx_Unimog_trans_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Unimog_trans_RFA
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Unimog_trans_RFA
)
export Gfx_Ural_4320_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Ural_4320_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Ural_4320_Metla_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Ural_4320_Metla_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Ural_4320_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Ural_4320_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Ural_4320_ZPU_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Ural_4320_ZPU_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Ural_4320_ZPU_SOV
)
export Gfx_Ural_4320_ZU_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Ural_4320_ZU_SOV
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Ural_4320_ZU_SOV
)
export Gfx_Ural_4320_trans_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Ural_4320_trans_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_VAB_CMD_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VAB_CMD_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VAB_CMD_FR + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_CMD_FR_Tourelle_01
            )
        ),
    ]
)
export Gfx_VAB_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VAB_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VAB_FR + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_FR_Tourelle_01
            )
        ),
    ]
)
export Gfx_VAB_HOT_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VAB_HOT_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VAB_HOT_FR

    SubDepictionGenerators = [ SubGenerators_Showroom_VAB_HOT_FR ]
)
export Gfx_VAB_MILAN_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VAB_MILAN_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VAB_MILAN_FR + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_02",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_MILAN_FR_Tourelle_02
            )
        ),
    ]
)
export Gfx_VAB_Mortar_81_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VAB_Mortar_81_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VAB_Mortar_81_FR
)
export Gfx_VAB_RASIT_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VAB_RASIT_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VAB_RASIT_FR
)
export Gfx_VAB_Reserve_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VAB_Reserve_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VAB_Reserve_FR + 
    [
        TSubDepiction
        (
            Anchors = 
            [
                "base_tourelle_01",
            ]
            Depiction = ShowroomDepictionTemplateInternal
            (
                MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_Reserve_FR_Tourelle_01
            )
        ),
    ]
)
export Gfx_VAB_T20_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VAB_T20_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_VBL_MILAN_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VBL_MILAN_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VBL_MILAN_FR
)
export Gfx_VBL_PC_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VBL_PC_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VBL_PC_FR
)
export Gfx_VBL_Reco_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VBL_Reco_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VBL_Reco_FR
)
export Gfx_VIB_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VIB_FR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_VLRA_20mm_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLRA_20mm_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLRA_20mm_FR
)
export Gfx_VLRA_HMG_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLRA_HMG_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLRA_HMG_FR
)
export Gfx_VLRA_MILAN_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLRA_MILAN_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLRA_MILAN_FR
)
export Gfx_VLRA_Mistral_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLRA_Mistral_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLRA_Mistral_FR
)
export Gfx_VLRA_Mortier81_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLRA_Mortier81_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLRA_Mortier81_FR
)
export Gfx_VLRA_supply_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLRA_supply_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLRA_supply_FR
)
export Gfx_VLRA_trans_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLRA_trans_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLRA_trans_FR
)
export Gfx_VLTT_P4_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLTT_P4_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLTT_P4_FR
)
export Gfx_VLTT_P4_MILAN_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLTT_P4_MILAN_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLTT_P4_MILAN_FR
)
export Gfx_VLTT_P4_MILAN_para_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLTT_P4_MILAN_para_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLTT_P4_MILAN_para_FR
)
export Gfx_VLTT_P4_PC_FR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_VLTT_P4_PC_FR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_VLTT_P4_PC_FR
)
export Gfx_Volvo_N10_supply_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Volvo_N10_supply_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Volvo_N10_supply_BEL
)
export Gfx_Volvo_N10_trans_BEL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Volvo_N10_trans_BEL
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_Volvo_N10_trans_BEL
)
export Gfx_W50_LA_A_25mm_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_W50_LA_A_25mm_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_W50_LA_A_25mm_DDR
)
export Gfx_W50_LA_A_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_W50_LA_A_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_W50_LA_A_DDR
)
export Gfx_Wiesel_20mm_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Wiesel_20mm_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_Wiesel_TOW_RFA_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_Wiesel_TOW_RFA
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ZSU_23_Shilka_Afghan_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ZSU_23_Shilka_Afghan_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ZSU_23_Shilka_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ZSU_23_Shilka_DDR
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ZSU_23_Shilka_POL_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ZSU_23_Shilka_POL
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ZSU_23_Shilka_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ZSU_23_Shilka_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ZSU_23_Shilka_reco_SOV_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ZSU_23_Shilka_reco_SOV
    Selector = SpecificVehicleDepictionSelector
)
export Gfx_ZSU_57_2_DDR_Showroom is ShowroomVehicleDepictionTemplate
(
    Alternatives = Alternatives_ZSU_57_2_DDR
    Selector = SpecificVehicleDepictionSelector
    SubDepictions = HumanSubDepictionsShowroom_ZSU_57_2_DDR
)
