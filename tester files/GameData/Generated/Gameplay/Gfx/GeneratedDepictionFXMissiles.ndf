// Ne pas éditer, ce fichier est généré par DepictionFxMissilesFileWriter


FXWeapon_AA_AIM120A_AMRAAM is FXWeapon_0
FXWeapon_AA_AIM132_ASRAAM is FXWeapon_1
FXWeapon_AA_AIM7F_Sparrow is FXWeapon_0
FXWeapon_AA_AIM7M_Sparrow is FXWeapon_0
FXWeapon_AA_AIM9J_Sidewinder is FXWeapon_1
FXWeapon_AA_AIM9L_Sidewinder is FXWeapon_1
FXWeapon_AA_AIM9M_Sidewinder is FXWeapon_1
FXWeapon_AA_AIM9P_Sidewinder is FXWeapon_1
FXWeapon_AA_AIM9P_Sidewinder_x2 is FXWeapon_1
FXWeapon_AA_Matra_R530 is FXWeapon_0
FXWeapon_AA_Matra_Super_530D is FXWeapon_0
FXWeapon_AA_Matra_Super_530F is FXWeapon_0
FXWeapon_AA_R13M is FXWeapon_1
FXWeapon_AA_R23R_Vympel is FXWeapon_0
FXWeapon_AA_R24MR_Vympel is FXWeapon_0
FXWeapon_AA_R24R_Vympel is FXWeapon_0
FXWeapon_AA_R27ER_Vympel is FXWeapon_0
FXWeapon_AA_R27R_Vympel is FXWeapon_0
FXWeapon_AA_R27T_Vympel is FXWeapon_0
FXWeapon_AA_R33_Vympel is FXWeapon_0
FXWeapon_AA_R37_Vympel is FXWeapon_0
FXWeapon_AA_R3R is FXWeapon_1
FXWeapon_AA_R40RD1 is FXWeapon_0
FXWeapon_AA_R40TD1 is FXWeapon_0
FXWeapon_AA_R550_Magic is FXWeapon_1
FXWeapon_AA_R550_Magic_II is FXWeapon_1
FXWeapon_AA_R60_Vympel is FXWeapon_1
FXWeapon_AA_R60_Vympel_helo is FXWeapon_1
FXWeapon_AA_R60M_Vympel is FXWeapon_1
FXWeapon_AA_R60M_Vympel_x2 is FXWeapon_1
FXWeapon_AA_R60M_Vympel_helo is FXWeapon_1
FXWeapon_AA_R73_Vympel is FXWeapon_1
FXWeapon_AA_R77_Vympel is FXWeapon_0
FXWeapon_AA_R98MR is FXWeapon_0
FXWeapon_AA_R98MT is FXWeapon_0
FXWeapon_AA_Skyflash is FXWeapon_0
FXWeapon_AA_Skyflash_SuperTEMP is FXWeapon_0
FXWeapon_AGM_9K121_Vikhr is FXWeapon_2
FXWeapon_AGM_9K121_Vikhr_x12 is FXWeapon_2
FXWeapon_AGM_9K121_Vikhr_x16 is FXWeapon_2
FXWeapon_AGM_9K121_Vikhr_x16_avion is FXWeapon_2
FXWeapon_AGM_9M114M_Ataka_x4 is FXWeapon_3
FXWeapon_AGM_9M114M_Ataka_x8 is FXWeapon_3
FXWeapon_AGM_9M114M_KokonM is FXWeapon_3
FXWeapon_AGM_9M114M_KokonM_x16 is FXWeapon_3
FXWeapon_AGM_9M114M_KokonM_x2 is FXWeapon_3
FXWeapon_AGM_9M114M_KokonM_x4 is FXWeapon_3
FXWeapon_AGM_9M114M_KokonM_x6 is FXWeapon_3
FXWeapon_AGM_9M114M_KokonM_x8 is FXWeapon_3
FXWeapon_AGM_9M14_MalyutkaP_x4 is FXWeapon_3
FXWeapon_AGM_9M14_MalyutkaP_x6 is FXWeapon_3
FXWeapon_AGM_9M17P_FalangaP is FXWeapon_4
FXWeapon_AGM_9M17P_FalangaP_x4 is FXWeapon_4
FXWeapon_AGM_9M17P_FalangaP_x6 is FXWeapon_4
FXWeapon_AGM_AGM114A is FXWeapon_5
FXWeapon_AGM_AGM114A_x16 is FXWeapon_5
FXWeapon_AGM_AGM114A_x2_salve is FXWeapon_5
FXWeapon_AGM_AGM114A_x2_sol is FXWeapon_5
FXWeapon_AGM_AGM114A_x4 is FXWeapon_5
FXWeapon_AGM_AGM114A_x8 is FXWeapon_5
FXWeapon_AGM_AGM45_Shrike is FXWeapon_6
FXWeapon_AGM_AGM65B_Maverick is FXWeapon_6
FXWeapon_AGM_AGM65D_Maverick is FXWeapon_6
FXWeapon_AGM_AGM65E_Maverick is FXWeapon_6
FXWeapon_AGM_AGM88_HARM is FXWeapon_6
FXWeapon_AGM_AJ_168_x2 is FXWeapon_6
FXWeapon_AGM_ALARM is FXWeapon_6
FXWeapon_AGM_ARMAT is FXWeapon_6
FXWeapon_AGM_AS11_x2 is FXWeapon_3
FXWeapon_AGM_AS11_x2_air is FXWeapon_3
FXWeapon_AGM_AS11_x4 is FXWeapon_3
FXWeapon_AGM_AS30 is FXWeapon_6
FXWeapon_AGM_AS30L is FXWeapon_6
FXWeapon_AGM_AS37_Martel is FXWeapon_6
FXWeapon_AGM_AS37_Martel_x2 is FXWeapon_6
FXWeapon_AGM_BGM71_TOW_x8 is FXWeapon_3
FXWeapon_AGM_BGM71C_FITOW_x8 is FXWeapon_3
FXWeapon_AGM_BGM71C_ITOW_x8 is FXWeapon_3
FXWeapon_AGM_BGM71D_TOW_2_x4 is FXWeapon_3
FXWeapon_AGM_BGM71D_TOW_2_x8 is FXWeapon_3
FXWeapon_AGM_HOT1 is FXWeapon_3
FXWeapon_AGM_HOT1_x4 is FXWeapon_3
FXWeapon_AGM_HOT1_x6 is FXWeapon_3
FXWeapon_AGM_HOT2 is FXWeapon_3
FXWeapon_AGM_HOT2_x4 is FXWeapon_3
FXWeapon_AGM_HOT2_x6 is FXWeapon_3
FXWeapon_AGM_Kh23M is FXWeapon_6
FXWeapon_AGM_Kh23M_helo is FXWeapon_6
FXWeapon_AGM_Kh25ML is FXWeapon_6
FXWeapon_AGM_Kh25MP is FXWeapon_6
FXWeapon_AGM_Kh25MPU is FXWeapon_6
FXWeapon_AGM_Kh28_X28 is FXWeapon_6
FXWeapon_AGM_Kh29D is FXWeapon_6
FXWeapon_AGM_Kh29L is FXWeapon_6
FXWeapon_AGM_Kh29T is FXWeapon_6
FXWeapon_AGM_Kh31P is FXWeapon_6
FXWeapon_AGM_Kh58U is FXWeapon_6
FXWeapon_AGM_SS11_TCA_x4 is FXWeapon_3
FXWeapon_AGM_SS11_x4 is FXWeapon_3
FXWeapon_ATGM_9K111_Fagot is FXWeapon_4
FXWeapon_ATGM_9K111M_Fagot_M is FXWeapon_4
FXWeapon_ATGM_9K121_Vikhr is FXWeapon_7
FXWeapon_ATGM_9M112_1_Kobra is FXWeapon_4
FXWeapon_ATGM_9M113_Konkurs_BMP2 is FXWeapon_4
FXWeapon_ATGM_9M113_Konkurs_x5 is FXWeapon_4
FXWeapon_ATGM_9M113_KonkursM is FXWeapon_4
FXWeapon_ATGM_9M113_KonkursM_late is FXWeapon_4
FXWeapon_ATGM_9M113_KonkursM_late_x5 is FXWeapon_4
FXWeapon_ATGM_9M114_Shturm_MTLB is FXWeapon_4
FXWeapon_ATGM_9M114M_Ataka is FXWeapon_4
FXWeapon_ATGM_9M114M_KokonM is FXWeapon_4
FXWeapon_ATGM_9M117_Bastion is FXWeapon_7
FXWeapon_ATGM_9M117M_Arkan is FXWeapon_7
FXWeapon_ATGM_9M119M_Refleks is FXWeapon_7
FXWeapon_ATGM_9M119M_Svir is FXWeapon_7
FXWeapon_ATGM_9M128_Agona is FXWeapon_4
FXWeapon_ATGM_9M14_MalyutkaP is FXWeapon_8
FXWeapon_ATGM_9M14_MalyutkaP_x2 is FXWeapon_8
FXWeapon_ATGM_9M14_MalyutkaP_x4 is FXWeapon_8
FXWeapon_ATGM_9M14_MalyutkaP_x6 is FXWeapon_8
FXWeapon_ATGM_9k111M_Faktoriya is FXWeapon_4
FXWeapon_ATGM_ADATS is FXWeapon_5
FXWeapon_ATGM_AS11_x4 is FXWeapon_8
FXWeapon_ATGM_BGM71_TOW is FXWeapon_4
FXWeapon_ATGM_BGM71_TOW_x2 is FXWeapon_4
FXWeapon_ATGM_BGM71_TOW_x4 is FXWeapon_4
FXWeapon_ATGM_BGM71_TOW_x8 is FXWeapon_4
FXWeapon_ATGM_BGM71C_ITOW is FXWeapon_4
FXWeapon_ATGM_BGM71C_ITOW_x2 is FXWeapon_4
FXWeapon_ATGM_BGM71C_ITOW_x4 is FXWeapon_4
FXWeapon_ATGM_BGM71C_ITOW_x8 is FXWeapon_4
FXWeapon_ATGM_BGM71D_TOW_2 is FXWeapon_4
FXWeapon_ATGM_BGM71D_TOW_2_x2 is FXWeapon_4
FXWeapon_ATGM_BGM71D_TOW_2_x4 is FXWeapon_4
FXWeapon_ATGM_BGM71D_TOW_2_x8 is FXWeapon_4
FXWeapon_ATGM_BGM71D_TOW_2A is FXWeapon_4
FXWeapon_ATGM_BGM71D_TOW_2A_x2 is FXWeapon_4
FXWeapon_ATGM_HOT1 is FXWeapon_4
FXWeapon_ATGM_HOT1_x4 is FXWeapon_4
FXWeapon_ATGM_HOT1_x6 is FXWeapon_4
FXWeapon_ATGM_HOT2 is FXWeapon_4
FXWeapon_ATGM_HOT2_x2 is FXWeapon_4
FXWeapon_ATGM_HOT2_x3 is FXWeapon_4
FXWeapon_ATGM_HOT2_x4 is FXWeapon_4
FXWeapon_ATGM_HOT2_x6 is FXWeapon_4
FXWeapon_ATGM_MGM551C_Shillelagh is FXWeapon_4
FXWeapon_ATGM_MILAN is FXWeapon_4
FXWeapon_ATGM_MILAN_2 is FXWeapon_4
FXWeapon_ATGM_MILAN_2_x2 is FXWeapon_4
FXWeapon_ATGM_Swingfire is FXWeapon_4
FXWeapon_ATGM_Swingfire_x2 is FXWeapon_4
FXWeapon_ATGM_Swingfire_x5 is FXWeapon_4
FXWeapon_ATGM_9K115_Metis_M is FXWeapon_4
FXWeapon_ATGM_Eryx is FXWeapon_4
FXWeapon_Bomb_BGL_400_x1 is FXWeapon_9
FXWeapon_Bomb_BGL_400_x2 is FXWeapon_9
FXWeapon_Bomb_CPU_123_x1 is FXWeapon_9
FXWeapon_Bomb_CPU_123_x2 is FXWeapon_9
FXWeapon_Bomb_GBU_10_x1 is FXWeapon_9
FXWeapon_Bomb_GBU_10_x2 is FXWeapon_9
FXWeapon_Bomb_GBU_12_x1 is FXWeapon_9
FXWeapon_Bomb_GBU_12_x2 is FXWeapon_9
FXWeapon_Bomb_GBU_27_x1 is FXWeapon_9
FXWeapon_Bomb_GBU_27_x2 is FXWeapon_9
FXWeapon_Bomb_KAB_1500Kr_x1 is FXWeapon_9
FXWeapon_Bomb_KAB_1500Kr_x2 is FXWeapon_9
FXWeapon_Bomb_KAB_1500L_x1 is FXWeapon_9
FXWeapon_Bomb_KAB_1500L_x2 is FXWeapon_9
FXWeapon_Bomb_KAB_1500L_x3 is FXWeapon_9
FXWeapon_Bomb_KAB_500Kr_x1 is FXWeapon_9
FXWeapon_Bomb_KAB_500Kr_x2 is FXWeapon_9
FXWeapon_Bomb_KAB_500L_x1 is FXWeapon_9
FXWeapon_Bomb_KAB_500L_x2 is FXWeapon_9
FXWeapon_Bomb_KAB_500L_x4 is FXWeapon_9
FXWeapon_FakeRoquette_S_24 is FXWeapon_10
FXWeapon_FakeRoquette_S_24_helo is FXWeapon_10
FXWeapon_Javelin is FXWeapon_10
FXWeapon_Javelin_LML is FXWeapon_10
FXWeapon_M47_DRAGON is FXWeapon_8
FXWeapon_M47_DRAGON_Bipied is FXWeapon_8
FXWeapon_M47_DRAGON_II is FXWeapon_4
FXWeapon_MANPAD_Blowpipe is FXWeapon_10
FXWeapon_MANPAD_FIM43 is FXWeapon_10
FXWeapon_MANPAD_FIM92 is FXWeapon_10
FXWeapon_MANPAD_FIM92_A is FXWeapon_10
FXWeapon_MANPAD_Javelin is FXWeapon_10
FXWeapon_MANPAD_Strela2M is FXWeapon_10
FXWeapon_MANPAD_Strela3 is FXWeapon_10
FXWeapon_MANPAD_igla is FXWeapon_10
FXWeapon_Mistral is FXWeapon_10
FXWeapon_Mistral_Celtic_x4 is FXWeapon_10
FXWeapon_SAM_9M311_Tunguska_x8 is FXWeapon_11
FXWeapon_SAM_9M33 is FXWeapon_11
FXWeapon_SAM_9M330_Tor_x8 is FXWeapon_12
FXWeapon_SAM_9M336_x3 is FXWeapon_13
FXWeapon_SAM_9M33M2_x6 is FXWeapon_11
FXWeapon_SAM_9M38M1_x4 is FXWeapon_13
FXWeapon_SAM_9M8M3_x2 is FXWeapon_14
FXWeapon_SAM_ADATS is FXWeapon_5
FXWeapon_SAM_Aspide_1A_x4 is FXWeapon_11
FXWeapon_SAM_FASTA_Strela2M_x4 is FXWeapon_10
FXWeapon_SAM_FASTA_Strela2M_x4_TOWED is FXWeapon_10
FXWeapon_SAM_FIM92_Stinger_CS is FXWeapon_10
FXWeapon_SAM_FIM92_Stinger_CS_x4 is FXWeapon_10
FXWeapon_SAM_FIM92_Stinger_x4 is FXWeapon_10
FXWeapon_SAM_FIM92_Stinger_x8 is FXWeapon_10
FXWeapon_SAM_FIM92C is FXWeapon_10
FXWeapon_SAM_I_Hawk is FXWeapon_11
FXWeapon_SAM_I_Hawk_x3 is FXWeapon_13
FXWeapon_SAM_Igla is FXWeapon_10
FXWeapon_SAM_IglaN is FXWeapon_10
FXWeapon_SAM_IglaN_1M is FXWeapon_10
FXWeapon_SAM_IglaV is FXWeapon_10
FXWeapon_SAM_IglaV_x4 is FXWeapon_10
FXWeapon_SAM_MIM72A is FXWeapon_11
FXWeapon_SAM_MIM72F is FXWeapon_11
FXWeapon_SAM_MIM72G is FXWeapon_11
FXWeapon_SAM_Mistral is FXWeapon_10
FXWeapon_SAM_R440_x4 is FXWeapon_11
FXWeapon_SAM_RAPIER is FXWeapon_11
FXWeapon_SAM_RAPIER_DARKFIRE_x6 is FXWeapon_11
FXWeapon_SAM_RAPIER_FSA_x4 is FXWeapon_11
FXWeapon_SAM_RAPIER_x4 is FXWeapon_11
FXWeapon_SAM_RAPIER_x8 is FXWeapon_11
FXWeapon_SAM_ROLAND is FXWeapon_11
FXWeapon_SAM_ROLAND_1 is FXWeapon_11
FXWeapon_SAM_ROLAND_1_x2 is FXWeapon_11
FXWeapon_SAM_ROLAND_2 is FXWeapon_11
FXWeapon_SAM_ROLAND_2_x2 is FXWeapon_11
FXWeapon_SAM_ROLAND_3 is FXWeapon_11
FXWeapon_SAM_ROLAND_3_x2 is FXWeapon_11
FXWeapon_SAM_Strela1_x4 is FXWeapon_10
FXWeapon_SAM_Strela10_x4 is FXWeapon_10
FXWeapon_SAM_Strela10M3_x4 is FXWeapon_10
FXWeapon_SAM_Strela2_x12 is FXWeapon_10
FXWeapon_SAM_Strela2_x2 is FXWeapon_10
FXWeapon_SAM_Strela2_x4 is FXWeapon_10
FXWeapon_SAM_Strela2M_x2 is FXWeapon_10
FXWeapon_0 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AA_gros )
FXWeapon_1 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AA_moyen )
FXWeapon_2 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AGM_generation3 )
FXWeapon_3 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AGM_generation2 )
FXWeapon_4 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_ATGM_Gen2 )
FXWeapon_5 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AGM_generation4 )
FXWeapon_6 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_gros )
FXWeapon_7 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_ATGM_Gen3 )
FXWeapon_8 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_ATGM_Gen1 )
FXWeapon_9 is FXMissileFiring( Action = $/GFX/GameFx/fx_depart_bombe_guidee )
FXWeapon_10 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AA_petit )
FXWeapon_11 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AA_moyen_sol_1 )
FXWeapon_12 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AA_TOR )
FXWeapon_13 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AA_big_sol_1 )
FXWeapon_14 is FXMissileFiring( Action = $/GFX/GameFx/fx_tir_missile_AA_very_big_sol_1 )
