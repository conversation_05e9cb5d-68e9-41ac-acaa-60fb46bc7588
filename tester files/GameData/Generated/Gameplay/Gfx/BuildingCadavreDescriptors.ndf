// Ne pas éditer, ce fichier est généré par BuildingCadavreDescriptorFileWriter_Specific


export Descriptor_UnitCadavre_FOB_BEL is TEntityDescriptor
(
    DescriptorId       = GUID:{54b82031-049f-472e-940e-896475d27013}
    ClassNameForDebug  = 'BuildingCadavre_FOB_BEL'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_BEL/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_BEL/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
export Descriptor_UnitCadavre_FOB_DDR is TEntityDescriptor
(
    DescriptorId       = GUID:{1d614751-10ed-439e-adcf-074cd077bb91}
    ClassNameForDebug  = 'BuildingCadavre_FOB_DDR'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_DDR/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_DDR/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
export Descriptor_UnitCadavre_FOB_FR is TEntityDescriptor
(
    DescriptorId       = GUID:{479b599b-449e-43a7-828d-c626fc56d7be}
    ClassNameForDebug  = 'BuildingCadavre_FOB_FR'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_FR/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_FR/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
export Descriptor_UnitCadavre_FOB_NL is TEntityDescriptor
(
    DescriptorId       = GUID:{ee7572e9-31a2-4d9e-889a-8fcac7493845}
    ClassNameForDebug  = 'BuildingCadavre_FOB_NL'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_NL/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_NL/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
export Descriptor_UnitCadavre_FOB_POL is TEntityDescriptor
(
    DescriptorId       = GUID:{c551e822-2ba5-4498-85f2-02480cd5821c}
    ClassNameForDebug  = 'BuildingCadavre_FOB_POL'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_POL/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_POL/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
export Descriptor_UnitCadavre_FOB_RFA is TEntityDescriptor
(
    DescriptorId       = GUID:{f2f8d4f5-5023-45df-bd1d-f1c38b87ba5d}
    ClassNameForDebug  = 'BuildingCadavre_FOB_RFA'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_RFA/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_RFA/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
export Descriptor_UnitCadavre_FOB_SOV is TEntityDescriptor
(
    DescriptorId       = GUID:{bee6f6eb-ccc9-4c61-a7ad-121c5bbf514e}
    ClassNameForDebug  = 'BuildingCadavre_FOB_SOV'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_SOV/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_SOV/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
export Descriptor_UnitCadavre_FOB_UK is TEntityDescriptor
(
    DescriptorId       = GUID:{c1603df4-5bbd-4ec3-87ac-b7276c9ebefa}
    ClassNameForDebug  = 'BuildingCadavre_FOB_UK'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_UK/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_UK/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
export Descriptor_UnitCadavre_FOB_US is TEntityDescriptor
(
    DescriptorId       = GUID:{06786af2-d58c-4404-a6bf-144080ca0ec9}
    ClassNameForDebug  = 'BuildingCadavre_FOB_US'
    ModulesDescriptors = [
        ~/CadavreFlagsModuleDescriptor,
        ~/BuildingCadavrePositionModuleDescriptor,
        ~/LinkTeamModuleDescriptor,
        BuildingCadavreModuleDescriptor
        (
            CadavreDuration = $/GFX/Unit/CadavreDurationApresFeu
            ModuleDescriptorsToSteal =
            [
                ~/Descriptor_Unit_FOB_US/ApparenceModel,
            ]
        ),
        ~/Descriptor_Unit_FOB_US/ApparenceModel,
        ~/PackSignauxModuleDescriptor,
        ~/DebugModuleDescriptor,
    ]
)
