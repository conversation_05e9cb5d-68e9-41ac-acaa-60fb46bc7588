// Ne pas éditer, ce fichier est généré par MissileCarriageFileWriter

eAAM is 1
eAGM is 2
eMountingMissile is 0
eMountingPod     is 1
eMountingBomb    is 2


export MissileCarriage_2K11_KRUG_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_2K11_KRUG_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_2K11_KRUG_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_2K11_KRUG_DDR.WeaponInfos
)


export MissileCarriage_2K11_KRUG_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_2K11_KRUG_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_2K11_KRUG_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_2K11_KRUG_POL.WeaponInfos
)


export MissileCarriage_2K11_KRUG_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K11_KRUG_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_2K11_KRUG_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_2K11_KRUG_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_2K11_KRUG_SOV.WeaponInfos
)


export MissileCarriage_2K12_KUB_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_2K12_KUB_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_2K12_KUB_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_2K12_KUB_DDR.WeaponInfos
)


export MissileCarriage_2K12_KUB_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_2K12_KUB_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_2K12_KUB_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_2K12_KUB_POL.WeaponInfos
)


export MissileCarriage_2K12_KUB_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_2K12_KUB_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_2K12_KUB_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_2K12_KUB_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_2K12_KUB_SOV.WeaponInfos
)


export MissileCarriage_AIFV_B_MILAN_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AIFV_B_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_AIFV_B_MILAN_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AIFV_B_MILAN_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_AIFV_B_MILAN_BEL.WeaponInfos
)


export MissileCarriage_AIFV_B_TOW_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AIFV_B_TOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_AIFV_B_TOW_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AIFV_B_TOW_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_AIFV_B_TOW_NL.WeaponInfos
)


export MissileCarriage_AMX_10_HOT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_10_HOT_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_AMX_10_HOT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AMX_10_HOT_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AMX_10_HOT_FR.WeaponInfos
)


export MissileCarriage_AMX_10_P_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_10_P_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_AMX_10_P_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AMX_10_P_MILAN_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_AMX_10_P_MILAN_FR.WeaponInfos
)


export MissileCarriage_AMX_13_mod56_MILAN_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AMX_13_mod56_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_AMX_13_mod56_MILAN_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AMX_13_mod56_MILAN_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_AMX_13_mod56_MILAN_BEL.WeaponInfos
)


export MissileCarriage_AT_T12R_Ruta_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12R_Ruta_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_AT_T12R_Ruta_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AT_T12R_Ruta_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_AT_T12R_Ruta_SOV.WeaponInfos
)


export MissileCarriage_AT_T12_Rapira_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12_Rapira_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_AT_T12_Rapira_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AT_T12_Rapira_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_AT_T12_Rapira_DDR.WeaponInfos
)


export MissileCarriage_AT_T12_Rapira_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AT_T12_Rapira_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_AT_T12_Rapira_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AT_T12_Rapira_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_AT_T12_Rapira_SOV.WeaponInfos
)


export MissileCarriage_ATteam_Fagot_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Fagot_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Fagot_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Fagot_DDR.WeaponInfos
)


export MissileCarriage_ATteam_Fagot_FJ_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_FJ_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Fagot_FJ_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Fagot_FJ_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Fagot_FJ_DDR.WeaponInfos
)


export MissileCarriage_ATteam_Fagot_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Fagot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Fagot_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Fagot_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Fagot_SOV.WeaponInfos
)


export MissileCarriage_ATteam_ITOW_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_ITOW_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_ITOW_NG_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_ITOW_NG_US.WeaponInfos
)


export MissileCarriage_ATteam_ITOW_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_ITOW_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_ITOW_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_ITOW_NL.WeaponInfos
)


export MissileCarriage_ATteam_ITOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_ITOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_ITOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_ITOW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_ITOW_US.WeaponInfos
)


export MissileCarriage_ATteam_KonkursM_TTsko_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_KonkursM_TTsko_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_KonkursM_TTsko_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_KonkursM_TTsko_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_KonkursM_TTsko_SOV.WeaponInfos
)


export MissileCarriage_ATteam_Konkurs_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Konkurs_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Konkurs_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Konkurs_DDR.WeaponInfos
)


export MissileCarriage_ATteam_Konkurs_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Konkurs_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Konkurs_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Konkurs_SOV.WeaponInfos
)


export MissileCarriage_ATteam_Konkurs_TTsko_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Konkurs_TTsko_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Konkurs_TTsko_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Konkurs_TTsko_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Konkurs_TTsko_SOV.WeaponInfos
)


export MissileCarriage_ATteam_Milan_1_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_1_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_1_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_1_BEL.WeaponInfos
)


export MissileCarriage_ATteam_Milan_1_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_1_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_1_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_1_FR.WeaponInfos
)


export MissileCarriage_ATteam_Milan_1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_1_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_1_RFA.WeaponInfos
)


export MissileCarriage_ATteam_Milan_1_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_1_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_1_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_1_UK.WeaponInfos
)


export MissileCarriage_ATteam_Milan_1_para_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_para_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_1_para_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_1_para_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_1_para_BEL.WeaponInfos
)


export MissileCarriage_ATteam_Milan_1_para_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_1_para_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_1_para_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_1_para_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_1_para_FR.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_BEL.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_FR.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_RFA.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_RIMa_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_RIMa_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_RIMa_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_RIMa_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_RIMa_FR.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_UK.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_para_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_para_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_para_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_para_BEL.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_para_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_para_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_para_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_para_FR.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_para_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_para_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_para_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_para_RFA.WeaponInfos
)


export MissileCarriage_ATteam_Milan_2_para_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_Milan_2_para_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_Milan_2_para_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_Milan_2_para_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_Milan_2_para_UK.WeaponInfos
)


export MissileCarriage_ATteam_TOW2A_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2A_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_TOW2A_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_TOW2A_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_TOW2A_US.WeaponInfos
)


export MissileCarriage_ATteam_TOW2_Aero_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_Aero_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_TOW2_Aero_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_TOW2_Aero_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_TOW2_Aero_US.WeaponInfos
)


export MissileCarriage_ATteam_TOW2_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_TOW2_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_TOW2_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_TOW2_NL.WeaponInfos
)


export MissileCarriage_ATteam_TOW2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_TOW2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_TOW2_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_TOW2_US.WeaponInfos
)


export MissileCarriage_ATteam_TOW2_para_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW2_para_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_TOW2_para_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_TOW2_para_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_TOW2_para_US.WeaponInfos
)


export MissileCarriage_ATteam_TOW_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_TOW_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_TOW_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_TOW_NL.WeaponInfos
)


export MissileCarriage_ATteam_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_ATteam_TOW_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_ATteam_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_ATteam_TOW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_ATteam_TOW_US.WeaponInfos
)


export MissileCarriage_Atteam_Dragon_Marines_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Dragon_Marines_NL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Atteam_Dragon_Marines_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Atteam_Dragon_Marines_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Atteam_Dragon_Marines_NL.WeaponInfos
)


export MissileCarriage_Atteam_Fagot_DShV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_DShV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Atteam_Fagot_DShV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Atteam_Fagot_DShV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Atteam_Fagot_DShV_SOV.WeaponInfos
)


export MissileCarriage_Atteam_Fagot_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Atteam_Fagot_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Atteam_Fagot_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Atteam_Fagot_POL.WeaponInfos
)


export MissileCarriage_Atteam_Fagot_Para_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_Para_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Atteam_Fagot_Para_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Atteam_Fagot_Para_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Atteam_Fagot_Para_POL.WeaponInfos
)


export MissileCarriage_Atteam_Fagot_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Fagot_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Atteam_Fagot_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Atteam_Fagot_VDV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Atteam_Fagot_VDV_SOV.WeaponInfos
)


export MissileCarriage_Atteam_Konkurs_DShV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_DShV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Atteam_Konkurs_DShV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Atteam_Konkurs_DShV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Atteam_Konkurs_DShV_SOV.WeaponInfos
)


export MissileCarriage_Atteam_Konkurs_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Atteam_Konkurs_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Atteam_Konkurs_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Atteam_Konkurs_POL.WeaponInfos
)


export MissileCarriage_Atteam_Konkurs_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Atteam_Konkurs_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Atteam_Konkurs_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Atteam_Konkurs_VDV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Atteam_Konkurs_VDV_SOV.WeaponInfos
)


export MissileCarriage_BMD_1P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_1P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMD_1P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMD_1P_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMD_1P_SOV.WeaponInfos
)


export MissileCarriage_BMD_2_CMD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_2_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMD_2_CMD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMD_2_CMD_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMD_2_CMD_SOV.WeaponInfos
)


export MissileCarriage_BMD_2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMD_2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMD_2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMD_2_SOV.WeaponInfos
)


export MissileCarriage_BMD_3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_3_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMD_3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMD_3_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMD_3_SOV.WeaponInfos
)


export MissileCarriage_BMD_3_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMD_3_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMD_3_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMD_3_reco_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMD_3_reco_SOV.WeaponInfos
)


export MissileCarriage_BMP_1PG_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1PG_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_1PG_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1PG_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1PG_SOV.WeaponInfos
)


export MissileCarriage_BMP_1P_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1P_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1P_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1P_DDR.WeaponInfos
)


export MissileCarriage_BMP_1P_Konkurs_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_1P_Konkurs_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1P_Konkurs_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1P_Konkurs_DDR.WeaponInfos
)


export MissileCarriage_BMP_1P_Konkurs_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_1P_Konkurs_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1P_Konkurs_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1P_Konkurs_SOV.WeaponInfos
)


export MissileCarriage_BMP_1P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_1P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1P_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1P_SOV.WeaponInfos
)


export MissileCarriage_BMP_1P_reco_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1P_reco_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1P_reco_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1P_reco_DDR.WeaponInfos
)


export MissileCarriage_BMP_1P_reco_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1P_reco_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1P_reco_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1P_reco_POL.WeaponInfos
)


export MissileCarriage_BMP_1P_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1P_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_1P_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1P_reco_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1P_reco_SOV.WeaponInfos
)


export MissileCarriage_BMP_1_CMD_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1_CMD_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1_CMD_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1_CMD_DDR.WeaponInfos
)


export MissileCarriage_BMP_1_CMD_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1_CMD_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1_CMD_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1_CMD_POL.WeaponInfos
)


export MissileCarriage_BMP_1_CMD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1_CMD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1_CMD_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1_CMD_SOV.WeaponInfos
)


export MissileCarriage_BMP_1_SP2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1_SP2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1_SP2_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1_SP2_DDR.WeaponInfos
)


export MissileCarriage_BMP_1_SP2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1_SP2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1_SP2_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1_SP2_POL.WeaponInfos
)


export MissileCarriage_BMP_1_SP2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1_SP2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1_SP2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1_SP2_SOV.WeaponInfos
)


export MissileCarriage_BMP_1_SP2_reco_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_1_SP2_reco_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=4 ),
    ]
)

export MissileCarriage_BMP_1_SP2_reco_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_1_SP2_reco_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_1_SP2_reco_POL.WeaponInfos
)


export MissileCarriage_BMP_2AG_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2AG_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_2AG_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_2AG_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_2AG_SOV.WeaponInfos
)


export MissileCarriage_BMP_2D_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2D_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_2D_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_2D_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_2D_SOV.WeaponInfos
)


export MissileCarriage_BMP_2D_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2D_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_2D_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_2D_reco_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_2D_reco_SOV.WeaponInfos
)


export MissileCarriage_BMP_2_CMD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_2_CMD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_2_CMD_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_2_CMD_SOV.WeaponInfos
)


export MissileCarriage_BMP_2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_2_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_2_DDR.WeaponInfos
)


export MissileCarriage_BMP_2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_2_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_2_POL.WeaponInfos
)


export MissileCarriage_BMP_2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_2_SOV.WeaponInfos
)


export MissileCarriage_BMP_2_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_2_reco_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_BMP_2_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_2_reco_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_2_reco_SOV.WeaponInfos
)


export MissileCarriage_BMP_3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BMP_3_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BMP_3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BMP_3_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BMP_3_SOV.WeaponInfos
)


export MissileCarriage_BRDM_2_Konkurs_M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_M_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_2_Konkurs_M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_2_Konkurs_M_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_2_Konkurs_M_SOV.WeaponInfos
)


export MissileCarriage_BRDM_2_Konkurs_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_2_Konkurs_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_2_Konkurs_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_2_Konkurs_POL.WeaponInfos
)


export MissileCarriage_BRDM_2_Konkurs_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Konkurs_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_2_Konkurs_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_2_Konkurs_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_2_Konkurs_SOV.WeaponInfos
)


export MissileCarriage_BRDM_2_Malyu_P_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=18 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_2_Malyu_P_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_2_Malyu_P_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_2_Malyu_P_POL.WeaponInfos
)


export MissileCarriage_BRDM_2_Malyu_P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_2_Malyu_P_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=18 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_2_Malyu_P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_2_Malyu_P_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_2_Malyu_P_SOV.WeaponInfos
)


export MissileCarriage_BRDM_Konkurs_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Konkurs_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_Konkurs_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_Konkurs_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_Konkurs_DDR.WeaponInfos
)


export MissileCarriage_BRDM_Malyu_P_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Malyu_P_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=18 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_Malyu_P_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_Malyu_P_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_Malyu_P_DDR.WeaponInfos
)


export MissileCarriage_BRDM_Strela_1_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_Strela_1_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_Strela_1_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_Strela_1_DDR.WeaponInfos
)


export MissileCarriage_BRDM_Strela_1_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_Strela_1_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_Strela_1_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_Strela_1_POL.WeaponInfos
)


export MissileCarriage_BRDM_Strela_1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BRDM_Strela_1_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BRDM_Strela_1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BRDM_Strela_1_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_BRDM_Strela_1_SOV.WeaponInfos
)


export MissileCarriage_BTR_70_S5_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_70_S5_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_BTR_70_S5_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BTR_70_S5_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_BTR_70_S5_SOV.WeaponInfos
)


export MissileCarriage_BTR_70_S8_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_70_S8_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=20 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_BTR_70_S8_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BTR_70_S8_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_BTR_70_S8_SOV.WeaponInfos
)


export MissileCarriage_BTR_D_Robot_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_BTR_D_Robot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_BTR_D_Robot_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_BTR_D_Robot_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_BTR_D_Robot_SOV.WeaponInfos
)


export MissileCarriage_Buk_9K37M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buk_9K37M_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Buk_9K37M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Buk_9K37M_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Buk_9K37M_SOV.WeaponInfos
)


export MissileCarriage_CUCV_Hellfire_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CUCV_Hellfire_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_CUCV_Hellfire_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_CUCV_Hellfire_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_CUCV_Hellfire_US.WeaponInfos
)


export MissileCarriage_Crotale_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Crotale_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Crotale_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Crotale_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Crotale_FR.WeaponInfos
)


export MissileCarriage_DCA_FASTA_4_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_FASTA_4_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_FASTA_4_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_FASTA_4_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_FASTA_4_DDR.WeaponInfos
)


export MissileCarriage_DCA_I_Hawk_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_I_Hawk_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_I_Hawk_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_I_Hawk_BEL.WeaponInfos
)


export MissileCarriage_DCA_I_Hawk_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_NL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_I_Hawk_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_I_Hawk_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_I_Hawk_NL.WeaponInfos
)


export MissileCarriage_DCA_I_Hawk_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_I_Hawk_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_I_Hawk_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_I_Hawk_RFA.WeaponInfos
)


export MissileCarriage_DCA_I_Hawk_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_I_Hawk_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_I_Hawk_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_I_Hawk_US.WeaponInfos
)


export MissileCarriage_DCA_I_Hawk_capture_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_I_Hawk_capture_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_I_Hawk_capture_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_I_Hawk_capture_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_I_Hawk_capture_DDR.WeaponInfos
)


export MissileCarriage_DCA_Javelin_LML_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Javelin_LML_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=9 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_Javelin_LML_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_Javelin_LML_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_Javelin_LML_UK.WeaponInfos
)


export MissileCarriage_DCA_Rapier_Darkfire_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_Darkfire_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_Rapier_Darkfire_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_Rapier_Darkfire_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_Rapier_Darkfire_UK.WeaponInfos
)


export MissileCarriage_DCA_Rapier_FSA_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_FSA_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_Rapier_FSA_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_Rapier_FSA_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_Rapier_FSA_UK.WeaponInfos
)


export MissileCarriage_DCA_Rapier_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_Rapier_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_Rapier_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_Rapier_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_Rapier_UK.WeaponInfos
)


export MissileCarriage_DCA_XM85_Chaparral_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_XM85_Chaparral_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_XM85_Chaparral_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_XM85_Chaparral_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_XM85_Chaparral_US.WeaponInfos
)


export MissileCarriage_DCA_XMIM_115A_Roland_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_XMIM_115A_Roland_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_DCA_XMIM_115A_Roland_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_XMIM_115A_Roland_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_XMIM_115A_Roland_US.WeaponInfos
)


export MissileCarriage_DCA_ZUR_23_2S_JOD_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_DCA_ZUR_23_2S_JOD_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_ZUR_23_2S_JOD_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_ZUR_23_2S_JOD_POL.WeaponInfos
)


export MissileCarriage_DCA_ZUR_23_2S_JOD_Para_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_DCA_ZUR_23_2S_JOD_Para_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_DCA_ZUR_23_2S_JOD_Para_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_DCA_ZUR_23_2S_JOD_Para_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_DCA_ZUR_23_2S_JOD_Para_POL.WeaponInfos
)


export MissileCarriage_FAV_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FAV_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_FAV_TOW_US_Tourelle_01 MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_FAV_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_FAV_TOW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_FAV_TOW_US.WeaponInfos
)


export MissileCarriage_FV102_Striker_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_FV102_Striker_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_FV102_Striker_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_FV102_Striker_BEL.WeaponInfos
)


export MissileCarriage_FV102_Striker_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_FV102_Striker_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_FV102_Striker_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_FV102_Striker_UK.WeaponInfos
)


export MissileCarriage_FV102_Striker_para_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV102_Striker_para_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_FV102_Striker_para_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_FV102_Striker_para_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_FV102_Striker_para_UK.WeaponInfos
)


export MissileCarriage_FV120_Spartan_MCT_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV120_Spartan_MCT_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_FV120_Spartan_MCT_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_FV120_Spartan_MCT_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_FV120_Spartan_MCT_UK.WeaponInfos
)


export MissileCarriage_FV432_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV432_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_FV432_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_FV432_MILAN_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_FV432_MILAN_UK.WeaponInfos
)


export MissileCarriage_FV438_Swingfire_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FV438_Swingfire_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_FV438_Swingfire_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_FV438_Swingfire_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_FV438_Swingfire_UK.WeaponInfos
)


export MissileCarriage_Faun_Kraka_TOW_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_Faun_Kraka_TOW_RFA_Tourelle_01 MissileCount=5 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Faun_Kraka_TOW_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Faun_Kraka_TOW_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Faun_Kraka_TOW_RFA.WeaponInfos
)


export MissileCarriage_Hibneryt_KG_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Hibneryt_KG_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Hibneryt_KG_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Hibneryt_KG_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Hibneryt_KG_POL.WeaponInfos
)


export MissileCarriage_Iltis_MILAN_2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_Tourelle_01 MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Iltis_MILAN_2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Iltis_MILAN_2_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Iltis_MILAN_2_RFA.WeaponInfos
)


export MissileCarriage_Iltis_MILAN_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_BEL_Tourelle_01 MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Iltis_MILAN_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Iltis_MILAN_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Iltis_MILAN_BEL.WeaponInfos
)


export MissileCarriage_Iltis_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Iltis_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_Iltis_MILAN_RFA_Tourelle_01 MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Iltis_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Iltis_MILAN_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Iltis_MILAN_RFA.WeaponInfos
)


export MissileCarriage_Jaguar_1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_1_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_1_RFA_Tourelle_01 MissileCount=20 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Jaguar_1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_1_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_1_RFA.WeaponInfos
)


export MissileCarriage_Jaguar_2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_Jaguar_2_RFA_Tourelle_01 MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Jaguar_2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_2_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_2_RFA.WeaponInfos
)


export MissileCarriage_LO_1800_FASTA_4_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LO_1800_FASTA_4_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_LO_1800_FASTA_4_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_LO_1800_FASTA_4_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_LO_1800_FASTA_4_DDR.WeaponInfos
)


export MissileCarriage_LSV_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LSV_MILAN_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_LSV_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_LSV_MILAN_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_LSV_MILAN_UK.WeaponInfos
)


export MissileCarriage_LUAZ_967M_Fagot_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_LUAZ_967M_Fagot_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_LUAZ_967M_Fagot_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_LUAZ_967M_Fagot_SOV.WeaponInfos
)


export MissileCarriage_LUAZ_967M_Fagot_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LUAZ_967M_Fagot_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_LUAZ_967M_Fagot_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_LUAZ_967M_Fagot_VDV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_LUAZ_967M_Fagot_VDV_SOV.WeaponInfos
)


export MissileCarriage_LandRover_MILAN_Para_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LandRover_MILAN_Para_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_LandRover_MILAN_Para_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_LandRover_MILAN_Para_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_LandRover_MILAN_Para_UK.WeaponInfos
)


export MissileCarriage_LandRover_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LandRover_MILAN_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_LandRover_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_LandRover_MILAN_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_LandRover_MILAN_UK.WeaponInfos
)


export MissileCarriage_LuAZ_967M_AA_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_LuAZ_967M_AA_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_LuAZ_967M_AA_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_LuAZ_967M_AA_VDV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_LuAZ_967M_AA_VDV_SOV.WeaponInfos
)


export MissileCarriage_M1025_Humvee_TOW_LUX is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_LUX_Tourelle_01 MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M1025_Humvee_TOW_LUX_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M1025_Humvee_TOW_LUX.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M1025_Humvee_TOW_LUX.WeaponInfos
)


export MissileCarriage_M1025_Humvee_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_US_Tourelle_01 MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M1025_Humvee_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M1025_Humvee_TOW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M1025_Humvee_TOW_US.WeaponInfos
)


export MissileCarriage_M1025_Humvee_TOW_para_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_M1025_Humvee_TOW_para_US_Tourelle_01 MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M1025_Humvee_TOW_para_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M1025_Humvee_TOW_para_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M1025_Humvee_TOW_para_US.WeaponInfos
)


export MissileCarriage_M113A1B_MILAN_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1B_MILAN_BEL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M113A1B_MILAN_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M113A1B_MILAN_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M113A1B_MILAN_BEL.WeaponInfos
)


export MissileCarriage_M113A1G_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1G_MILAN_RFA_Tourelle_01 MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M113A1G_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M113A1G_MILAN_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M113A1G_MILAN_RFA.WeaponInfos
)


export MissileCarriage_M113A1_Dragon_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_M113A1_Dragon_NG_US_Tourelle_02 MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M113A1_Dragon_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M113A1_Dragon_NG_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M113A1_Dragon_NG_US.WeaponInfos
)


export MissileCarriage_M113A1_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A1_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M113A1_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M113A1_TOW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M113A1_TOW_US.WeaponInfos
)


export MissileCarriage_M113A2_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113A2_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M113A2_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M113A2_TOW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M113A2_TOW_US.WeaponInfos
)


export MissileCarriage_M113_Dragon_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M113_Dragon_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_M113_Dragon_US_Tourelle_02 MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M113_Dragon_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M113_Dragon_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M113_Dragon_US.WeaponInfos
)


export MissileCarriage_M151A2_TOW_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M151A2_TOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_M151A2_TOW_NG_US_Tourelle_01 MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M151A2_TOW_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M151A2_TOW_NG_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M151A2_TOW_NG_US.WeaponInfos
)


export MissileCarriage_M201_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M201_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M201_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M201_MILAN_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M201_MILAN_FR.WeaponInfos
)


export MissileCarriage_M274_Mule_ITOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M274_Mule_ITOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M274_Mule_ITOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M274_Mule_ITOW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M274_Mule_ITOW_US.WeaponInfos
)


export MissileCarriage_M2A1_Bradley_IFV_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A1_Bradley_IFV_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M2A1_Bradley_IFV_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M2A1_Bradley_IFV_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M2A1_Bradley_IFV_US.WeaponInfos
)


export MissileCarriage_M2A1_Bradley_Leader_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A1_Bradley_Leader_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M2A1_Bradley_Leader_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M2A1_Bradley_Leader_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M2A1_Bradley_Leader_US.WeaponInfos
)


export MissileCarriage_M2A2_Bradley_IFV_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A2_Bradley_IFV_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M2A2_Bradley_IFV_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M2A2_Bradley_IFV_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M2A2_Bradley_IFV_US.WeaponInfos
)


export MissileCarriage_M2A2_Bradley_Leader_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2A2_Bradley_Leader_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M2A2_Bradley_Leader_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M2A2_Bradley_Leader_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M2A2_Bradley_Leader_US.WeaponInfos
)


export MissileCarriage_M2_Bradley_IFV_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M2_Bradley_IFV_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M2_Bradley_IFV_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M2_Bradley_IFV_NG_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M2_Bradley_IFV_NG_US.WeaponInfos
)


export MissileCarriage_M38A1_TOW_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M38A1_TOW_NL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M38A1_TOW_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M38A1_TOW_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M38A1_TOW_NL.WeaponInfos
)


export MissileCarriage_M3A1_Bradley_CFV_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M3A1_Bradley_CFV_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M3A1_Bradley_CFV_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M3A1_Bradley_CFV_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M3A1_Bradley_CFV_US.WeaponInfos
)


export MissileCarriage_M3A2_Bradley_CFV_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M3A2_Bradley_CFV_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_M3A2_Bradley_CFV_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M3A2_Bradley_CFV_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M3A2_Bradley_CFV_US.WeaponInfos
)


export MissileCarriage_M48_Chaparral_MIM72F_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M48_Chaparral_MIM72F_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M48_Chaparral_MIM72F_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M48_Chaparral_MIM72F_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M48_Chaparral_MIM72F_US.WeaponInfos
)


export MissileCarriage_M551A1_TTS_Sheridan_CMD_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_CMD_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M551A1_TTS_Sheridan_CMD_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M551A1_TTS_Sheridan_CMD_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M551A1_TTS_Sheridan_CMD_US.WeaponInfos
)


export MissileCarriage_M551A1_TTS_Sheridan_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M551A1_TTS_Sheridan_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M551A1_TTS_Sheridan_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M551A1_TTS_Sheridan_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M551A1_TTS_Sheridan_US.WeaponInfos
)


export MissileCarriage_M901A1_ITW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901A1_ITW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M901A1_ITW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M901A1_ITW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M901A1_ITW_US.WeaponInfos
)


export MissileCarriage_M901_TOW_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901_TOW_NG_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M901_TOW_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M901_TOW_NG_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M901_TOW_NG_US.WeaponInfos
)


export MissileCarriage_M901_TOW_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M901_TOW_US
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M901_TOW_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M901_TOW_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_M901_TOW_US.WeaponInfos
)


export MissileCarriage_M998_Avenger_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Avenger_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M998_Avenger_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M998_Avenger_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_M998_Avenger_US.WeaponInfos
)


export MissileCarriage_M998_Avenger_nonPara_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_M998_Avenger_nonPara_US
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_M998_Avenger_nonPara_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_M998_Avenger_nonPara_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_M998_Avenger_nonPara_US.WeaponInfos
)


export MissileCarriage_MCV_80_Warrior_MILAN_ERA_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_ERA_UK_Tourelle_02 MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MCV_80_Warrior_MILAN_ERA_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MCV_80_Warrior_MILAN_ERA_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_MCV_80_Warrior_MILAN_ERA_UK.WeaponInfos
)


export MissileCarriage_MCV_80_Warrior_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_MCV_80_Warrior_MILAN_UK_Tourelle_02 MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MCV_80_Warrior_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MCV_80_Warrior_MILAN_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_MCV_80_Warrior_MILAN_UK.WeaponInfos
)


export MissileCarriage_MTLB_Shturm_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Shturm_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_MTLB_Shturm_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MTLB_Shturm_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_MTLB_Shturm_DDR.WeaponInfos
)


export MissileCarriage_MTLB_Shturm_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Shturm_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_MTLB_Shturm_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MTLB_Shturm_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_MTLB_Shturm_SOV.WeaponInfos
)


export MissileCarriage_MTLB_Strela10M3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10M3_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_MTLB_Strela10M3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MTLB_Strela10M3_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_MTLB_Strela10M3_SOV.WeaponInfos
)


export MissileCarriage_MTLB_Strela10_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_MTLB_Strela10_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MTLB_Strela10_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_MTLB_Strela10_DDR.WeaponInfos
)


export MissileCarriage_MTLB_Strela10_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_MTLB_Strela10_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MTLB_Strela10_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_MTLB_Strela10_POL.WeaponInfos
)


export MissileCarriage_MTLB_Strela10_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MTLB_Strela10_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_MTLB_Strela10_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MTLB_Strela10_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_MTLB_Strela10_SOV.WeaponInfos
)


export MissileCarriage_Marder_1A1_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=5 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Marder_1A1_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Marder_1A1_MILAN_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Marder_1A1_MILAN_RFA.WeaponInfos
)


export MissileCarriage_Marder_1A2_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A2_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=5 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Marder_1A2_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Marder_1A2_MILAN_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Marder_1A2_MILAN_RFA.WeaponInfos
)


export MissileCarriage_Marder_1A3_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_1A3_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=5 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Marder_1A3_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Marder_1A3_MILAN_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Marder_1A3_MILAN_RFA.WeaponInfos
)


export MissileCarriage_Marder_Roland_2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_Roland_2_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Marder_Roland_2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Marder_Roland_2_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Marder_Roland_2_RFA.WeaponInfos
)


export MissileCarriage_Marder_Roland_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Marder_Roland_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Marder_Roland_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Marder_Roland_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Marder_Roland_RFA.WeaponInfos
)


export MissileCarriage_OT_62_TOPAS_JOD_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OT_62_TOPAS_JOD_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_OT_62_TOPAS_JOD_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_OT_62_TOPAS_JOD_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_OT_62_TOPAS_JOD_POL.WeaponInfos
)


export MissileCarriage_OT_64_SKOT_2AM_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OT_64_SKOT_2AM_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_OT_64_SKOT_2AM_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_OT_64_SKOT_2AM_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_OT_64_SKOT_2AM_POL.WeaponInfos
)


export MissileCarriage_Osa_9K33M3_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Osa_9K33M3_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Osa_9K33M3_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Osa_9K33M3_DDR.WeaponInfos
)


export MissileCarriage_Osa_9K33M3_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_POL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Osa_9K33M3_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Osa_9K33M3_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Osa_9K33M3_POL.WeaponInfos
)


export MissileCarriage_Osa_9K33M3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Osa_9K33M3_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Osa_9K33M3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Osa_9K33M3_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Osa_9K33M3_SOV.WeaponInfos
)


export MissileCarriage_Roland_2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Roland_2_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Roland_2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Roland_2_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Roland_2_FR.WeaponInfos
)


export MissileCarriage_Roland_3_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Roland_3_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Roland_3_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Roland_3_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Roland_3_FR.WeaponInfos
)


export MissileCarriage_Supacat_ATMP_Javelin_LML_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_Supacat_ATMP_Javelin_LML_UK_Tourelle_01 MissileCount=9 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Supacat_ATMP_Javelin_LML_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Supacat_ATMP_Javelin_LML_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Supacat_ATMP_Javelin_LML_UK.WeaponInfos
)


export MissileCarriage_Supacat_ATMP_MILAN_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Supacat_ATMP_MILAN_UK
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=9 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Supacat_ATMP_MILAN_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Supacat_ATMP_MILAN_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Supacat_ATMP_MILAN_UK.WeaponInfos
)


export MissileCarriage_T55AM2B_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM2B_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T55AM2B_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T55AM2B_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_T55AM2B_DDR.WeaponInfos
)


export MissileCarriage_T55AM_1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T55AM_1_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T55AM_1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T55AM_1_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_T55AM_1_SOV.WeaponInfos
)


export MissileCarriage_T62MD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62MD_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T62MD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T62MD_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_T62MD_SOV.WeaponInfos
)


export MissileCarriage_T62MV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62MV_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T62MV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T62MV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_T62MV_SOV.WeaponInfos
)


export MissileCarriage_T62M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T62M_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T62M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T62M_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_T62M_SOV.WeaponInfos
)


export MissileCarriage_T64BV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T64BV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T64BV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T64BV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_T64BV_SOV.WeaponInfos
)


export MissileCarriage_T64B_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T64B_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T64B_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T64B_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_T64B_SOV.WeaponInfos
)


export MissileCarriage_T72S_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T72S_DDR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T72S_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T72S_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_T72S_DDR.WeaponInfos
)


export MissileCarriage_T80BV_Beast_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80BV_Beast_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T80BV_Beast_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T80BV_Beast_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_T80BV_Beast_SOV.WeaponInfos
)


export MissileCarriage_T80BV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80BV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T80BV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T80BV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_T80BV_SOV.WeaponInfos
)


export MissileCarriage_T80B_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80B_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T80B_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T80B_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_T80B_SOV.WeaponInfos
)


export MissileCarriage_T80UD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80UD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T80UD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T80UD_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_T80UD_SOV.WeaponInfos
)


export MissileCarriage_T80U_CMD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80U_CMD_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T80U_CMD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T80U_CMD_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_T80U_CMD_SOV.WeaponInfos
)


export MissileCarriage_T80U_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_T80U_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_T80U_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_T80U_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_T80U_SOV.WeaponInfos
)


export MissileCarriage_TPZ_Fuchs_MILAN_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MeshDescriptor=$/GFX/DepictionResources/Modele_TPZ_Fuchs_MILAN_RFA_Tourelle_02 MissileCount=5 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_TPZ_Fuchs_MILAN_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_TPZ_Fuchs_MILAN_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_TPZ_Fuchs_MILAN_RFA.WeaponInfos
)


export MissileCarriage_Tor_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tor_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Tor_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tor_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tor_SOV.WeaponInfos
)


export MissileCarriage_Tracked_Rapier_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tracked_Rapier_UK
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Tracked_Rapier_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tracked_Rapier_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tracked_Rapier_UK.WeaponInfos
)


export MissileCarriage_Tunguska_2K22_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tunguska_2K22_SOV
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Tunguska_2K22_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tunguska_2K22_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tunguska_2K22_SOV.WeaponInfos
)


export MissileCarriage_UAZ_469_Fagot_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_DDR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_UAZ_469_Fagot_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_UAZ_469_Fagot_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_UAZ_469_Fagot_DDR.WeaponInfos
)


export MissileCarriage_UAZ_469_Fagot_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_UAZ_469_Fagot_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_UAZ_469_Fagot_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_UAZ_469_Fagot_POL.WeaponInfos
)


export MissileCarriage_UAZ_469_Fagot_Para_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Fagot_Para_POL
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_UAZ_469_Fagot_Para_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_UAZ_469_Fagot_Para_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_UAZ_469_Fagot_Para_POL.WeaponInfos
)


export MissileCarriage_UAZ_469_Konkurs_VDV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UAZ_469_Konkurs_VDV_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_UAZ_469_Konkurs_VDV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_UAZ_469_Konkurs_VDV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_UAZ_469_Konkurs_VDV_SOV.WeaponInfos
)


export MissileCarriage_Unimog_U1350L_Para_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Unimog_U1350L_Para_BEL
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Unimog_U1350L_Para_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Unimog_U1350L_Para_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Unimog_U1350L_Para_BEL.WeaponInfos
)


export MissileCarriage_Ural_4320_Metla_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ural_4320_Metla_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Ural_4320_Metla_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Ural_4320_Metla_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Ural_4320_Metla_SOV.WeaponInfos
)


export MissileCarriage_VAB_HOT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_HOT_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_VAB_HOT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_VAB_HOT_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_VAB_HOT_FR.WeaponInfos
)


export MissileCarriage_VAB_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VAB_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_VAB_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_VAB_MILAN_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_VAB_MILAN_FR.WeaponInfos
)


export MissileCarriage_VBL_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VBL_MILAN_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_VBL_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_VBL_MILAN_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_VBL_MILAN_FR.WeaponInfos
)


export MissileCarriage_VLRA_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLRA_MILAN_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_VLRA_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_VLRA_MILAN_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_VLRA_MILAN_FR.WeaponInfos
)


export MissileCarriage_VLRA_Mistral_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLRA_Mistral_FR
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_VLRA_Mistral_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_VLRA_Mistral_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_VLRA_Mistral_FR.WeaponInfos
)


export MissileCarriage_VLTT_P4_MILAN_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLTT_P4_MILAN_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_VLTT_P4_MILAN_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_VLTT_P4_MILAN_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_VLTT_P4_MILAN_FR.WeaponInfos
)


export MissileCarriage_VLTT_P4_MILAN_para_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_VLTT_P4_MILAN_para_FR
    PylonSet = ~/DepictionPylonSet_Vehicle_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_VLTT_P4_MILAN_para_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_VLTT_P4_MILAN_para_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Vehicle_Default_Showroom
    WeaponInfos = ~/MissileCarriage_VLTT_P4_MILAN_para_FR.WeaponInfos
)


export MissileCarriage_Wiesel_TOW_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Wiesel_TOW_RFA
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Wiesel_TOW_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Wiesel_TOW_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Wiesel_TOW_RFA.WeaponInfos
)



export MissileCarriage_A109BA_TOW_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A109BA_TOW_BEL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_A109BA_TOW_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A109BA_TOW_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A109BA_TOW_BEL.WeaponInfos
)


export MissileCarriage_A109BA_TOW_twin_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A109BA_TOW_twin_BEL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_A109BA_TOW_twin_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A109BA_TOW_twin_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A109BA_TOW_twin_BEL.WeaponInfos
)


export MissileCarriage_AH1E_Cobra_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1E_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH1E_Cobra_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH1E_Cobra_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH1E_Cobra_US.WeaponInfos
)


export MissileCarriage_AH1F_ATAS_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_ATAS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=14 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH1F_ATAS_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH1F_ATAS_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH1F_ATAS_US.WeaponInfos
)


export MissileCarriage_AH1F_CNITE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_CNITE_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH1F_CNITE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH1F_CNITE_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH1F_CNITE_US.WeaponInfos
)


export MissileCarriage_AH1F_Cobra_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Cobra_NG_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=14 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH1F_Cobra_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH1F_Cobra_NG_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH1F_Cobra_NG_US.WeaponInfos
)


export MissileCarriage_AH1F_Cobra_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=14 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH1F_Cobra_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH1F_Cobra_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH1F_Cobra_US.WeaponInfos
)


export MissileCarriage_AH1F_HeavyHog_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_HeavyHog_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH1F_HeavyHog_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH1F_HeavyHog_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH1F_HeavyHog_US.WeaponInfos
)


export MissileCarriage_AH1F_Hog_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1F_Hog_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH1F_Hog_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH1F_Hog_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH1F_Hog_US.WeaponInfos
)


export MissileCarriage_AH1S_Cobra_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH1S_Cobra_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH1S_Cobra_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH1S_Cobra_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH1S_Cobra_US.WeaponInfos
)


export MissileCarriage_AH64_Apache_ATAS_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_ATAS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH64_Apache_ATAS_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH64_Apache_ATAS_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH64_Apache_ATAS_US.WeaponInfos
)


export MissileCarriage_AH64_Apache_NG_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_NG_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH64_Apache_NG_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH64_Apache_NG_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH64_Apache_NG_US.WeaponInfos
)


export MissileCarriage_AH64_Apache_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH64_Apache_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH64_Apache_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH64_Apache_US.WeaponInfos
)


export MissileCarriage_AH64_Apache_emp1_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_emp1_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_AH64_Apache_emp1_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH64_Apache_emp1_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH64_Apache_emp1_US.WeaponInfos
)


export MissileCarriage_AH64_Apache_emp2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH64_Apache_emp2_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_AH64_Apache_emp2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH64_Apache_emp2_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH64_Apache_emp2_US.WeaponInfos
)


export MissileCarriage_AH6C_Little_Bird_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH6C_Little_Bird_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=14 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
    ]
)

export MissileCarriage_AH6C_Little_Bird_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH6C_Little_Bird_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH6C_Little_Bird_US.WeaponInfos
)


export MissileCarriage_AH6G_Little_Bird_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_AH6G_Little_Bird_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=19 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_AH6G_Little_Bird_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_AH6G_Little_Bird_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_AH6G_Little_Bird_US.WeaponInfos
)


export MissileCarriage_Alouette_III_SS11_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alouette_III_SS11_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Alouette_III_SS11_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alouette_III_SS11_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Alouette_III_SS11_FR.WeaponInfos
)


export MissileCarriage_Bo_105_PAH_1A1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Bo_105_PAH_1A1_RFA
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Bo_105_PAH_1A1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Bo_105_PAH_1A1_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Bo_105_PAH_1A1_RFA.WeaponInfos
)


export MissileCarriage_Bo_105_PAH_1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Bo_105_PAH_1_RFA
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Bo_105_PAH_1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Bo_105_PAH_1_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Bo_105_PAH_1_RFA.WeaponInfos
)


export MissileCarriage_Gazelle_HOT_2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_HOT_2_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Gazelle_HOT_2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Gazelle_HOT_2_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Gazelle_HOT_2_FR.WeaponInfos
)


export MissileCarriage_Gazelle_HOT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_HOT_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Gazelle_HOT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Gazelle_HOT_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Gazelle_HOT_FR.WeaponInfos
)


export MissileCarriage_Gazelle_Mistral_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_Mistral_FR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Gazelle_Mistral_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Gazelle_Mistral_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Gazelle_Mistral_FR.WeaponInfos
)


export MissileCarriage_Gazelle_SNEB_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_SNEB_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
    ]
)

export MissileCarriage_Gazelle_SNEB_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Gazelle_SNEB_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Gazelle_SNEB_UK.WeaponInfos
)


export MissileCarriage_Gazelle_SNEB_reco_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Gazelle_SNEB_reco_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
    ]
)

export MissileCarriage_Gazelle_SNEB_reco_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Gazelle_SNEB_reco_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Gazelle_SNEB_reco_UK.WeaponInfos
)


export MissileCarriage_Ka_50_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ka_50_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Ka_50_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Ka_50_AA_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Ka_50_AA_SOV.WeaponInfos
)


export MissileCarriage_Ka_50_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Ka_50_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Ka_50_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Ka_50_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Ka_50_SOV.WeaponInfos
)


export MissileCarriage_Lynx_AH_Mk1_LBH_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk1_LBH_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Lynx_AH_Mk1_LBH_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Lynx_AH_Mk1_LBH_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Lynx_AH_Mk1_LBH_UK.WeaponInfos
)


export MissileCarriage_Lynx_AH_Mk1_TOW_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk1_TOW_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Lynx_AH_Mk1_TOW_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Lynx_AH_Mk1_TOW_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Lynx_AH_Mk1_TOW_UK.WeaponInfos
)


export MissileCarriage_Lynx_AH_Mk7_I_TOW2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW2_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Lynx_AH_Mk7_I_TOW2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Lynx_AH_Mk7_I_TOW2_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Lynx_AH_Mk7_I_TOW2_UK.WeaponInfos
)


export MissileCarriage_Lynx_AH_Mk7_I_TOW_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_I_TOW_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Lynx_AH_Mk7_I_TOW_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Lynx_AH_Mk7_I_TOW_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Lynx_AH_Mk7_I_TOW_UK.WeaponInfos
)


export MissileCarriage_Lynx_AH_Mk7_SNEB_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Lynx_AH_Mk7_SNEB_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
    ]
)

export MissileCarriage_Lynx_AH_Mk7_SNEB_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Lynx_AH_Mk7_SNEB_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Lynx_AH_Mk7_SNEB_UK.WeaponInfos
)


export MissileCarriage_MH_60A_DAP_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MH_60A_DAP_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=19 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_MH_60A_DAP_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MH_60A_DAP_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_MH_60A_DAP_US.WeaponInfos
)


export MissileCarriage_Mi_14PL_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_14PL_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=1 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Mi_14PL_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_14PL_AT_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_14PL_AT_DDR.WeaponInfos
)


export MissileCarriage_Mi_24D_AA_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_AA_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24D_AA_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24D_AA_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24D_AA_DDR.WeaponInfos
)


export MissileCarriage_Mi_24D_Desant_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_Desant_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_24D_Desant_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24D_Desant_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24D_Desant_SOV.WeaponInfos
)


export MissileCarriage_Mi_24D_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=128 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24D_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24D_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24D_POL.WeaponInfos
)


export MissileCarriage_Mi_24D_s5_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s5_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24D_s5_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24D_s5_AT_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24D_s5_AT_DDR.WeaponInfos
)


export MissileCarriage_Mi_24D_s5_AT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s5_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24D_s5_AT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24D_s5_AT_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24D_s5_AT_SOV.WeaponInfos
)


export MissileCarriage_Mi_24D_s8_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=80 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24D_s8_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24D_s8_AT_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24D_s8_AT_DDR.WeaponInfos
)


export MissileCarriage_Mi_24D_s8_AT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=80 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24D_s8_AT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24D_s8_AT_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24D_s8_AT_POL.WeaponInfos
)


export MissileCarriage_Mi_24D_s8_AT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24D_s8_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=80 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24D_s8_AT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24D_s8_AT_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24D_s8_AT_SOV.WeaponInfos
)


export MissileCarriage_Mi_24K_reco_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24K_reco_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24K_reco_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24K_reco_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24K_reco_SOV.WeaponInfos
)


export MissileCarriage_Mi_24P_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24P_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24P_AA_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24P_AA_SOV.WeaponInfos
)


export MissileCarriage_Mi_24P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=80 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24P_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24P_SOV.WeaponInfos
)


export MissileCarriage_Mi_24P_s8_AT2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_s8_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24P_s8_AT2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24P_s8_AT2_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24P_s8_AT2_DDR.WeaponInfos
)


export MissileCarriage_Mi_24P_s8_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24P_s8_AT_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=80 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24P_s8_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24P_s8_AT_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24P_s8_AT_DDR.WeaponInfos
)


export MissileCarriage_Mi_24VP_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24VP_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24VP_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24VP_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24VP_SOV.WeaponInfos
)


export MissileCarriage_Mi_24V_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_AA_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24V_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24V_AA_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24V_AA_SOV.WeaponInfos
)


export MissileCarriage_Mi_24V_AT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_AT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24V_AT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24V_AT_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24V_AT_SOV.WeaponInfos
)


export MissileCarriage_Mi_24V_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24V_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24V_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24V_POL.WeaponInfos
)


export MissileCarriage_Mi_24V_RKT2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_RKT2_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=80 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24V_RKT2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24V_RKT2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24V_RKT2_SOV.WeaponInfos
)


export MissileCarriage_Mi_24V_RKT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_RKT_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=20 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24V_RKT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24V_RKT_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24V_RKT_SOV.WeaponInfos
)


export MissileCarriage_Mi_24V_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_24V_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=96 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_24V_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_24V_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_24V_SOV.WeaponInfos
)


export MissileCarriage_Mi_2_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_AA_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_2_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_2_AA_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_2_AA_POL.WeaponInfos
)


export MissileCarriage_Mi_2_ATGM_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_ATGM_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_2_ATGM_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_2_ATGM_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_2_ATGM_POL.WeaponInfos
)


export MissileCarriage_Mi_2_rocket_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_rocket_DDR
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_2_rocket_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_2_rocket_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_2_rocket_DDR.WeaponInfos
)


export MissileCarriage_Mi_2_rocket_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_2_rocket_POL
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_2_rocket_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_2_rocket_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_2_rocket_POL.WeaponInfos
)


export MissileCarriage_Mi_8MTV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8MTV_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_8MTV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8MTV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8MTV_SOV.WeaponInfos
)


export MissileCarriage_Mi_8MT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8MT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_8MT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8MT_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8MT_POL.WeaponInfos
)


export MissileCarriage_Mi_8TB_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=128 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TB_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TB_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TB_DDR.WeaponInfos
)


export MissileCarriage_Mi_8TB_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=192 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TB_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TB_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TB_SOV.WeaponInfos
)


export MissileCarriage_Mi_8TB_reco_Marine_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TB_reco_Marine_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=128 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TB_reco_Marine_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TB_reco_Marine_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TB_reco_Marine_DDR.WeaponInfos
)


export MissileCarriage_Mi_8TV_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TV_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_DDR.WeaponInfos
)


export MissileCarriage_Mi_8TV_Gunship_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_Gunship_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_8TV_Gunship_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_Gunship_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_Gunship_SOV.WeaponInfos
)


export MissileCarriage_Mi_8TV_PodGatling_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TV_PodGatling_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_PodGatling_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_PodGatling_DDR.WeaponInfos
)


export MissileCarriage_Mi_8TV_PodGatling_PodAGL_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_PodGatling_PodAGL_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TV_PodGatling_PodAGL_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_PodGatling_PodAGL_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_PodGatling_PodAGL_SOV.WeaponInfos
)


export MissileCarriage_Mi_8TV_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_8TV_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_SOV.WeaponInfos
)


export MissileCarriage_Mi_8TV_UPK_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_UPK_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TV_UPK_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_UPK_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_UPK_DDR.WeaponInfos
)


export MissileCarriage_Mi_8TV_s57_16_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_16_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TV_s57_16_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_s57_16_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_s57_16_SOV.WeaponInfos
)


export MissileCarriage_Mi_8TV_s57_32_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_32_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TV_s57_32_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_s57_32_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_s57_32_DDR.WeaponInfos
)


export MissileCarriage_Mi_8TV_s57_32_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s57_32_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=64 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TV_s57_32_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_s57_32_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_s57_32_SOV.WeaponInfos
)


export MissileCarriage_Mi_8TV_s80_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8TV_s80_SOV
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mi_8TV_s80_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8TV_s80_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8TV_s80_SOV.WeaponInfos
)


export MissileCarriage_Mi_8T_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8T_DDR
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_8T_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8T_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8T_DDR.WeaponInfos
)


export MissileCarriage_Mi_8T_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mi_8T_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mi_8T_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mi_8T_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Mi_8T_POL.WeaponInfos
)


export MissileCarriage_OH58D_Combat_Scout_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58D_Combat_Scout_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=14 MissileType=eAGM MountingType=eMountingPod WeaponIndex=1 ),
    ]
)

export MissileCarriage_OH58D_Combat_Scout_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_OH58D_Combat_Scout_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_OH58D_Combat_Scout_US.WeaponInfos
)


export MissileCarriage_OH58D_Kiowa_Warrior_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58D_Kiowa_Warrior_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_OH58D_Kiowa_Warrior_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_OH58D_Kiowa_Warrior_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_OH58D_Kiowa_Warrior_US.WeaponInfos
)


export MissileCarriage_OH58_CS_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OH58_CS_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=1 ),
    ]
)

export MissileCarriage_OH58_CS_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_OH58_CS_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_OH58_CS_US.WeaponInfos
)


export MissileCarriage_UH1M_gunship_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_UH1M_gunship_US
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_UH1M_gunship_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_UH1M_gunship_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_UH1M_gunship_US.WeaponInfos
)


export MissileCarriage_W3W_Sokol_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_W3W_Sokol_AA_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=20 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_W3W_Sokol_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_W3W_Sokol_AA_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_W3W_Sokol_AA_POL.WeaponInfos
)


export MissileCarriage_W3W_Sokol_RKT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_W3W_Sokol_RKT_POL
    PylonSet = ~/DepictionPylonSet_Helico_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=20 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=20 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_W3W_Sokol_RKT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_W3W_Sokol_RKT_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_W3W_Sokol_RKT_POL.WeaponInfos
)


export MissileCarriage_Westland_Scout_SS11_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Westland_Scout_SS11_UK
    PylonSet = ~/DepictionPylonSet_Helico_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=1 ),
    ]
)

export MissileCarriage_Westland_Scout_SS11_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Westland_Scout_SS11_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Helico_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Westland_Scout_SS11_UK.WeaponInfos
)



export MissileCarriage_A10_Thunderbolt_II_ATGM_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_ATGM_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_A10_Thunderbolt_II_ATGM_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A10_Thunderbolt_II_ATGM_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A10_Thunderbolt_II_ATGM_US.WeaponInfos
)


export MissileCarriage_A10_Thunderbolt_II_Rkt_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_Rkt_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=76 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_A10_Thunderbolt_II_Rkt_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A10_Thunderbolt_II_Rkt_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A10_Thunderbolt_II_Rkt_US.WeaponInfos
)


export MissileCarriage_A10_Thunderbolt_II_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A10_Thunderbolt_II_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_A10_Thunderbolt_II_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A10_Thunderbolt_II_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A10_Thunderbolt_II_US.WeaponInfos
)


export MissileCarriage_A37B_Dragonfly_HE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_US
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_A37B_Dragonfly_HE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A37B_Dragonfly_HE_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_US_Showroom
    WeaponInfos = ~/MissileCarriage_A37B_Dragonfly_HE_US.WeaponInfos
)


export MissileCarriage_A37B_Dragonfly_NPLM_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_NPLM_US
    PylonSet = ~/DepictionPylonSet_Airplane_US
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_A37B_Dragonfly_NPLM_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A37B_Dragonfly_NPLM_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_US_Showroom
    WeaponInfos = ~/MissileCarriage_A37B_Dragonfly_NPLM_US.WeaponInfos
)


export MissileCarriage_A37B_Dragonfly_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A37B_Dragonfly_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=28 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_A37B_Dragonfly_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A37B_Dragonfly_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A37B_Dragonfly_US.WeaponInfos
)


export MissileCarriage_A6E_Intruder_SEAD_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A6E_Intruder_SEAD_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_A6E_Intruder_SEAD_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A6E_Intruder_SEAD_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A6E_Intruder_SEAD_US.WeaponInfos
)


export MissileCarriage_A6E_Intruder_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A6E_Intruder_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_A6E_Intruder_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A6E_Intruder_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A6E_Intruder_US.WeaponInfos
)


export MissileCarriage_A7D_Corsair_II_AT_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_AT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
    ]
)

export MissileCarriage_A7D_Corsair_II_AT_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A7D_Corsair_II_AT_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A7D_Corsair_II_AT_US.WeaponInfos
)


export MissileCarriage_A7D_Corsair_II_CLU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_CLU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_A7D_Corsair_II_CLU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A7D_Corsair_II_CLU_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A7D_Corsair_II_CLU_US.WeaponInfos
)


export MissileCarriage_A7D_Corsair_II_RKT_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_RKT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=114 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_A7D_Corsair_II_RKT_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A7D_Corsair_II_RKT_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A7D_Corsair_II_RKT_US.WeaponInfos
)


export MissileCarriage_A7D_Corsair_II_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_A7D_Corsair_II_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_A7D_Corsair_II_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_A7D_Corsair_II_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_A7D_Corsair_II_US.WeaponInfos
)


export MissileCarriage_Alpha_Jet_A_clu_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_clu_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Alpha_Jet_A_clu_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alpha_Jet_A_clu_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_Alpha_Jet_A_clu_RFA.WeaponInfos
)


export MissileCarriage_Alpha_Jet_A_he_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_he_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Alpha_Jet_A_he_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alpha_Jet_A_he_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_Alpha_Jet_A_he_RFA.WeaponInfos
)


export MissileCarriage_Alpha_Jet_A_nplm_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_nplm_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_Alpha_Jet_A_nplm_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alpha_Jet_A_nplm_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_Alpha_Jet_A_nplm_RFA.WeaponInfos
)


export MissileCarriage_Alpha_Jet_A_rkt_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_A_rkt_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Alpha_Jet_A_rkt_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alpha_Jet_A_rkt_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_Alpha_Jet_A_rkt_RFA.WeaponInfos
)


export MissileCarriage_Alpha_Jet_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Alpha_Jet_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alpha_Jet_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_Alpha_Jet_BEL.WeaponInfos
)


export MissileCarriage_Alpha_Jet_E_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_E_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Alpha_Jet_E_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alpha_Jet_E_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_Alpha_Jet_E_FR.WeaponInfos
)


export MissileCarriage_Alpha_Jet_E_NPLM_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_E_NPLM_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Alpha_Jet_E_NPLM_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alpha_Jet_E_NPLM_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_Alpha_Jet_E_NPLM_FR.WeaponInfos
)


export MissileCarriage_Alpha_Jet_HE2_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Alpha_Jet_HE2_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Alpha_Jet_HE2_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Alpha_Jet_HE2_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_Alpha_Jet_HE2_BEL.WeaponInfos
)


export MissileCarriage_Buccaneer_S2B_ATGM_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_ATGM_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Buccaneer_S2B_ATGM_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Buccaneer_S2B_ATGM_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Buccaneer_S2B_ATGM_UK.WeaponInfos
)


export MissileCarriage_Buccaneer_S2B_GBU_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_GBU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Buccaneer_S2B_GBU_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Buccaneer_S2B_GBU_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Buccaneer_S2B_GBU_UK.WeaponInfos
)


export MissileCarriage_Buccaneer_S2B_HE_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Buccaneer_S2B_HE_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Buccaneer_S2B_HE_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Buccaneer_S2B_HE_UK.WeaponInfos
)


export MissileCarriage_Buccaneer_S2B_SEAD_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Buccaneer_S2B_SEAD_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=1 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Buccaneer_S2B_SEAD_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Buccaneer_S2B_SEAD_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Buccaneer_S2B_SEAD_UK.WeaponInfos
)


export MissileCarriage_CM170_Magister_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CM170_Magister_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
    ]
)

export MissileCarriage_CM170_Magister_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_CM170_Magister_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_CM170_Magister_FR.WeaponInfos
)


export MissileCarriage_CM170_Magister_SS11_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_CM170_Magister_SS11_FR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_CM170_Magister_SS11_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_CM170_Magister_SS11_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_CM170_Magister_SS11_FR.WeaponInfos
)


export MissileCarriage_EA6B_Prowler_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_EA6B_Prowler_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_EA6B_Prowler_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_EA6B_Prowler_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_EA6B_Prowler_US.WeaponInfos
)


export MissileCarriage_F104G_Starfighter_AT_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_AT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F104G_Starfighter_AT_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F104G_Starfighter_AT_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F104G_Starfighter_AT_RFA.WeaponInfos
)


export MissileCarriage_F104G_Starfighter_HE_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_HE_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_F104G_Starfighter_HE_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F104G_Starfighter_HE_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F104G_Starfighter_HE_RFA.WeaponInfos
)


export MissileCarriage_F104G_Starfighter_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F104G_Starfighter_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F104G_Starfighter_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F104G_Starfighter_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F104G_Starfighter_RFA.WeaponInfos
)


export MissileCarriage_F111E_Aardvark_CBU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_F111E_Aardvark_CBU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F111E_Aardvark_CBU_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F111E_Aardvark_CBU_US.WeaponInfos
)


export MissileCarriage_F111E_Aardvark_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_F111E_Aardvark_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F111E_Aardvark_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F111E_Aardvark_US.WeaponInfos
)


export MissileCarriage_F111E_Aardvark_napalm_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111E_Aardvark_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_F111E_Aardvark_napalm_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F111E_Aardvark_napalm_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F111E_Aardvark_napalm_US.WeaponInfos
)


export MissileCarriage_F111F_Aardvark_CBU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_F111F_Aardvark_CBU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F111F_Aardvark_CBU_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F111F_Aardvark_CBU_US.WeaponInfos
)


export MissileCarriage_F111F_Aardvark_LGB2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_LGB2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F111F_Aardvark_LGB2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F111F_Aardvark_LGB2_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F111F_Aardvark_LGB2_US.WeaponInfos
)


export MissileCarriage_F111F_Aardvark_LGB_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_LGB_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F111F_Aardvark_LGB_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F111F_Aardvark_LGB_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F111F_Aardvark_LGB_US.WeaponInfos
)


export MissileCarriage_F111F_Aardvark_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_F111F_Aardvark_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F111F_Aardvark_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F111F_Aardvark_US.WeaponInfos
)


export MissileCarriage_F111F_Aardvark_napalm_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F111F_Aardvark_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_F111F_Aardvark_napalm_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F111F_Aardvark_napalm_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F111F_Aardvark_napalm_US.WeaponInfos
)


export MissileCarriage_F117_Nighthawk_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F117_Nighthawk_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F117_Nighthawk_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F117_Nighthawk_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F117_Nighthawk_US.WeaponInfos
)


export MissileCarriage_F15C_Eagle_AA2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15C_Eagle_AA2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F15C_Eagle_AA2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F15C_Eagle_AA2_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F15C_Eagle_AA2_US.WeaponInfos
)


export MissileCarriage_F15C_Eagle_AA_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15C_Eagle_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F15C_Eagle_AA_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F15C_Eagle_AA_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F15C_Eagle_AA_US.WeaponInfos
)


export MissileCarriage_F15E_StrikeEagle_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F15E_StrikeEagle_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F15E_StrikeEagle_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F15E_StrikeEagle_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F15E_StrikeEagle_US.WeaponInfos
)


export MissileCarriage_F16A_AA2_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA2_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16A_AA2_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16A_AA2_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16A_AA2_NL.WeaponInfos
)


export MissileCarriage_F16A_AA_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16A_AA_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16A_AA_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16A_AA_BEL.WeaponInfos
)


export MissileCarriage_F16A_AA_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_AA_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16A_AA_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16A_AA_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16A_AA_NL.WeaponInfos
)


export MissileCarriage_F16A_CBU_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_CBU_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16A_CBU_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16A_CBU_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16A_CBU_BEL.WeaponInfos
)


export MissileCarriage_F16A_CLU_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_CLU_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16A_CLU_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16A_CLU_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16A_CLU_NL.WeaponInfos
)


export MissileCarriage_F16A_HE_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16A_HE_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16A_HE_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16A_HE_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16A_HE_NL.WeaponInfos
)


export MissileCarriage_F16C_LGB_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16C_LGB_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16C_LGB_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16C_LGB_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16C_LGB_US.WeaponInfos
)


export MissileCarriage_F16E_AA2_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AA2_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_AA2_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_AA2_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_AA2_US.WeaponInfos
)


export MissileCarriage_F16E_AA_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_AA_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_AA_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_AA_US.WeaponInfos
)


export MissileCarriage_F16E_AGM_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_AGM_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_AGM_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_AGM_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_AGM_US.WeaponInfos
)


export MissileCarriage_F16E_CBU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_CBU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_CBU_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_CBU_US.WeaponInfos
)


export MissileCarriage_F16E_HE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_HE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_HE_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_HE_US.WeaponInfos
)


export MissileCarriage_F16E_SEAD_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_SEAD_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_SEAD_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_SEAD_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_SEAD_US.WeaponInfos
)


export MissileCarriage_F16E_TER_CLU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_TER_CLU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_TER_CLU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_TER_CLU_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_TER_CLU_US.WeaponInfos
)


export MissileCarriage_F16E_TER_HE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_TER_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_TER_HE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_TER_HE_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_TER_HE_US.WeaponInfos
)


export MissileCarriage_F16E_napalm_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F16E_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F16E_napalm_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F16E_napalm_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F16E_napalm_US.WeaponInfos
)


export MissileCarriage_F4E_Phantom_II_AA_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_AA_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=5 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4E_Phantom_II_AA_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4E_Phantom_II_AA_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4E_Phantom_II_AA_US.WeaponInfos
)


export MissileCarriage_F4E_Phantom_II_AT_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_AT_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F4E_Phantom_II_AT_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4E_Phantom_II_AT_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4E_Phantom_II_AT_US.WeaponInfos
)


export MissileCarriage_F4E_Phantom_II_CBU_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_CBU_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4E_Phantom_II_CBU_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4E_Phantom_II_CBU_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4E_Phantom_II_CBU_US.WeaponInfos
)


export MissileCarriage_F4E_Phantom_II_HE_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_HE_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4E_Phantom_II_HE_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4E_Phantom_II_HE_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4E_Phantom_II_HE_US.WeaponInfos
)


export MissileCarriage_F4E_Phantom_II_napalm_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4E_Phantom_II_napalm_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4E_Phantom_II_napalm_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4E_Phantom_II_napalm_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4E_Phantom_II_napalm_US.WeaponInfos
)


export MissileCarriage_F4F_Phantom_II_AA_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_AA_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4F_Phantom_II_AA_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4F_Phantom_II_AA_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4F_Phantom_II_AA_RFA.WeaponInfos
)


export MissileCarriage_F4F_Phantom_II_AT_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_AT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F4F_Phantom_II_AT_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4F_Phantom_II_AT_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4F_Phantom_II_AT_RFA.WeaponInfos
)


export MissileCarriage_F4F_Phantom_II_HE1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_HE1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=12 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4F_Phantom_II_HE1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4F_Phantom_II_HE1_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4F_Phantom_II_HE1_RFA.WeaponInfos
)


export MissileCarriage_F4F_Phantom_II_HE2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_HE2_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=5 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4F_Phantom_II_HE2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4F_Phantom_II_HE2_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4F_Phantom_II_HE2_RFA.WeaponInfos
)


export MissileCarriage_F4F_Phantom_II_RKT2_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4F_Phantom_II_RKT2_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4F_Phantom_II_RKT2_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4F_Phantom_II_RKT2_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4F_Phantom_II_RKT2_RFA.WeaponInfos
)


export MissileCarriage_F4_Phantom_AA_F3_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_AA_F3_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4_Phantom_AA_F3_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4_Phantom_AA_F3_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4_Phantom_AA_F3_UK.WeaponInfos
)


export MissileCarriage_F4_Phantom_GR2_HE_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4_Phantom_GR2_HE_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4_Phantom_GR2_HE_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4_Phantom_GR2_HE_UK.WeaponInfos
)


export MissileCarriage_F4_Phantom_GR2_NPLM_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_NPLM_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4_Phantom_GR2_NPLM_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4_Phantom_GR2_NPLM_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4_Phantom_GR2_NPLM_UK.WeaponInfos
)


export MissileCarriage_F4_Phantom_GR2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Phantom_GR2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=4 ),
    ]
)

export MissileCarriage_F4_Phantom_GR2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4_Phantom_GR2_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4_Phantom_GR2_UK.WeaponInfos
)


export MissileCarriage_F4_Wild_Weasel_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F4_Wild_Weasel_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=5 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F4_Wild_Weasel_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F4_Wild_Weasel_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F4_Wild_Weasel_US.WeaponInfos
)


export MissileCarriage_F5A_FreedomFighter_AA_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_AA_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F5A_FreedomFighter_AA_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F5A_FreedomFighter_AA_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F5A_FreedomFighter_AA_NL.WeaponInfos
)


export MissileCarriage_F5A_FreedomFighter_CLU_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_CLU_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F5A_FreedomFighter_CLU_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F5A_FreedomFighter_CLU_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F5A_FreedomFighter_CLU_NL.WeaponInfos
)


export MissileCarriage_F5A_FreedomFighter_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F5A_FreedomFighter_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F5A_FreedomFighter_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F5A_FreedomFighter_NL.WeaponInfos
)


export MissileCarriage_F5A_FreedomFighter_NPLM_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_NPLM_NL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_F5A_FreedomFighter_NPLM_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F5A_FreedomFighter_NPLM_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F5A_FreedomFighter_NPLM_NL.WeaponInfos
)


export MissileCarriage_F5A_FreedomFighter_RKT_NL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F5A_FreedomFighter_RKT_NL
    PylonSet = ~/DepictionPylonSet_Airplane_UK
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_F5A_FreedomFighter_RKT_NL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F5A_FreedomFighter_RKT_NL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_UK_Showroom
    WeaponInfos = ~/MissileCarriage_F5A_FreedomFighter_RKT_NL.WeaponInfos
)


export MissileCarriage_F8P_Crusader_AA2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F8P_Crusader_AA2_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F8P_Crusader_AA2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F8P_Crusader_AA2_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F8P_Crusader_AA2_FR.WeaponInfos
)


export MissileCarriage_F8P_Crusader_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_F8P_Crusader_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_F8P_Crusader_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_F8P_Crusader_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_F8P_Crusader_FR.WeaponInfos
)


export MissileCarriage_FA16_CAS_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_FA16_CAS_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=3 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=4 ),
    ]
)

export MissileCarriage_FA16_CAS_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_FA16_CAS_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_FA16_CAS_US.WeaponInfos
)


export MissileCarriage_G91_R3_Gina_HE_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_HE_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_G91_R3_Gina_HE_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_G91_R3_Gina_HE_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_G91_R3_Gina_HE_RFA.WeaponInfos
)


export MissileCarriage_G91_R3_Gina_NPL_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_NPL_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_G91_R3_Gina_NPL_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_G91_R3_Gina_NPL_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_G91_R3_Gina_NPL_RFA.WeaponInfos
)


export MissileCarriage_G91_R3_Gina_RKT_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_G91_R3_Gina_RKT_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_G91_R3_Gina_RKT_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_G91_R3_Gina_RKT_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_G91_R3_Gina_RKT_RFA.WeaponInfos
)


export MissileCarriage_Harrier_CLU_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_CLU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Harrier_CLU_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Harrier_CLU_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Harrier_CLU_UK.WeaponInfos
)


export MissileCarriage_Harrier_GR5_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_GR5_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Harrier_GR5_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Harrier_GR5_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Harrier_GR5_UK.WeaponInfos
)


export MissileCarriage_Harrier_HE1_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_HE1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Harrier_HE1_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Harrier_HE1_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Harrier_HE1_UK.WeaponInfos
)


export MissileCarriage_Harrier_HE2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_HE2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Harrier_HE2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Harrier_HE2_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Harrier_HE2_UK.WeaponInfos
)


export MissileCarriage_Harrier_RKT1_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_RKT1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Harrier_RKT1_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Harrier_RKT1_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Harrier_RKT1_UK.WeaponInfos
)


export MissileCarriage_Harrier_RKT2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_RKT2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Harrier_RKT2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Harrier_RKT2_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Harrier_RKT2_UK.WeaponInfos
)


export MissileCarriage_Harrier_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Harrier_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Harrier_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Harrier_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Harrier_UK.WeaponInfos
)


export MissileCarriage_Jaguar_ATGM_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_ATGM_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Jaguar_ATGM_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_ATGM_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_ATGM_FR.WeaponInfos
)


export MissileCarriage_Jaguar_CLU_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_CLU_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_CLU_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_CLU_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_CLU_UK.WeaponInfos
)


export MissileCarriage_Jaguar_HE1_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE1_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_HE1_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_HE1_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_HE1_UK.WeaponInfos
)


export MissileCarriage_Jaguar_HE2_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE2_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_HE2_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_HE2_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_HE2_UK.WeaponInfos
)


export MissileCarriage_Jaguar_HE_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_HE_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_HE_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_HE_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_HE_FR.WeaponInfos
)


export MissileCarriage_Jaguar_RKT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_RKT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_RKT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_RKT_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_RKT_FR.WeaponInfos
)


export MissileCarriage_Jaguar_RKT_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_RKT_UK
    PylonSet = ~/DepictionPylonSet_Airplane_UK
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_RKT_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_RKT_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_UK_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_RKT_UK.WeaponInfos
)


export MissileCarriage_Jaguar_SEAD2_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_SEAD2_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=1 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_SEAD2_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_SEAD2_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_SEAD2_FR.WeaponInfos
)


export MissileCarriage_Jaguar_SEAD_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_SEAD_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=1 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Jaguar_SEAD_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_SEAD_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_SEAD_FR.WeaponInfos
)


export MissileCarriage_Jaguar_clu_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_clu_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_clu_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_clu_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_clu_FR.WeaponInfos
)


export MissileCarriage_Jaguar_nplm_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_nplm_FR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_nplm_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_nplm_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_nplm_FR.WeaponInfos
)


export MissileCarriage_Jaguar_overwing_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Jaguar_overwing_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Jaguar_overwing_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Jaguar_overwing_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Jaguar_overwing_UK.WeaponInfos
)


export MissileCarriage_L39ZO_CLU_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_L39ZO_CLU_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_L39ZO_CLU_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_L39ZO_CLU_DDR.WeaponInfos
)


export MissileCarriage_L39ZO_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_L39ZO_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_L39ZO_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_L39ZO_DDR.WeaponInfos
)


export MissileCarriage_L39ZO_HE1_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_HE1_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_RFA
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_L39ZO_HE1_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_L39ZO_HE1_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_RFA_Showroom
    WeaponInfos = ~/MissileCarriage_L39ZO_HE1_DDR.WeaponInfos
)


export MissileCarriage_L39ZO_HE1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_HE1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_L39ZO_HE1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_L39ZO_HE1_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_L39ZO_HE1_SOV.WeaponInfos
)


export MissileCarriage_L39ZO_NPLM_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_L39ZO_NPLM_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_L39ZO_NPLM_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_L39ZO_NPLM_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_L39ZO_NPLM_SOV.WeaponInfos
)


export MissileCarriage_MiG_17PF_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_17PF_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_17PF_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_17PF_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_17PF_POL.WeaponInfos
)


export MissileCarriage_MiG_21PFM_AA_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21PFM_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_21PFM_AA_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21PFM_AA_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21PFM_AA_DDR.WeaponInfos
)


export MissileCarriage_MiG_21PFM_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21PFM_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=32 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_21PFM_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21PFM_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21PFM_DDR.WeaponInfos
)


export MissileCarriage_MiG_21bis_AA2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_21bis_AA2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21bis_AA2_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21bis_AA2_DDR.WeaponInfos
)


export MissileCarriage_MiG_21bis_AA3_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA3_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_21bis_AA3_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21bis_AA3_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21bis_AA3_DDR.WeaponInfos
)


export MissileCarriage_MiG_21bis_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_21bis_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21bis_AA_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21bis_AA_POL.WeaponInfos
)


export MissileCarriage_MiG_21bis_CLU_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_21bis_CLU_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21bis_CLU_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21bis_CLU_DDR.WeaponInfos
)


export MissileCarriage_MiG_21bis_HE_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_HE_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_21bis_HE_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21bis_HE_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21bis_HE_DDR.WeaponInfos
)


export MissileCarriage_MiG_21bis_HE_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_HE_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_21bis_HE_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21bis_HE_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21bis_HE_POL.WeaponInfos
)


export MissileCarriage_MiG_21bis_NPLM_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_NPLM_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_21bis_NPLM_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21bis_NPLM_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21bis_NPLM_DDR.WeaponInfos
)


export MissileCarriage_MiG_21bis_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_21bis_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_21bis_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_21bis_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_21bis_POL.WeaponInfos
)


export MissileCarriage_MiG_23BN_AT2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23BN_AT2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23BN_AT2_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23BN_AT2_DDR.WeaponInfos
)


export MissileCarriage_MiG_23BN_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_AT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_23BN_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23BN_AT_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23BN_AT_DDR.WeaponInfos
)


export MissileCarriage_MiG_23BN_CLU_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_CLU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_23BN_CLU_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23BN_CLU_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23BN_CLU_DDR.WeaponInfos
)


export MissileCarriage_MiG_23BN_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_23BN_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23BN_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23BN_DDR.WeaponInfos
)


export MissileCarriage_MiG_23BN_KMGU_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_KMGU_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23BN_KMGU_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23BN_KMGU_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23BN_KMGU_DDR.WeaponInfos
)


export MissileCarriage_MiG_23BN_RKT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_RKT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23BN_RKT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23BN_RKT_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23BN_RKT_DDR.WeaponInfos
)


export MissileCarriage_MiG_23BN_nplm_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23BN_nplm_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_23BN_nplm_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23BN_nplm_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23BN_nplm_DDR.WeaponInfos
)


export MissileCarriage_MiG_23MF_AA2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23MF_AA2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23MF_AA2_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23MF_AA2_POL.WeaponInfos
)


export MissileCarriage_MiG_23MF_AA_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23MF_AA_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23MF_AA_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23MF_AA_DDR.WeaponInfos
)


export MissileCarriage_MiG_23MF_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23MF_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23MF_AA_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23MF_AA_POL.WeaponInfos
)


export MissileCarriage_MiG_23MF_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MF_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_23MF_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23MF_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23MF_DDR.WeaponInfos
)


export MissileCarriage_MiG_23MLD_AA1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MLD_AA1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23MLD_AA1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23MLD_AA1_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23MLD_AA1_SOV.WeaponInfos
)


export MissileCarriage_MiG_23MLD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23MLD_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23MLD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23MLD_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23MLD_SOV.WeaponInfos
)


export MissileCarriage_MiG_23ML_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23ML_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23ML_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23ML_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23ML_DDR.WeaponInfos
)


export MissileCarriage_MiG_23ML_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23ML_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23ML_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23ML_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23ML_SOV.WeaponInfos
)


export MissileCarriage_MiG_23P_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_23P_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_23P_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_23P_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_23P_SOV.WeaponInfos
)


export MissileCarriage_MiG_25BM_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_25BM_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_25BM_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_25BM_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_25BM_SOV.WeaponInfos
)


export MissileCarriage_MiG_25RBF_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_25RBF_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_25RBF_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_25RBF_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_25RBF_SOV.WeaponInfos
)


export MissileCarriage_MiG_27K_AT1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_AT1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_27K_AT1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27K_AT1_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27K_AT1_SOV.WeaponInfos
)


export MissileCarriage_MiG_27K_AT2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_AT2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_27K_AT2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27K_AT2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27K_AT2_SOV.WeaponInfos
)


export MissileCarriage_MiG_27K_LGB_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_LGB_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_27K_LGB_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27K_LGB_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27K_LGB_SOV.WeaponInfos
)


export MissileCarriage_MiG_27K_SEAD_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27K_SEAD_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_27K_SEAD_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27K_SEAD_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27K_SEAD_SOV.WeaponInfos
)


export MissileCarriage_MiG_27M_CLU_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_CLU_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_27M_CLU_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27M_CLU_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27M_CLU_SOV.WeaponInfos
)


export MissileCarriage_MiG_27M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_27M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27M_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27M_SOV.WeaponInfos
)


export MissileCarriage_MiG_27M_bombe_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_bombe_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_27M_bombe_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27M_bombe_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27M_bombe_SOV.WeaponInfos
)


export MissileCarriage_MiG_27M_napalm_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_napalm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_27M_napalm_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27M_napalm_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27M_napalm_SOV.WeaponInfos
)


export MissileCarriage_MiG_27M_rkt_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_rkt_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_27M_rkt_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27M_rkt_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27M_rkt_SOV.WeaponInfos
)


export MissileCarriage_MiG_27M_sead_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_27M_sead_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_27M_sead_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_27M_sead_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_27M_sead_SOV.WeaponInfos
)


export MissileCarriage_MiG_29_AA2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_29_AA2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_29_AA2_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_29_AA2_POL.WeaponInfos
)


export MissileCarriage_MiG_29_AA2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_29_AA2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_29_AA2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_29_AA2_SOV.WeaponInfos
)


export MissileCarriage_MiG_29_AA3_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA3_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_29_AA3_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_29_AA3_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_29_AA3_SOV.WeaponInfos
)


export MissileCarriage_MiG_29_AA_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_29_AA_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_29_AA_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_29_AA_DDR.WeaponInfos
)


export MissileCarriage_MiG_29_AA_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_29_AA_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_29_AA_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_29_AA_POL.WeaponInfos
)


export MissileCarriage_MiG_29_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_29_AA_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_29_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_29_AA_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_29_AA_SOV.WeaponInfos
)


export MissileCarriage_MiG_31M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_MiG_31M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_31M_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_31M_SOV.WeaponInfos
)


export MissileCarriage_MiG_31_AA1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31_AA1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_31_AA1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_31_AA1_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_31_AA1_SOV.WeaponInfos
)


export MissileCarriage_MiG_31_AA2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_MiG_31_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_MiG_31_AA2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_MiG_31_AA2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_MiG_31_AA2_SOV.WeaponInfos
)


export MissileCarriage_Mirage_2000_C_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_2000_C_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_2000_C_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_2000_C_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_2000_C_FR.WeaponInfos
)


export MissileCarriage_Mirage_5_BA_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_5_BA_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_5_BA_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_5_BA_BEL.WeaponInfos
)


export MissileCarriage_Mirage_5_BA_CLU_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_CLU_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_5_BA_CLU_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_5_BA_CLU_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_5_BA_CLU_BEL.WeaponInfos
)


export MissileCarriage_Mirage_5_BA_MIRSIP_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_MIRSIP_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_5_BA_MIRSIP_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_5_BA_MIRSIP_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_5_BA_MIRSIP_BEL.WeaponInfos
)


export MissileCarriage_Mirage_5_BA_NPLM_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_NPLM_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_5_BA_NPLM_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_5_BA_NPLM_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_5_BA_NPLM_BEL.WeaponInfos
)


export MissileCarriage_Mirage_5_BA_RKT_BEL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_BA_RKT_BEL
    PylonSet = ~/DepictionPylonSet_Airplane_US
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=14 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=14 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_5_BA_RKT_BEL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_5_BA_RKT_BEL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_US_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_5_BA_RKT_BEL.WeaponInfos
)


export MissileCarriage_Mirage_5_F_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_5_F_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_5_F_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_5_F_FR.WeaponInfos
)


export MissileCarriage_Mirage_5_F_clu_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_clu_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mirage_5_F_clu_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_5_F_clu_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_5_F_clu_FR.WeaponInfos
)


export MissileCarriage_Mirage_5_F_nplm_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_5_F_nplm_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_5_F_nplm_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_5_F_nplm_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_5_F_nplm_FR.WeaponInfos
)


export MissileCarriage_Mirage_F1_CT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_F1_CT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_F1_CT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_F1_CT_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_F1_CT_FR.WeaponInfos
)


export MissileCarriage_Mirage_F1_C_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_F1_C_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_F1_C_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_F1_C_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_F1_C_FR.WeaponInfos
)


export MissileCarriage_Mirage_III_E_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_III_E_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=1 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_III_E_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_III_E_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_III_E_FR.WeaponInfos
)


export MissileCarriage_Mirage_IV_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_IV_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Mirage_IV_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_IV_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_IV_FR.WeaponInfos
)


export MissileCarriage_Mirage_IV_SEAD_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Mirage_IV_SEAD_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=3 ),
    ]
)

export MissileCarriage_Mirage_IV_SEAD_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Mirage_IV_SEAD_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Mirage_IV_SEAD_FR.WeaponInfos
)


export MissileCarriage_OA10A_US is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_OA10A_US
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=38 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_OA10A_US_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_OA10A_US.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_OA10A_US.WeaponInfos
)


export MissileCarriage_Su_15TM_AA2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_15TM_AA2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_15TM_AA2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_15TM_AA2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_15TM_AA2_SOV.WeaponInfos
)


export MissileCarriage_Su_15TM_AA_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_15TM_AA_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=1 MissileType=eAAM WeaponIndex=1 ),
        TMissileCarriageWeaponInfo( MissileCount=1 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_15TM_AA_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_15TM_AA_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_15TM_AA_SOV.WeaponInfos
)


export MissileCarriage_Su_17M4_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_17M4_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=20 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_17M4_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_17M4_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_17M4_SOV.WeaponInfos
)


export MissileCarriage_Su_17_cluster_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_17_cluster_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_17_cluster_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_17_cluster_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_17_cluster_POL.WeaponInfos
)


export MissileCarriage_Su_22_AT2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_AT2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_AT2_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_AT2_DDR.WeaponInfos
)


export MissileCarriage_Su_22_AT_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_AT_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_AT_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_AT_DDR.WeaponInfos
)


export MissileCarriage_Su_22_AT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_AT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_AT_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_AT_POL.WeaponInfos
)


export MissileCarriage_Su_22_AT_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_AT_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_AT_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_AT_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_AT_SOV.WeaponInfos
)


export MissileCarriage_Su_22_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_DDR.WeaponInfos
)


export MissileCarriage_Su_22_HE2_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_HE2_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_HE2_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_HE2_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_HE2_DDR.WeaponInfos
)


export MissileCarriage_Su_22_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_POL.WeaponInfos
)


export MissileCarriage_Su_22_RKT2_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_RKT2_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=80 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_RKT2_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_RKT2_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_RKT2_POL.WeaponInfos
)


export MissileCarriage_Su_22_RKT_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_RKT_POL
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_RKT_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_RKT_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_RKT_POL.WeaponInfos
)


export MissileCarriage_Su_22_SEAD_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_SEAD_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_SEAD_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_SEAD_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_SEAD_DDR.WeaponInfos
)


export MissileCarriage_Su_22_SEAD_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_SEAD_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_SEAD_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_SEAD_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_SEAD_POL.WeaponInfos
)


export MissileCarriage_Su_22_UPK_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_UPK_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=80 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_UPK_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_UPK_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_UPK_DDR.WeaponInfos
)


export MissileCarriage_Su_22_clu_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_clu_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_clu_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_clu_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_clu_DDR.WeaponInfos
)


export MissileCarriage_Su_22_clu_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_clu_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_clu_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_clu_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_clu_POL.WeaponInfos
)


export MissileCarriage_Su_22_nplm_DDR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_nplm_DDR
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_nplm_DDR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_nplm_DDR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_nplm_DDR.WeaponInfos
)


export MissileCarriage_Su_22_nplm_POL is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_22_nplm_POL
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_22_nplm_POL_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_22_nplm_POL.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Su_22_nplm_POL.WeaponInfos
)


export MissileCarriage_Su_24MP_SEAD2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24MP_SEAD2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24MP_SEAD2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24MP_SEAD2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24MP_SEAD2_SOV.WeaponInfos
)


export MissileCarriage_Su_24MP_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24MP_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24MP_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24MP_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24MP_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_AT1_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_AT1_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_AT1_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_AT1_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_AT1_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_AT2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_AT2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_AT2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_AT2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_AT2_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_LGB2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_LGB2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_LGB2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_LGB2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_LGB2_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_LGB_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_LGB_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_LGB_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_LGB_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_LGB_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_clu2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_clu2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_clu2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_clu2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_clu2_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_clu_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_clu_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_clu_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_clu_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_clu_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_nplm_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_nplm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_nplm_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_nplm_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_nplm_SOV.WeaponInfos
)


export MissileCarriage_Su_24M_thermo_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_24M_thermo_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Su_24M_thermo_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_24M_thermo_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_24M_thermo_SOV.WeaponInfos
)


export MissileCarriage_Su_25T_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25T_SOV
    PylonSet = ~/DepictionPylonSet_ATGM
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=16 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_25T_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_25T_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_ATGM_Showroom
    WeaponInfos = ~/MissileCarriage_Su_25T_SOV.WeaponInfos
)


export MissileCarriage_Su_25_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_25_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_25_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_25_SOV.WeaponInfos
)


export MissileCarriage_Su_25_clu_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_clu_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_25_clu_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_25_clu_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_25_clu_SOV.WeaponInfos
)


export MissileCarriage_Su_25_he_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_he_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_25_he_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_25_he_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_25_he_SOV.WeaponInfos
)


export MissileCarriage_Su_25_nplm_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_nplm_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_25_nplm_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_25_nplm_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_25_nplm_SOV.WeaponInfos
)


export MissileCarriage_Su_25_rkt2_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_rkt2_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=10 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=4 ),
    ]
)

export MissileCarriage_Su_25_rkt2_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_25_rkt2_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_25_rkt2_SOV.WeaponInfos
)


export MissileCarriage_Su_25_rkt_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_25_rkt_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=40 MissileType=eAGM MountingType=eMountingPod WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_25_rkt_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_25_rkt_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_25_rkt_SOV.WeaponInfos
)


export MissileCarriage_Su_27K_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_27K_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=4 ),
    ]
)

export MissileCarriage_Su_27K_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_27K_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_27K_SOV.WeaponInfos
)


export MissileCarriage_Su_27S_SOV is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Su_27S_SOV
    PylonSet = ~/DepictionPylonSet_Airplane_SOV
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Su_27S_SOV_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Su_27S_SOV.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_SOV_Showroom
    WeaponInfos = ~/MissileCarriage_Su_27S_SOV.WeaponInfos
)


export MissileCarriage_Super_Etendard_AT_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_AT_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Super_Etendard_AT_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Super_Etendard_AT_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Super_Etendard_AT_FR.WeaponInfos
)


export MissileCarriage_Super_Etendard_CLU_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_CLU_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Super_Etendard_CLU_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Super_Etendard_CLU_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Super_Etendard_CLU_FR.WeaponInfos
)


export MissileCarriage_Super_Etendard_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=36 MissileType=eAGM MountingType=eMountingPod WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Super_Etendard_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Super_Etendard_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Super_Etendard_FR.WeaponInfos
)


export MissileCarriage_Super_Etendard_HE_FR is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Super_Etendard_HE_FR
    PylonSet = ~/DepictionPylonSet_Airplane_FR
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
    ]
)

export MissileCarriage_Super_Etendard_HE_FR_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Super_Etendard_HE_FR.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_FR_Showroom
    WeaponInfos = ~/MissileCarriage_Super_Etendard_HE_FR.WeaponInfos
)


export MissileCarriage_Tornado_ADV_HE_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_HE_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=6 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Tornado_ADV_HE_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tornado_ADV_HE_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tornado_ADV_HE_UK.WeaponInfos
)


export MissileCarriage_Tornado_ADV_SEAD_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_SEAD_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Tornado_ADV_SEAD_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tornado_ADV_SEAD_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tornado_ADV_SEAD_UK.WeaponInfos
)


export MissileCarriage_Tornado_ADV_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Tornado_ADV_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tornado_ADV_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tornado_ADV_UK.WeaponInfos
)


export MissileCarriage_Tornado_ADV_clu_UK is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_ADV_clu_UK
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=8 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Tornado_ADV_clu_UK_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tornado_ADV_clu_UK.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tornado_ADV_clu_UK.WeaponInfos
)


export MissileCarriage_Tornado_IDS_AT1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_AT1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=4 MissileType=eAGM WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Tornado_IDS_AT1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tornado_IDS_AT1_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tornado_IDS_AT1_RFA.WeaponInfos
)


export MissileCarriage_Tornado_IDS_CLUS_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_CLUS_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=5 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Tornado_IDS_CLUS_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tornado_IDS_CLUS_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tornado_IDS_CLUS_RFA.WeaponInfos
)


export MissileCarriage_Tornado_IDS_HE1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_HE1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=3 MissileType=eAGM MountingType=eMountingBomb WeaponIndex=2 ),
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Tornado_IDS_HE1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tornado_IDS_HE1_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tornado_IDS_HE1_RFA.WeaponInfos
)


export MissileCarriage_Tornado_IDS_MW1_RFA is TMissileCarriageConnoisseur
(
    MeshDescriptor = $/GFX/DepictionResources/Modele_Tornado_IDS_MW1_RFA
    PylonSet = ~/DepictionPylonSet_Airplane_Default
    WeaponInfos = 
    [
        TMissileCarriageWeaponInfo( MissileCount=2 MissileType=eAAM WeaponIndex=3 ),
    ]
)

export MissileCarriage_Tornado_IDS_MW1_RFA_Showroom is TMissileCarriageConnoisseur
(
    MeshDescriptor = ~/MissileCarriage_Tornado_IDS_MW1_RFA.MeshDescriptor
    PylonSet = ~/DepictionPylonSet_Airplane_Default_Showroom
    WeaponInfos = ~/MissileCarriage_Tornado_IDS_MW1_RFA.WeaponInfos
)



