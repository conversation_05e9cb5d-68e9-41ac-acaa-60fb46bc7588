// Ne pas éditer, ce fichier est généré par DecksFileWriter


export Descriptor_Deck_BEL_16e_Mecanisee_multi is TDeckDescriptor
(
    DeckName = 'ZYYGBZIEQE'
    DeckDivision = ~/Descriptor_Deck_Division_BEL_16e_Mecanisee_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_AIFV_B_CMD_BEL_1_1,
        ~/Descriptor_Deck_Pack_MAN_Z311_BEL_0_1,
        ~/Descriptor_Deck_Pack_MAN_Z311_BEL_0_1,
        ~/Descriptor_Deck_Pack_Volvo_N10_supply_BEL_0_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_CMD_BEL_AIFV_B_50_BEL_1_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_MG_BEL_AIFV_B_C25_BEL_1_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_MG_BEL_AIFV_B_C25_BEL_1_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_AT_BEL_AIFV_B_50_BEL_1_1,
        ~/Descriptor_Deck_Pack_HMGteam_M2HB_BEL_AIFV_B_50_BEL_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_1_BEL_M113A1B_MILAN_BEL_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_BEL_AIFV_B_MILAN_BEL_1_1,
        ~/Descriptor_Deck_Pack_LightRifles_CMD_LUX_M1038_Humvee_LUX_1_1,
        ~/Descriptor_Deck_Pack_LightRifles_LUX_M1038_Humvee_LUX_1_1,
        ~/Descriptor_Deck_Pack_Mortier_81mm_BEL_Iltis_trans_BEL_1_1,
        ~/Descriptor_Deck_Pack_M109A2_BEL_0_1,
        ~/Descriptor_Deck_Pack_M109A2_BEL_0_1,
        ~/Descriptor_Deck_Pack_Leopard_1A5_CMD_BEL_1_1,
        ~/Descriptor_Deck_Pack_Leopard_1BE_BEL_1_1,
        ~/Descriptor_Deck_Pack_Leopard_1BE_BEL_1_1,
        ~/Descriptor_Deck_Pack_Leopard_1BE_BEL_1_1,
        ~/Descriptor_Deck_Pack_Leopard_1A5_BEL_1_1,
        ~/Descriptor_Deck_Pack_FV102_Striker_BEL_1_1,
        ~/Descriptor_Deck_Pack_LRRP_BEL_Unimog_trans_BEL_2_1,
        ~/Descriptor_Deck_Pack_Sniper_ESR_BEL_Iltis_trans_BEL_2_1,
        ~/Descriptor_Deck_Pack_FV107_Scimitar_BEL_1_1,
        ~/Descriptor_Deck_Pack_Epervier_BEL_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Mistral_BEL_Iltis_trans_BEL_1_1,
        ~/Descriptor_Deck_Pack_MAN_Z311_Mi50_BEL_1_1,
        ~/Descriptor_Deck_Pack_Gepard_1A2_BEL_1_1,
        ~/Descriptor_Deck_Pack_A109BA_TOW_BEL_1_1,
        ~/Descriptor_Deck_Pack_A109BA_TOW_twin_BEL_2_1,
        ~/Descriptor_Deck_Pack_F16A_AA_BEL_1_1,
        ~/Descriptor_Deck_Pack_Mirage_5_BA_RKT_BEL_1_1,
        ~/Descriptor_Deck_Pack_Mirage_5_BA_MIRSIP_BEL_1_1,
        ~/Descriptor_Deck_Pack_Mirage_5_BA_CLU_BEL_1_1,
    ]
)

export Descriptor_Deck_RDA_9_Panzer_multi is TDeckDescriptor
(
    DeckName = 'JLFAWVYJKY'
    DeckDivision = ~/Descriptor_Deck_Division_RDA_9_Panzer_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MotRifles_RPG27_DDR_BMP_2_DDR_2_1,
        ~/Descriptor_Deck_Pack_MotRifles_RPG27_DDR_BMP_2_DDR_2_1,
        ~/Descriptor_Deck_Pack_MotRifles_DDR_BMP_1P_Konkurs_DDR_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_DDR_BMP_1_SP1_DDR_0_1,
        ~/Descriptor_Deck_Pack_Engineers_DDR_BTR_70_DDR_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Fagot_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_FOB_DDR_1_1,
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_1,
        ~/Descriptor_Deck_Pack_T815_supply_DDR_0_1,
        ~/Descriptor_Deck_Pack_BTR_60_CHAIKA_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72M1_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72M_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72MUV2_DDR_2_1,
        ~/Descriptor_Deck_Pack_T72MUV2_DDR_2_1,
        ~/Descriptor_Deck_Pack_T72MUV2_DDR_2_1,
        ~/Descriptor_Deck_Pack_T72M1_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72M1_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72M1_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72S_DDR_1_1,
        ~/Descriptor_Deck_Pack_MTLB_Shturm_DDR_0_1,
        ~/Descriptor_Deck_Pack_Scout_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_BRM_1_DDR_0_1,
        ~/Descriptor_Deck_Pack_Mi_8PPA_SOV_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_DDR_1_1,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_DDR_1_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_DDR_0_1,
        ~/Descriptor_Deck_Pack_Mi_2_rocket_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_24D_s5_AT_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_23ML_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_23MF_AA_DDR_1_1,
        ~/Descriptor_Deck_Pack_Su_22_AT_DDR_1_1,
        ~/Descriptor_Deck_Pack_Su_22_RKT_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mortier_PM43_120mm_DDR_MTLB_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_2S3_DDR_0_1,
        ~/Descriptor_Deck_Pack_RM70_85_DDR_0_1,
    ]
)

export Descriptor_Deck_RDA_4_MSD_multi is TDeckDescriptor
(
    DeckName = 'SGGDGYLPQD'
    DeckDivision = ~/Descriptor_Deck_Division_RDA_4_MSD_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MTLB_supply_DDR_0_1,
        ~/Descriptor_Deck_Pack_T813_DDR_0_1,
        ~/Descriptor_Deck_Pack_T813_DDR_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_DDR_BTR_70_DDR_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_DDR_BTR_70_DDR_0_1,
        ~/Descriptor_Deck_Pack_Fallschirmjager_CMD_DDR_UAZ_469_trans_DDR_3_1,
        ~/Descriptor_Deck_Pack_Fallschirmjager_DDR_W50_LA_A_DDR_2_1,
        ~/Descriptor_Deck_Pack_2S1_DDR_0_1,
        ~/Descriptor_Deck_Pack_2S3_DDR_0_1,
        ~/Descriptor_Deck_Pack_Howz_M46_130mm_DDR_MTLB_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_PT76B_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55AM2_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55A_DDR_0_1,
        ~/Descriptor_Deck_Pack_T55AM2_DDR_0_1,
        ~/Descriptor_Deck_Pack_T55AM2_DDR_0_1,
        ~/Descriptor_Deck_Pack_T55AM2B_DDR_1_1,
        ~/Descriptor_Deck_Pack_BRDM_Konkurs_DDR_1_1,
        ~/Descriptor_Deck_Pack_BRDM_Konkurs_DDR_1_1,
        ~/Descriptor_Deck_Pack_Scout_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_HvyScout_DDR_BMP_1P_reco_DDR_0_1,
        ~/Descriptor_Deck_Pack_Mi_2_reco_DDR_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_DDR_1_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_DDR_0_1,
        ~/Descriptor_Deck_Pack_Fallschirmjager_Metys_DDR_W50_LA_A_DDR_2_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_SPG9_FJ_DDR_UAZ_469_trans_DDR_2_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_32_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_24D_s8_AT_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_24D_AA_DDR_2_1,
        ~/Descriptor_Deck_Pack_MiG_29_AA_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_23BN_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_23BN_CLU_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8_supply_DDR_0_1,
    ]
)

export Descriptor_Deck_RDA_7_Panzer_multi is TDeckDescriptor
(
    DeckName = 'KFGIGDUXXL'
    DeckDivision = ~/Descriptor_Deck_Division_RDA_7_Panzer_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_DDR_0_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_DDR_0_1,
        ~/Descriptor_Deck_Pack_FOB_DDR_1_1,
        ~/Descriptor_Deck_Pack_MP_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_DDR_BMP_1_SP2_DDR_0_1,
        ~/Descriptor_Deck_Pack_MotSchutzen_DDR_BMP_1_SP2_DDR_0_1,
        ~/Descriptor_Deck_Pack_Engineers_DDR_BTR_70_DDR_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_DDR_BTR_70_DDR_0_1,
        ~/Descriptor_Deck_Pack_MiG_21bis_AA2_DDR_2_1,
        ~/Descriptor_Deck_Pack_T55A_CMD_DDR_2_1,
        ~/Descriptor_Deck_Pack_T55A_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55A_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55A_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72M_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72M_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72M_DDR_1_1,
        ~/Descriptor_Deck_Pack_T72M1_DDR_1_1,
        ~/Descriptor_Deck_Pack_Scout_LRRP_DDR_W50_LA_A_DDR_2_1,
        ~/Descriptor_Deck_Pack_HvyScout_DDR_BMP_1P_reco_DDR_0_1,
        ~/Descriptor_Deck_Pack_BRM_1_DDR_0_1,
        ~/Descriptor_Deck_Pack_Mi_2_reco_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_UPK_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_32_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mortier_PM43_120mm_DDR_W50_LA_A_DDR_0_1,
        ~/Descriptor_Deck_Pack_2S1_DDR_0_1,
        ~/Descriptor_Deck_Pack_2S3_DDR_0_1,
        ~/Descriptor_Deck_Pack_MFRW_RM70_DDR_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_DDR_0_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_DDR_0_1,
        ~/Descriptor_Deck_Pack_Mi_24D_s5_AT_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_23BN_AT_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_23MF_DDR_1_1,
        ~/Descriptor_Deck_Pack_Su_22_nplm_DDR_1_1,
    ]
)

export Descriptor_Deck_WP_Unternehmen_Zentrum_multi is TDeckDescriptor
(
    DeckName = 'TTRBSAYZQM'
    DeckDivision = ~/Descriptor_Deck_Division_WP_Unternehmen_Zentrum_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_FOB_DDR_1_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_DDR_0_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_DDR_0_1,
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_1,
        ~/Descriptor_Deck_Pack_Wachregiment_CMD_DDR_PSzH_IV_DDR_2_1,
        ~/Descriptor_Deck_Pack_Wachregiment_RPG_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_Wachregiment_RPG_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_Wachregiment_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_Strela_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SVD_DDR_BTR_70_DDR_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Fagot_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_SPG9_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_2S1_DDR_0_1,
        ~/Descriptor_Deck_Pack_2S3_DDR_0_1,
        ~/Descriptor_Deck_Pack_MFRW_RM70_DDR_0_1,
        ~/Descriptor_Deck_Pack_T55A_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55A_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55A_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55A_DDR_1_1,
        ~/Descriptor_Deck_Pack_TO_55_DDR_1_1,
        ~/Descriptor_Deck_Pack_BRDM_Malyu_P_DDR_1_1,
        ~/Descriptor_Deck_Pack_Scout_DDR_UAZ_469_Reco_DDR_1_1,
        ~/Descriptor_Deck_Pack_Grenzer_Flam_DDR_W50_LA_A_DDR_0_1,
        ~/Descriptor_Deck_Pack_BRM_1_DDR_1_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_DDR_1_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_DDR_1_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_DDR_1_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_DDR_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_PodGatling_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_32_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_21bis_AA2_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_21bis_NPLM_DDR_1_1,
        ~/Descriptor_Deck_Pack_L39ZO_DDR_1_1,
        ~/Descriptor_Deck_Pack_L39ZO_CLU_DDR_1_1,
        ~/Descriptor_Deck_Pack_MFRW_RM70_DDR_0_1,
    ]
)

export Descriptor_Deck_RDA_Rugen_Gruppierung is TDeckDescriptor
(
    DeckName = 'LADKNTTNZK'
    DeckDivision = ~/Descriptor_Deck_Division_RDA_Rugen_Gruppierung
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Reserve_CMD_DDR_W50_LA_A_DDR_1_1,
        ~/Descriptor_Deck_Pack_Reserve_DDR_W50_LA_A_DDR_0_1,
        ~/Descriptor_Deck_Pack_HMGteam_AGS17_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_POL_Star_266_POL_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_POL_OT_62_TOPAS_POL_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SVD_POL_OT_62_TOPAS_POL_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Naval_Flam_DDR_W50_LA_A_DDR_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_Howz_M46_130mm_DDR_MTLB_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_Howz_D30_122mm_DDR_MTLB_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_BTR_50_MRF_DDR_0_1,
        ~/Descriptor_Deck_Pack_BTR_50_MRF_DDR_0_1,
        ~/Descriptor_Deck_Pack_T55A_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55A_DDR_1_1,
        ~/Descriptor_Deck_Pack_T55AM_Merida_POL_1_1,
        ~/Descriptor_Deck_Pack_T72_DDR_1_1,
        ~/Descriptor_Deck_Pack_KSK18_DDR_W50_LA_A_DDR_2_1,
        ~/Descriptor_Deck_Pack_Scout_DDR_UAZ_469_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TB_reco_Marine_DDR_1_1,
        ~/Descriptor_Deck_Pack_PT76B_DDR_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_DDR_BTR_152A_DDR_1_1,
        ~/Descriptor_Deck_Pack_DCA_KS19_100mm_DDR_T813_trans_DDR_1_1,
        ~/Descriptor_Deck_Pack_LO_1800_FASTA_4_DDR_1_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_DDR_1_1,
        ~/Descriptor_Deck_Pack_2K11_KRUG_DDR_1_1,
        ~/Descriptor_Deck_Pack_2K11_KRUG_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TB_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_14PL_AT_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_23ML_DDR_2_1,
        ~/Descriptor_Deck_Pack_MiG_23BN_KMGU_DDR_1_1,
        ~/Descriptor_Deck_Pack_L39ZO_CLU_DDR_2_1,
        ~/Descriptor_Deck_Pack_L39ZO_HE1_DDR_2_1,
        ~/Descriptor_Deck_Pack_FOB_DDR_1_1,
        ~/Descriptor_Deck_Pack_PTS_M_supply_DDR_0_1,
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_1,
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_1,
    ]
)

export Descriptor_Deck_FR_11e_Para_multi is TDeckDescriptor
(
    DeckName = 'YSPZTECEMB'
    DeckDivision = ~/Descriptor_Deck_Division_FR_11e_Para_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_VLTT_P4_MILAN_para_FR_2_1,
        ~/Descriptor_Deck_Pack_VLTT_P4_MILAN_para_FR_2_1,
        ~/Descriptor_Deck_Pack_ERC_90_Sagaie_CMD_FR_2_1,
        ~/Descriptor_Deck_Pack_ERC_90_Sagaie_FR_2_1,
        ~/Descriptor_Deck_Pack_ERC_90_Sagaie_FR_2_1,
        ~/Descriptor_Deck_Pack_Scout_para_FR_VLTT_P4_FR_2_1,
        ~/Descriptor_Deck_Pack_Scout_para_FR_VLTT_P4_FR_2_1,
        ~/Descriptor_Deck_Pack_SAS_FR_VLRA_HMG_FR_2_1,
        ~/Descriptor_Deck_Pack_AML_60_FR_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_20mm_reco_FR_2_1,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_2_FR_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_2_FR_1_1,
        ~/Descriptor_Deck_Pack_Puma_Pirate_FR_1_1,
        ~/Descriptor_Deck_Pack_Mirage_2000_C_FR_1_1,
        ~/Descriptor_Deck_Pack_Mirage_5_F_FR_1_1,
        ~/Descriptor_Deck_Pack_Puma_PC_FR_2_1,
        ~/Descriptor_Deck_Pack_VLRA_supply_FR_1_1,
        ~/Descriptor_Deck_Pack_Puma_FR_1_1,
        ~/Descriptor_Deck_Pack_Para_CMD_FR_VLRA_trans_FR_2_1,
        ~/Descriptor_Deck_Pack_Para_FR_VLRA_trans_FR_1_1,
        ~/Descriptor_Deck_Pack_Para_FR_VLRA_trans_FR_1_1,
        ~/Descriptor_Deck_Pack_Para_Legion_FR_VLRA_trans_FR_2_1,
        ~/Descriptor_Deck_Pack_Para_Legion_FR_VLRA_trans_FR_2_1,
        ~/Descriptor_Deck_Pack_Para_Sapeurs_Flam_FR_VLRA_trans_FR_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_para_FR_VLTT_P4_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Aero_CMD_FR_Super_Puma_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Aero_FR_Super_Puma_FR_1_1,
        ~/Descriptor_Deck_Pack_VLRA_Mortier81_FR_1_1,
        ~/Descriptor_Deck_Pack_Howz_M101_105mm_FR_VLRA_trans_FR_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Mistral_para_FR_VLTT_P4_FR_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Mistral_para_FR_VLTT_P4_FR_1_1,
        ~/Descriptor_Deck_Pack_VLRA_20mm_FR_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_Mistral_FR_1_1,
        ~/Descriptor_Deck_Pack_Jaguar_clu_FR_1_1,
        ~/Descriptor_Deck_Pack_Jaguar_SEAD2_FR_1_1,
        ~/Descriptor_Deck_Pack_Puma_Pirate_FR_1_1,
    ]
)

export Descriptor_Deck_FR_152e_Infanterie_multi is TDeckDescriptor
(
    DeckName = 'LBRGMDXMTK'
    DeckDivision = ~/Descriptor_Deck_Division_FR_152e_Infanterie_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_FOB_FR_1_1,
        ~/Descriptor_Deck_Pack_Alouette_II_CMD_FR_1_1,
        ~/Descriptor_Deck_Pack_TRM_2000_supply_FR_0_1,
        ~/Descriptor_Deck_Pack_TRM_2000_supply_FR_0_1,
        ~/Descriptor_Deck_Pack_TRM_10000_supply_FR_0_1,
        ~/Descriptor_Deck_Pack_Reserviste_CMD_FR_VAB_Reserve_FR_1_1,
        ~/Descriptor_Deck_Pack_Reserviste_FR_TRM_2000_FR_0_1,
        ~/Descriptor_Deck_Pack_Rifles_RIMa_CMD_FR_VAB_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_RIMa_FR_VAB_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_RIMa_APILAS_FR_VAB_FR_1_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_M40A1_FR_M201_FR_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_1_FR_VAB_MILAN_FR_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_RIMa_FR_VAB_MILAN_FR_1_1,
        ~/Descriptor_Deck_Pack_Commandos_Air_FR_VIB_FR_2_1,
        ~/Descriptor_Deck_Pack_Obusier_155mm_mle1950_FR_TRM_10000_FR_0_1,
        ~/Descriptor_Deck_Pack_Obusier_155mm_mle1950_FR_TRM_10000_FR_0_1,
        ~/Descriptor_Deck_Pack_Mortier_MORT61_120mm_FR_TRM_2000_FR_1_1,
        ~/Descriptor_Deck_Pack_AML_90_CMD_FR_2_1,
        ~/Descriptor_Deck_Pack_AML_90_Reserve_FR_1_1,
        ~/Descriptor_Deck_Pack_AML_90_Reserve_FR_1_1,
        ~/Descriptor_Deck_Pack_AML_90_Reserve_FR_1_1,
        ~/Descriptor_Deck_Pack_VAB_HOT_FR_0_1,
        ~/Descriptor_Deck_Pack_Sniper_RIMa_FR_VLTT_P4_FR_1_1,
        ~/Descriptor_Deck_Pack_Scout_RIMa_FR_VAB_FR_1_1,
        ~/Descriptor_Deck_Pack_AML_90_FR_1_1,
        ~/Descriptor_Deck_Pack_EBR_90mm_FR_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Mistral_FR_M201_FR_0_1,
        ~/Descriptor_Deck_Pack_DCA_76T2_20mm_CPA_FR_TRM_2000_FR_1_1,
        ~/Descriptor_Deck_Pack_Crotale_FR_1_1,
        ~/Descriptor_Deck_Pack_Crotale_FR_1_1,
        ~/Descriptor_Deck_Pack_Ecureuil_20mm_FR_1_1,
        ~/Descriptor_Deck_Pack_Alouette_III_SS11_FR_1_1,
        ~/Descriptor_Deck_Pack_CM170_Magister_FR_2_1,
        ~/Descriptor_Deck_Pack_F8P_Crusader_AA2_FR_2_1,
        ~/Descriptor_Deck_Pack_Alpha_Jet_E_NPLM_FR_2_1,
        ~/Descriptor_Deck_Pack_Super_Etendard_AT_FR_1_1,
    ]
)

export Descriptor_Deck_FR_5e_Blindee_multi is TDeckDescriptor
(
    DeckName = 'LPUBQMILBV'
    DeckDivision = ~/Descriptor_Deck_Division_FR_5e_Blindee_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_VAB_CMD_FR_1_1,
        ~/Descriptor_Deck_Pack_TRM_2000_supply_FR_0_1,
        ~/Descriptor_Deck_Pack_TRM_2000_supply_FR_0_1,
        ~/Descriptor_Deck_Pack_Puma_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_CMD_FR_VAB_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_FR_VAB_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_APILAS_FR_VAB_FR_0_1,
        ~/Descriptor_Deck_Pack_Sapeurs_Flam_FR_TRM_2000_FR_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_FR_VAB_MILAN_FR_0_1,
        ~/Descriptor_Deck_Pack_Commandos_FR_Super_Puma_FR_2_1,
        ~/Descriptor_Deck_Pack_VAB_Mortar_81_FR_0_1,
        ~/Descriptor_Deck_Pack_VAB_Mortar_81_FR_0_1,
        ~/Descriptor_Deck_Pack_AMX_30_AuF1_FR_0_1,
        ~/Descriptor_Deck_Pack_AMX_30_AuF1_FR_0_1,
        ~/Descriptor_Deck_Pack_AMX_30_B2_CMD_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_30_B_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_30_B2_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_30_B2_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_30_B2_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_30_B2_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_30_B2_Brennus_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_30_EBG_FR_0_1,
        ~/Descriptor_Deck_Pack_VAB_HOT_FR_1_1,
        ~/Descriptor_Deck_Pack_Scout_FR_VLRA_HMG_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_10_RC_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_10_RCR_FR_1_1,
        ~/Descriptor_Deck_Pack_Alouette_III_reco_FR_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Mistral_FR_VLTT_P4_FR_0_1,
        ~/Descriptor_Deck_Pack_AMX_13_DCA_FR_1_1,
        ~/Descriptor_Deck_Pack_Roland_3_FR_0_1,
        ~/Descriptor_Deck_Pack_Mirage_F1_C_FR_1_1,
        ~/Descriptor_Deck_Pack_Mirage_5_F_nplm_FR_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_2_FR_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_2_FR_1_1,
        ~/Descriptor_Deck_Pack_Mirage_5_F_clu_FR_1_1,
        ~/Descriptor_Deck_Pack_Roland_2_FR_0_1,
    ]
)

export Descriptor_Deck_RDA_KdA_Bezirk_Erfurt_multi is TDeckDescriptor
(
    DeckName = 'PSXGGJVXIM'
    DeckDivision = ~/Descriptor_Deck_Division_RDA_KdA_Bezirk_Erfurt_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MTLB_supply_DDR_0_1,
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_1,
        ~/Descriptor_Deck_Pack_Mi_26_SOV_1_1,
        ~/Descriptor_Deck_Pack_FOB_DDR_1_1,
        ~/Descriptor_Deck_Pack_KdA_CMD_DDR_SPW_152K_DDR_1_1,
        ~/Descriptor_Deck_Pack_KdA_DDR_SPW_152K_DDR_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_DDR_SPW_152K_DDR_0_1,
        ~/Descriptor_Deck_Pack_HMGteam_AGS17_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_Engineers_DDR_W50_LA_A_DDR_0_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_SPG9_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_MP_Combat_SOV_GAZ_66B_SOV_2_1,
        ~/Descriptor_Deck_Pack_Reserve_DDR_SPW_152K_DDR_0_1,
        ~/Descriptor_Deck_Pack_Reserve_HMG_DDR_SPW_152K_DDR_0_1,
        ~/Descriptor_Deck_Pack_MiG_23MF_AA_DDR_1_1,
        ~/Descriptor_Deck_Pack_MiG_25RBF_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_27M_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24MP_SOV_1_1,
        ~/Descriptor_Deck_Pack_T54B_CMD_DDR_1_1,
        ~/Descriptor_Deck_Pack_T34_85M_DDR_0_1,
        ~/Descriptor_Deck_Pack_T62M1_SOV_0_1,
        ~/Descriptor_Deck_Pack_T62M_SOV_0_1,
        ~/Descriptor_Deck_Pack_Scout_KdA_DDR_W50_LA_A_DDR_0_1,
        ~/Descriptor_Deck_Pack_PT76B_DDR_0_1,
        ~/Descriptor_Deck_Pack_Mi_2_reco_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_PodGatling_PodAGL_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TB_DDR_1_1,
        ~/Descriptor_Deck_Pack_Mi_24P_s8_AT2_DDR_1_1,
        ~/Descriptor_Deck_Pack_BM24M_DDR_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_DCA_ZU_23_2_DDR_UAZ_469_trans_DDR_0_1,
        ~/Descriptor_Deck_Pack_Osa_9K33M3_DDR_0_1,
        ~/Descriptor_Deck_Pack_Buk_9K37M_SOV_0_1,
        ~/Descriptor_Deck_Pack_BM30_Smerch_SOV_0_1,
        ~/Descriptor_Deck_Pack_2S7M_Malka_SOV_1_1,
        ~/Descriptor_Deck_Pack_2S7M_Malka_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRDM_Konkurs_DDR_1_1,
    ]
)

export Descriptor_Deck_NL_4e_Divisie_multi is TDeckDescriptor
(
    DeckName = 'SWAEPDCBHE'
    DeckDivision = ~/Descriptor_Deck_Division_NL_4e_Divisie_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_AIFV_B_Cargo_NL_0_1,
        ~/Descriptor_Deck_Pack_DAF_YA_4400_supply_NL_0_1,
        ~/Descriptor_Deck_Pack_Reserve_NL_DAF_YA_4400_NL_0_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_Dragon_NL_AIFV_B_C25_NL_1_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_M72_LAW_NL_AIFV_B_C25_NL_1_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_Carl_NL_DAF_YA_4400_NL_1_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_Carl_NL_DAF_YA_4400_NL_1_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_Carl_NL_DAF_YA_4400_NL_1_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW2_NL_LandRover_NL_0_1,
        ~/Descriptor_Deck_Pack_Mortier_MORT61_120mm_NL_DAF_YA_4400_NL_0_1,
        ~/Descriptor_Deck_Pack_Howz_M114_155mm_NL_DAF_YHZ_2300_trans_NL_0_1,
        ~/Descriptor_Deck_Pack_M110A2_HOWZ_NL_1_1,
        ~/Descriptor_Deck_Pack_AIFV_B_TOW_NL_0_1,
        ~/Descriptor_Deck_Pack_Leopard_1A1_NL_0_1,
        ~/Descriptor_Deck_Pack_Leopard_2A4B_NL_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A4B_NL_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A4B_NL_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A4_NL_0_1,
        ~/Descriptor_Deck_Pack_Leopard_2A4B_CMD_NL_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A4B_CMD_NL_1_1,
        ~/Descriptor_Deck_Pack_Bo_105_CB_NL_1_1,
        ~/Descriptor_Deck_Pack_M113_CV_25mm_NL_0_1,
        ~/Descriptor_Deck_Pack_Engineers_Scout_NL_M113A1_reco_NL_0_1,
        ~/Descriptor_Deck_Pack_DCA_Bofors_upgrade_NL_DAF_YA_4400_NL_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_NL_LandRover_NL_0_1,
        ~/Descriptor_Deck_Pack_Gepard_1A2_NL_0_1,
        ~/Descriptor_Deck_Pack_F5A_FreedomFighter_NL_1_1,
        ~/Descriptor_Deck_Pack_F5A_FreedomFighter_RKT_NL_1_1,
        ~/Descriptor_Deck_Pack_F16A_AA_NL_1_1,
        ~/Descriptor_Deck_Pack_F16A_AA_NL_1_1,
        ~/Descriptor_Deck_Pack_F16A_CLU_NL_1_1,
        ~/Descriptor_Deck_Pack_AIFV_B_CMD_NL_1_1,
        ~/Descriptor_Deck_Pack_AIFV_B_CMD_NL_1_1,
        ~/Descriptor_Deck_Pack_M110A2_HOWZ_NL_1_1,
        ~/Descriptor_Deck_Pack_Scout_AT_NL_Alouette_III_trans_NL_1_1,
        ~/Descriptor_Deck_Pack_Engineers_NL_M113A1_NL_1_1,
    ]
)

export Descriptor_Deck_POL_20_Pancerna_multi is TDeckDescriptor
(
    DeckName = 'ONLIADMYGS'
    DeckDivision = ~/Descriptor_Deck_Division_POL_20_Pancerna_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_FOB_POL_1_1,
        ~/Descriptor_Deck_Pack_GAZ_66_POL_0_1,
        ~/Descriptor_Deck_Pack_GAZ_66_POL_0_1,
        ~/Descriptor_Deck_Pack_Star_266_supply_POL_0_1,
        ~/Descriptor_Deck_Pack_Engineers_CMD_POL_OT_64_SKOT_2_POL_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_POL_BMP_1_SP2_POL_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SVD_POL_BMP_1_SP2_POL_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SVD_POL_BMP_1_SP2_POL_1_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_SPG9_POL_BMP_1_SP2_POL_1_1,
        ~/Descriptor_Deck_Pack_Atteam_Fagot_POL_BMP_1_SP2_POL_1_1,
        ~/Descriptor_Deck_Pack_2S1_POL_1_1,
        ~/Descriptor_Deck_Pack_2S1_POL_1_1,
        ~/Descriptor_Deck_Pack_DANA_POL_0_1,
        ~/Descriptor_Deck_Pack_BM21_Grad_POL_0_1,
        ~/Descriptor_Deck_Pack_T55A_CMD_POL_1_1,
        ~/Descriptor_Deck_Pack_T55A_POL_1_1,
        ~/Descriptor_Deck_Pack_T55AS_POL_1_1,
        ~/Descriptor_Deck_Pack_T72M1_CMD_POL_1_1,
        ~/Descriptor_Deck_Pack_T72M_POL_1_1,
        ~/Descriptor_Deck_Pack_T72M1_POL_1_1,
        ~/Descriptor_Deck_Pack_T72M1_POL_1_1,
        ~/Descriptor_Deck_Pack_T72M1_Wilk_POL_1_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Malyu_P_POL_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Scout_POL_BAV_485_POL_1_1,
        ~/Descriptor_Deck_Pack_Scout_LRRP_POL_Honker_RYS_POL_2_1,
        ~/Descriptor_Deck_Pack_Scout_SF_POL_Honker_RYS_POL_2_1,
        ~/Descriptor_Deck_Pack_BRM_1_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_2_gunship_POL_1_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_POL_1_1,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_POL_1_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_24D_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_24D_s8_AT_POL_1_1,
        ~/Descriptor_Deck_Pack_MiG_21bis_POL_2_1,
        ~/Descriptor_Deck_Pack_MiG_21bis_HE_POL_2_1,
        ~/Descriptor_Deck_Pack_Su_22_AT_POL_1_1,
    ]
)

export Descriptor_Deck_POL_Korpus_Desantowy_multi is TDeckDescriptor
(
    DeckName = 'RUBMFOCSHC'
    DeckDivision = ~/Descriptor_Deck_Division_POL_Korpus_Desantowy_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BAV_485_Supply_POL_0_1,
        ~/Descriptor_Deck_Pack_Mi_6_POL_1_1,
        ~/Descriptor_Deck_Pack_UAZ_469_CMD_Para_POL_2_1,
        ~/Descriptor_Deck_Pack_MLRS_WP_8z_POL_UAZ_469_trans_POL_0_1,
        ~/Descriptor_Deck_Pack_Mortier_2B9_Vasilek_Para_POL_UAZ_469_trans_POL_1_1,
        ~/Descriptor_Deck_Pack_2S7_Pion_POL_1_1,
        ~/Descriptor_Deck_Pack_ASU_85_POL_1_1,
        ~/Descriptor_Deck_Pack_T55A_CMD_POL_1_1,
        ~/Descriptor_Deck_Pack_T55A_POL_0_1,
        ~/Descriptor_Deck_Pack_T55AS_POL_0_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Malyu_P_POL_1_1,
        ~/Descriptor_Deck_Pack_Atteam_Fagot_POL_OT_62_TOPAS_SPG9_POL_0_1,
        ~/Descriptor_Deck_Pack_Naval_Rifle_POL_OT_62_TOPAS_2AP_POL_1_1,
        ~/Descriptor_Deck_Pack_Naval_Rifle_POL_OT_62_TOPAS_2AP_POL_1_1,
        ~/Descriptor_Deck_Pack_Para_Metis_POL_GAZ_66B_POL_2_1,
        ~/Descriptor_Deck_Pack_Para_POL_GAZ_66B_POL_2_1,
        ~/Descriptor_Deck_Pack_Para_CMD_POL_GAZ_66B_POL_2_1,
        ~/Descriptor_Deck_Pack_UAZ_469_SPG9_Para_POL_2_1,
        ~/Descriptor_Deck_Pack_Naval_Engineers_POL_BAV_485_POL_1_1,
        ~/Descriptor_Deck_Pack_W3RR_Procjon_POL_1_1,
        ~/Descriptor_Deck_Pack_Scout_LRRP_Para_POL_UAZ_469_trans_POL_2_1,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_Para_POL_UAZ_469_trans_POL_2_1,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_Naval_POL_OT_62_TOPAS_JOD_POL_2_1,
        ~/Descriptor_Deck_Pack_Hibneryt_KG_POL_1_1,
        ~/Descriptor_Deck_Pack_W3W_Sokol_AA_POL_1_1,
        ~/Descriptor_Deck_Pack_W3W_Sokol_AA_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_24D_s8_AT_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_POL_1_1,
        ~/Descriptor_Deck_Pack_MiG_29_AA_POL_1_1,
        ~/Descriptor_Deck_Pack_MiG_29_AA_POL_1_1,
        ~/Descriptor_Deck_Pack_Su_22_nplm_POL_1_1,
        ~/Descriptor_Deck_Pack_Su_22_AT_POL_1_1,
        ~/Descriptor_Deck_Pack_Su_22_SEAD_POL_1_1,
        ~/Descriptor_Deck_Pack_BRDM_2_POL_1_1,
        ~/Descriptor_Deck_Pack_Naval_Engineers_Flam_POL_OT_62_TOPAS_POL_1_1,
    ]
)

export Descriptor_Deck_POL_4_Zmechanizowana_multi is TDeckDescriptor
(
    DeckName = 'TXTNNGPIUS'
    DeckDivision = ~/Descriptor_Deck_Division_POL_4_Zmechanizowana_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_UAZ_469_CMD_POL_1_1,
        ~/Descriptor_Deck_Pack_OT_64_SKOT_CMD_POL_1_1,
        ~/Descriptor_Deck_Pack_GAZ_66_POL_0_1,
        ~/Descriptor_Deck_Pack_KrAZ_255B_supply_POL_0_1,
        ~/Descriptor_Deck_Pack_RM70_85_POL_0_1,
        ~/Descriptor_Deck_Pack_Howz_ML20_152mm_POL_KrAZ_255B_POL_0_1,
        ~/Descriptor_Deck_Pack_Mortier_PM43_120mm_POL_Star_266_POL_0_1,
        ~/Descriptor_Deck_Pack_Scout_SF_POL_Honker_RYS_POL_2_1,
        ~/Descriptor_Deck_Pack_Engineers_Scout_POL_BAV_485_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_24D_s8_AT_POL_1_1,
        ~/Descriptor_Deck_Pack_Mi_2_AA_POL_1_1,
        ~/Descriptor_Deck_Pack_Su_22_AT_POL_1_1,
        ~/Descriptor_Deck_Pack_Su_22_clu_POL_1_1,
        ~/Descriptor_Deck_Pack_MiG_23MF_AA_POL_1_1,
        ~/Descriptor_Deck_Pack_T55AMS_Merida_POL_0_1,
        ~/Descriptor_Deck_Pack_T55AM_Merida_POL_1_1,
        ~/Descriptor_Deck_Pack_T55AM_Merida_POL_1_1,
        ~/Descriptor_Deck_Pack_T55AM_Merida_CMD_POL_1_1,
        ~/Descriptor_Deck_Pack_T55A_POL_0_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_POL_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_POL_BMP_1_SP2_POL_2_1,
        ~/Descriptor_Deck_Pack_Scout_POL_UAZ_469_trans_POL_0_1,
        ~/Descriptor_Deck_Pack_BRDM_1_PSNR1_POL_0_1,
        ~/Descriptor_Deck_Pack_Osa_9K33M3_POL_0_1,
        ~/Descriptor_Deck_Pack_Osa_9K33M3_POL_0_1,
        ~/Descriptor_Deck_Pack_BRDM_Strela_1_POL_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_POL_OT_64_SKOT_2P_POL_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_SVD_POL_Star_266_POL_1_1,
        ~/Descriptor_Deck_Pack_Rifles_POL_Star_266_POL_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_POL_Star_266_POL_1_1,
        ~/Descriptor_Deck_Pack_Commandos_POL_W3_Sokol_POL_3_1,
        ~/Descriptor_Deck_Pack_Engineers_POL_Star_266_POL_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_POL_OT_64_SKOT_2_POL_1_1,
        ~/Descriptor_Deck_Pack_WSW_POL_Star_266_POL_1_1,
    ]
)

export Descriptor_Deck_RFA_2_PzGrenadier_multi is TDeckDescriptor
(
    DeckName = 'RPGUDLLPPH'
    DeckDivision = ~/Descriptor_Deck_Division_RFA_2_PzGrenadier_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Security_RFA_Unimog_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Bo_105_reco_RFA_1_1,
        ~/Descriptor_Deck_Pack_Jager_Aufk_RFA_Unimog_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Redeye_RFA_Iltis_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_Tornado_ADV_UK_1_1,
        ~/Descriptor_Deck_Pack_Tornado_IDS_MW1_RFA_1_1,
        ~/Descriptor_Deck_Pack_Tornado_IDS_CLUS_RFA_1_1,
        ~/Descriptor_Deck_Pack_Fallschirmjager_Scout_RFA_Iltis_trans_RFA_2_1,
        ~/Descriptor_Deck_Pack_Bo_105_PAH_1A1_RFA_1_1,
        ~/Descriptor_Deck_Pack_TPZ_Fuchs_CMD_RFA_1_1,
        ~/Descriptor_Deck_Pack_Unimog_S_404_RFA_0_1,
        ~/Descriptor_Deck_Pack_Unimog_S_404_RFA_0_1,
        ~/Descriptor_Deck_Pack_MAN_Kat_6x6_RFA_0_1,
        ~/Descriptor_Deck_Pack_Jager_RFA_Unimog_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Jager_RFA_Unimog_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_RFA_Unimog_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Engineers_RFA_Unimog_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_M40A1_RFA_Iltis_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Fallschirmjager_CMD_RFA_Unimog_trans_RFA_3_1,
        ~/Descriptor_Deck_Pack_Fallschirm_RFA_Unimog_trans_RFA_2_1,
        ~/Descriptor_Deck_Pack_HMGteam_MG3_FJ_RFA_Iltis_trans_RFA_2_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_para_RFA_Iltis_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_M113_PzMorser_RFA_0_1,
        ~/Descriptor_Deck_Pack_M109A3G_HOWZ_RFA_0_1,
        ~/Descriptor_Deck_Pack_M109A3G_HOWZ_RFA_0_1,
        ~/Descriptor_Deck_Pack_Lars_2_RFA_0_1,
        ~/Descriptor_Deck_Pack_Leopard_1A5_CMD_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_1A5_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_1A5_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_CMD_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_RFA_1_1,
        ~/Descriptor_Deck_Pack_Luchs_A1_RFA_0_1,
        ~/Descriptor_Deck_Pack_Gepard_1A2_RFA_0_1,
        ~/Descriptor_Deck_Pack_DCA_I_Hawk_RFA_MAN_Kat_6x6_trans_RFA_0_1,
    ]
)

export Descriptor_Deck_RFA_5_Panzer_multi is TDeckDescriptor
(
    DeckName = 'BVMJTZRQND'
    DeckDivision = ~/Descriptor_Deck_Division_RFA_5_Panzer_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M577_RFA_1_1,
        ~/Descriptor_Deck_Pack_M113A1G_supply_RFA_0_1,
        ~/Descriptor_Deck_Pack_M113A1G_supply_RFA_0_1,
        ~/Descriptor_Deck_Pack_MAN_Kat_6x6_RFA_0_1,
        ~/Descriptor_Deck_Pack_Panzergrenadier_CMD_RFA_Marder_1A2_RFA_1_1,
        ~/Descriptor_Deck_Pack_Security_RFA_Unimog_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Panzergrenadier_IFV_RFA_Marder_1A2_MILAN_RFA_1_1,
        ~/Descriptor_Deck_Pack_Panzergrenadier_IFV_RFA_Marder_1A2_MILAN_RFA_1_1,
        ~/Descriptor_Deck_Pack_Panzergrenadier_IFV_RFA_Marder_1A2_MILAN_RFA_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_RFA_Marder_1A2_MILAN_RFA_0_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_RFA_TPZ_Fuchs_1_RFA_0_1,
        ~/Descriptor_Deck_Pack_Lars_2_RFA_0_1,
        ~/Descriptor_Deck_Pack_M270_MLRS_RFA_0_1,
        ~/Descriptor_Deck_Pack_M109A3G_HOWZ_RFA_0_1,
        ~/Descriptor_Deck_Pack_M109A3G_HOWZ_RFA_0_1,
        ~/Descriptor_Deck_Pack_Leopard_1A5_CMD_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_1A5_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_CMD_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_CMD_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A4_RFA_0_1,
        ~/Descriptor_Deck_Pack_Leopard_2A4_RFA_0_1,
        ~/Descriptor_Deck_Pack_Jager_Aufk_RFA_Unimog_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Fernspaher_RFA_Iltis_trans_RFA_2_1,
        ~/Descriptor_Deck_Pack_TPZ_Fuchs_RASIT_RFA_0_1,
        ~/Descriptor_Deck_Pack_Alouette_II_reco_RFA_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Redeye_RFA_Iltis_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_Gepard_1A2_RFA_0_1,
        ~/Descriptor_Deck_Pack_Marder_Roland_RFA_0_1,
        ~/Descriptor_Deck_Pack_Bo_105_PAH_1A1_RFA_1_1,
        ~/Descriptor_Deck_Pack_Leopard_2A3_RFA_1_1,
        ~/Descriptor_Deck_Pack_F4F_Phantom_II_AA_RFA_1_1,
        ~/Descriptor_Deck_Pack_Tornado_IDS_CLUS_RFA_1_1,
        ~/Descriptor_Deck_Pack_Tornado_IDS_MW1_RFA_1_1,
    ]
)

export Descriptor_Deck_RFA_TerrKdo_Sud_multi is TDeckDescriptor
(
    DeckName = 'DEHWUWMIWE'
    DeckDivision = ~/Descriptor_Deck_Division_RFA_TerrKdo_Sud_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_FOB_RFA_1_1,
        ~/Descriptor_Deck_Pack_MAN_Kat_6x6_RFA_0_1,
        ~/Descriptor_Deck_Pack_MAN_Kat_6x6_RFA_0_1,
        ~/Descriptor_Deck_Pack_CH53G_RFA_0_1,
        ~/Descriptor_Deck_Pack_Engineers_CMD_RFA_Iltis_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_HeimatschutzJager_RFA_Unimog_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Jager_RFA_M113A1G_RFA_1_1,
        ~/Descriptor_Deck_Pack_Jager_noAT_RFA_M113A1G_MILAN_RFA_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_RFA_Unimog_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_M40A1_RFA_Iltis_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_FR_VLTT_P4_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Aero_CMD_FR_Super_Puma_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Aero_FR_Super_Puma_FR_1_1,
        ~/Descriptor_Deck_Pack_HS30_Panzermorser_120mm_RFA_0_1,
        ~/Descriptor_Deck_Pack_HS30_Panzermorser_120mm_RFA_0_1,
        ~/Descriptor_Deck_Pack_FH70_155mm_RFA_MAN_Kat_6x6_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_M110A2_HOWZ_US_1_1,
        ~/Descriptor_Deck_Pack_M110A2_HOWZ_US_1_1,
        ~/Descriptor_Deck_Pack_M48A2GA2_CMD_RFA_1_1,
        ~/Descriptor_Deck_Pack_M48A2C_RFA_0_1,
        ~/Descriptor_Deck_Pack_M48A2GA2_RFA_0_1,
        ~/Descriptor_Deck_Pack_M48A2GA2_RFA_0_1,
        ~/Descriptor_Deck_Pack_VLTT_P4_MILAN_FR_0_1,
        ~/Descriptor_Deck_Pack_Fallschirm_B1_RFA_Unimog_trans_RFA_2_1,
        ~/Descriptor_Deck_Pack_AMX_10_RC_FR_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_20mm_reco_FR_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Redeye_RFA_Iltis_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_VLRA_20mm_FR_1_1,
        ~/Descriptor_Deck_Pack_Marder_Roland_RFA_0_1,
        ~/Descriptor_Deck_Pack_DCA_I_Hawk_RFA_MAN_Kat_6x6_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_Gazelle_20mm_FR_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_2_FR_1_1,
        ~/Descriptor_Deck_Pack_Mirage_2000_C_FR_1_1,
        ~/Descriptor_Deck_Pack_Alpha_Jet_A_he_RFA_1_1,
        ~/Descriptor_Deck_Pack_Alpha_Jet_A_clu_RFA_1_1,
        ~/Descriptor_Deck_Pack_F104G_Starfighter_AT_RFA_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Aero_FR_Super_Puma_FR_1_1,
    ]
)

export Descriptor_Deck_SOV_119IndTkBrig_multi is TDeckDescriptor
(
    DeckName = 'SKNOKFFZJC'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_119IndTkBrig_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1P_SOV_0_1,
        ~/Descriptor_Deck_Pack_MiG_29_AA_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24M_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24MP_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80U_SOV_0_1,
        ~/Descriptor_Deck_Pack_T80U_SOV_0_1,
        ~/Descriptor_Deck_Pack_T80U_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80UD_SOV_0_1,
        ~/Descriptor_Deck_Pack_Scout_LRRP_SOV_GAZ_66B_SOV_2_1,
        ~/Descriptor_Deck_Pack_BRDM_2_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_RKT_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_RKT_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mortier_2B9_Vasilek_nonPara_SOV_GAZ_66_SOV_0_1,
        ~/Descriptor_Deck_Pack_Howz_MstaB_150mm_SOV_Ural_4320_trans_SOV_0_1,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_SOV_0_1,
        ~/Descriptor_Deck_Pack_Tunguska_2K22_SOV_0_1,
        ~/Descriptor_Deck_Pack_Engineers_SOV_BTR_60_SOV_0_1,
        ~/Descriptor_Deck_Pack_Spetsnaz_CMD_SOV_GAZ_66B_SOV_3_1,
        ~/Descriptor_Deck_Pack_Spetsnaz_SOV_GAZ_66B_SOV_2_1,
        ~/Descriptor_Deck_Pack_Spetsnaz_FireSupport_SOV_UAZ_469_SOV_2_1,
        ~/Descriptor_Deck_Pack_T80UD_SOV_0_1,
        ~/Descriptor_Deck_Pack_TO_55_SOV_0_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_0_1,
        ~/Descriptor_Deck_Pack_AT_T12_Rapira_SOV_MTLB_transp_SOV_0_1,
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_8TZ_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24P_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24P_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_Gunship_SOV_1_1,
    ]
)

export Descriptor_Deck_SOV_157_Rifle_multi is TDeckDescriptor
(
    DeckName = 'MSNGLCZFKZ'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_157_Rifle_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_1,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_1,
        ~/Descriptor_Deck_Pack_KrAZ_255B_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_6_SOV_1_1,
        ~/Descriptor_Deck_Pack_Reserve_CMD_SOV_BTR_152K_SOV_1_1,
        ~/Descriptor_Deck_Pack_Reserve_SOV_BTR_152K_SOV_0_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_Reserve_SOV_Ural_4320_trans_SOV_0_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_B11_Reserve_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Fagot_SOV_UAZ_469_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_SOV_BMP_1P_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1_SP2_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_SOV_BTR_60_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mortier_PM43_120mm_SOV_Ural_4320_trans_SOV_0_1,
        ~/Descriptor_Deck_Pack_Howz_Br5M_280mm_SOV_KrAZ_255B_SOV_0_1,
        ~/Descriptor_Deck_Pack_A222_Bereg_SOV_0_1,
        ~/Descriptor_Deck_Pack_BM24M_SOV_0_1,
        ~/Descriptor_Deck_Pack_T55A_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_T55A_SOV_1_1,
        ~/Descriptor_Deck_Pack_T55A_obr81_SOV_1_1,
        ~/Descriptor_Deck_Pack_T55AM_1_SOV_1_1,
        ~/Descriptor_Deck_Pack_TO_55_SOV_1_1,
        ~/Descriptor_Deck_Pack_AT_KSM65_100mm_SOV_KrAZ_255B_SOV_1_1,
        ~/Descriptor_Deck_Pack_Scout_SOV_BTR_40_SOV_1_1,
        ~/Descriptor_Deck_Pack_HvyScout_Reserve_SOV_BTR_40_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_1,
        ~/Descriptor_Deck_Pack_Alouette_III_SOV_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_3_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_Ural_4320_ZPU_SOV_1_1,
        ~/Descriptor_Deck_Pack_DCA_KS30_130mm_SOV_KrAZ_255B_SOV_1_1,
        ~/Descriptor_Deck_Pack_2K11_KRUG_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_8TB_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_27K_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_25_he_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_25_rkt2_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_25_SOV_1_1,
    ]
)

export Descriptor_Deck_SOV_25_Tank_multi is TDeckDescriptor
(
    DeckName = 'SZDBRMISSA'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_25_Tank_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_8TZ_SOV_0_1,
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_2_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1P_Konkurs_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1P_Konkurs_SOV_1_1,
        ~/Descriptor_Deck_Pack_HMGteam_NSV_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_Engineers_SOV_MTLB_transp_SOV_0_1,
        ~/Descriptor_Deck_Pack_2S19_MstaS_SOV_0_1,
        ~/Descriptor_Deck_Pack_BM21_Grad_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mortier_2S12_120mm_SOV_MTLB_transp_SOV_0_1,
        ~/Descriptor_Deck_Pack_T64BV_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64BV_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64A_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64BV1_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64BV1_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64BV_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64BV_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64BV_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64B1_SOV_1_1,
        ~/Descriptor_Deck_Pack_T64B1_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_HvyScout_SOV_BMP_2_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_Scout_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_SOV_0_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_SOV_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_RKT2_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_27S_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_27S_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_29_AA_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_25_rkt2_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_25_SOV_1_1,
        ~/Descriptor_Deck_Pack_Pchela_1T_SOV_1_1,
    ]
)

export Descriptor_Deck_SOV_27_Gds_Rifle_multi is TDeckDescriptor
(
    DeckName = 'BFMTKMUMTD'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_27_Gds_Rifle_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BTR_80_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_TTsko_SOV_BMP_2_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_Metis_TTsko_SOV_BTR_80_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_TTsko_SOV_BMP_2AG_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_TTsko_SOV_BMP_2AG_SOV_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_TTsko_SOV_GAZ_66_SOV_1_1,
        ~/Descriptor_Deck_Pack_FireSupport_TTsko_SOV_GAZ_66_SOV_1_1,
        ~/Descriptor_Deck_Pack_2S23_Nona_SVK_SOV_0_1,
        ~/Descriptor_Deck_Pack_2S3M1_Akatsiya_SOV_1_1,
        ~/Descriptor_Deck_Pack_2S3M1_Akatsiya_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_0_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_0_1,
        ~/Descriptor_Deck_Pack_HvyScout_TTsko_SOV_BMP_2_reco_SOV_0_1,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_Scout_Spetsnaz_SOV_Mi_8TV_SOV_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_TTsko_SOV_UAZ_469_SOV_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_TTsko_SOV_UAZ_469_SOV_1_1,
        ~/Descriptor_Deck_Pack_MTLB_Strela10M3_SOV_0_1,
        ~/Descriptor_Deck_Pack_Tor_SOV_0_1,
        ~/Descriptor_Deck_Pack_Tunguska_2K22_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_24V_RKT_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_29_AA2_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_27M_bombe_SOV_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_TTsko_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_BM21_Grad_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AA_SOV_1_1,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_1,
        ~/Descriptor_Deck_Pack_Su_25T_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_0_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_0_1,
    ]
)

export Descriptor_Deck_SOV_35_AirAslt_Brig_multi is TDeckDescriptor
(
    DeckName = 'FBQIDKOWTQ'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_35_AirAslt_Brig_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Mi_8K_CMD_SOV_2_1,
        ~/Descriptor_Deck_Pack_UAZ_469_supply_VDV_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TZ_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_26_SOV_1_1,
        ~/Descriptor_Deck_Pack_VDV_CMD_SOV_UAZ_469_SOV_2_1,
        ~/Descriptor_Deck_Pack_VDV_Mech_SOV_BMD_2_SOV_2_1,
        ~/Descriptor_Deck_Pack_VDV_Mech_SOV_BMD_2_SOV_2_1,
        ~/Descriptor_Deck_Pack_VDV_Metis_SOV_GAZ_66B_SOV_1_1,
        ~/Descriptor_Deck_Pack_Atteam_Konkurs_VDV_SOV_BTR_D_Robot_SOV_2_1,
        ~/Descriptor_Deck_Pack_Spetsnaz_FireSupport_SOV_UAZ_469_SOV_2_1,
        ~/Descriptor_Deck_Pack_Spetsnaz_SOV_GAZ_66B_SOV_2_1,
        ~/Descriptor_Deck_Pack_Spetsnaz_CMD_SOV_Mi_8TV_non_arme_SOV_3_1,
        ~/Descriptor_Deck_Pack_DShV_SOV_Mi_8TV_SOV_2_1,
        ~/Descriptor_Deck_Pack_Howz_D30_122mm_VDV_SOV_GAZ_66B_SOV_1_1,
        ~/Descriptor_Deck_Pack_MTLB_Vasilek_SOV_1_1,
        ~/Descriptor_Deck_Pack_BM21V_GradV_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mortier_Nona_K_120mm_SOV_UAZ_469_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_1_1,
        ~/Descriptor_Deck_Pack_2S9_Nona_SOV_1_1,
        ~/Descriptor_Deck_Pack_UAZ_469_Konkurs_VDV_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_2_1,
        ~/Descriptor_Deck_Pack_Scout_VDV_SOV_UAZ_469_Reco_SOV_2_1,
        ~/Descriptor_Deck_Pack_Scout_VDV_SOV_UAZ_469_Reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_VDV_SOV_BTR_ZD_Skrezhet_SOV_2_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_VDV_SOV_BTR_ZD_Skrezhet_SOV_2_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_Afghan_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRDM_Strela_1_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_32_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_29_AA_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_25BM_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_27K_LGB_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_27M_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_1,
        ~/Descriptor_Deck_Pack_Ka_50_SOV_2_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AA_SOV_1_1,
        ~/Descriptor_Deck_Pack_Ka_50_AA_SOV_2_1,
    ]
)

export Descriptor_Deck_SOV_39_Gds_Rifle_multi is TDeckDescriptor
(
    DeckName = 'FRXHTCHOCO'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_39_Gds_Rifle_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_2_1,
        ~/Descriptor_Deck_Pack_Engineers_CMD_SOV_BTR_60_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_Metis_SOV_BTR_60_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_SOV_BTR_60_SOV_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_SOV_BTR_60_SOV_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_SOV_BTR_60_SOV_0_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_SPG9_SOV_BTR_60_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AA_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_32_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_1_1,
        ~/Descriptor_Deck_Pack_HvyScout_SOV_BMP_1P_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_Scout_SOV_UAZ_469_Reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_1_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_1,
        ~/Descriptor_Deck_Pack_BM21_Grad_SOV_0_1,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_0_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AA_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_27S_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_25_rkt_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_25_SOV_1_1,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_SOV_1_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_SOV_0_1,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_RPG22_SOV_BMP_2_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_RPG22_SOV_BMP_2_SOV_1_1,
    ]
)

export Descriptor_Deck_SOV_6IndMSBrig_multi is TDeckDescriptor
(
    DeckName = 'IGOVXYAUMV'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_6IndMSBrig_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_FOB_SOV_1_1,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_1,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_SOV_BMP_1PG_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1PG_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1PG_SOV_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_SOV_BTR_60_SOV_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_SOV_UAZ_469_SOV_1_1,
        ~/Descriptor_Deck_Pack_Luftsturmjager_CMD_DDR_Mi_8T_DDR_3_1,
        ~/Descriptor_Deck_Pack_Luftsturmjager_DDR_Mi_8T_DDR_2_1,
        ~/Descriptor_Deck_Pack_Howz_ZiS3_76mm_DDR_W50_LA_A_DDR_0_1,
        ~/Descriptor_Deck_Pack_T64BV_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_T55A_DDR_1_1,
        ~/Descriptor_Deck_Pack_T64BV_SOV_0_1,
        ~/Descriptor_Deck_Pack_T64BV_SOV_0_1,
        ~/Descriptor_Deck_Pack_T80B_SOV_0_1,
        ~/Descriptor_Deck_Pack_Scout_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_HvyScout_DDR_BMP_1P_reco_DDR_1_1,
        ~/Descriptor_Deck_Pack_Alfa_Group_SOV_GAZ_66_SOV_2_1,
        ~/Descriptor_Deck_Pack_Buk_9K37M_SOV_0_1,
        ~/Descriptor_Deck_Pack_Tunguska_2K22_SOV_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_Gunship_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_Gunship_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_31M_SOV_1_1,
        ~/Descriptor_Deck_Pack_L39ZO_HE1_SOV_1_1,
        ~/Descriptor_Deck_Pack_L39ZO_NPLM_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_2_gunship_DDR_1_1,
        ~/Descriptor_Deck_Pack_AT_T12_Rapira_SOV_MTLB_transp_SOV_1_1,
        ~/Descriptor_Deck_Pack_KrAZ_255B_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_2S5_GiatsintS_SOV_0_1,
        ~/Descriptor_Deck_Pack_KrAZ_255B_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_MiG_21bis_CLU_DDR_1_1,
        ~/Descriptor_Deck_Pack_BM27_Uragan_SOV_0_1,
    ]
)

export Descriptor_Deck_SOV_76_VDV_multi is TDeckDescriptor
(
    DeckName = 'LPCRPUZJTW'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_76_VDV_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_LUAZ_967M_CMD_VDV_SOV_1_1,
        ~/Descriptor_Deck_Pack_BMD_2_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_LUAZ_967M_supply_SOV_1_1,
        ~/Descriptor_Deck_Pack_GAZ_66_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_VDV_CMD_SOV_BMD_2_SOV_2_1,
        ~/Descriptor_Deck_Pack_VDV_CMD_SOV_LUAZ_967M_SOV_2_1,
        ~/Descriptor_Deck_Pack_VDV_SOV_BTR_D_SOV_1_1,
        ~/Descriptor_Deck_Pack_VDV_SOV_BTR_D_SOV_1_1,
        ~/Descriptor_Deck_Pack_VDV_Combine_SOV_BTR_D_SOV_1_1,
        ~/Descriptor_Deck_Pack_VDV_Metis_SOV_BTR_D_SOV_1_1,
        ~/Descriptor_Deck_Pack_Spetsnaz_Vympel_SOV_GAZ_66B_SOV_2_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_SPG9_VDV_SOV_LUAZ_967M_SOV_1_1,
        ~/Descriptor_Deck_Pack_Atteam_Fagot_VDV_SOV_BTR_D_Robot_SOV_2_1,
        ~/Descriptor_Deck_Pack_Atteam_Konkurs_VDV_SOV_BTR_D_Robot_SOV_2_1,
        ~/Descriptor_Deck_Pack_2S9_Nona_SOV_1_1,
        ~/Descriptor_Deck_Pack_2S9_Nona_SOV_1_1,
        ~/Descriptor_Deck_Pack_BM21V_GradV_SOV_1_1,
        ~/Descriptor_Deck_Pack_BM21V_GradV_SOV_1_1,
        ~/Descriptor_Deck_Pack_BMD_3_SOV_1_1,
        ~/Descriptor_Deck_Pack_BMD_3_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_1_1,
        ~/Descriptor_Deck_Pack_Scout_Spetsnaz_VDV_SOV_BTR_D_reco_SOV_2_1,
        ~/Descriptor_Deck_Pack_Scout_LRRP_SOV_GAZ_66B_SOV_2_1,
        ~/Descriptor_Deck_Pack_BMD_1_Reostat_SOV_1_1,
        ~/Descriptor_Deck_Pack_BMD_3_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_Pchela_1T_SOV_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_VDV_SOV_BTR_ZD_Skrezhet_SOV_1_1,
        ~/Descriptor_Deck_Pack_GAZ_66B_ZU_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRDM_Strela_1_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_31_AA1_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24MP_EW_SOV_2_1,
        ~/Descriptor_Deck_Pack_Su_24MP_SEAD2_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24M_thermo_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24M_AT2_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24M_LGB2_SOV_1_1,
        ~/Descriptor_Deck_Pack_GAZ_66_supply_SOV_0_1,
    ]
)

export Descriptor_Deck_SOV_79_Gds_Tank_multi is TDeckDescriptor
(
    DeckName = 'YSVBQNJFUH'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_79_Gds_Tank_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_2_1,
        ~/Descriptor_Deck_Pack_T80BV_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_SOV_1_1,
        ~/Descriptor_Deck_Pack_T80BV_Beast_SOV_2_1,
        ~/Descriptor_Deck_Pack_T80BV_Beast_SOV_2_1,
        ~/Descriptor_Deck_Pack_TO_55_SOV_0_1,
        ~/Descriptor_Deck_Pack_Scout_LRRP_SOV_GAZ_66B_SOV_2_1,
        ~/Descriptor_Deck_Pack_HvyScout_SOV_BMP_2_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_SOV_1_1,
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_1_1,
        ~/Descriptor_Deck_Pack_KrAZ_255B_supply_SOV_0_1,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_SOV_UAZ_469_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_2_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_RPG22_SOV_BMP_2D_SOV_1_1,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_2_SOV_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flam_SOV_BTR_60_SOV_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_SOV_UAZ_469_SOV_0_1,
        ~/Descriptor_Deck_Pack_2S3M_Akatsiya_SOV_0_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_s80_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_16_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_29_AA3_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24M_clu2_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_24M_nplm_SOV_1_1,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_1_1,
        ~/Descriptor_Deck_Pack_Tunguska_2K22_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8R_SOV_1_1,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_0_1,
        ~/Descriptor_Deck_Pack_2K12_KUB_SOV_0_1,
        ~/Descriptor_Deck_Pack_FOB_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mortier_2S12_120mm_SOV_MTLB_transp_SOV_1_1,
    ]
)

export Descriptor_Deck_UK_4th_Armoured_multi is TDeckDescriptor
(
    DeckName = 'CISOZFOUBO'
    DeckDivision = ~/Descriptor_Deck_Division_UK_4th_Armoured_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Alvis_Stalwart_UK_0_1,
        ~/Descriptor_Deck_Pack_AEC_Militant_UK_0_1,
        ~/Descriptor_Deck_Pack_CH47D_Chinook_supply_UK_0_1,
        ~/Descriptor_Deck_Pack_Rifles_CMD_UK_Saxon_UK_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Mot_UK_Saxon_UK_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Mot_UK_Saxon_UK_1_1,
        ~/Descriptor_Deck_Pack_Engineers_UK_FV432_SCAT_UK_1_1,
        ~/Descriptor_Deck_Pack_HMGteam_MAG_UK_LandRover_UK_1_1,
        ~/Descriptor_Deck_Pack_RCL_L6_Wombat_UK_Bedford_MJ_4t_trans_UK_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_1_UK_FV432_MILAN_UK_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_UK_FV120_Spartan_MCT_UK_1_1,
        ~/Descriptor_Deck_Pack_FV432_Mortar_UK_0_1,
        ~/Descriptor_Deck_Pack_FV433_Abbot_UK_1_1,
        ~/Descriptor_Deck_Pack_M107A2_175mm_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_CMD_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_CMD_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk6_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk9_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk9_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_UK_1_1,
        ~/Descriptor_Deck_Pack_FV102_Striker_UK_1_1,
        ~/Descriptor_Deck_Pack_Scout_Motorized_UK_Saxon_UK_1_1,
        ~/Descriptor_Deck_Pack_Sniper_UK_LandRover_UK_1_1,
        ~/Descriptor_Deck_Pack_FV721_Fox_UK_1_1,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_Chancellor_UK_1_1,
        ~/Descriptor_Deck_Pack_DCA_Javelin_LML_UK_Bedford_MJ_4t_trans_UK_1_1,
        ~/Descriptor_Deck_Pack_DCA_Rapier_Darkfire_UK_Rover_101FC_UK_1_1,
        ~/Descriptor_Deck_Pack_Tracked_Rapier_UK_1_1,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk1_TOW_UK_2_1,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_I_TOW_UK_1_1,
        ~/Descriptor_Deck_Pack_F4_Phantom_AA_F3_UK_1_1,
        ~/Descriptor_Deck_Pack_F4_Phantom_GR2_HE_UK_1_1,
        ~/Descriptor_Deck_Pack_F4_Phantom_GR2_UK_1_1,
        ~/Descriptor_Deck_Pack_M107A2_175mm_UK_1_1,
        ~/Descriptor_Deck_Pack_Jaguar_RKT_UK_1_1,
    ]
)

export Descriptor_Deck_UK_1st_Armoured_multi is TDeckDescriptor
(
    DeckName = 'SWPIKAMRSW'
    DeckDivision = ~/Descriptor_Deck_Division_UK_1st_Armoured_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Bedford_MJ_4t_UK_0_1,
        ~/Descriptor_Deck_Pack_Alvis_Stalwart_UK_0_1,
        ~/Descriptor_Deck_Pack_CH47D_Chinook_supply_UK_0_1,
        ~/Descriptor_Deck_Pack_FOB_UK_1_1,
        ~/Descriptor_Deck_Pack_Rifles_CMD_UK_MCV_80_Warrior_UK_1_1,
        ~/Descriptor_Deck_Pack_Rifles_UK_MCV_80_Warrior_MILAN_UK_1_1,
        ~/Descriptor_Deck_Pack_Rifles_UK_MCV_80_Warrior_MILAN_UK_1_1,
        ~/Descriptor_Deck_Pack_Engineers_UK_FV432_UK_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_UK_FV120_Spartan_MCT_UK_0_1,
        ~/Descriptor_Deck_Pack_Paratroopers_CMD_UK_LandRover_UK_2_1,
        ~/Descriptor_Deck_Pack_Paratroopers_MILAN_TA_UK_Bedford_MJ_4t_trans_UK_1_1,
        ~/Descriptor_Deck_Pack_FV432_Mortar_UK_0_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_CMD_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_UK_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_UK_1_1,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_1_1,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_1_1,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_1_1,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk3_UK_0_1,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk3_UK_0_1,
        ~/Descriptor_Deck_Pack_LRRP_UK_LandRover_UK_2_1,
        ~/Descriptor_Deck_Pack_FV107_Scimitar_UK_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_UK_1_1,
        ~/Descriptor_Deck_Pack_Scout_AT_UK_Lynx_AH_Mk1_UK_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Javelin_UK_LandRover_UK_1_1,
        ~/Descriptor_Deck_Pack_DCA_Javelin_LML_UK_Bedford_MJ_4t_trans_UK_1_1,
        ~/Descriptor_Deck_Pack_Tracked_Rapier_UK_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_SNEB_UK_1_1,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_I_TOW_UK_1_1,
        ~/Descriptor_Deck_Pack_Tornado_ADV_UK_1_1,
        ~/Descriptor_Deck_Pack_Tornado_ADV_SEAD_UK_1_1,
        ~/Descriptor_Deck_Pack_Tornado_ADV_HE_UK_1_1,
        ~/Descriptor_Deck_Pack_FV433_Abbot_UK_0_1,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_I_TOW_UK_1_1,
    ]
)

export Descriptor_Deck_UK_2nd_Infantry_multi is TDeckDescriptor
(
    DeckName = 'MRGDDWWHJW'
    DeckDivision = ~/Descriptor_Deck_Division_UK_2nd_Infantry_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Saxon_CMD_UK_1_1,
        ~/Descriptor_Deck_Pack_Bedford_MJ_4t_UK_0_1,
        ~/Descriptor_Deck_Pack_Puma_UK_1_1,
        ~/Descriptor_Deck_Pack_CH47D_Chinook_supply_UK_0_1,
        ~/Descriptor_Deck_Pack_Territorial_CMD_UK_Bedford_MJ_4t_trans_UK_1_1,
        ~/Descriptor_Deck_Pack_Territorial_UK_Bedford_MJ_4t_trans_UK_0_1,
        ~/Descriptor_Deck_Pack_Territorial_UK_Bedford_MJ_4t_trans_UK_0_1,
        ~/Descriptor_Deck_Pack_Territorial_UK_Bedford_MJ_4t_trans_UK_0_1,
        ~/Descriptor_Deck_Pack_Engineers_TA_UK_Bedford_MJ_4t_trans_UK_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_UK_Lynx_AH_Mk1_LBH_UK_0_1,
        ~/Descriptor_Deck_Pack_Airmobile_CMD_UK_Lynx_AH_Mk1_UK_1_1,
        ~/Descriptor_Deck_Pack_Airmobile_MILAN_UK_Lynx_AH_Mk1_UK_1_1,
        ~/Descriptor_Deck_Pack_Airmobile_Mot_UK_Saxon_UK_1_1,
        ~/Descriptor_Deck_Pack_SAS_UK_Bedford_MJ_4t_trans_UK_3_1,
        ~/Descriptor_Deck_Pack_81mm_mortar_UK_Lynx_AH_Mk1_UK_1_1,
        ~/Descriptor_Deck_Pack_M107A2_175mm_UK_1_1,
        ~/Descriptor_Deck_Pack_M107A2_175mm_UK_1_1,
        ~/Descriptor_Deck_Pack_LandRover_MILAN_UK_1_1,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_CMD_UK_1_1,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_0_1,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_0_1,
        ~/Descriptor_Deck_Pack_LandRover_MILAN_UK_1_1,
        ~/Descriptor_Deck_Pack_Scout_UK_LandRover_UK_1_1,
        ~/Descriptor_Deck_Pack_FV721_Fox_UK_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_UK_1_1,
        ~/Descriptor_Deck_Pack_Scout_UK_Gazelle_trans_UK_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Blowpipe_UK_LandRover_UK_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Javelin_UK_LandRover_UK_1_1,
        ~/Descriptor_Deck_Pack_DCA_Rapier_FSA_UK_Bedford_MJ_4t_trans_UK_1_1,
        ~/Descriptor_Deck_Pack_Tracked_Rapier_UK_1_1,
        ~/Descriptor_Deck_Pack_Gazelle_SNEB_UK_1_1,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_I_TOW_UK_1_1,
        ~/Descriptor_Deck_Pack_Harrier_UK_1_1,
        ~/Descriptor_Deck_Pack_Harrier_RKT1_UK_1_1,
        ~/Descriptor_Deck_Pack_Harrier_RKT2_UK_1_1,
    ]
)

export Descriptor_Deck_NATO_Garnison_Berlin_multi is TDeckDescriptor
(
    DeckName = 'BRXFUPCHPW'
    DeckDivision = ~/Descriptor_Deck_Division_NATO_Garnison_Berlin_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M35_supply_US_0_1,
        ~/Descriptor_Deck_Pack_TRM_2000_supply_FR_0_1,
        ~/Descriptor_Deck_Pack_UH1H_supply_US_1_1,
        ~/Descriptor_Deck_Pack_FOB_UK_1_1,
        ~/Descriptor_Deck_Pack_LightRifles_CMD_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_LightRifles_LAW_US_M1038_Humvee_US_0_1,
        ~/Descriptor_Deck_Pack_LightRifles_RCL_US_M1038_Humvee_US_0_1,
        ~/Descriptor_Deck_Pack_Sapeurs_Flam_FR_TRM_2000_FR_0_1,
        ~/Descriptor_Deck_Pack_RCL_L6_Wombat_UK_LandRover_UK_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_FR_VLTT_P4_FR_0_1,
        ~/Descriptor_Deck_Pack_Rifles_CMD_FR_VAB_FR_1_1,
        ~/Descriptor_Deck_Pack_Rifles_FR_VAB_FR_1_1,
        ~/Descriptor_Deck_Pack_VAB_Mortar_81_FR_0_1,
        ~/Descriptor_Deck_Pack_M109A2_HOWZ_US_0_1,
        ~/Descriptor_Deck_Pack_M109A2_HOWZ_US_0_1,
        ~/Descriptor_Deck_Pack_AMX_30_B_CMD_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_13_90mm_FR_1_1,
        ~/Descriptor_Deck_Pack_AMX_30_B_FR_1_1,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_UK_0_1,
        ~/Descriptor_Deck_Pack_M1IP_Abrams_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M1IP_Abrams_US_0_1,
        ~/Descriptor_Deck_Pack_M1025_Humvee_TOW_US_0_1,
        ~/Descriptor_Deck_Pack_Scout_FR_VLTT_P4_FR_0_1,
        ~/Descriptor_Deck_Pack_FV721_Fox_UK_0_1,
        ~/Descriptor_Deck_Pack_Alouette_III_reco_FR_1_1,
        ~/Descriptor_Deck_Pack_GreenBerets_MP5_US_UH1H_Huey_US_2_1,
        ~/Descriptor_Deck_Pack_DCA_Javelin_LML_UK_Bedford_MJ_4t_trans_UK_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Mistral_FR_VLTT_P4_FR_0_1,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_0_1,
        ~/Descriptor_Deck_Pack_Harrier_UK_1_1,
        ~/Descriptor_Deck_Pack_Harrier_CLU_UK_1_1,
        ~/Descriptor_Deck_Pack_Mirage_IV_FR_2_1,
        ~/Descriptor_Deck_Pack_Mirage_IV_SEAD_FR_2_1,
        ~/Descriptor_Deck_Pack_F117_Nighthawk_US_2_1,
        ~/Descriptor_Deck_Pack_F117_Nighthawk_US_2_1,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_I_TOW_UK_1_1,
    ]
)

export Descriptor_Deck_UK_5th_Airborne_Brigade_multi is TDeckDescriptor
(
    DeckName = 'DXKFNWMLIP'
    DeckDivision = ~/Descriptor_Deck_Division_UK_5th_Airborne_Brigade_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_F16A_AA2_NL_1_1,
        ~/Descriptor_Deck_Pack_F4F_Phantom_II_AA_RFA_1_1,
        ~/Descriptor_Deck_Pack_Buccaneer_S2B_SEAD_UK_1_1,
        ~/Descriptor_Deck_Pack_Mirage_5_BA_BEL_1_1,
        ~/Descriptor_Deck_Pack_Iltis_para_CMD_RFA_2_1,
        ~/Descriptor_Deck_Pack_ParaCmdo_AT_BEL_Unimog_U1350L_BEL_2_1,
        ~/Descriptor_Deck_Pack_ParaCmdo_AT_BEL_Unimog_U1350L_BEL_2_1,
        ~/Descriptor_Deck_Pack_ParaCmdo_BEL_Unimog_U1350L_Para_BEL_2_1,
        ~/Descriptor_Deck_Pack_Pathfinders_UK_Rover_101FC_UK_2_1,
        ~/Descriptor_Deck_Pack_Scout_ParaCmdo_BEL_A109BA_BEL_1_1,
        ~/Descriptor_Deck_Pack_Scout_ParaCmdo_Mech_BEL_Iltis_trans_BEL_1_1,
        ~/Descriptor_Deck_Pack_LSV_MILAN_UK_3_1,
        ~/Descriptor_Deck_Pack_Westland_Scout_SS11_UK_1_1,
        ~/Descriptor_Deck_Pack_Wiesel_TOW_RFA_1_1,
        ~/Descriptor_Deck_Pack_Faun_Kraka_TOW_RFA_1_1,
        ~/Descriptor_Deck_Pack_Supacat_ATMP_MILAN_UK_1_1,
        ~/Descriptor_Deck_Pack_FV107_Scimitar_para_BEL_1_1,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_I_TOW_UK_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Mistral_para_BEL_Iltis_trans_BEL_1_1,
        ~/Descriptor_Deck_Pack_Supacat_ATMP_Javelin_LML_UK_1_1,
        ~/Descriptor_Deck_Pack_Faun_Kraka_20mm_RFA_1_1,
        ~/Descriptor_Deck_Pack_DCA_Rapier_Darkfire_UK_Rover_101FC_UK_0_1,
        ~/Descriptor_Deck_Pack_FV105_Sultan_BEL_2_1,
        ~/Descriptor_Deck_Pack_Rover_101FC_supply_UK_0_1,
        ~/Descriptor_Deck_Pack_Unimog_U1350L_supply_BEL_1_1,
        ~/Descriptor_Deck_Pack_Paratroopers_UK_Rover_101FC_UK_1_1,
        ~/Descriptor_Deck_Pack_Paratroopers_UK_Rover_101FC_UK_1_1,
        ~/Descriptor_Deck_Pack_Marines_NL_DAF_YA_4400_NL_1_1,
        ~/Descriptor_Deck_Pack_Fallschirm_Engineers_RFA_Unimog_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Gurkhas_CMD_UK_LandRover_UK_2_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_para_RFA_Iltis_trans_RFA_1_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_1_para_BEL_Unimog_U1350L_BEL_1_1,
        ~/Descriptor_Deck_Pack_Howz_L118_105mm_UK_Rover_101FC_UK_1_1,
        ~/Descriptor_Deck_Pack_Buccaneer_S2B_ATGM_UK_2_1,
        ~/Descriptor_Deck_Pack_FV101_Scorpion_para_BEL_1_1,
        ~/Descriptor_Deck_Pack_81mm_mortar_CLU_UK_LandRover_UK_1_1,
    ]
)

export Descriptor_Deck_US_11ACR_multi is TDeckDescriptor
(
    DeckName = 'HXXKRWZDMH'
    DeckDivision = ~/Descriptor_Deck_Division_US_11ACR_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_UH60A_CO_US_2_1,
        ~/Descriptor_Deck_Pack_Unimog_S_404_RFA_0_1,
        ~/Descriptor_Deck_Pack_UH60A_Supply_US_1_1,
        ~/Descriptor_Deck_Pack_CH47_Super_Chinook_US_0_1,
        ~/Descriptor_Deck_Pack_Panzergrenadier_CMD_RFA_Marder_1A3_RFA_1_1,
        ~/Descriptor_Deck_Pack_Panzergrenadier_IFV_RFA_Marder_1A3_MILAN_RFA_1_1,
        ~/Descriptor_Deck_Pack_Rifles_Cavalry_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Dragon_US_M1038_Humvee_US_0_1,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_RFA_Iltis_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_M109A3G_HOWZ_RFA_0_1,
        ~/Descriptor_Deck_Pack_M113_PzMorser_RFA_0_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_BGS_RFA_Iltis_trans_RFA_0_1,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_0_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_reco_US_1_1,
        ~/Descriptor_Deck_Pack_M3A2_Bradley_CFV_US_1_1,
        ~/Descriptor_Deck_Pack_OH58D_Kiowa_Warrior_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M998_Humvee_US_0_1,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_1_1,
        ~/Descriptor_Deck_Pack_DCA_I_Hawk_US_M35_trans_US_0_1,
        ~/Descriptor_Deck_Pack_AH1F_CNITE_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_CNITE_US_1_1,
        ~/Descriptor_Deck_Pack_F16E_AA_US_1_1,
        ~/Descriptor_Deck_Pack_F16E_SEAD_US_1_1,
        ~/Descriptor_Deck_Pack_F16E_AGM_US_1_1,
        ~/Descriptor_Deck_Pack_M728_CEV_US_0_1,
        ~/Descriptor_Deck_Pack_F16C_LGB_US_1_1,
    ]
)

export Descriptor_Deck_US_24th_Inf_multi is TDeckDescriptor
(
    DeckName = 'RZRPDDGIES'
    DeckDivision = ~/Descriptor_Deck_Division_US_24th_Inf_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_FOB_US_1_1,
        ~/Descriptor_Deck_Pack_M548A2_supply_US_0_1,
        ~/Descriptor_Deck_Pack_M548A2_supply_US_0_1,
        ~/Descriptor_Deck_Pack_UH1H_supply_US_1_1,
        ~/Descriptor_Deck_Pack_MP_CMD_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_MP_RCL_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_Rifles_half_Dragon_NG_US_M2_Bradley_IFV_NG_US_0_1,
        ~/Descriptor_Deck_Pack_Rifles_half_LAW_NG_US_M2_Bradley_IFV_NG_US_0_1,
        ~/Descriptor_Deck_Pack_Rifles_half_Dragon_US_M2A1_Bradley_IFV_US_0_1,
        ~/Descriptor_Deck_Pack_Rifles_half_AT4_US_M2A1_Bradley_IFV_US_0_1,
        ~/Descriptor_Deck_Pack_Engineers_Flash_US_M151_MUTT_trans_US_0_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW_US_M151_MUTT_trans_US_0_1,
        ~/Descriptor_Deck_Pack_AeroRifles_US_UH1H_Huey_US_1_1,
        ~/Descriptor_Deck_Pack_M106A2_HOWZ_US_0_1,
        ~/Descriptor_Deck_Pack_M109A2_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M270_MLRS_US_0_1,
        ~/Descriptor_Deck_Pack_M1_Abrams_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M1_Abrams_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M1_Abrams_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M1IP_Abrams_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M1IP_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1IP_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M151A2_TOW_NG_US_0_1,
        ~/Descriptor_Deck_Pack_Scout_NG_US_M151A2_scout_US_0_1,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_0_1,
        ~/Descriptor_Deck_Pack_Scout_US_UH1H_Huey_US_0_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_NG_US_M151_MUTT_trans_US_0_1,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_0_1,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_0_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_NG_US_0_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_NG_US_0_1,
        ~/Descriptor_Deck_Pack_F15C_Eagle_AA2_US_1_1,
        ~/Descriptor_Deck_Pack_F15E_StrikeEagle_US_1_1,
        ~/Descriptor_Deck_Pack_F111F_Aardvark_CBU_US_1_1,
    ]
)

export Descriptor_Deck_US_35th_Inf_multi is TDeckDescriptor
(
    DeckName = 'IQBGXZCRGB'
    DeckDivision = ~/Descriptor_Deck_Division_US_35th_Inf_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_NatGuard_CMD_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_NatGuard_M67_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_NatGuard_Dragon_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_NatGuard_Dragon_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_NatGuard_Engineers_Flam_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_M40A1_NG_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_M106A2_Howz_NG_US_1_1,
        ~/Descriptor_Deck_Pack_M106A2_Howz_NG_US_1_1,
        ~/Descriptor_Deck_Pack_M109A2_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M109A2_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M60A1_RISE_Passive_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M60A1_RISE_Passive_US_1_1,
        ~/Descriptor_Deck_Pack_M60A3_Patton_NG_US_1_1,
        ~/Descriptor_Deck_Pack_M60A3_ERA_Patton_US_1_1,
        ~/Descriptor_Deck_Pack_M113A2_TOW_US_1_1,
        ~/Descriptor_Deck_Pack_M113A2_TOW_US_1_1,
        ~/Descriptor_Deck_Pack_HvyScout_NG_Dragon_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_HvyScout_NG_Dragon_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_LRRP_CEWI_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_A37B_Dragonfly_US_2_1,
        ~/Descriptor_Deck_Pack_M60A1_RISE_Passive_reco_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_NG_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_M42_Duster_US_1_1,
        ~/Descriptor_Deck_Pack_DCA_XMIM_115A_Roland_US_1_1,
        ~/Descriptor_Deck_Pack_DCA_I_Hawk_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_UH1M_gunship_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_NG_US_0_1,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M151_MUTT_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_Gama_Goat_supply_US_1_1,
        ~/Descriptor_Deck_Pack_M812_supply_US_0_1,
        ~/Descriptor_Deck_Pack_A37B_Dragonfly_HE_US_1_1,
        ~/Descriptor_Deck_Pack_A37B_Dragonfly_NPLM_US_1_1,
        ~/Descriptor_Deck_Pack_F4E_Phantom_II_AT_US_1_1,
    ]
)

export Descriptor_Deck_US_3rd_Arm_multi is TDeckDescriptor
(
    DeckName = 'UNCZVMGPSQ'
    DeckDivision = ~/Descriptor_Deck_Division_US_3rd_Arm_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Rifles_half_CMD_US_M2A2_Bradley_IFV_US_2_1,
        ~/Descriptor_Deck_Pack_Rifles_half_Dragon_US_M2A2_Bradley_IFV_US_0_1,
        ~/Descriptor_Deck_Pack_Rifles_half_AT4_US_M2A1_Bradley_IFV_US_1_1,
        ~/Descriptor_Deck_Pack_Rifles_half_AT4_US_M2A1_Bradley_IFV_US_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flash_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_Scout_US_M151A2_scout_US_1_1,
        ~/Descriptor_Deck_Pack_Sniper_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_1_1,
        ~/Descriptor_Deck_Pack_OH58D_Kiowa_Warrior_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_1,
        ~/Descriptor_Deck_Pack_M270_MLRS_cluster_US_0_1,
        ~/Descriptor_Deck_Pack_M109A2_HOWZ_US_0_1,
        ~/Descriptor_Deck_Pack_M109A2_HOWZ_US_0_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1HA_Abrams_US_0_1,
        ~/Descriptor_Deck_Pack_M1A1HA_Abrams_US_0_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_emp1_US_1_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_emp2_US_1_1,
        ~/Descriptor_Deck_Pack_M2A2_Bradley_Leader_US_1_1,
        ~/Descriptor_Deck_Pack_M113A2_supply_US_0_1,
        ~/Descriptor_Deck_Pack_M113A2_supply_US_0_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW2_US_M151_MUTT_trans_US_0_1,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_0_1,
        ~/Descriptor_Deck_Pack_F15C_Eagle_AA_US_1_1,
        ~/Descriptor_Deck_Pack_F111E_Aardvark_CBU_US_1_1,
        ~/Descriptor_Deck_Pack_F111E_Aardvark_napalm_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1HA_Abrams_US_0_1,
        ~/Descriptor_Deck_Pack_M728_CEV_US_0_1,
        ~/Descriptor_Deck_Pack_M901A1_ITW_US_1_1,
    ]
)

export Descriptor_Deck_US_6th_Light_multi is TDeckDescriptor
(
    DeckName = 'WGTUVQYDKY'
    DeckDivision = ~/Descriptor_Deck_Division_US_6th_Light_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_CUCV_US_0_1,
        ~/Descriptor_Deck_Pack_M35_supply_US_0_1,
        ~/Descriptor_Deck_Pack_CH54B_Tarhe_supply_US_0_1,
        ~/Descriptor_Deck_Pack_UH1A_US_1_1,
        ~/Descriptor_Deck_Pack_NatGuard_CMD_US_M113A1_Dragon_NG_US_1_1,
        ~/Descriptor_Deck_Pack_NatGuard_LAW_US_M113A1_Dragon_NG_US_1_1,
        ~/Descriptor_Deck_Pack_Security_USMC_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_ATteam_ITOW_NG_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_ATteam_RCL_M40A1_NG_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_Airborne_CMD_US_M1038_Humvee_US_2_1,
        ~/Descriptor_Deck_Pack_Airborne_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Airborne_Dragon_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Navy_SEAL_US_M35_trans_US_3_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW2_para_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Howz_M119_105mm_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_Mortier_107mm_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Mortier_107mm_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_M151A2_TOW_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M901_TOW_NG_US_1_1,
        ~/Descriptor_Deck_Pack_M1025_Humvee_TOW_US_1_1,
        ~/Descriptor_Deck_Pack_Sniper_NG_Alaska_US_M151A2_scout_US_2_1,
        ~/Descriptor_Deck_Pack_HvyScout_NG_Alaska_US_M113A1_ACAV_NG_US_2_1,
        ~/Descriptor_Deck_Pack_LRRP_US_UH1H_Huey_US_2_1,
        ~/Descriptor_Deck_Pack_M48A5_reco_NG_US_1_1,
        ~/Descriptor_Deck_Pack_M48A5_reco_NG_US_1_1,
        ~/Descriptor_Deck_Pack_OA10A_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_NG_US_M151_MUTT_trans_US_0_1,
        ~/Descriptor_Deck_Pack_DCA_M167_Vulcan_20mm_nonPara_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_DCA_I_Hawk_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_1,
        ~/Descriptor_Deck_Pack_F15C_Eagle_AA2_US_1_1,
        ~/Descriptor_Deck_Pack_A7D_Corsair_II_US_1_1,
        ~/Descriptor_Deck_Pack_A7D_Corsair_II_AT_US_1_1,
        ~/Descriptor_Deck_Pack_A7D_Corsair_II_RKT_US_1_1,
        ~/Descriptor_Deck_Pack_A6E_Intruder_SEAD_US_1_1,
        ~/Descriptor_Deck_Pack_M1025_Humvee_TOW_US_1_1,
    ]
)

export Descriptor_Deck_US_82nd_Airborne_multi is TDeckDescriptor
(
    DeckName = 'CAJUCIFHGO'
    DeckDivision = ~/Descriptor_Deck_Division_US_82nd_Airborne_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_OH58C_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M35_supply_US_0_1,
        ~/Descriptor_Deck_Pack_UH60A_Supply_US_1_1,
        ~/Descriptor_Deck_Pack_Airborne_Engineer_CMD_US_M1038_Humvee_US_2_1,
        ~/Descriptor_Deck_Pack_Airborne_Dragon_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Airborne_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Airborne_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_HMGteam_M2HB_AB_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Airborne_Engineers_Flash_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW2_para_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_AeroRifles_CMD_US_UH60A_Black_Hawk_US_2_1,
        ~/Descriptor_Deck_Pack_Howz_M102_105mm_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Airborne_Scout_US_M1025_Humvee_scout_US_1_1,
        ~/Descriptor_Deck_Pack_LAV_25_M1047_US_US_1_1,
        ~/Descriptor_Deck_Pack_OH58D_Combat_Scout_US_1_1,
        ~/Descriptor_Deck_Pack_M998_Avenger_US_1_1,
        ~/Descriptor_Deck_Pack_AH6C_Little_Bird_US_3_1,
        ~/Descriptor_Deck_Pack_AH1F_ATAS_US_1_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_emp2_US_1_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_US_1_1,
        ~/Descriptor_Deck_Pack_M551A1_TTS_Sheridan_US_1_1,
        ~/Descriptor_Deck_Pack_M551A1_TTS_Sheridan_US_1_1,
        ~/Descriptor_Deck_Pack_M551A1_TTS_Sheridan_US_1_1,
        ~/Descriptor_Deck_Pack_M1025_Humvee_TOW_para_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_para_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_OH58_CS_US_1_1,
        ~/Descriptor_Deck_Pack_F15C_Eagle_AA_US_1_1,
        ~/Descriptor_Deck_Pack_F111F_Aardvark_CBU_US_1_1,
        ~/Descriptor_Deck_Pack_F111F_Aardvark_napalm_US_1_1,
        ~/Descriptor_Deck_Pack_A10_Thunderbolt_II_Rkt_US_1_1,
        ~/Descriptor_Deck_Pack_F16E_SEAD_US_1_1,
        ~/Descriptor_Deck_Pack_AeroRifles_AB_US_UH60A_Black_Hawk_US_1_1,
        ~/Descriptor_Deck_Pack_CH47_Super_Chinook_US_0_1,
        ~/Descriptor_Deck_Pack_AeroRifles_AB_US_UH60A_Black_Hawk_US_1_1,
        ~/Descriptor_Deck_Pack_CH47_Super_Chinook_US_0_1,
        ~/Descriptor_Deck_Pack_M551A1_ACAV_Sheridan_US_1_1,
    ]
)

export Descriptor_Deck_US_8th_Inf_multi is TDeckDescriptor
(
    DeckName = 'QQYUUBQMEO'
    DeckDivision = ~/Descriptor_Deck_Division_US_8th_Inf_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Rifles_CMD_US_M113A3_US_1_1,
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_1_1,
        ~/Descriptor_Deck_Pack_Rifles_LAW_US_M113_Dragon_US_1_1,
        ~/Descriptor_Deck_Pack_Rifles_LAW_US_M113_Dragon_US_1_1,
        ~/Descriptor_Deck_Pack_Engineers_Flash_US_M113A3_US_0_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW2_US_M151_MUTT_trans_US_0_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW2_US_M151_MUTT_trans_US_0_1,
        ~/Descriptor_Deck_Pack_MP_RCL_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_Ranger_US_UH60A_Black_Hawk_US_2_1,
        ~/Descriptor_Deck_Pack_M60A3_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M60A3_Patton_US_0_1,
        ~/Descriptor_Deck_Pack_M1_Abrams_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_0_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_0_1,
        ~/Descriptor_Deck_Pack_Scout_US_M113_ACAV_US_0_1,
        ~/Descriptor_Deck_Pack_LRRP_US_M998_Humvee_US_2_1,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_1_1,
        ~/Descriptor_Deck_Pack_OH58D_Kiowa_Warrior_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_1_1,
        ~/Descriptor_Deck_Pack_DCA_I_Hawk_US_M35_trans_US_0_1,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_1,
        ~/Descriptor_Deck_Pack_M110A2_HOWZ_US_1_1,
        ~/Descriptor_Deck_Pack_M110A2_HOWZ_US_1_1,
        ~/Descriptor_Deck_Pack_M106A2_HOWZ_US_0_1,
        ~/Descriptor_Deck_Pack_F16E_AA_US_1_1,
        ~/Descriptor_Deck_Pack_F111F_Aardvark_LGB_US_1_1,
        ~/Descriptor_Deck_Pack_F111F_Aardvark_US_1_1,
        ~/Descriptor_Deck_Pack_A10_Thunderbolt_II_ATGM_US_1_1,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_0_1,
        ~/Descriptor_Deck_Pack_M35_supply_US_0_1,
        ~/Descriptor_Deck_Pack_M35_supply_US_0_1,
        ~/Descriptor_Deck_Pack_HEMTT_US_0_1,
    ]
)

export Descriptor_Deck_US_9th_Mot_multi is TDeckDescriptor
(
    DeckName = 'VNHPMSAWYT'
    DeckDivision = ~/Descriptor_Deck_Division_US_9th_Mot_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_NatGuard_CMD_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_NatGuard_LAW_US_M35_trans_US_0_1,
        ~/Descriptor_Deck_Pack_NatGuard_Dragon_US_M35_trans_US_0_1,
        ~/Descriptor_Deck_Pack_MP_RCL_US_M1025_Humvee_MP_US_1_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_LightRifles_CMD_US_M998_Humvee_AGL_US_1_1,
        ~/Descriptor_Deck_Pack_LightRifles_LAW_US_M998_Humvee_HMG_US_1_1,
        ~/Descriptor_Deck_Pack_LightRifles_Dragon_US_M998_Humvee_HMG_US_1_1,
        ~/Descriptor_Deck_Pack_M106A2_Howz_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M60A1_AVLM_US_0_1,
        ~/Descriptor_Deck_Pack_M109A2_NG_US_0_1,
        ~/Descriptor_Deck_Pack_M60A3_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M60A3_Patton_NG_US_1_1,
        ~/Descriptor_Deck_Pack_M60A3_Patton_NG_US_1_1,
        ~/Descriptor_Deck_Pack_M60A3_Patton_US_1_1,
        ~/Descriptor_Deck_Pack_M1025_Humvee_TOW_US_1_1,
        ~/Descriptor_Deck_Pack_CUCV_Hellfire_US_1_1,
        ~/Descriptor_Deck_Pack_Scout_NG_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_Sniper_M82_US_FAV_trans_US_1_1,
        ~/Descriptor_Deck_Pack_FAV_AGL_US_1_1,
        ~/Descriptor_Deck_Pack_FAV_TOW_US_1_1,
        ~/Descriptor_Deck_Pack_MQM_105_Aquila_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_CUCV_trans_US_1_1,
        ~/Descriptor_Deck_Pack_DCA_XM85_Chaparral_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_M998_Avenger_nonPara_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_Hog_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_1,
        ~/Descriptor_Deck_Pack_F15C_Eagle_AA2_US_1_1,
        ~/Descriptor_Deck_Pack_FA16_CAS_US_1_1,
        ~/Descriptor_Deck_Pack_F16E_HE_US_1_1,
        ~/Descriptor_Deck_Pack_M270_MLRS_cluster_US_0_1,
        ~/Descriptor_Deck_Pack_HMGteam_Mk19_US_CUCV_trans_US_1_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW2A_US_CUCV_trans_US_1_1,
        ~/Descriptor_Deck_Pack_CUCV_US_0_1,
        ~/Descriptor_Deck_Pack_CUCV_US_0_1,
        ~/Descriptor_Deck_Pack_M35_supply_US_0_1,
    ]
)

export Descriptor_Deck_US_101st_Airmobile_multi is TDeckDescriptor
(
    DeckName = 'SOPQEOVMKR'
    DeckDivision = ~/Descriptor_Deck_Division_US_101st_Airmobile_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M1025_Humvee_CMD_US_2_1,
        ~/Descriptor_Deck_Pack_OH58C_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_Gama_Goat_supply_US_1_1,
        ~/Descriptor_Deck_Pack_CH47_Super_Chinook_US_1_1,
        ~/Descriptor_Deck_Pack_GreenBerets_CMD_US_MH47D_Super_Chinook_US_3_1,
        ~/Descriptor_Deck_Pack_GreenBerets_ODA_US_MH47D_Super_Chinook_US_2_1,
        ~/Descriptor_Deck_Pack_Aero_half_Dragon_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_MP_RCL_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_AeroEngineers_US_M1038_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_AeroRifles_AB_US_UH60A_Black_Hawk_US_1_1,
        ~/Descriptor_Deck_Pack_AeroRifles_Dragon_US_UH60A_Black_Hawk_US_1_1,
        ~/Descriptor_Deck_Pack_AeroRifles_AT4_US_CH47_Chinook_US_1_1,
        ~/Descriptor_Deck_Pack_ATteam_TOW2_Aero_US_Gama_Goat_trans_US_1_1,
        ~/Descriptor_Deck_Pack_Howz_M198_155mm_US_M35_trans_US_1_1,
        ~/Descriptor_Deck_Pack_81mm_mortar_Aero_US_Gama_Goat_trans_US_1_1,
        ~/Descriptor_Deck_Pack_M1IP_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1IP_Abrams_US_1_1,
        ~/Descriptor_Deck_Pack_M1025_Humvee_TOW_US_1_1,
        ~/Descriptor_Deck_Pack_EH60A_EW_US_1_1,
        ~/Descriptor_Deck_Pack_OH58D_Combat_Scout_US_1_1,
        ~/Descriptor_Deck_Pack_LRRP_Aero_US_CH47_Chinook_US_1_1,
        ~/Descriptor_Deck_Pack_DeltaForce_US_M998_Humvee_Delta_US_2_1,
        ~/Descriptor_Deck_Pack_Sniper_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_Aero_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_DCA_M167A2_Vulcan_20mm_Aero_US_Gama_Goat_trans_US_1_1,
        ~/Descriptor_Deck_Pack_AH1F_HeavyHog_US_1_1,
        ~/Descriptor_Deck_Pack_MH_60A_DAP_US_3_1,
        ~/Descriptor_Deck_Pack_F15C_Eagle_AA2_US_1_1,
        ~/Descriptor_Deck_Pack_F4E_Phantom_II_AA_US_1_1,
        ~/Descriptor_Deck_Pack_F4E_Phantom_II_HE_US_1_1,
        ~/Descriptor_Deck_Pack_M274_Mule_RCL_US_1_1,
        ~/Descriptor_Deck_Pack_AH6C_Little_Bird_US_3_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_ATAS_US_1_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_ATAS_US_1_1,
        ~/Descriptor_Deck_Pack_AH64_Apache_US_1_1,
        ~/Descriptor_Deck_Pack_Howz_M102_105mm_US_CH47_Chinook_US_1_1,
    ]
)

export Descriptor_Deck_SOV_56_AirAslt_Brig_multi is TDeckDescriptor
(
    DeckName = 'RDCLMXSIZI'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_56_AirAslt_Brig_multi
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BTR_60_CHAIKA_CMD_SOV_1_1,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_1,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_6_SOV_1_1,
        ~/Descriptor_Deck_Pack_DShV_SOV_BMP_2D_SOV_1_1,
        ~/Descriptor_Deck_Pack_DShV_SOV_BMP_2D_SOV_1_1,
        ~/Descriptor_Deck_Pack_DShV_Metis_SOV_BTR_70_AGS_SOV_1_1,
        ~/Descriptor_Deck_Pack_DShV_RPG16_SOV_GTMU_1D_AGS_SOV_1_1,
        ~/Descriptor_Deck_Pack_DShV_Afgantsy_SOV_Mi_24D_Desant_SOV_2_1,
        ~/Descriptor_Deck_Pack_DShV_Hvy_SOV_Mi_8TV_non_arme_SOV_2_1,
        ~/Descriptor_Deck_Pack_DShV_Hvy_SOV_Mi_8TV_non_arme_SOV_2_1,
        ~/Descriptor_Deck_Pack_Security_SOV_Ural_4320_trans_SOV_0_1,
        ~/Descriptor_Deck_Pack_Atteam_Konkurs_DShV_SOV_LUAZ_967M_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mortier_240mm_M240_Cluster_SOV_KrAZ_255B_SOV_1_1,
        ~/Descriptor_Deck_Pack_BM21_Grad_SOV_1_1,
        ~/Descriptor_Deck_Pack_T62MD_CMD_SOV_2_1,
        ~/Descriptor_Deck_Pack_T62MD1_SOV_1_1,
        ~/Descriptor_Deck_Pack_T62MD_SOV_1_1,
        ~/Descriptor_Deck_Pack_LUAZ_967M_Fagot_SOV_1_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_8PPA_SOV_1_1,
        ~/Descriptor_Deck_Pack_Scout_SpetsnazGRU_Stinger_SOV_LUAZ_967M_SOV_2_1,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_DShV_SOV_BTR_70D_SOV_1_1,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_SOV_1_1,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_Afghan_SOV_1_1,
        ~/Descriptor_Deck_Pack_Ural_4320_ZU_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24P_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24P_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_23MLD_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_23MLD_AA1_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_23MLD_AA1_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_27K_AT2_SOV_1_1,
        ~/Descriptor_Deck_Pack_MiG_27K_LGB_SOV_1_1,
        ~/Descriptor_Deck_Pack_Engineers_DShV_SOV_BTR_70_S8_SOV_1_1,
    ]
)

export Descriptor_Deck_FR_11e_para_challenge_OP_5_Southag_Para_IA is TDeckDescriptor
(
    DeckName = 'SAAGGANQTR'
    DeckIdentifier = 'OP_5_Southag_Para_IA'
    DeckDivision = ~/Descriptor_Deck_Division_FR_11e_para_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Rifles_Aero_FR_Super_Puma_FR_1_4,
        ~/Descriptor_Deck_Pack_Rifles_Aero_CMD_FR_Super_Puma_FR_1_4,
        ~/Descriptor_Deck_Pack_Scout_para_FR_VLRA_HMG_FR_1_6,
        ~/Descriptor_Deck_Pack_VBL_Reco_FR_1_6,
        ~/Descriptor_Deck_Pack_AML_60_FR_1_6,
        ~/Descriptor_Deck_Pack_Mortier_MORT61_120mm_para_FR_VLRA_trans_FR_1_4,
        ~/Descriptor_Deck_Pack_VLRA_supply_FR_1_4,
        ~/Descriptor_Deck_Pack_VLTT_P4_PC_FR_1_4,
        ~/Descriptor_Deck_Pack_ATteam_Milan_1_para_FR_VLRA_trans_FR_1_4,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_FR_1_5,
        ~/Descriptor_Deck_Pack_Para_FR_VAB_FR_1_6,
        ~/Descriptor_Deck_Pack_Para_Sapeurs_FR_VAB_FR_1_6,
        ~/Descriptor_Deck_Pack_SAS_Sniper_FR_VAB_FR_1_4,
    ]
)

export Descriptor_Deck_SOV_79_Gds_Tank_challenge_OP_BASE_BB_IA is TDeckDescriptor
(
    DeckName = 'IBNYUGGVGI'
    DeckIdentifier = 'OP_BASE_BB_IA'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_79_Gds_Tank_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_0_999,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_999,
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_3_999,
        ~/Descriptor_Deck_Pack_Howz_D30_122mm_SOV_MTLB_transp_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80BV_CMD_SOV_3_999,
        ~/Descriptor_Deck_Pack_T62M1_SOV_2_999,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1P_SOV_3_999,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_SOV_1_999,
        ~/Descriptor_Deck_Pack_Tunguska_2K22_SOV_2_999,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_2_999,
        ~/Descriptor_Deck_Pack_HvyScout_SOV_BMP_1P_reco_SOV_0_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_2_999,
        ~/Descriptor_Deck_Pack_BRDM_2_SOV_2_999,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_24VP_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8TV_s80_SOV_2_999,
        ~/Descriptor_Deck_Pack_MiG_23MLD_AA1_SOV_2_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_999,
    ]
)

export Descriptor_Deck_US_3rd_Arm_challenge_OP_BASE_BB_Player is TDeckDescriptor
(
    DeckName = 'XIGLNUDIWS'
    DeckIdentifier = 'OP_BASE_BB_Player'
    DeckDivision = ~/Descriptor_Deck_Division_US_3rd_Arm_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M35_supply_US_1_4,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_4,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_4,
        ~/Descriptor_Deck_Pack_M577_US_1_3,
        ~/Descriptor_Deck_Pack_HMGteam_M60_US_M998_Humvee_US_1_1,
        ~/Descriptor_Deck_Pack_Rifles_US_M35_trans_US_1_4,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_1,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_1_2,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_1_2,
        ~/Descriptor_Deck_Pack_M113_ACAV_US_1_3,
        ~/Descriptor_Deck_Pack_M901A1_ITW_US_1_3,
        ~/Descriptor_Deck_Pack_M106A2_HOWZ_US_2_4,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_15,
        ~/Descriptor_Deck_Pack_M1A1HA_Abrams_CMD_US_2_1,
        ~/Descriptor_Deck_Pack_M1025_Humvee_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M1025_Humvee_CMD_US_1_1,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_1_4,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_1_4,
        ~/Descriptor_Deck_Pack_Scout_US_M113_ACAV_US_2_4,
        ~/Descriptor_Deck_Pack_A10_Thunderbolt_II_ATGM_US_1_2,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "ZRMILCMBPM"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (3,1),
                        (12,3),
                        (5,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AFXAGTUHDY"
                    PackIndexUnitNumberList =
                    [
                        (9,3),
                        (10,3),
                        (4,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "ZISMTAUYGO"
                    PackIndexUnitNumberList =
                    [
                        (7,2),
                        (8,2),
                        (6,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "JYZDSITBSM"
                    PackIndexUnitNumberList =
                    [
                        (11,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "SRHWTUYLYQ"
                    PackIndexUnitNumberList =
                    [
                        (0,12),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "YRYIJYWVRT"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (13,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (12,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (12,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (12,4),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "LGWJRRZQVH"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (14,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (16,4),
                        (18,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (16,4),
                        (18,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_1"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "FCSWUWUIEP"
                    PackIndexUnitNumberList =
                    [
                        (19,2),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_US_11ACR_challenge_OP_BASE_BLS_Ally is TDeckDescriptor
(
    DeckName = 'QTYATWRDBA'
    DeckIdentifier = 'OP_BASE_BLS_Ally'
    DeckDivision = ~/Descriptor_Deck_Division_US_11ACR_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M35_supply_US_1_100,
        ~/Descriptor_Deck_Pack_M577_US_1_100,
        ~/Descriptor_Deck_Pack_Engineer_CMD_US_M998_Humvee_US_3_100,
        ~/Descriptor_Deck_Pack_Engineers_Dragon_US_M998_Humvee_US_2_100,
        ~/Descriptor_Deck_Pack_M106A2_HOWZ_US_2_100,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_2_100,
        ~/Descriptor_Deck_Pack_M901A1_ITW_US_2_100,
        ~/Descriptor_Deck_Pack_M113_ACAV_US_1_100,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_2_100,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_1_100,
        ~/Descriptor_Deck_Pack_A10_Thunderbolt_II_ATGM_US_2_100,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_CMD_US_3_100,
        ~/Descriptor_Deck_Pack_M1025_Humvee_AGL_nonPara_US_2_100,
        ~/Descriptor_Deck_Pack_Scout_US_M1025_Humvee_scout_US_2_100,
        ~/Descriptor_Deck_Pack_Panzergrenadier_CMD_RFA_Unimog_trans_RFA_3_100,
        ~/Descriptor_Deck_Pack_Jager_RFA_Unimog_trans_RFA_1_100,
        ~/Descriptor_Deck_Pack_Security_RFA_Unimog_trans_RFA_0_100,
        ~/Descriptor_Deck_Pack_ATteam_RCL_M40A1_RFA_Unimog_trans_RFA_1_100,
        ~/Descriptor_Deck_Pack_Unimog_S_404_RFA_0_100,
        ~/Descriptor_Deck_Pack_Jager_CMD_RFA_Unimog_trans_RFA_3_100,
        ~/Descriptor_Deck_Pack_HeimatschutzJager_RFA_Unimog_trans_RFA_1_100,
        ~/Descriptor_Deck_Pack_Mortier_Tampella_120mm_RFA_Unimog_trans_RFA_2_100,
        ~/Descriptor_Deck_Pack_BGS_RFA_Unimog_trans_RFA_2_100,
        ~/Descriptor_Deck_Pack_DCA_FK20_2_20mm_Zwillinge_RFA_Unimog_trans_RFA_1_100,
    ]
)

export Descriptor_Deck_SOV_39_Gds_Rifle_challenge_OP_BASE_BLS_IA is TDeckDescriptor
(
    DeckName = 'QPWYLUTPYN'
    DeckIdentifier = 'OP_BASE_BLS_IA'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_39_Gds_Rifle_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_2_999,
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_2_999,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_999,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1PG_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_Metis_SOV_BMP_1P_SOV_1_999,
        ~/Descriptor_Deck_Pack_BM21_Grad_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80BV_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_0_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8R_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_24V_AA_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_32_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8TV_s80_SOV_1_999,
        ~/Descriptor_Deck_Pack_2K12_KUB_SOV_1_999,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_SOV_BTR_60_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_Metis_SOV_BMP_1P_SOV_1_999,
        ~/Descriptor_Deck_Pack_Engineers_CMD_SOV_BTR_60_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_0_999,
    ]
)

export Descriptor_Deck_US_11ACR_challenge_OP_BASE_BLS_Player is TDeckDescriptor
(
    DeckName = 'VXJMVTRDUH'
    DeckIdentifier = 'OP_BASE_BLS_Player'
    DeckDivision = ~/Descriptor_Deck_Division_US_11ACR_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M35_supply_US_1_100,
        ~/Descriptor_Deck_Pack_Unimog_S_404_RFA_1_100,
        ~/Descriptor_Deck_Pack_M577_US_1_100,
        ~/Descriptor_Deck_Pack_Engineers_Dragon_US_M998_Humvee_US_1_100,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M113A3_US_3_100,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_3_100,
        ~/Descriptor_Deck_Pack_DCA_FK20_2_20mm_Zwillinge_RFA_Unimog_trans_RFA_1_100,
        ~/Descriptor_Deck_Pack_Scout_US_M1025_Humvee_scout_US_2_100,
        ~/Descriptor_Deck_Pack_M1025_Humvee_AGL_nonPara_US_2_100,
        ~/Descriptor_Deck_Pack_BGS_RFA_Unimog_trans_RFA_2_100,
        ~/Descriptor_Deck_Pack_Jager_RFA_Unimog_trans_RFA_0_100,
        ~/Descriptor_Deck_Pack_Jager_RFA_Unimog_trans_RFA_1_100,
        ~/Descriptor_Deck_Pack_Security_RFA_Unimog_trans_RFA_1_100,
        ~/Descriptor_Deck_Pack_Mortier_Tampella_120mm_RFA_Unimog_trans_RFA_2_100,
        ~/Descriptor_Deck_Pack_Jager_CMD_RFA_Unimog_trans_RFA_1_100,
        ~/Descriptor_Deck_Pack_A10_Thunderbolt_II_ATGM_US_1_100,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_CMD_US_1_100,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_100,
        ~/Descriptor_Deck_Pack_M901A1_ITW_US_1_100,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_2_100,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "BETUFEMGDL"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SLWSVLLPZP"
                    PackIndexUnitNumberList =
                    [
                        (2,1),
                        (3,4),
                        (0,5),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "DLZTKEQFFT"
                    PackIndexUnitNumberList =
                    [
                        (16,1),
                        (17,2),
                        (19,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "UQOKRWWLYC"
                    PackIndexUnitNumberList =
                    [
                        (16,1),
                        (17,2),
                        (19,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "EBAXRQFWNO"
                    PackIndexUnitNumberList =
                    [
                        (8,2),
                        (7,2),
                        (18,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "FQMCKESHIX"
                    PackIndexUnitNumberList =
                    [
                        (5,4),
                        (4,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "HYKDXVFKPJ"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "QPXHKBIWGL"
                    PackIndexUnitNumberList =
                    [
                        (14,1),
                        (12,2),
                        (1,5),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "OIQGCEKCDM"
                    PackIndexUnitNumberList =
                    [
                        (9,1),
                        (10,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "CRZWJJYXZT"
                    PackIndexUnitNumberList =
                    [
                        (9,1),
                        (10,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "OPDTEQBKBP"
                    PackIndexUnitNumberList =
                    [
                        (13,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "EHFLKHNINV"
                    PackIndexUnitNumberList =
                    [
                        (6,4),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_1"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "FCSWUWUIEP"
                    PackIndexUnitNumberList =
                    [
                        (15,2),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_US_3rd_Arm_challenge_OP_BASE_GOT_IA1 is TDeckDescriptor
(
    DeckName = 'GCOZBJCCFF'
    DeckIdentifier = 'OP_BASE_GOT_IA1'
    DeckDivision = ~/Descriptor_Deck_Division_US_3rd_Arm_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M577_US_0_999,
        ~/Descriptor_Deck_Pack_M151_MUTT_CMD_US_0_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_0_999,
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_1_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_1_999,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_1_999,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_1_999,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_1_999,
        ~/Descriptor_Deck_Pack_M106A2_HOWZ_US_1_999,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_999,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_0_999,
        ~/Descriptor_Deck_Pack_AH1F_Hog_US_1_999,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_999,
        ~/Descriptor_Deck_Pack_OH58_CS_US_1_999,
        ~/Descriptor_Deck_Pack_LRRP_US_UH60A_Black_Hawk_US_1_999,
        ~/Descriptor_Deck_Pack_OH58C_Scout_US_1_999,
    ]
)

export Descriptor_Deck_US_3rd_Arm_challenge_OP_BASE_GOT_IA2 is TDeckDescriptor
(
    DeckName = 'IDICNZQQRP'
    DeckIdentifier = 'OP_BASE_GOT_IA2'
    DeckDivision = ~/Descriptor_Deck_Division_US_3rd_Arm_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_1_999,
        ~/Descriptor_Deck_Pack_HMGteam_M2HB_US_M2A1_Bradley_IFV_US_1_999,
        ~/Descriptor_Deck_Pack_HMGteam_M2HB_US_M2A1_Bradley_IFV_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_half_Dragon_US_M2A1_Bradley_IFV_US_1_999,
        ~/Descriptor_Deck_Pack_M113_ACAV_US_1_999,
        ~/Descriptor_Deck_Pack_M113_ACAV_US_1_999,
        ~/Descriptor_Deck_Pack_M106A2_HOWZ_US_1_999,
        ~/Descriptor_Deck_Pack_M901_TOW_US_1_999,
        ~/Descriptor_Deck_Pack_M901_TOW_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_1_999,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_1_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_1_999,
        ~/Descriptor_Deck_Pack_M113A1_TOW_US_1_999,
        ~/Descriptor_Deck_Pack_M577_US_1_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_CMD_US_M113A3_US_1_999,
        ~/Descriptor_Deck_Pack_M109A2_HOWZ_US_1_999,
    ]
)

export Descriptor_Deck_RFA_12_Panzer_challenge_OP_BASE_GOT_IA3 is TDeckDescriptor
(
    DeckName = 'QPYAJTGPPN'
    DeckIdentifier = 'OP_BASE_GOT_IA3'
    DeckDivision = ~/Descriptor_Deck_Division_RFA_12_Panzer_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Luchs_A1_RFA_1_999,
        ~/Descriptor_Deck_Pack_M577_RFA_0_999,
        ~/Descriptor_Deck_Pack_Leopard_2A3_CMD_RFA_1_999,
        ~/Descriptor_Deck_Pack_Leopard_2A3_CMD_RFA_1_999,
        ~/Descriptor_Deck_Pack_Leopard_1A1_RFA_1_999,
        ~/Descriptor_Deck_Pack_Leopard_1A1_RFA_1_999,
        ~/Descriptor_Deck_Pack_Leopard_1A1_RFA_1_999,
        ~/Descriptor_Deck_Pack_Jaguar_2_RFA_1_999,
        ~/Descriptor_Deck_Pack_Gepard_1A2_RFA_1_999,
        ~/Descriptor_Deck_Pack_Luchs_A1_RFA_1_999,
        ~/Descriptor_Deck_Pack_M577_RFA_0_999,
        ~/Descriptor_Deck_Pack_M113A1G_supply_RFA_0_999,
    ]
)

export Descriptor_Deck_RDA_11MSD_challenge_OP_BASE_GOT_Player is TDeckDescriptor
(
    DeckName = 'EVTAGXNEPV'
    DeckIdentifier = 'OP_BASE_GOT_Player'
    DeckDivision = ~/Descriptor_Deck_Division_RDA_11MSD_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_3,
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_3,
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_3,
        ~/Descriptor_Deck_Pack_Ural_4320_DDR_0_3,
        ~/Descriptor_Deck_Pack_BTR_60_CMD_DDR_3_3,
        ~/Descriptor_Deck_Pack_MotRifles_Metis_DDR_BTR_70_DDR_2_9,
        ~/Descriptor_Deck_Pack_Engineers_CMD_DDR_UAZ_469_trans_DDR_2_3,
        ~/Descriptor_Deck_Pack_ATteam_Fagot_DDR_UAZ_469_trans_DDR_2_9,
        ~/Descriptor_Deck_Pack_AT_T12_Rapira_DDR_MTLB_trans_DDR_3_6,
        ~/Descriptor_Deck_Pack_AT_T12_Rapira_DDR_MTLB_trans_DDR_3_6,
        ~/Descriptor_Deck_Pack_BRDM_Konkurs_DDR_2_9,
        ~/Descriptor_Deck_Pack_Scout_DDR_UAZ_469_trans_DDR_2_12,
        ~/Descriptor_Deck_Pack_BRDM_2_DDR_3_9,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_DDR_UAZ_469_trans_DDR_2_12,
        ~/Descriptor_Deck_Pack_BRDM_Strela_1_DDR_2_8,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_DDR_3_6,
        ~/Descriptor_Deck_Pack_Mi_24P_s8_AT2_DDR_2_1,
        ~/Descriptor_Deck_Pack_Su_22_clu_DDR_2_3,
        ~/Descriptor_Deck_Pack_Howz_D30_122mm_DDR_MTLB_trans_DDR_2_4,
        ~/Descriptor_Deck_Pack_Howz_D30_122mm_DDR_MTLB_trans_DDR_2_4,
        ~/Descriptor_Deck_Pack_Mortier_PM43_120mm_DDR_MTLB_trans_DDR_2_4,
        ~/Descriptor_Deck_Pack_MotRifles_Metis_DDR_BMP_1_SP1_DDR_3_9,
        ~/Descriptor_Deck_Pack_Howz_D30_122mm_DDR_MTLB_trans_DDR_3_4,
        ~/Descriptor_Deck_Pack_Mortier_PM43_120mm_DDR_MTLB_trans_DDR_3_4,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_DDR_UAZ_469_trans_DDR_3_12,
        ~/Descriptor_Deck_Pack_HMGteam_AGS17_DDR_UAZ_469_trans_DDR_3_6,
        ~/Descriptor_Deck_Pack_BRDM_Strela_1_DDR_3_8,
        ~/Descriptor_Deck_Pack_Mi_24P_s8_AT2_DDR_2_1,
        ~/Descriptor_Deck_Pack_Su_22_AT_DDR_2_3,
        ~/Descriptor_Deck_Pack_Scout_DDR_UAZ_469_Reco_DDR_3_12,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_DDR_BTR_70_DDR_1_9,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_DDR_BTR_70_DDR_1_9,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_DDR_BTR_70_DDR_1_9,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "LSKTJWMLCY"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "QPXHKBIWGL"
                    PackIndexUnitNumberList =
                    [
                        (4,1),
                        (5,2),
                        (0,8),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AAIWEZVTZN"
                    PackIndexUnitNumberList =
                    [
                        (25,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "FGPDEUABPI"
                    PackIndexUnitNumberList =
                    [
                        (29,4),
                        (12,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "FPKZLIBICB"
                    PackIndexUnitNumberList =
                    [
                        (14,2),
                        (15,1),
                        (24,6),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "OPDTEQBKBP"
                    PackIndexUnitNumberList =
                    [
                        (18,3),
                        (22,1),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "IHCDFIICYD"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "QPXHKBIWGL"
                    PackIndexUnitNumberList =
                    [
                        (6,1),
                        (5,1),
                        (20,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "OIQGCEKCDM"
                    PackIndexUnitNumberList =
                    [
                        (30,3),
                        (7,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "CRZWJJYXZT"
                    PackIndexUnitNumberList =
                    [
                        (30,3),
                        (7,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "XSQBUZHFTL"
                    PackIndexUnitNumberList =
                    [
                        (30,3),
                        (7,1),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "VWPETUIMJA"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "QPXHKBIWGL"
                    PackIndexUnitNumberList =
                    [
                        (6,1),
                        (5,1),
                        (20,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "OIQGCEKCDM"
                    PackIndexUnitNumberList =
                    [
                        (30,3),
                        (7,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "CRZWJJYXZT"
                    PackIndexUnitNumberList =
                    [
                        (30,3),
                        (7,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "XSQBUZHFTL"
                    PackIndexUnitNumberList =
                    [
                        (30,3),
                        (7,1),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "VGGMGCGPJS"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "QPXHKBIWGL"
                    PackIndexUnitNumberList =
                    [
                        (6,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "OIQGCEKCDM"
                    PackIndexUnitNumberList =
                    [
                        (10,1),
                        (8,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "CRZWJJYXZT"
                    PackIndexUnitNumberList =
                    [
                        (10,1),
                        (8,4),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_4"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "GHBXFHBFGX"
                    PackIndexUnitNumberList =
                    [
                        (16,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "NXGFGNGFXN"
                    PackIndexUnitNumberList =
                    [
                        (28,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "NXFGHXNHN"
                    PackIndexUnitNumberList =
                    [
                        (17,1),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_UK_Blues_Royals_challenge_OP_BASE_HUR_Ally is TDeckDescriptor
(
    DeckName = 'RVMZYXPTQA'
    DeckIdentifier = 'OP_BASE_HUR_Ally'
    DeckDivision = ~/Descriptor_Deck_Division_UK_Blues_Royals_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_FV432_CMD_UK_1_3,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (0,1),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_SOV_35_AirAslt_Brig_challenge_OP_BASE_HUR_IA1 is TDeckDescriptor
(
    DeckName = 'JEEBNQCDWC'
    DeckIdentifier = 'OP_BASE_HUR_IA1'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_35_AirAslt_Brig_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_UAZ_469_supply_VDV_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8TZ_SOV_1_999,
        ~/Descriptor_Deck_Pack_BMD_1_CMD_SOV_3_999,
        ~/Descriptor_Deck_Pack_UAZ_469_CMD_VDV_SOV_2_999,
        ~/Descriptor_Deck_Pack_Atteam_Fagot_VDV_SOV_BTR_D_Robot_SOV_3_999,
        ~/Descriptor_Deck_Pack_Engineers_Flam_VDV_SOV_UAZ_469_SOV_2_999,
        ~/Descriptor_Deck_Pack_VDV_Mech_SOV_BMD_2_SOV_1_999,
        ~/Descriptor_Deck_Pack_VDV_Metis_SOV_GAZ_66B_SOV_2_999,
        ~/Descriptor_Deck_Pack_VDV_SOV_GAZ_66B_SOV_2_999,
        ~/Descriptor_Deck_Pack_VDV_HMG_SOV_GAZ_66B_SOV_3_999,
        ~/Descriptor_Deck_Pack_DShV_CMD_SOV_Mi_8TV_SOV_3_999,
        ~/Descriptor_Deck_Pack_DShV_SOV_Mi_8TV_SOV_1_999,
        ~/Descriptor_Deck_Pack_VDV_CMD_SOV_BTR_D_SOV_3_999,
        ~/Descriptor_Deck_Pack_Engineers_CMD_VDV_SOV_UAZ_469_SOV_3_999,
        ~/Descriptor_Deck_Pack_2S9_Nona_SOV_1_999,
        ~/Descriptor_Deck_Pack_MTLB_Vasilek_SOV_1_999,
        ~/Descriptor_Deck_Pack_Howz_D30_122mm_VDV_SOV_GAZ_66B_SOV_1_999,
        ~/Descriptor_Deck_Pack_T64B_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_2_999,
        ~/Descriptor_Deck_Pack_AT_D44_85mm_VDV_SOV_GAZ_66B_SOV_2_999,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_2_999,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_999,
        ~/Descriptor_Deck_Pack_Scout_LRRP_SOV_Mi_8TV_SOV_1_999,
        ~/Descriptor_Deck_Pack_Scout_VDV_SOV_UAZ_469_Reco_SOV_3_999,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_3_999,
        ~/Descriptor_Deck_Pack_Ka_50_SOV_3_999,
        ~/Descriptor_Deck_Pack_Ka_50_SOV_3_999,
        ~/Descriptor_Deck_Pack_Mi_8TV_s80_SOV_2_999,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_2_999,
        ~/Descriptor_Deck_Pack_Su_25_clu_SOV_2_999,
        ~/Descriptor_Deck_Pack_MiG_23ML_SOV_2_999,
        ~/Descriptor_Deck_Pack_Mi_24V_RKT_SOV_2_4,
        ~/Descriptor_Deck_Pack_MiG_25BM_SOV_1_999,
    ]
)

export Descriptor_Deck_UK_Blues_Royals_challenge_OP_BASE_HUR_Player is TDeckDescriptor
(
    DeckName = 'DGVHKXYWKN'
    DeckIdentifier = 'OP_BASE_HUR_Player'
    DeckDivision = ~/Descriptor_Deck_Division_UK_Blues_Royals_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_1_3,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_1_3,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_1_3,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_UK_1_3,
        ~/Descriptor_Deck_Pack_Challenger_1_Mk1_CMD_UK_1_1,
        ~/Descriptor_Deck_Pack_FV105_Sultan_UK_1_3,
        ~/Descriptor_Deck_Pack_Rifles_AT_UK_FV432_UK_1_9,
        ~/Descriptor_Deck_Pack_Ferret_Mk2_UK_1_9,
        ~/Descriptor_Deck_Pack_Rifles_AT_UK_FV432_UK_1_9,
        ~/Descriptor_Deck_Pack_Scout_UK_FV103_Spartan_UK_1_9,
        ~/Descriptor_Deck_Pack_FV101_Scorpion_UK_1_9,
        ~/Descriptor_Deck_Pack_FV438_Swingfire_UK_1_9,
        ~/Descriptor_Deck_Pack_DCA_Rapier_FSA_UK_Bedford_MJ_4t_trans_UK_3_10,
        ~/Descriptor_Deck_Pack_Tracked_Rapier_UK_3_6,
        ~/Descriptor_Deck_Pack_MANPAD_Javelin_UK_LandRover_UK_1_12,
        ~/Descriptor_Deck_Pack_Bedford_MJ_4t_UK_0_5,
        ~/Descriptor_Deck_Pack_Bedford_MJ_4t_UK_0_5,
        ~/Descriptor_Deck_Pack_Bedford_MJ_4t_UK_0_5,
        ~/Descriptor_Deck_Pack_DCA_Javelin_LML_UK_Bedford_MJ_4t_trans_UK_3_6,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "MOQJAJQKCU"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SLWSVLLPZP"
                    PackIndexUnitNumberList =
                    [
                        (5,3),
                        (6,4),
                        (15,15),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WBESWFYVOK"
                    PackIndexUnitNumberList =
                    [
                        (9,4),
                        (10,4),
                        (7,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TABDGYHUPF"
                    PackIndexUnitNumberList =
                    [
                        (11,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "ZISMTAUYGO"
                    PackIndexUnitNumberList =
                    [
                        (18,6),
                        (12,4),
                        (13,4),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "RCWSMVEAWA"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SLWSVLLPZP"
                    PackIndexUnitNumberList =
                    [
                        (4,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "GITRTZAZLS"
                    PackIndexUnitNumberList =
                    [
                        (0,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "MATKYCBLCT"
                    PackIndexUnitNumberList =
                    [
                        (0,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "NQZWSPPSTW"
                    PackIndexUnitNumberList =
                    [
                        (0,3),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_RFA_TerrKdo_Sud_challenge_OP_BASE_KR_IA1 is TDeckDescriptor
(
    DeckName = 'VAFLSUNPTO'
    DeckIdentifier = 'OP_BASE_KR_IA1'
    DeckDivision = ~/Descriptor_Deck_Division_RFA_TerrKdo_Sud_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Iltis_RFA_2_999,
        ~/Descriptor_Deck_Pack_M577_RFA_2_999,
        ~/Descriptor_Deck_Pack_Scout_RFA_Iltis_trans_RFA_2_999,
        ~/Descriptor_Deck_Pack_Jager_CMD_RFA_Iltis_trans_RFA_2_999,
        ~/Descriptor_Deck_Pack_Jager_Aufk_RFA_Unimog_trans_RFA_2_999,
        ~/Descriptor_Deck_Pack_Jager_RFA_M113A1G_RFA_2_999,
        ~/Descriptor_Deck_Pack_HMGteam_MG3_RFA_Iltis_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_ATteam_Milan_1_RFA_Iltis_trans_RFA_2_999,
        ~/Descriptor_Deck_Pack_HS30_Panzermorser_120mm_RFA_2_999,
        ~/Descriptor_Deck_Pack_M48A2C_RFA_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Redeye_RFA_Iltis_trans_RFA_2_999,
        ~/Descriptor_Deck_Pack_Unimog_S_404_RFA_1_999,
        ~/Descriptor_Deck_Pack_M48A2GA2_RFA_2_999,
        ~/Descriptor_Deck_Pack_M48A2GA2_CMD_RFA_2_999,
    ]
)

export Descriptor_Deck_US_8th_Inf_challenge_OP_BASE_KR_IA2 is TDeckDescriptor
(
    DeckName = 'QIKVIPSJXZ'
    DeckIdentifier = 'OP_BASE_KR_IA2'
    DeckDivision = ~/Descriptor_Deck_Division_US_8th_Inf_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M577_US_2_999,
        ~/Descriptor_Deck_Pack_HMGteam_M60_US_M113A3_US_2_999,
        ~/Descriptor_Deck_Pack_M106A2_HOWZ_US_2_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_2_999,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_999,
        ~/Descriptor_Deck_Pack_M60A3_Patton_US_2_999,
        ~/Descriptor_Deck_Pack_M60A3_CMD_US_2_999,
        ~/Descriptor_Deck_Pack_Rifles_CMD_US_M113A3_US_2_999,
        ~/Descriptor_Deck_Pack_Rifles_US_M113A3_US_2_999,
        ~/Descriptor_Deck_Pack_Rifles_HMG_US_M113A3_US_2_999,
        ~/Descriptor_Deck_Pack_OH58D_Kiowa_Warrior_US_2_999,
        ~/Descriptor_Deck_Pack_OH58C_Scout_US_2_999,
        ~/Descriptor_Deck_Pack_AH1F_Hog_US_2_999,
        ~/Descriptor_Deck_Pack_CH47_Super_Chinook_US_1_999,
        ~/Descriptor_Deck_Pack_Alpha_Jet_A_nplm_RFA_2_999,
        ~/Descriptor_Deck_Pack_Alpha_Jet_A_he_RFA_2_999,
        ~/Descriptor_Deck_Pack_F16E_AA_US_2_999,
    ]
)

export Descriptor_Deck_SOV_35_AirAslt_Brig_challenge_OP_BASE_KR_Player is TDeckDescriptor
(
    DeckName = 'HUSOTKTUJT'
    DeckIdentifier = 'OP_BASE_KR_Player'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_35_AirAslt_Brig_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMD_1_CMD_SOV_2_2,
        ~/Descriptor_Deck_Pack_VDV_Mech_SOV_BMD_1_SOV_2_14,
        ~/Descriptor_Deck_Pack_Scout_VDV_SOV_UAZ_469_SOV_2_6,
        ~/Descriptor_Deck_Pack_2S9_Nona_SOV_2_4,
        ~/Descriptor_Deck_Pack_BM21V_GradV_SOV_2_6,
        ~/Descriptor_Deck_Pack_Atteam_Konkurs_VDV_SOV_BTR_D_Robot_SOV_2_3,
        ~/Descriptor_Deck_Pack_GAZ_66B_ZU_SOV_2_2,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_VDV_SOV_BTR_ZD_Skrezhet_SOV_2_8,
        ~/Descriptor_Deck_Pack_UAZ_469_supply_VDV_SOV_1_12,
        ~/Descriptor_Deck_Pack_VDV_CMD_SOV_BTR_D_SOV_2_1,
        ~/Descriptor_Deck_Pack_HMGteam_AGS17_VDV_SOV_UAZ_469_SOV_2_2,
        ~/Descriptor_Deck_Pack_VDV_HMG_SOV_GAZ_66B_SOV_2_3,
        ~/Descriptor_Deck_Pack_Mortier_2B9_Vasilek_SOV_UAZ_469_SOV_2_4,
        ~/Descriptor_Deck_Pack_DShV_CMD_SOV_Mi_8TV_non_arme_SOV_2_1,
        ~/Descriptor_Deck_Pack_DShV_SOV_Mi_8TV_non_arme_SOV_2_12,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_2_2,
        ~/Descriptor_Deck_Pack_Mi_24VP_SOV_2_2,
        ~/Descriptor_Deck_Pack_Mi_24V_AA_SOV_2_2,
        ~/Descriptor_Deck_Pack_Mi_8K_CMD_SOV_2_2,
        ~/Descriptor_Deck_Pack_Engineers_Scout_SOV_Mi_2_trans_SOV_2_3,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_SOV_BTR_60_SOV_2_6,
        ~/Descriptor_Deck_Pack_Mortier_2B9_Vasilek_SOV_UAZ_469_SOV_2_4,
        ~/Descriptor_Deck_Pack_Mi_8R_SOV_2_1,
        ~/Descriptor_Deck_Pack_Mi_26_SOV_1_2,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "AIFMQGIAGU"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (0,2),
                        (1,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "EBAXRQFWNO"
                    PackIndexUnitNumberList =
                    [
                        (2,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "JYZDSITBSM"
                    PackIndexUnitNumberList =
                    [
                        (3,4),
                        (4,6),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "HBLUTIMQXB"
                    PackIndexUnitNumberList =
                    [
                        (6,2),
                        (7,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "SRHWTUYLYQ"
                    PackIndexUnitNumberList =
                    [
                        (8,12),
                        (20,6),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "MWICGOJROV"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (9,1),
                        (10,2),
                        (1,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "EBAXRQFWNO"
                    PackIndexUnitNumberList =
                    [
                        (2,2),
                        (12,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (1,3),
                        (7,1),
                        (11,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (1,3),
                        (7,1),
                        (11,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (1,3),
                        (7,1),
                        (11,1),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "GHUYXATXPF"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (13,1),
                        (18,2),
                        (14,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "EBAXRQFWNO"
                    PackIndexUnitNumberList =
                    [
                        (19,3),
                        (22,1),
                        (12,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                        (7,1),
                        (5,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                        (7,1),
                        (5,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                        (7,1),
                        (5,1),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_2"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "AIIMCNDONF"
                    PackIndexUnitNumberList =
                    [
                        (15,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "MMISGNOMQO"
                    PackIndexUnitNumberList =
                    [
                        (16,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "HBLUTIMQXB"
                    PackIndexUnitNumberList =
                    [
                        (17,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "OMMSABUJXJ"
                    PackIndexUnitNumberList =
                    [
                        (23,2),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_US_3rd_Arm_challenge_OP_BASE_RJ_IA is TDeckDescriptor
(
    DeckName = 'XIGLNUDIWS'
    DeckIdentifier = 'OP_BASE_RJ_IA'
    DeckDivision = ~/Descriptor_Deck_Division_US_3rd_Arm_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_2_999,
        ~/Descriptor_Deck_Pack_M1A1HA_Abrams_US_3_999,
        ~/Descriptor_Deck_Pack_M901_TOW_US_1_999,
        ~/Descriptor_Deck_Pack_M109A2_HOWZ_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_half_Dragon_US_M2A1_Bradley_IFV_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_CMD_US_M35_trans_US_3_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_1_999,
        ~/Descriptor_Deck_Pack_M113A2_supply_US_1_999,
        ~/Descriptor_Deck_Pack_M1025_Humvee_CMD_US_1_999,
        ~/Descriptor_Deck_Pack_M1025_Humvee_CMD_US_1_999,
        ~/Descriptor_Deck_Pack_M1025_Humvee_CMD_US_1_999,
        ~/Descriptor_Deck_Pack_HMGteam_M60_US_M2A1_Bradley_IFV_US_1_999,
        ~/Descriptor_Deck_Pack_HMGteam_M60_US_M2A1_Bradley_IFV_US_1_999,
        ~/Descriptor_Deck_Pack_HMGteam_M60_US_M2A1_Bradley_IFV_US_1_999,
        ~/Descriptor_Deck_Pack_Engineers_US_M113A3_US_2_999,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_CMD_US_1_999,
        ~/Descriptor_Deck_Pack_Scout_US_UH60A_Black_Hawk_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_2_999,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_0_999,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_999,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_1_999,
        ~/Descriptor_Deck_Pack_AH64_Apache_emp1_US_1_999,
        ~/Descriptor_Deck_Pack_AH64_Apache_US_1_999,
        ~/Descriptor_Deck_Pack_M577_US_0_999,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_999,
        ~/Descriptor_Deck_Pack_AH64_Apache_emp2_US_1_999,
        ~/Descriptor_Deck_Pack_AH64_Apache_US_0_999,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_3_999,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_3_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_2_999,
    ]
)

export Descriptor_Deck_SOV_79_Gds_Tank_challenge_OP_BASE_RJ_Player is TDeckDescriptor
(
    DeckName = 'SJOVWSADJC'
    DeckIdentifier = 'OP_BASE_RJ_Player'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_79_Gds_Tank_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_1_2,
        ~/Descriptor_Deck_Pack_BRDM_2_SOV_1_2,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_2,
        ~/Descriptor_Deck_Pack_HvyScout_SOV_BMP_1P_reco_SOV_1_2,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_1_2,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_SOV_1_2,
        ~/Descriptor_Deck_Pack_Tunguska_2K22_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mortier_2S12_120mm_SOV_MTLB_transp_SOV_2_4,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_1_2,
        ~/Descriptor_Deck_Pack_T80B_CMD_SOV_1_2,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_8,
        ~/Descriptor_Deck_Pack_T80BV_SOV_1_8,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_8,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_0_8,
        ~/Descriptor_Deck_Pack_T80BV_SOV_1_8,
        ~/Descriptor_Deck_Pack_T80B_SOV_1_8,
        ~/Descriptor_Deck_Pack_BRDM_2_SOV_1_2,
        ~/Descriptor_Deck_Pack_DShV_CMD_SOV_Mi_8TV_SOV_2_2,
        ~/Descriptor_Deck_Pack_DShV_SOV_Mi_8TV_non_arme_SOV_1_9,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_Mi_2_trans_SOV_1_2,
        ~/Descriptor_Deck_Pack_HMGteam_PKM_SOV_Mi_2_trans_SOV_1_2,
        ~/Descriptor_Deck_Pack_Mi_24K_reco_SOV_1_1,
        ~/Descriptor_Deck_Pack_Mi_24P_SOV_1_1,
        ~/Descriptor_Deck_Pack_Su_25_nplm_SOV_1_2,
        ~/Descriptor_Deck_Pack_MiG_23ML_SOV_1_3,
        ~/Descriptor_Deck_Pack_Su_24M_SOV_1_2,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "AIFMQGIAGU"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (0,1),
                        (1,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "EBAXRQFWNO"
                    PackIndexUnitNumberList =
                    [
                        (2,2),
                        (3,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "HBLUTIMQXB"
                    PackIndexUnitNumberList =
                    [
                        (5,2),
                        (6,1),
                        (4,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "KPNJIRFMVK"
                    PackIndexUnitNumberList =
                    [
                        (7,4),
                        (8,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "YHHKVXFYTB"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (9,1),
                        (12,6),
                        (1,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (10,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (10,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (11,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "JFDLGUGPAH"
                    PackIndexUnitNumberList =
                    [
                        (11,3),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "BIUSBBTLXM"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (9,1),
                        (12,6),
                        (1,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (10,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (10,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (11,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "JFDLGUGPAH"
                    PackIndexUnitNumberList =
                    [
                        (11,3),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "CDWDIWURMP"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (17,1),
                        (18,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (18,3),
                        (20,1),
                        (19,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (18,3),
                        (20,1),
                        (19,1),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_2"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "EBAXRQFWNO"
                    PackIndexUnitNumberList =
                    [
                        (21,1),
                        (22,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (24,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (23,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (25,2),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_SOV_79_Gds_Tank_challenge_OP_BASE_SH_Ally_L is TDeckDescriptor
(
    DeckName = 'EFFSOFCOVX'
    DeckIdentifier = 'OP_BASE_SH_Ally_L'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_79_Gds_Tank_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MTLB_CMD_SOV_0_3,
    ]
)

export Descriptor_Deck_SOV_27_Gds_Rifle_challenge_OP_BASE_SH_Ally_R is TDeckDescriptor
(
    DeckName = 'AYRQRMNAWN'
    DeckIdentifier = 'OP_BASE_SH_Ally_R'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_27_Gds_Rifle_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_1_3,
    ]
)

export Descriptor_Deck_US_3rd_Arm_challenge_OP_BASE_SH_IA1 is TDeckDescriptor
(
    DeckName = 'XIGLNUDIWS'
    DeckIdentifier = 'OP_BASE_SH_IA1'
    DeckDivision = ~/Descriptor_Deck_Division_US_3rd_Arm_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M1A1_Abrams_CMD_US_2_999,
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_2_999,
        ~/Descriptor_Deck_Pack_M901A1_ITW_US_2_999,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_2_999,
        ~/Descriptor_Deck_Pack_Scout_US_M998_Humvee_US_2_999,
        ~/Descriptor_Deck_Pack_OH58C_Scout_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_2_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_2_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_2_999,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_2_999,
        ~/Descriptor_Deck_Pack_M48_Chaparral_MIM72F_US_2_999,
        ~/Descriptor_Deck_Pack_M113A2_supply_US_1_999,
        ~/Descriptor_Deck_Pack_Mortier_107mm_US_M151_MUTT_trans_US_2_999,
        ~/Descriptor_Deck_Pack_AH1F_Hog_US_2_999,
        ~/Descriptor_Deck_Pack_AH1F_Hog_US_2_999,
        ~/Descriptor_Deck_Pack_M728_CEV_US_2_999,
        ~/Descriptor_Deck_Pack_Engineer_CMD_US_M113A3_US_2_999,
        ~/Descriptor_Deck_Pack_Rifles_US_M35_trans_US_2_999,
        ~/Descriptor_Deck_Pack_HMGteam_M60_US_M998_Humvee_US_2_999,
        ~/Descriptor_Deck_Pack_HMGteam_M60_US_M998_Humvee_US_2_999,
        ~/Descriptor_Deck_Pack_M113A2_supply_US_1_999,
        ~/Descriptor_Deck_Pack_M151_MUTT_CMD_US_2_999,
    ]
)

export Descriptor_Deck_US_8th_Inf_challenge_OP_BASE_SH_IA2 is TDeckDescriptor
(
    DeckName = 'QIKVIPSJXZ'
    DeckIdentifier = 'OP_BASE_SH_IA2'
    DeckDivision = ~/Descriptor_Deck_Division_US_8th_Inf_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_M1A1_Abrams_US_1_999,
        ~/Descriptor_Deck_Pack_Rifles_half_LAW_US_M2A1_Bradley_IFV_US_0_999,
        ~/Descriptor_Deck_Pack_ATteam_TOW2_US_M113A3_US_0_999,
        ~/Descriptor_Deck_Pack_M35_supply_US_0_999,
        ~/Descriptor_Deck_Pack_M577_US_3_999,
        ~/Descriptor_Deck_Pack_M106A2_HOWZ_US_2_999,
        ~/Descriptor_Deck_Pack_M3A1_Bradley_CFV_US_2_999,
        ~/Descriptor_Deck_Pack_M163_PIVADS_US_2_999,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_2_999,
        ~/Descriptor_Deck_Pack_Engineers_Flash_US_M998_Humvee_US_2_999,
    ]
)

export Descriptor_Deck_RFA_TerrKdo_Sud_challenge_OP_BASE_SH_IA3 is TDeckDescriptor
(
    DeckName = 'PJQELQWBBX'
    DeckIdentifier = 'OP_BASE_SH_IA3'
    DeckDivision = ~/Descriptor_Deck_Division_RFA_TerrKdo_Sud_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MAN_Kat_6x6_RFA_1_999,
        ~/Descriptor_Deck_Pack_MAN_Kat_6x6_RFA_1_999,
        ~/Descriptor_Deck_Pack_CH53G_RFA_1_999,
        ~/Descriptor_Deck_Pack_UH1D_Supply_RFA_1_999,
        ~/Descriptor_Deck_Pack_HeimatschutzJager_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_HeimatschutzJager_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_Engineers_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_Engineers_Flam_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_Engineers_AT_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_Rifles_Aero_FR_Super_Puma_FR_1_999,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_FR_Super_Puma_FR_1_999,
        ~/Descriptor_Deck_Pack_Rifles_Aero_CMD_FR_Super_Puma_FR_2_999,
        ~/Descriptor_Deck_Pack_Engineers_AT_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_Howz_M101_105mm_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_Howz_M101_105mm_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_HS30_Panzermorser_120mm_RFA_1_999,
        ~/Descriptor_Deck_Pack_KanJagdPanzer_RFA_1_999,
        ~/Descriptor_Deck_Pack_M48A2C_RFA_2_999,
        ~/Descriptor_Deck_Pack_M48A2GA2_RFA_1_999,
        ~/Descriptor_Deck_Pack_M48A2GA2_RFA_1_999,
        ~/Descriptor_Deck_Pack_VLTT_P4_MILAN_FR_1_999,
        ~/Descriptor_Deck_Pack_M48A2GA2_CMD_RFA_3_999,
        ~/Descriptor_Deck_Pack_Gazelle_20mm_reco_FR_1_999,
        ~/Descriptor_Deck_Pack_Jager_Aufk_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_Jager_Aufk_RFA_Unimog_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_Bofors_40mm_RFA_MAN_Kat_6x6_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Redeye_RFA_Iltis_trans_RFA_1_999,
        ~/Descriptor_Deck_Pack_DCA_I_Hawk_RFA_MAN_Kat_6x6_trans_RFA_2_999,
        ~/Descriptor_Deck_Pack_Marder_Roland_2_RFA_1_999,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_FR_1_999,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_2_FR_1_999,
        ~/Descriptor_Deck_Pack_Mirage_2000_C_FR_1_999,
        ~/Descriptor_Deck_Pack_Mirage_5_F_clu_FR_1_999,
        ~/Descriptor_Deck_Pack_F4F_Phantom_II_HE1_RFA_1_999,
    ]
)

export Descriptor_Deck_SOV_57_GMRD_challenge_OP_BASE_SH_Player is TDeckDescriptor
(
    DeckName = 'LRELUYUNRY'
    DeckIdentifier = 'OP_BASE_SH_Player'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_57_GMRD_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_1_4,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_2_SOV_1_4,
        ~/Descriptor_Deck_Pack_T62MV_SOV_1_4,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_1_4,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_4,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_4,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_4,
        ~/Descriptor_Deck_Pack_Engineers_CMD_SOV_UAZ_469_SOV_1_4,
        ~/Descriptor_Deck_Pack_Engineers_SOV_BTR_60_SOV_1_4,
        ~/Descriptor_Deck_Pack_Engineers_Flam_SOV_BTR_60_SOV_1_4,
        ~/Descriptor_Deck_Pack_Mi_8TV_s80_SOV_1_4,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_4,
        ~/Descriptor_Deck_Pack_Mi_24V_AT_SOV_1_4,
        ~/Descriptor_Deck_Pack_TO_55_SOV_1_4,
        ~/Descriptor_Deck_Pack_Engineers_CMD_SOV_BMP_1P_SOV_1_4,
        ~/Descriptor_Deck_Pack_BRDM_2_SOV_1_4,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "IMKHWKBLFM"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (0,1),
                        (1,2),
                        (13,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "ZISMTAUYGO"
                    PackIndexUnitNumberList =
                    [
                        (15,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "SRHWTUYLYQ"
                    PackIndexUnitNumberList =
                    [
                        (4,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "SCRDVVAAUD"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (14,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (8,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (8,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (9,1),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "FHKFTFHPLX"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (10,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (11,1),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_FR_7e_Blindee_challenge_OP_BASE_TDS_Ally is TDeckDescriptor
(
    DeckName = 'BAPEFRPLHH'
    DeckIdentifier = 'OP_BASE_TDS_Ally'
    DeckDivision = ~/Descriptor_Deck_Division_FR_7e_Blindee_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_VAB_CMD_FR_1_3,
    ]
)

export Descriptor_Deck_SOV_79_Gds_Tank_challenge_OP_BASE_TDS_IA1 is TDeckDescriptor
(
    DeckName = 'EDQLAONOLE'
    DeckIdentifier = 'OP_BASE_TDS_IA1'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_79_Gds_Tank_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_T80UD_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80U_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1P_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRDM_2_SOV_1_999,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_1_999,
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_1_999,
        ~/Descriptor_Deck_Pack_TO_55_SOV_1_999,
    ]
)

export Descriptor_Deck_SOV_79_Gds_Tank_challenge_OP_BASE_TDS_IA2 is TDeckDescriptor
(
    DeckName = 'NEPZAQDRRN'
    DeckIdentifier = 'OP_BASE_TDS_IA2'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_79_Gds_Tank_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Engineers_SOV_GAZ_66_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRDM_2_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_Engineers_Scout_SOV_GAZ_66_SOV_1_999,
        ~/Descriptor_Deck_Pack_Engineers_CMD_SOV_UAZ_469_SOV_1_999,
        ~/Descriptor_Deck_Pack_Engineers_CMD_SOV_UAZ_469_SOV_1_999,
        ~/Descriptor_Deck_Pack_MTLB_supply_SOV_0_999,
        ~/Descriptor_Deck_Pack_2S3M_Akatsiya_SOV_1_999,
        ~/Descriptor_Deck_Pack_Engineers_Flam_SOV_GAZ_66_SOV_1_999,
        ~/Descriptor_Deck_Pack_TOS1_Buratino_SOV_2_999,
        ~/Descriptor_Deck_Pack_TOS1_Buratino_SOV_2_999,
        ~/Descriptor_Deck_Pack_TOS1_Buratino_SOV_2_999,
        ~/Descriptor_Deck_Pack_TOS1_Buratino_SOV_2_999,
        ~/Descriptor_Deck_Pack_2S3M_Akatsiya_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1P_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_2_999,
        ~/Descriptor_Deck_Pack_BTR_60_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRDM_2_Konkurs_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mortier_2B9_Vasilek_SOV_UAZ_469_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_16_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_16_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8TV_s80_SOV_1_999,
        ~/Descriptor_Deck_Pack_Mi_8TV_s80_SOV_1_999,
        ~/Descriptor_Deck_Pack_Su_24M_clu_SOV_1_999,
        ~/Descriptor_Deck_Pack_Su_24M_nplm_SOV_1_999,
        ~/Descriptor_Deck_Pack_TO_55_SOV_1_999,
        ~/Descriptor_Deck_Pack_HvyScout_SOV_BMP_1P_reco_SOV_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_1_999,
    ]
)

export Descriptor_Deck_SOV_79_Gds_Tank_challenge_OP_BASE_TDS_IA3 is TDeckDescriptor
(
    DeckName = 'AMGJNIXTWY'
    DeckIdentifier = 'OP_BASE_TDS_IA3'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_79_Gds_Tank_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_MTLB_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_BRDM_2_SOV_1_999,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_999,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_2_999,
        ~/Descriptor_Deck_Pack_2S3M_Akatsiya_SOV_2_999,
        ~/Descriptor_Deck_Pack_MotRifles_CMD_SOV_BMP_1P_SOV_3_999,
        ~/Descriptor_Deck_Pack_MotRifles_Metis_SOV_BMP_1P_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_BTR_SOV_GAZ_66_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1P_SOV_1_999,
        ~/Descriptor_Deck_Pack_MANPAD_Igla_SOV_UAZ_469_SOV_1_999,
        ~/Descriptor_Deck_Pack_MotRifles_HMG_SOV_GAZ_66_SOV_1_999,
        ~/Descriptor_Deck_Pack_T80B_SOV_2_999,
        ~/Descriptor_Deck_Pack_T80B_CMD_SOV_1_999,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_1_999,
        ~/Descriptor_Deck_Pack_2S3M_Akatsiya_SOV_1_999,
        ~/Descriptor_Deck_Pack_ATteam_Konkurs_SOV_UAZ_469_SOV_1_999,
    ]
)

export Descriptor_Deck_FR_7e_Blindee_challenge_OP_BASE_TDS_Player is TDeckDescriptor
(
    DeckName = 'UZUPQEBUYZ'
    DeckIdentifier = 'OP_BASE_TDS_Player'
    DeckDivision = ~/Descriptor_Deck_Division_FR_7e_Blindee_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_TRM_2000_supply_FR_1_10,
        ~/Descriptor_Deck_Pack_TRM_2000_supply_FR_1_10,
        ~/Descriptor_Deck_Pack_AMX_10_PC_CMD_FR_1_3,
        ~/Descriptor_Deck_Pack_MANPAD_Mistral_FR_VAB_T20_FR_1_8,
        ~/Descriptor_Deck_Pack_TRM_2000_20mm_FR_1_9,
        ~/Descriptor_Deck_Pack_AMX_13_DCA_FR_1_6,
        ~/Descriptor_Deck_Pack_AMX_13_DCA_FR_1_6,
        ~/Descriptor_Deck_Pack_VBL_Reco_FR_2_9,
        ~/Descriptor_Deck_Pack_VBL_MILAN_FR_1_4,
        ~/Descriptor_Deck_Pack_AMX_30_AuF1_FR_1_2,
        ~/Descriptor_Deck_Pack_Mortier_MORT61_120mm_FR_TRM_2000_FR_1_4,
        ~/Descriptor_Deck_Pack_Rifles_CMD_FR_VAB_FR_1_3,
        ~/Descriptor_Deck_Pack_Groupe_AT_FR_VAB_MILAN_FR_2_6,
        ~/Descriptor_Deck_Pack_Commandos_FR_TRM_2000_FR_3_6,
        ~/Descriptor_Deck_Pack_Chasseurs_FR_TRM_2000_FR_1_15,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_FR_VLTT_P4_FR_2_15,
        ~/Descriptor_Deck_Pack_AMX_10_RC_FR_1_4,
        ~/Descriptor_Deck_Pack_Scout_FR_VLTT_P4_FR_2_9,
        ~/Descriptor_Deck_Pack_AMX_30_B2_CMD_FR_1_2,
        ~/Descriptor_Deck_Pack_AMX_13_90mm_FR_2_6,
        ~/Descriptor_Deck_Pack_AMX_10_HOT_FR_1_6,
        ~/Descriptor_Deck_Pack_AMX_30_B2_FR_1_9,
        ~/Descriptor_Deck_Pack_AMX_13_90mm_FR_2_6,
        ~/Descriptor_Deck_Pack_Mirage_III_E_FR_1_3,
        ~/Descriptor_Deck_Pack_Jaguar_ATGM_FR_1_2,
        ~/Descriptor_Deck_Pack_Jaguar_HE_FR_1_2,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_FR_1_6,
        ~/Descriptor_Deck_Pack_Gazelle_HOT_2_FR_1_4,
        ~/Descriptor_Deck_Pack_VAB_Mortar_81_FR_1_3,
        ~/Descriptor_Deck_Pack_VAB_Mortar_81_FR_1_3,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "IJOVDIJCWA"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (2,2),
                        (0,15),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "HBLUTIMQXB"
                    PackIndexUnitNumberList =
                    [
                        (4,2),
                        (3,2),
                        (5,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "EBAXRQFWNO"
                    PackIndexUnitNumberList =
                    [
                        (7,3),
                        (8,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "KPNJIRFMVK"
                    PackIndexUnitNumberList =
                    [
                        (10,4),
                        (28,4),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_5"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (11,2),
                        (12,2),
                        (13,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                        (15,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                        (15,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                        (15,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_7"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (2,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (16,2),
                        (17,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (16,2),
                        (17,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_6"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "SUJBJHQBBH"
                    PackIndexUnitNumberList =
                    [
                        (18,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "AEHXPSCGSH"
                    PackIndexUnitNumberList =
                    [
                        (19,8),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "TXLIVWUPSM"
                    PackIndexUnitNumberList =
                    [
                        (21,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "WAKIQBNMIP"
                    PackIndexUnitNumberList =
                    [
                        (21,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "MMISGNOMQO"
                    PackIndexUnitNumberList =
                    [
                        (20,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "cha_1"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "squad_1"
                    PackIndexUnitNumberList =
                    [
                        (26,4),
                        (27,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "squad_2"
                    PackIndexUnitNumberList =
                    [
                        (23,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "squad_3"
                    PackIndexUnitNumberList =
                    [
                        (24,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "squad_4"
                    PackIndexUnitNumberList =
                    [
                        (25,2),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_UK_5th_Airborne_Brigade_challenge_OP_NORTH_MAR_BEL is TDeckDescriptor
(
    DeckName = 'JXINIYFNXC'
    DeckIdentifier = 'OP_NORTH_MAR_BEL'
    DeckDivision = ~/Descriptor_Deck_Division_UK_5th_Airborne_Brigade_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Paratroopers_CMD_UK_LandRover_UK_2_10,
        ~/Descriptor_Deck_Pack_Paratroopers_UK_Rover_101FC_UK_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_CMD_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_AT_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_UK_Supacat_ATMP_UK_2_10,
        ~/Descriptor_Deck_Pack_Paratroopers_Engineers_UK_Rover_101FC_UK_3_10,
        ~/Descriptor_Deck_Pack_Paratroopers_UK_Rover_101FC_UK_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_AT_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_81mm_mortar_Para_UK_Supacat_ATMP_UK_2_10,
        ~/Descriptor_Deck_Pack_Sniper_UK_Supacat_ATMP_UK_3_10,
        ~/Descriptor_Deck_Pack_Scout_ParaCmdo_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_Pathfinders_UK_CH47_Chinook_UK_3_10,
        ~/Descriptor_Deck_Pack_Pathfinders_UK_Rover_101FC_UK_2_10,
        ~/Descriptor_Deck_Pack_Mortier_81mm_para_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_Mortier_107mm_para_BEL_Unimog_U1350L_BEL_2_10,
    ]
)

export Descriptor_Deck_UK_5th_Airborne_Brigade_challenge_OP_NORTH_MAR_PLAYER is TDeckDescriptor
(
    DeckName = 'XJMQBNGHTX'
    DeckIdentifier = 'OP_NORTH_MAR_PLAYER'
    DeckDivision = ~/Descriptor_Deck_Division_UK_5th_Airborne_Brigade_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_FV105_Sultan_para_BEL_1_10,
        ~/Descriptor_Deck_Pack_Unimog_supply_BEL_1_10,
        ~/Descriptor_Deck_Pack_FV101_Scorpion_para_BEL_1_10,
        ~/Descriptor_Deck_Pack_FV107_Scimitar_para_BEL_1_10,
        ~/Descriptor_Deck_Pack_FV102_Striker_para_UK_2_10,
        ~/Descriptor_Deck_Pack_FV101_Scorpion_UK_2_10,
        ~/Descriptor_Deck_Pack_FV107_Scimitar_UK_2_10,
        ~/Descriptor_Deck_Pack_Scout_ParaCmdo_Mech_BEL_FV103_Spartan_para_BEL_1_10,
        ~/Descriptor_Deck_Pack_Westland_Scout_SS11_UK_1_10,
        ~/Descriptor_Deck_Pack_Gazelle_SNEB_reco_UK_1_10,
        ~/Descriptor_Deck_Pack_Gazelle_CMD_UK_1_10,
        ~/Descriptor_Deck_Pack_Supacat_ATMP_supply_UK_2_10,
        ~/Descriptor_Deck_Pack_Mortier_Tampella_120mm_para_RFA_Faun_kraka_RFA_1_10,
        ~/Descriptor_Deck_Pack_Supacat_ATMP_MILAN_UK_1_10,
        ~/Descriptor_Deck_Pack_Supacat_ATMP_Javelin_LML_UK_2_10,
        ~/Descriptor_Deck_Pack_Gazelle_UK_1_10,
        ~/Descriptor_Deck_Pack_Iltis_para_CMD_RFA_1_10,
        ~/Descriptor_Deck_Pack_Fallschirm_RFA_Unimog_trans_RFA_1_10,
        ~/Descriptor_Deck_Pack_Fallschirm_Engineers_RFA_Unimog_trans_RFA_1_10,
        ~/Descriptor_Deck_Pack_Fallschirm_B1_RFA_Unimog_trans_RFA_1_10,
        ~/Descriptor_Deck_Pack_CH53G_RFA_1_10,
        ~/Descriptor_Deck_Pack_Faun_Kraka_Log_RFA_1_10,
        ~/Descriptor_Deck_Pack_Fallschirmjager_CMD_RFA_Unimog_trans_RFA_1_10,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_para_RFA_Faun_kraka_RFA_1_10,
        ~/Descriptor_Deck_Pack_LandRover_CMD_Para_UK_3_10,
        ~/Descriptor_Deck_Pack_LSV_MILAN_UK_3_10,
        ~/Descriptor_Deck_Pack_LSV_M2HB_UK_3_10,
        ~/Descriptor_Deck_Pack_MANPAD_Javelin_UK_Supacat_ATMP_UK_1_10,
        ~/Descriptor_Deck_Pack_Mortier_107mm_para_BEL_Unimog_U1350L_BEL_1_10,
        ~/Descriptor_Deck_Pack_Wiesel_TOW_RFA_1_10,
        ~/Descriptor_Deck_Pack_Wiesel_20mm_RFA_CH53G_trans_RFA_1_10,
        ~/Descriptor_Deck_Pack_SAS_UK_CH47_Chinook_UK_3_10,
        ~/Descriptor_Deck_Pack_DCA_Oerlikon_GDF_002_35mm_UK_Rover_101FC_UK_1_10,
        ~/Descriptor_Deck_Pack_LRRP_UK_LandRover_UK_3_10,
        ~/Descriptor_Deck_Pack_Paratroopers_CMD_UK_LandRover_UK_1_10,
        ~/Descriptor_Deck_Pack_Paratroopers_UK_Rover_101FC_UK_1_10,
        ~/Descriptor_Deck_Pack_Paratroopers_UK_Rover_101FC_UK_1_10,
        ~/Descriptor_Deck_Pack_81mm_mortar_Para_UK_Supacat_ATMP_UK_1_10,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_UK_Supacat_ATMP_UK_1_10,
        ~/Descriptor_Deck_Pack_HMGteam_MAG_para_UK_Supacat_ATMP_UK_1_10,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "ALAMEB"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "QAKJNXRRRG"
                    PackIndexUnitNumberList =
                    [
                        (0,2),
                        (1,6),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "JHCHYMTDKY"
                    PackIndexUnitNumberList =
                    [
                        (2,4),
                        (3,4),
                        (7,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "QSZFKKOXBE"
                    PackIndexUnitNumberList =
                    [
                        (2,4),
                        (3,4),
                        (7,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "ALAMLGT"
                    PackIndexUnitNumberList =
                    [
                        (5,2),
                        (6,2),
                        (4,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "ALAMSAS"
                    PackIndexUnitNumberList =
                    [
                        (33,2),
                        (31,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "ALAMFJB"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "GUKJSXEWDZ"
                    PackIndexUnitNumberList =
                    [
                        (22,1),
                        (19,2),
                        (12,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "UYGPVAOZNQ"
                    PackIndexUnitNumberList =
                    [
                        (20,2),
                        (21,6),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "SXIIPLTZDL"
                    PackIndexUnitNumberList =
                    [
                        (17,3),
                        (23,1),
                        (18,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "VIASDOIFLW"
                    PackIndexUnitNumberList =
                    [
                        (17,3),
                        (23,1),
                        (18,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "MUKJYFOFLY"
                    PackIndexUnitNumberList =
                    [
                        (30,2),
                        (29,4),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "ALAMUKA1"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "AFSDTRHFDJ"
                    PackIndexUnitNumberList =
                    [
                        (34,1),
                        (39,2),
                        (11,6),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "JJBFRUUKQE"
                    PackIndexUnitNumberList =
                    [
                        (35,2),
                        (38,1),
                        (37,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "MWAHCSJGPL"
                    PackIndexUnitNumberList =
                    [
                        (35,2),
                        (38,1),
                        (37,1),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "ALAMAAC"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "WGBGQDABLA"
                    PackIndexUnitNumberList =
                    [
                        (10,1),
                        (15,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "OBWKKGSZTP"
                    PackIndexUnitNumberList =
                    [
                        (9,1),
                        (8,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "ROFZTNSYMX"
                    PackIndexUnitNumberList =
                    [
                        (9,1),
                        (8,2),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_POL_30RMD_84MSR_challenge_OP_NORTH_MAR_POL is TDeckDescriptor
(
    DeckName = 'IAOMEMEULP'
    DeckIdentifier = 'OP_NORTH_MAR_POL'
    DeckDivision = ~/Descriptor_Deck_Division_POL_30RMD_84MSR_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_HMGteam_PKM_POL_UAZ_469_trans_POL_1_20,
        ~/Descriptor_Deck_Pack_HMGteam_NSV_POL_UAZ_469_trans_POL_1_10,
        ~/Descriptor_Deck_Pack_WSW_POL_Star_266_POL_0_8,
        ~/Descriptor_Deck_Pack_Reserve_POL_Star_266_POL_0_90,
        ~/Descriptor_Deck_Pack_Rifles_CMD_POL_BMP_1_SP2_POL_2_10,
        ~/Descriptor_Deck_Pack_Rifles_POL_BMP_1_SP2_POL_2_50,
        ~/Descriptor_Deck_Pack_Engineers_POL_Star_266_POL_2_6,
        ~/Descriptor_Deck_Pack_ATteam_RCL_SPG9_POL_UAZ_469_trans_POL_1_10,
        ~/Descriptor_Deck_Pack_Groupe_AT_POL_Star_266_POL_2_8,
        ~/Descriptor_Deck_Pack_Mortier_M43_82mm_POL_UAZ_469_trans_POL_0_12,
        ~/Descriptor_Deck_Pack_Mortier_PM43_120mm_POL_UAZ_469_trans_POL_1_6,
        ~/Descriptor_Deck_Pack_T34_85M_CMD_POL_0_6,
        ~/Descriptor_Deck_Pack_T34_85M_POL_0_30,
        ~/Descriptor_Deck_Pack_AT_D44_85mm_POL_Star_266_POL_1_12,
        ~/Descriptor_Deck_Pack_T54B_CMD_POL_2_4,
        ~/Descriptor_Deck_Pack_T54B_POL_2_20,
        ~/Descriptor_Deck_Pack_DCA_AZP_S60_POL_Star_266_POL_1_12,
        ~/Descriptor_Deck_Pack_LO_1800_ZPU_2_POL_1_12,
        ~/Descriptor_Deck_Pack_Scout_POL_UAZ_469_Reco_POL_1_12,
    ]
)

export Descriptor_Deck_UK_5th_Airborne_Brigade_challenge_OP_NORTH_MAR_UK is TDeckDescriptor
(
    DeckName = 'YUHNIGINKZ'
    DeckIdentifier = 'OP_NORTH_MAR_UK'
    DeckDivision = ~/Descriptor_Deck_Division_UK_5th_Airborne_Brigade_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Paratroopers_CMD_UK_LandRover_UK_2_10,
        ~/Descriptor_Deck_Pack_Paratroopers_UK_Rover_101FC_UK_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_CMD_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_AT_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_ATteam_Milan_2_UK_Supacat_ATMP_UK_2_10,
        ~/Descriptor_Deck_Pack_Paratroopers_Engineers_UK_Rover_101FC_UK_3_10,
        ~/Descriptor_Deck_Pack_Paratroopers_UK_Rover_101FC_UK_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_ParaCmdo_AT_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_81mm_mortar_Para_UK_Supacat_ATMP_UK_2_10,
        ~/Descriptor_Deck_Pack_Sniper_UK_Supacat_ATMP_UK_3_10,
        ~/Descriptor_Deck_Pack_Scout_ParaCmdo_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_Pathfinders_UK_CH47_Chinook_UK_3_10,
        ~/Descriptor_Deck_Pack_Pathfinders_UK_Rover_101FC_UK_2_10,
        ~/Descriptor_Deck_Pack_Scout_Para_UK_Rover_101FC_UK_2_10,
        ~/Descriptor_Deck_Pack_Mortier_81mm_para_BEL_Unimog_U1350L_BEL_2_10,
        ~/Descriptor_Deck_Pack_Mortier_107mm_para_BEL_Unimog_U1350L_BEL_2_10,
    ]
)

export Descriptor_Deck_RFA_2_PzGrenadier_challenge_OP_NORTH_RTG_ALLY is TDeckDescriptor
(
    DeckName = 'BPTJAYDPUX'
    DeckIdentifier = 'OP_NORTH_RTG_ALLY'
    DeckDivision = ~/Descriptor_Deck_Division_RFA_2_PzGrenadier_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_TPZ_Fuchs_CMD_RFA_1_3,
    ]
)

export Descriptor_Deck_DDR_20MSD_challenge_OP_NORTH_RTG_IA1 is TDeckDescriptor
(
    DeckName = 'IVEXRHVFTA'
    DeckIdentifier = 'OP_NORTH_RTG_IA1'
    DeckDivision = ~/Descriptor_Deck_Division_DDR_20MSD_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BTR_60_CMD_DDR_1_999,
    ]
)

export Descriptor_Deck_SOV_138_TkInde_challenge_OP_NORTH_RTG_IA2 is TDeckDescriptor
(
    DeckName = 'UFWUXIBVOT'
    DeckIdentifier = 'OP_NORTH_RTG_IA2'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_138_TkInde_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_MotRifles_SOV_BMP_1P_SOV_1_50,
        ~/Descriptor_Deck_Pack_T64B_SOV_1_50,
        ~/Descriptor_Deck_Pack_2S1_Gvozdika_SOV_3_50,
        ~/Descriptor_Deck_Pack_Ural_4320_SOV_1_50,
        ~/Descriptor_Deck_Pack_Mortier_2S12_120mm_SOV_GAZ_66_SOV_2_50,
        ~/Descriptor_Deck_Pack_T64B_CMD_SOV_2_50,
        ~/Descriptor_Deck_Pack_HvyScout_SOV_BMP_1P_reco_SOV_2_50,
        ~/Descriptor_Deck_Pack_T64BV1_SOV_1_50,
        ~/Descriptor_Deck_Pack_MTLB_Strela10_SOV_1_50,
        ~/Descriptor_Deck_Pack_BRM_1_SOV_1_50,
        ~/Descriptor_Deck_Pack_Mi_8TV_s57_16_SOV_1_50,
    ]
)

export Descriptor_Deck_NL_4e_Divisie_challenge_OP_NORTH_RTG_PLAYER is TDeckDescriptor
(
    DeckName = 'CKYVSBETXA'
    DeckIdentifier = 'OP_NORTH_RTG_PLAYER'
    DeckDivision = ~/Descriptor_Deck_Division_NL_4e_Divisie_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_AIFV_B_CMD_NL_1_2,
        ~/Descriptor_Deck_Pack_AIFV_B_CMD_NL_1_2,
        ~/Descriptor_Deck_Pack_AIFV_B_CMD_NL_1_2,
        ~/Descriptor_Deck_Pack_HMGteam_M2HB_NL_LandRover_NL_1_8,
        ~/Descriptor_Deck_Pack_Scout_NL_LandRover_NL_2_9,
        ~/Descriptor_Deck_Pack_Scout_AT_NL_LandRover_NL_2_6,
        ~/Descriptor_Deck_Pack_AH1F_Hog_US_2_2,
        ~/Descriptor_Deck_Pack_AH1F_Hog_US_2_2,
        ~/Descriptor_Deck_Pack_AH1F_Cobra_US_2_9,
        ~/Descriptor_Deck_Pack_AH1F_Hog_US_2_2,
        ~/Descriptor_Deck_Pack_Leopard_2A4_CMD_NL_3_1,
        ~/Descriptor_Deck_Pack_Mech_Rifles_CMD_NL_AIFV_B_C25_NL_2_2,
        ~/Descriptor_Deck_Pack_OH58C_CMD_US_2_9,
        ~/Descriptor_Deck_Pack_OH58D_Combat_Scout_US_1_9,
        ~/Descriptor_Deck_Pack_EH60A_EW_US_3_9,
        ~/Descriptor_Deck_Pack_OH58D_Kiowa_Warrior_US_2_9,
        ~/Descriptor_Deck_Pack_Mech_Rifles_M72_LAW_NL_AIFV_B_50_NL_1_6,
        ~/Descriptor_Deck_Pack_Mech_Rifles_Carl_NL_AIFV_B_50_NL_1_9,
        ~/Descriptor_Deck_Pack_M113_CV_25mm_NL_2_6,
        ~/Descriptor_Deck_Pack_MANPAD_Stinger_C_NL_LandRover_NL_1_8,
        ~/Descriptor_Deck_Pack_Gepard_1A2_NL_1_4,
        ~/Descriptor_Deck_Pack_DAF_YA_4400_supply_NL_1_5,
        ~/Descriptor_Deck_Pack_DAF_YA_4400_supply_NL_1_5,
        ~/Descriptor_Deck_Pack_DAF_YHZ_2300_NL_1_2,
        ~/Descriptor_Deck_Pack_Mech_Rifles_Dragon_NL_AIFV_B_C25_NL_3_6,
        ~/Descriptor_Deck_Pack_AIFV_B_TOW_NL_2_9,
        ~/Descriptor_Deck_Pack_Leopard_2A4_NL_2_3,
        ~/Descriptor_Deck_Pack_Mortier_MORT61_120mm_NL_AIFV_B_50_NL_1_4,
        ~/Descriptor_Deck_Pack_AIFV_B_Radar_NL_2_4,
        ~/Descriptor_Deck_Pack_Engineers_Scout_NL_M113A1_reco_NL_1_8,
        ~/Descriptor_Deck_Pack_AIFV_B_Cargo_NL_1_10,
        ~/Descriptor_Deck_Pack_Mech_Rifles_CMD_NL_AIFV_B_C25_NL_2_2,
        ~/Descriptor_Deck_Pack_Mech_Rifles_Carl_NL_AIFV_B_50_NL_1_9,
        ~/Descriptor_Deck_Pack_Leopard_2A4_NL_2_3,
        ~/Descriptor_Deck_Pack_UH60A_Supply_US_1_9,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "TLCGWRXMIB"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (0,2),
                        (16,4),
                        (3,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (18,2),
                        (5,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (4,4),
                        (28,3),
                        (29,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (20,3),
                        (19,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (30,10),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "MBXHNCRJJX"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (11,2),
                        (24,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (17,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (17,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (25,4),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "GTKWKUORNP"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (11,2),
                        (24,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (17,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (17,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (26,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (27,4),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "UEVZJIOPLE"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (12,1),
                        (34,4),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (15,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (8,2),
                        (6,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    PackIndexUnitNumberList =
                    [
                        (14,1),
                        (13,2),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_BEL_1BEC_challenge_OP_NORTH_SURV_BEL is TDeckDescriptor
(
    DeckName = 'PJMMKCXWFW'
    DeckIdentifier = 'OP_NORTH_SURV_BEL'
    DeckDivision = ~/Descriptor_Deck_Division_BEL_1BEC_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_ParaCmdo_CMD_BEL_2_8,
    ]
)

export Descriptor_Deck_DDR_23_MSB_challenge_OP_NORTH_SURV_DDR is TDeckDescriptor
(
    DeckName = 'XIGMFIRPLP'
    DeckIdentifier = 'OP_NORTH_SURV_DDR'
    DeckDivision = ~/Descriptor_Deck_Division_DDR_23_MSB_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_2K11_KRUG_DDR_2_8,
    ]
)

export Descriptor_Deck_UK_4th_Armoured_challenge_OP_NORTH_SURV_PLAYER is TDeckDescriptor
(
    DeckName = 'KGCHFNWUUS'
    DeckIdentifier = 'OP_NORTH_SURV_PLAYER'
    DeckDivision = ~/Descriptor_Deck_Division_UK_4th_Armoured_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_Alvis_Stalwart_UK_2_999,
        ~/Descriptor_Deck_Pack_Rifles_CMD_UK_FV432_UK_2_999,
        ~/Descriptor_Deck_Pack_Rifles_AT_UK_FV432_UK_2_999,
        ~/Descriptor_Deck_Pack_FV102_Striker_UK_2_999,
        ~/Descriptor_Deck_Pack_FV103_Spartan_GSR_UK_2_999,
        ~/Descriptor_Deck_Pack_FV107_Scimitar_UK_2_999,
        ~/Descriptor_Deck_Pack_Scout_AT_UK_FV103_Spartan_UK_2_999,
        ~/Descriptor_Deck_Pack_MANPAD_Javelin_UK_FV432_SCAT_UK_2_999,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "ABCDEFGHIJ"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "AFSDTRHFDJ"
                    PackIndexUnitNumberList =
                    [
                        (1,1),
                        (2,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "EBAXRQFWNO"
                    PackIndexUnitNumberList =
                    [
                        (6,1),
                        (4,1),
                        (5,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "JOBFTPNMXP"
                    PackIndexUnitNumberList =
                    [
                        (3,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "HBLUTIMQXB"
                    PackIndexUnitNumberList =
                    [
                        (7,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "PJPBNSPBOL"
                    PackIndexUnitNumberList =
                    [
                        (0,1),
                    ]
                ),
            ]
        ),
    ]
)

export Descriptor_Deck_SOV_9_Gds_Tank_challenge_OP_NORTH_SURV_SOV is TDeckDescriptor
(
    DeckName = 'OTFHKSWPLV'
    DeckIdentifier = 'OP_NORTH_SURV_SOV'
    DeckDivision = ~/Descriptor_Deck_Division_SOV_9_Gds_Tank_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_2K12_KUB_SOV_2_8,
    ]
)

export Descriptor_Deck_POL_11TkD_challenge_OP_NORTH_WH_ALLY is TDeckDescriptor
(
    DeckName = 'MSTBCUCXJI'
    DeckIdentifier = 'OP_NORTH_WH_ALLY'
    DeckDivision = ~/Descriptor_Deck_Division_POL_11TkD_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_POL_0_3,
    ]
)

export Descriptor_Deck_UK_2nd_Infantry_challenge_OP_NORTH_WH_IA is TDeckDescriptor
(
    DeckName = 'CXNHGTNRHO'
    DeckIdentifier = 'OP_NORTH_WH_IA'
    DeckDivision = ~/Descriptor_Deck_Division_UK_2nd_Infantry_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_AEC_Militant_UK_0_10,
        ~/Descriptor_Deck_Pack_Bedford_MJ_4t_trans_UK_0_5,
        ~/Descriptor_Deck_Pack_Airmobile_UK_Rover_101FC_UK_0_7,
        ~/Descriptor_Deck_Pack_Airmobile_Mot_UK_Saxon_UK_0_8,
        ~/Descriptor_Deck_Pack_FH70_155mm_UK_Bedford_MJ_4t_trans_UK_0_10,
        ~/Descriptor_Deck_Pack_Airmobile_Mot_UK_Saxon_UK_0_8,
        ~/Descriptor_Deck_Pack_FH70_155mm_UK_Bedford_MJ_4t_trans_UK_0_10,
        ~/Descriptor_Deck_Pack_LandRover_MILAN_UK_0_9,
        ~/Descriptor_Deck_Pack_LandRover_MILAN_UK_0_9,
        ~/Descriptor_Deck_Pack_LandRover_MILAN_UK_0_9,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_CMD_UK_0_2,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_CMD_UK_0_2,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_CMD_UK_0_2,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk11_CMD_UK_0_2,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk9_UK_0_9,
        ~/Descriptor_Deck_Pack_FV4201_Chieftain_Mk9_UK_0_9,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_SNEB_UK_0_4,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_I_TOW_UK_0_4,
        ~/Descriptor_Deck_Pack_Lynx_AH_Mk7_SNEB_UK_0_4,
    ]
)

export Descriptor_Deck_POL_11TkD_challenge_OP_NORTH_WH_PLAYER is TDeckDescriptor
(
    DeckName = 'MSTBCUCXJI'
    DeckIdentifier = 'OP_NORTH_WH_PLAYER'
    DeckDivision = ~/Descriptor_Deck_Division_POL_11TkD_challenge
    DeckPackList =
    [
        ~/Descriptor_Deck_Pack_BMP_1_CMD_POL_2_3,
        ~/Descriptor_Deck_Pack_HMGteam_PKM_POL_UAZ_469_trans_POL_1_9,
        ~/Descriptor_Deck_Pack_BRDM_2_POL_1_9,
        ~/Descriptor_Deck_Pack_Scout_POL_OT_65_POL_1_9,
        ~/Descriptor_Deck_Pack_ZSU_23_Shilka_POL_3_6,
        ~/Descriptor_Deck_Pack_Hibneryt_KG_POL_1_6,
        ~/Descriptor_Deck_Pack_MANPAD_Strela_2M_POL_UAZ_469_trans_POL_3_12,
        ~/Descriptor_Deck_Pack_BRDM_2_Malyu_P_POL_1_7,
        ~/Descriptor_Deck_Pack_Star_266_supply_POL_1_6,
        ~/Descriptor_Deck_Pack_KrAZ_255B_supply_POL_1_2,
        ~/Descriptor_Deck_Pack_MotRifles_POL_BMP_1_SP2_POL_1_9,
        ~/Descriptor_Deck_Pack_MotRifles_POL_BMP_1_SP2_POL_1_9,
        ~/Descriptor_Deck_Pack_MotRifles_SVD_POL_BMP_1_SP2_POL_1_9,
        ~/Descriptor_Deck_Pack_T72M1_CMD_POL_2_1,
        ~/Descriptor_Deck_Pack_T72M1_POL_1_5,
        ~/Descriptor_Deck_Pack_T72M1_POL_1_5,
        ~/Descriptor_Deck_Pack_Engineers_CMD_POL_OT_64_SKOT_2A_POL_2_6,
        ~/Descriptor_Deck_Pack_Engineers_POL_OT_64_SKOT_2A_POL_2_6,
        ~/Descriptor_Deck_Pack_Engineers_Flam_POL_OT_64_SKOT_2A_POL_2_6,
        ~/Descriptor_Deck_Pack_Atteam_Fagot_POL_BMP_1_SP2_POL_2_6,
    ]
    DeckCombatGroupList =
    [
        TDeckCombatGroupDescriptor
        (
            Name = "ZZKKZZVRVE"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "JOIVEMBJJI"
                    PackIndexUnitNumberList =
                    [
                        (10,2),
                        (12,1),
                        (19,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "MSMPVWPGGN"
                    PackIndexUnitNumberList =
                    [
                        (2,2),
                        (3,1),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "FNMAENNLZY"
                    PackIndexUnitNumberList =
                    [
                        (0,2),
                        (8,6),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "YVKFIWSNRI"
                    PackIndexUnitNumberList =
                    [
                        (6,3),
                        (4,1),
                        (5,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "XIPHZFYZRG"
                    PackIndexUnitNumberList =
                    [
                        (16,1),
                        (17,1),
                        (18,2),
                    ]
                ),
            ]
        ),
        TDeckCombatGroupDescriptor
        (
            Name = "ZSZFIHDHFS"
            SmartGroupList =
            [
                TDeckSmartGroupDescriptor
                (
                    Name = "JCNMAGMNGX"
                    PackIndexUnitNumberList =
                    [
                        (13,1),
                        (3,2),
                        (2,2),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "JRTLVRNIXR"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "ZMBSTJIBRQ"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                    ]
                ),
                TDeckSmartGroupDescriptor
                (
                    Name = "XESLIAGOBD"
                    PackIndexUnitNumberList =
                    [
                        (14,3),
                    ]
                ),
            ]
        ),
    ]
)

