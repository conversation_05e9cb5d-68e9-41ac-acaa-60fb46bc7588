template BUCKMovableDescriptor
[
    // ++ BUCKContainerDescriptor
    ElementName : string = "",
    UniqueName : string = "",
    RequiredTags : LIST<string> = [],
    ForbiddenTags : LIST<string> = [],
    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI(),

    MagnifierMultiplication : float = 0.0,

    PointerEventsToAllow : int = ~/EAllowablePointerEventType/None,
    HidePointerEvents : bool = false,

    GridAlign : bool = false,

    FitStyle : int = ~/ContainerFitStyle/None,
    ChildFitToContent : bool = false,

    ClipContent : bool = false,

    HasBackground : bool = false,
    BackgroundBlockColorToken : string = "",

    HasBorder : bool = false,
    BordersToDraw : int = ~/TBorderSide/Default,
    BorderThicknessToken : string = "",
    BorderLineColorToken : string = "",

    BackgroundLocalRenderLayer : int = 0,
    BorderLocalRenderLayer : int = 0,

    ComponentStateLocked : bool = false,

    Components : LIST<TBUCKContainerDescriptor> = [],
    // -- BUCKContainerDescriptor

    PointerDistanceBeforeDrag : float = 0.0,                              // Détermine la distance avant laquelle le panneau commencera a bouger
    CannotExitContainer : bool = false                                    // Si mis a vrai le movable container ne peux pas sortir de la box de son parent
]
is TBUCKMovableDescriptor
(
    // ++ BUCKContainerDescriptor
    ElementName = <ElementName>
    UniqueName = <UniqueName>
    RequiredTags = <RequiredTags>
    ForbiddenTags = <ForbiddenTags>
    ComponentFrame = <ComponentFrame>

    MagnifierMultiplication = <MagnifierMultiplication>

    PointerEventsToAllow = <PointerEventsToAllow>
    HidePointerEvents = <HidePointerEvents>

    GridAlign = <GridAlign>

    FitStyle = <FitStyle>
    ChildFitToContent = <ChildFitToContent>

    ClipContent = <ClipContent>

    UniformDrawer = $/UserInterface/UIUniformDrawer

    HasBackground = <HasBackground>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    HasBorder = <HasBorder>
    BordersToDraw = <BordersToDraw>
    BorderThicknessToken = <BorderThicknessToken>
    BorderLineColorToken = <BorderLineColorToken>

    BackgroundLocalRenderLayer = <BackgroundLocalRenderLayer>
    BorderLocalRenderLayer = <BorderLocalRenderLayer>

    ComponentStateLocked = <ComponentStateLocked>

    Components = <Components>
    // -- BUCKContainerDescriptor

    PointerDistanceBeforeDrag = <PointerDistanceBeforeDrag>
    CannotExitContainer = <CannotExitContainer>
)
