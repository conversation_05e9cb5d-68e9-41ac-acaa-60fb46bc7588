template BUCKChronoAnimatedTextureDescriptor
[
    // ++ BUCKContainerDescriptor
    ElementName : string = "",
    UniqueName : string = "",
    RequiredTags : LIST<string> = [],
    ForbiddenTags : LIST<string> = [],
    ComponentFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI(),

    MagnifierMultiplication : float = 0.0,

    PointerEventsToAllow : int = ~/EAllowablePointerEventType/None,
    HidePointerEvents : bool = false,

    GridAlign : bool = false,

    FitStyle : int = ~/ContainerFitStyle/None,
    ChildFitToContent : bool = false,

    ClipContent : bool = false,
    IsClippable :bool = true,

    HasBackground : bool = false,
    BackgroundBlockColorToken : string = "",

    HasBorder : bool = false,
    BordersToDraw : int = ~/TBorderSide/Default,
    BorderThicknessToken : string = "",
    BorderLineColorToken : string = "",

    BackgroundLocalRenderLayer : int = 0,
    BorderLocalRenderLayer : int = 0,

    ComponentStateLocked : bool = false,

    Components : List<TBUCKContainerDescriptor> = [],
    // -- BUCKContainerDescriptor

    // ++ BUCKTextureDescriptor
    TextureDrawer : string = 'ColorMultiply',
    TextureFrame : TUIFramePropertyRTTI = TUIFramePropertyRTTI(),
    TextureToken : string = '',
    TextureColorToken : string = '',
    ResizeMode : int = ~/TextureResizeMode/UserDefined,
    TileTextureInComponent : bool = false,
    ClipTextureToComponent : bool = false,
    LocalRenderLayer : int = 0,
    // -- BUCKTextureDescriptor

    ChronoDrawer : TUICommonChronoDrawer,                                             // Drawer utilisé pour le dessin de la texture chrono
    ChronoTexture : string = "",                                                      // Le token de texture du chrono

    ChronoBackgroundColor : string = "",                                                // Le token de couleur de début de l'animation
    ChronoForegroundColor : string = "",                                                // Le token de couleur de fin de l'animation
]
is TBUCKChronoAnimatedTextureDescriptor
(
    // ++ BUCKContainerDescriptor
    ElementName = <ElementName>
    UniqueName = <UniqueName>
    RequiredTags = <RequiredTags>
    ForbiddenTags = <ForbiddenTags>
    ComponentFrame = <ComponentFrame>

    MagnifierMultiplication = <MagnifierMultiplication>

    PointerEventsToAllow = <PointerEventsToAllow>
    HidePointerEvents = <HidePointerEvents>

    GridAlign = <GridAlign>

    FitStyle = <FitStyle>
    ChildFitToContent = <ChildFitToContent>

    ClipContent = <ClipContent>
    IsClippable = <IsClippable>

    UniformDrawer = $/UserInterface/UIUniformDrawer

    HasBackground = <HasBackground>
    BackgroundBlockColorToken = <BackgroundBlockColorToken>

    HasBorder = <HasBorder>
    BordersToDraw = <BordersToDraw>
    BorderThicknessToken = <BorderThicknessToken>
    BorderLineColorToken = <BorderLineColorToken>

    BackgroundLocalRenderLayer = <BackgroundLocalRenderLayer>
    LocalRenderLayer = <LocalRenderLayer>
    BorderLocalRenderLayer = <BorderLocalRenderLayer>
    ChronoLocalRenderLayer = (<LocalRenderLayer> != 0 ? <LocalRenderLayer> + 1 : 0)

    ComponentStateLocked = <ComponentStateLocked>

    Components = <Components>
    // -- BUCKContainerDescriptor

    // ++ BUCKTextureDescriptor
    TextureDrawer = <TextureDrawer>
    TextureFrame = <TextureFrame>
    TextureToken = <TextureToken>
    TextureColorToken = <TextureColorToken>
    ResizeMode = <ResizeMode>
    TileTextureInComponent = <TileTextureInComponent>
    ClipTextureToComponent = <ClipTextureToComponent>
    // -- BUCKTextureDescriptor

    ChronoDrawer = <ChronoDrawer>
    ChronoTexture = <ChronoTexture>

    ChronoBackgroundColor = <ChronoBackgroundColor>
    ChronoForegroundColor = <ChronoForegroundColor>
)
