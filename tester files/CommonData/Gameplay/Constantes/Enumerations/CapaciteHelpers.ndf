//-----------------------------------------------------------------------------
// A maintenir synchro avec les valeurs de CapaciteEnum.h
//-----------------------------------------------------------------------------

CapaciteDeclenchementType_automatique                 is 0
CapaciteDeclenchementType_ordre_cibles_une_seule_fois is 1

CapaciteTargetFilter_tout                      is 0
CapaciteTargetFilter_ennemi                    is 1
CapaciteTargetFilter_allie                     is 2
CapaciteTargetFilter_joueur                    is 3
CapaciteTargetFilter_allie_different_de_caster is 4

CapaciteCumulEffect_oui             is 0
CapaciteCumulEffect_jamais          is 1
CapaciteCumulEffect_autres_lanceurs is 2

CapaciteFeedbackActivationMask_never       is 0
CapaciteFeedbackActivationMask_allied      is 1
CapaciteFeedbackActivationMask_enemy       is 2
CapaciteFeedbackActivationMask_anyalliance is CapaciteFeedbackActivationMask_allied | CapaciteFeedbackActivationMask_enemy
CapaciteFeedbackActivationMask_selected    is 4
CapaciteFeedbackActivationMask_soloselected is 8
CapaciteFeedbackActivationMask_always      is -1
