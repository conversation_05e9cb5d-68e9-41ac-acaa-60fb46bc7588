Tactic_Descriptor_Unit_FakeTarget is TEntityDescriptor
(
    DescriptorId         = GUID:{00000000-0000-0000-0000-000006000000}
    ClassNameForDebug    = 'FakeTarget'
    ModulesDescriptors =
    [
        TFlagsModuleDescriptor
        (
            InitialFlagSet =
            [
                Flag_FakeTarget,
                Flag_Detectable,
            ]
        ),
        TPositionModuleDescriptor
        (
            InGeoDb        = True
        ),
        TVisibilityModuleDescriptor
        (
            UnitConcealmentBonus = 1.0
        ),
        TScannerConfigurationDescriptor
        (
        ),
        TReverseScannerWithIdentificationDescriptor
        (
            VisibilityRollRule = TModernWarfareVisibilityRollRule
            (
                VisibilityRuleDescriptor = $/GFX/VisionRules/StandardWargameVisibilityRollRule
            )
        ),
        TLinkTeamModuleDescriptor
        (
        ),
        TPackSignauxModuleDescriptor
        (
        ),
        TTagsModuleDescriptor
        (
            TagSet = [
                "AllUnits",
            ]
        ),
        TTargetDataModuleDescriptor
        (
        ),
    ]
)
