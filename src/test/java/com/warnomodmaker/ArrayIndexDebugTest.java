package test.java.com.warnomodmaker;

import com.warnomodmaker.parser.LineBasedWriter;
import com.warnomodmaker.model.ModificationTracker;
import com.warnomodmaker.model.NDFValue;

import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;

public class ArrayIndexDebugTest {
    public static void main(String[] args) throws Exception {
        // Read the UniteDescriptor file
        String content = Files.readString(Paths.get("tester files/GameData/Generated/Gameplay/Gfx/UniteDescriptor.ndf"));

        // Create a LineBasedWriter to test array element detection
        StringWriter writer = new StringWriter();
        ModificationTracker tracker = new ModificationTracker();
        LineBasedWriter lineWriter = new LineBasedWriter(writer, content, tracker);

        // Test the specific failing property path
        String unitName = "Descriptor_Unit_2K11_KRUG_DDR";
        String propertyPath = "ModulesDescriptors[16].BlindageProperties.ResistanceSides.Index";

        System.out.println("Testing property path: " + unitName + "." + propertyPath);

        // This should trigger the debug output in findArrayElementPropertyLine
        try {
            // Add a modification and try to write it
            NDFValue oldValue = NDFValue.createNumber(1);
            NDFValue newValue = NDFValue.createNumber(2);
            tracker.recordModification(unitName, propertyPath, oldValue, newValue);

            // This will call findArrayElementPropertyLine internally
            lineWriter.write(Arrays.asList());

        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
