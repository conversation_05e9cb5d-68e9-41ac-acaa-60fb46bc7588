package test.java.com.warnomodmaker;

import com.warnomodmaker.parser.LineBasedWriter;
import com.warnomodmaker.model.ModificationTracker;
import com.warnomodmaker.model.NDFValue;

import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;

public class ArrayIndexDebugTest {
    public static void main(String[] args) throws Exception {
        // Read the UniteDescriptor file
        String content = Files.readString(Paths.get("tester files/GameData/Generated/Gameplay/Gfx/UniteDescriptor.ndf"));

        // Create a LineBasedWriter to test array element detection
        StringWriter writer = new StringWriter();
        ModificationTracker tracker = new ModificationTracker();
        LineBasedWriter lineWriter = new LineBasedWriter(writer, content, tracker);

        // Test both patterns
        String unitName = "Descriptor_Unit_2K11_KRUG_DDR";

        System.out.println("=== Testing Array Index Pattern ===");
        String arrayPattern = "ModulesDescriptors[15].MaxPhysicalDamages";
        System.out.println("Testing: " + unitName + "." + arrayPattern);
        try {
            NDFValue oldValue1 = NDFValue.createNumber(6);
            NDFValue newValue1 = NDFValue.createNumber(7);
            tracker.recordModification(unitName, arrayPattern, oldValue1, newValue1);
            lineWriter.write(Arrays.asList());
            System.out.println("+ Array index pattern SUCCESS");
        } catch (Exception e) {
            System.out.println("- Array index pattern FAILED: " + e.getMessage());
        }

        System.out.println("\n=== Testing Nested Property Pattern ===");
        String nestedPattern = "ModulesDescriptors[16].BlindageProperties.ResistanceSides.Index";
        System.out.println("Testing: " + unitName + "." + nestedPattern);
        try {
            NDFValue oldValue2 = NDFValue.createNumber(1);
            NDFValue newValue2 = NDFValue.createNumber(2);
            tracker.recordModification(unitName, nestedPattern, oldValue2, newValue2);
            lineWriter.write(Arrays.asList());
            System.out.println("+ Nested property pattern SUCCESS");
        } catch (Exception e) {
            System.out.println("- Nested property pattern FAILED: " + e.getMessage());
        }
    }
}
