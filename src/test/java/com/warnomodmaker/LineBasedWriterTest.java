package test.java.com.warnomodmaker;

import com.warnomodmaker.parser.*;
import com.warnomodmaker.model.*;
import java.io.*;
import java.util.*;

/**
 * Test the line-based writer functionality specifically
 */
public class LineBasedWriterTest {
    
    public static void main(String[] args) throws Exception {
        System.out.println("Testing Line-Based Writer...");
        
        // Read a test file
        String testFile = "tester files/GameData/Generated/Gameplay/Gfx/UniteDescriptor.ndf";
        String originalContent = readFileContent(testFile);
        
        // Parse it
        NDFParser parser = new NDFParser(new StringReader(originalContent));
        parser.setOriginalSourceContent(originalContent);
        parser.setFileType(NDFValue.NDFFileType.UNITE_DESCRIPTOR);
        List<NDFValue.ObjectValue> objects = parser.parse();
        
        System.out.println("Parsed " + objects.size() + " objects");
        
        // Create a modification tracker and make a test modification
        ModificationTracker tracker = new ModificationTracker();
        
        if (!objects.isEmpty()) {
            NDFValue.ObjectValue firstUnit = objects.get(0);
            String unitName = firstUnit.getInstanceName();
            
            System.out.println("Making test modification to unit: " + unitName);
            
            // Find a property to modify
            NDFValue healthValue = firstUnit.getProperty("MaxDamages");
            if (healthValue != null && healthValue instanceof NDFValue.NumberValue) {
                NDFValue.NumberValue numberVal = (NDFValue.NumberValue) healthValue;
                double oldValue = numberVal.getValue();
                double newValue = oldValue + 100; // Add 100 to health
                
                // Create new value
                NDFValue newHealthValue = NDFValue.createNumber(newValue, numberVal.wasOriginallyInteger());
                
                // Record the modification
                tracker.recordModification(unitName, "MaxDamages", healthValue, newHealthValue);
                
                // Apply the modification to the object
                firstUnit.setProperty("MaxDamages", newHealthValue);
                
                System.out.println("Modified " + unitName + ".MaxDamages: " + oldValue + " -> " + newValue);
                System.out.println("Tracker has " + tracker.getModificationCount() + " modifications");
            }
        }
        
        // Now test the line-based writer
        StringWriter output = new StringWriter();
        NDFWriter writer = new NDFWriter(output, true);
        writer.setOriginalSourceContent(originalContent);
        writer.setModificationTracker(tracker);
        
        // Mark the modified object
        if (!objects.isEmpty()) {
            writer.markObjectAsModified(objects.get(0));
        }
        
        // Write the file
        writer.write(objects);
        
        String writtenContent = output.toString();
        
        // Compare results
        System.out.println("\nComparison Results:");
        System.out.println("Original length: " + originalContent.length());
        System.out.println("Written length:  " + writtenContent.length());
        
        // Check if the modification was applied
        if (writtenContent.contains("MaxDamages")) {
            System.out.println("✓ MaxDamages property found in output");
        } else {
            System.out.println("✗ MaxDamages property NOT found in output");
        }
        
        // Count differences
        int differences = 0;
        int minLength = Math.min(originalContent.length(), writtenContent.length());
        for (int i = 0; i < minLength; i++) {
            if (originalContent.charAt(i) != writtenContent.charAt(i)) {
                differences++;
                if (differences <= 5) { // Show first 5 differences
                    System.out.println("Difference at position " + i + ": '" + 
                                     originalContent.charAt(i) + "' -> '" + writtenContent.charAt(i) + "'");
                }
            }
        }
        
        System.out.println("Total character differences: " + differences);
        
        if (differences == 0 && originalContent.length() == writtenContent.length()) {
            System.out.println("✗ NO CHANGES DETECTED - Line-based writer may not be working");
        } else {
            System.out.println("✓ Changes detected - Line-based writer appears to be working");
        }
    }
    
    private static String readFileContent(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            boolean first = true;
            while ((line = reader.readLine()) != null) {
                if (!first) content.append("\n");
                content.append(line);
                first = false;
            }
        }
        return content.toString();
    }
}
