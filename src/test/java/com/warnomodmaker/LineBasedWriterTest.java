package test.java.com.warnomodmaker;

import com.warnomodmaker.parser.*;
import com.warnomodmaker.model.*;
import com.warnomodmaker.model.PropertyUpdater.ModificationType;
import java.io.*;
import java.util.*;

/**
 * Test the line-based writer functionality specifically
 */
public class LineBasedWriterTest {
    
    public static void main(String[] args) throws Exception {
        System.out.println("Testing Line-Based Writer...");
        
        // Read a test file
        String testFile = "tester files/GameData/Generated/Gameplay/Gfx/UniteDescriptor.ndf";
        String originalContent = readFileContent(testFile);
        
        // Parse it
        NDFParser parser = new NDFParser(new StringReader(originalContent));
        parser.setOriginalSourceContent(originalContent);
        parser.setFileType(NDFValue.NDFFileType.UNITE_DESCRIPTOR);
        List<NDFValue.ObjectValue> objects = parser.parse();
        
        System.out.println("Parsed " + objects.size() + " objects");
        
        // Create a modification tracker and make a test modification
        ModificationTracker tracker = new ModificationTracker();
        
        if (!objects.isEmpty()) {
            NDFValue.ObjectValue firstUnit = objects.get(0);
            String unitName = firstUnit.getInstanceName();
            
            System.out.println("Making test modification to unit: " + unitName);

            // Debug: Show available properties
            System.out.println("Available properties:");
            for (Map.Entry<String, NDFValue> entry : firstUnit.getProperties().entrySet()) {
                String propName = entry.getKey();
                NDFValue propValue = entry.getValue();
                System.out.println("  - " + propName + " = " + propValue + " (type: " + propValue.getClass().getSimpleName() + ")");
            }

            // Find a property to modify - use ClassNameForDebug (string property)
            NDFValue classNameValue = firstUnit.getProperty("ClassNameForDebug");
            if (classNameValue != null && classNameValue instanceof NDFValue.StringValue) {
                NDFValue.StringValue stringVal = (NDFValue.StringValue) classNameValue;
                String oldValue = stringVal.getValue();
                String newValue = oldValue + "_MODIFIED"; // Add suffix

                // Create new value
                NDFValue newClassNameValue = NDFValue.createString(newValue, stringVal.useDoubleQuotes());

                // Record the modification BEFORE applying it
                System.out.println("Recording modification:");
                System.out.println("  unitName: " + unitName);
                System.out.println("  propertyPath: ClassNameForDebug");
                System.out.println("  oldValue: " + classNameValue + " (type: " + (classNameValue != null ? classNameValue.getClass().getSimpleName() : "null") + ")");
                System.out.println("  newValue: " + newClassNameValue + " (type: " + (newClassNameValue != null ? newClassNameValue.getClass().getSimpleName() : "null") + ")");

                tracker.recordModification(unitName, "ClassNameForDebug", classNameValue, newClassNameValue);

                System.out.println("After recording: tracker has " + tracker.getModificationCount() + " modifications");

                // Apply the modification to the object
                firstUnit.setProperty("ClassNameForDebug", newClassNameValue);

                System.out.println("Modified " + unitName + ".ClassNameForDebug: " + oldValue + " -> " + newValue);
                System.out.println("Tracker has " + tracker.getModificationCount() + " modifications");
            }
        }
        
        // Now test the line-based writer
        StringWriter output = new StringWriter();
        NDFWriter writer = new NDFWriter(output, true);
        writer.setOriginalSourceContent(originalContent);
        writer.setModificationTracker(tracker);

        // Debug output
        System.out.println("Debug: originalContent != null: " + (originalContent != null));
        System.out.println("Debug: tracker != null: " + (tracker != null));
        System.out.println("Debug: tracker.getModificationCount(): " + tracker.getModificationCount());
        
        // Mark the modified object
        if (!objects.isEmpty()) {
            writer.markObjectAsModified(objects.get(0));
        }
        
        // Write the file
        writer.write(objects);
        
        String writtenContent = output.toString();
        
        // Compare results
        System.out.println("\nComparison Results:");
        System.out.println("Original length: " + originalContent.length());
        System.out.println("Written length:  " + writtenContent.length());
        
        // Check if the modification was applied
        if (writtenContent.contains("_MODIFIED")) {
            System.out.println("✓ Modified DescriptorId found in output");
        } else {
            System.out.println("✗ Modified DescriptorId NOT found in output");
        }
        
        // Count differences
        int differences = 0;
        int minLength = Math.min(originalContent.length(), writtenContent.length());
        for (int i = 0; i < minLength; i++) {
            if (originalContent.charAt(i) != writtenContent.charAt(i)) {
                differences++;
                if (differences <= 5) { // Show first 5 differences
                    System.out.println("Difference at position " + i + ": '" + 
                                     originalContent.charAt(i) + "' -> '" + writtenContent.charAt(i) + "'");
                }
            }
        }
        
        System.out.println("Total character differences: " + differences);
        
        if (differences == 0 && originalContent.length() == writtenContent.length()) {
            System.out.println("✗ NO CHANGES DETECTED - Line-based writer may not be working");
        } else {
            System.out.println("✓ Changes detected - Line-based writer appears to be working");
        }
    }
    
    private static String readFileContent(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            boolean first = true;
            while ((line = reader.readLine()) != null) {
                if (!first) content.append("\n");
                content.append(line);
                first = false;
            }
        }
        return content.toString();
    }
}
