package com.warnomodmaker.parser;

import com.warnomodmaker.model.NDFValue;
import com.warnomodmaker.model.ModificationTracker;
import com.warnomodmaker.model.ModificationRecord;

import java.io.IOException;
import java.io.Writer;
import java.util.ArrayList;
import java.util.List;
import java.util.*;

/**
 * LINE-BASED ARCHITECTURE: Simple and reliable writer that replaces only modified lines
 * This solves the git diff problem by preserving all unmodified content exactly
 */
public class LineBasedWriter {
    private final Writer writer;
    private final SourceLineTracker lineTracker;
    private final ModificationTracker modificationTracker;
    
    public LineBasedWriter(Writer writer, String originalContent, ModificationTracker modificationTracker) {
        this.writer = writer;
        this.lineTracker = new SourceLineTracker(originalContent);
        this.modificationTracker = modificationTracker;
    }
    
    /**
     * Write the file with line-based modifications applied
     */
    public void write(List<NDFValue.ObjectValue> objects) throws IOException {
        // Apply all modifications to the line tracker
        applyModifications();
        
        // Write the complete output with modifications
        String output = lineTracker.generateOutput();
        writer.write(output);
        

    }
    
    /**
     * Apply all tracked modifications to the appropriate lines
     */
    private void applyModifications() {
        if (modificationTracker == null) {
            return;
        }
        
        List<ModificationRecord> modifications = modificationTracker.getAllModifications();
        
        for (ModificationRecord mod : modifications) {
            applyModification(mod);
        }
    }
    
    /**
     * Apply a single modification to the appropriate line
     */
    private void applyModification(ModificationRecord mod) {
        String unitName = mod.getUnitName();
        String propertyPath = mod.getPropertyPath();
        String oldValue = mod.getOldValue();
        String newValue = mod.getNewValue();

        int lineNumber = findPropertyLine(unitName, propertyPath);

        if (lineNumber >= 0) {
            boolean success = replaceValueInLine(lineNumber, propertyPath, oldValue, newValue);
            if (!success) {
                throw new IllegalStateException("Failed to replace value in line " + (lineNumber + 1) + " for " + unitName + "." + propertyPath + " - NO FALLBACKS ALLOWED");
            }
        } else {
            throw new IllegalStateException("Could not find property line for " + unitName + "." + propertyPath + " - NO FALLBACKS ALLOWED");
        }
    }
    
    /**
     * Find the line number containing a property for a specific unit
     */
    private int findPropertyLine(String unitName, String propertyPath) {
        // Strategy 1: Look for the unit definition first, then find the property within that context
        int unitLineStart = findUnitDefinitionLine(unitName);

        if (unitLineStart >= 0) {
            // Handle array index patterns like ModulesDescriptors[15].MaxPhysicalDamages
            if (propertyPath.matches(".*\\[\\d+\\]\\..*")) {
                return findArrayElementPropertyLine(unitLineStart, propertyPath);
            }

            // Look for the property within the next reasonable range (e.g., 50 lines)
            int searchEnd = Math.min(unitLineStart + 50, lineTracker.getLineCount());

            for (int i = unitLineStart; i < searchEnd; i++) {
                String line = lineTracker.getOriginalLine(i);
                if (containsProperty(line, propertyPath)) {
                    return i;
                }
            }
        }

        return -1;
    }
    
    /**
     * Find the line containing a unit definition
     */
    private int findUnitDefinitionLine(String unitName) {
        for (int i = 0; i < lineTracker.getLineCount(); i++) {
            String line = lineTracker.getOriginalLine(i);

            if (line.contains("export " + unitName + " is ") ||
                line.contains(unitName + " is TEntityDescriptor")) {
                return i;
            }
        }

        return -1;
    }

    /**
     * Find property line for array element patterns like ModulesDescriptors[15].MaxPhysicalDamages
     */
    private int findArrayElementPropertyLine(int unitLineStart, String propertyPath) {
        // Parse the pattern: ModulesDescriptors[15].MaxPhysicalDamages
        String[] parts = propertyPath.split("\\[\\d+\\]\\.");
        if (parts.length != 2) {
            return -1;
        }

        String arrayName = parts[0]; // "ModulesDescriptors"
        String targetProperty = parts[1]; // "MaxPhysicalDamages"

        // Extract the index
        int startBracket = propertyPath.indexOf('[');
        int endBracket = propertyPath.indexOf(']');
        int targetIndex = Integer.parseInt(propertyPath.substring(startBracket + 1, endBracket));

        // Find the array start
        int arrayStartLine = -1;
        int searchEnd = Math.min(unitLineStart + 200, lineTracker.getLineCount());

        for (int i = unitLineStart; i < searchEnd; i++) {
            String line = lineTracker.getOriginalLine(i);
            if (line.trim().startsWith(arrayName + " = [")) {
                arrayStartLine = i;
                break;
            }
        }

        if (arrayStartLine == -1) {
            return -1;
        }

        // Find all array elements first
        List<ArrayElement> elements = findArrayElements(arrayStartLine);

        // DEBUG: Print array elements found
        System.out.println("DEBUG: Found " + elements.size() + " array elements for " + arrayName);
        for (int i = 0; i < elements.size(); i++) {
            ArrayElement elem = elements.get(i);
            String firstLine = lineTracker.getOriginalLine(elem.startLine).trim();
            System.out.println("  [" + i + "] lines " + elem.startLine + "-" + elem.endLine + ": " + firstLine);
        }

        // Check if target index is valid
        if (targetIndex >= elements.size()) {
            System.out.println("DEBUG: Target index " + targetIndex + " >= elements.size() " + elements.size());
            return -1;
        }

        // Get the target element
        ArrayElement targetElement = elements.get(targetIndex);

        // Search for the property within the target element
        for (int line = targetElement.startLine; line <= targetElement.endLine; line++) {
            String lineContent = lineTracker.getOriginalLine(line);
            if (containsProperty(lineContent, targetProperty)) {
                return line;
            }
        }

        return -1;
    }

    /**
     * Helper class to represent an array element
     */
    private static class ArrayElement {
        int startLine;
        int endLine;

        ArrayElement(int startLine, int endLine) {
            this.startLine = startLine;
            this.endLine = endLine;
        }
    }

    /**
     * Find all array elements in the ModulesDescriptors array
     */
    private List<ArrayElement> findArrayElements(int arrayStartLine) {
        List<ArrayElement> elements = new ArrayList<>();
        int currentLine = arrayStartLine + 1;
        int elementStart = -1;
        int parenDepth = 0;
        boolean inElement = false;

        while (currentLine < lineTracker.getLineCount()) {
            String line = lineTracker.getOriginalLine(currentLine);
            String trimmed = line.trim();

            // Skip empty lines and comments
            if (trimmed.isEmpty() || trimmed.startsWith("//")) {
                currentLine++;
                continue;
            }

            // End of array
            if (trimmed.equals("]") || trimmed.startsWith("]")) {
                // Close any open element
                if (inElement && elementStart != -1) {
                    elements.add(new ArrayElement(elementStart, currentLine - 1));
                }
                break;
            }

            // Count parentheses to track nesting
            for (char c : trimmed.toCharArray()) {
                if (c == '(') parenDepth++;
                if (c == ')') parenDepth--;
            }

            // Start of new element: not a property assignment and not inside parentheses
            if (!inElement && !trimmed.contains(" = ") && parenDepth == 0) {
                if (elementStart != -1) {
                    // Close previous element
                    elements.add(new ArrayElement(elementStart, currentLine - 1));
                }
                elementStart = currentLine;
                inElement = true;
            }

            // If we're at depth 0 and see a comma, this might be the end of a single-line element
            if (inElement && parenDepth == 0 && trimmed.endsWith(",")) {
                elements.add(new ArrayElement(elementStart, currentLine));
                elementStart = -1;
                inElement = false;
            }

            currentLine++;
        }

        // Close any remaining element
        if (inElement && elementStart != -1) {
            elements.add(new ArrayElement(elementStart, currentLine - 1));
        }

        return elements;
    }

    /**
     * Check if a line starts an array element
     */
    private boolean isArrayElementStart(String trimmed) {
        // Skip empty lines and comments
        if (trimmed.isEmpty() || trimmed.startsWith("//")) {
            return false;
        }

        // End of array
        if (trimmed.equals("]") || trimmed.startsWith("]")) {
            return false;
        }

        // Lines that contain property assignments (have = sign) are not array element starts
        if (trimmed.contains(" = ")) {
            return false;
        }

        // Array elements can be:
        // 1. Template references: ~/Something, $/Something
        // 2. Module descriptors: TTypeUnitModuleDescriptor, TFormationModuleDescriptor(...)
        // 3. Named elements: ApparenceModel is VehicleApparenceModuleDescriptor, WeaponManager is $/GFX/...
        // 4. Simple references that end with comma

        return trimmed.startsWith("~/") ||
               trimmed.startsWith("$/") ||
               trimmed.contains("ModuleDescriptor") ||
               trimmed.contains(" is ") ||
               (!trimmed.contains("=") && (trimmed.endsWith(",") || trimmed.contains("(")));
    }

    /**
     * Find the end line of an array element starting at the given line
     */
    private int findArrayElementEnd(int startLine) {
        String startTrimmed = lineTracker.getOriginalLine(startLine).trim();

        // Single-line element (ends with comma)
        if (startTrimmed.endsWith(",")) {
            return startLine;
        }

        // Multi-line element - track parentheses depth
        int parenDepth = 0;
        int currentLine = startLine;

        while (currentLine < lineTracker.getLineCount()) {
            String line = lineTracker.getOriginalLine(currentLine);
            String trimmed = line.trim();

            // Count parentheses
            for (char c : trimmed.toCharArray()) {
                if (c == '(') parenDepth++;
                if (c == ')') parenDepth--;
            }

            // If we're back to depth 0 and see a comma, this is the end
            if (currentLine > startLine && parenDepth == 0 && trimmed.endsWith(",")) {
                return currentLine;
            }

            // If we hit the end of the array
            if (trimmed.equals("]") || trimmed.startsWith("]")) {
                return currentLine - 1;
            }

            currentLine++;
        }

        return currentLine - 1;
    }

    

    
    /**
     * Extract the final property name from a property path
     */
    private String extractFinalPropertyName(String propertyPath) {
        if (propertyPath.contains(".")) {
            String[] parts = propertyPath.split("\\.");
            return parts[parts.length - 1];
        }
        
        // Handle array access patterns like "PropertyName[*]"
        if (propertyPath.contains("[")) {
            return propertyPath.substring(0, propertyPath.indexOf("["));
        }
        
        return propertyPath;
    }
    
    /**
     * Check if a line contains a specific property assignment
     */
    private boolean containsProperty(String line, String propertyPath) {
        String propertyName = extractFinalPropertyName(propertyPath);
        String trimmedLine = line.trim();

        if (trimmedLine.startsWith(propertyName)) {
            String afterPropertyName = trimmedLine.substring(propertyName.length()).trim();
            if (afterPropertyName.startsWith("=")) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Replace a value in a specific line while preserving formatting
     */
    private boolean replaceValueInLine(int lineNumber, String propertyPath, String oldValue, String newValue) {
        String originalLine = lineTracker.getOriginalLine(lineNumber);
        
        if (originalLine.isEmpty()) {
            return false;
        }
        
        // Clean the values for comparison
        String cleanOldValue = cleanValue(oldValue);
        String cleanNewValue = cleanValue(newValue);
        
        // Try different replacement strategies
        String newLine = originalLine;
        
        // Strategy 1: Direct value replacement
        if (originalLine.contains(cleanOldValue)) {
            newLine = originalLine.replace(cleanOldValue, cleanNewValue);
        }
        // Strategy 2: Quoted value replacement
        else if (originalLine.contains("'" + cleanOldValue + "'")) {
            newLine = originalLine.replace("'" + cleanOldValue + "'", "'" + cleanNewValue + "'");
        }
        else if (originalLine.contains("\"" + cleanOldValue + "\"")) {
            newLine = originalLine.replace("\"" + cleanOldValue + "\"", "\"" + cleanNewValue + "\"");
        }
        
        // Apply the modification if the line changed
        if (!newLine.equals(originalLine)) {
            lineTracker.modifyLine(lineNumber, newLine);
            return true;
        }
        
        return false;
    }
    
    /**
     * Clean a value string for comparison and replacement
     */
    private String cleanValue(String value) {
        if (value == null) {
            return "";
        }

        String cleaned = value.trim();

        // Handle ModificationRecord prefixes (SQ: for single quotes, DQ: for double quotes)
        if (cleaned.startsWith("SQ:")) {
            cleaned = cleaned.substring(3); // Remove "SQ:" prefix
        } else if (cleaned.startsWith("DQ:")) {
            cleaned = cleaned.substring(3); // Remove "DQ:" prefix
        }

        // Remove surrounding quotes if present
        if ((cleaned.startsWith("'") && cleaned.endsWith("'")) ||
            (cleaned.startsWith("\"") && cleaned.endsWith("\""))) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
        }

        return cleaned;
    }
    
    /**
     * Get modification statistics for debugging
     */
    public String getModificationStats() {
        return lineTracker.getModificationSummary();
    }
}
