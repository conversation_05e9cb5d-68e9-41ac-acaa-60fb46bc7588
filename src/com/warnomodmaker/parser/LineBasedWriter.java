package com.warnomodmaker.parser;

import com.warnomodmaker.model.NDFValue;
import com.warnomodmaker.model.ModificationTracker;
import com.warnomodmaker.model.ModificationRecord;

import java.io.IOException;
import java.io.Writer;
import java.util.*;

/**
 * LINE-BASED ARCHITECTURE: Simple and reliable writer that replaces only modified lines
 * This solves the git diff problem by preserving all unmodified content exactly
 */
public class LineBasedWriter {
    private final Writer writer;
    private final SourceLineTracker lineTracker;
    private final ModificationTracker modificationTracker;
    
    public LineBasedWriter(Writer writer, String originalContent, ModificationTracker modificationTracker) {
        this.writer = writer;
        this.lineTracker = new SourceLineTracker(originalContent);
        this.modificationTracker = modificationTracker;
    }
    
    /**
     * Write the file with line-based modifications applied
     */
    public void write(List<NDFValue.ObjectValue> objects) throws IOException {
        // Apply all modifications to the line tracker
        applyModifications();
        
        // Write the complete output with modifications
        String output = lineTracker.generateOutput();
        writer.write(output);
        

    }
    
    /**
     * Apply all tracked modifications to the appropriate lines
     */
    private void applyModifications() {
        if (modificationTracker == null) {
            return;
        }
        
        List<ModificationRecord> modifications = modificationTracker.getAllModifications();
        
        for (ModificationRecord mod : modifications) {
            applyModification(mod);
        }
    }
    
    /**
     * Apply a single modification to the appropriate line
     */
    private void applyModification(ModificationRecord mod) {
        String unitName = mod.getUnitName();
        String propertyPath = mod.getPropertyPath();
        String oldValue = mod.getOldValue();
        String newValue = mod.getNewValue();

        int lineNumber = findPropertyLine(unitName, propertyPath);

        if (lineNumber >= 0) {
            boolean success = replaceValueInLine(lineNumber, propertyPath, oldValue, newValue);
            if (!success) {
                throw new IllegalStateException("Failed to replace value in line " + (lineNumber + 1) + " for " + unitName + "." + propertyPath + " - NO FALLBACKS ALLOWED");
            }
        } else {
            throw new IllegalStateException("Could not find property line for " + unitName + "." + propertyPath + " - NO FALLBACKS ALLOWED");
        }
    }
    
    /**
     * Find the line number containing a property for a specific unit
     */
    private int findPropertyLine(String unitName, String propertyPath) {
        // Strategy 1: Look for the unit definition first, then find the property within that context
        int unitLineStart = findUnitDefinitionLine(unitName);

        if (unitLineStart >= 0) {
            // Handle array index patterns like ModulesDescriptors[15].MaxPhysicalDamages
            if (propertyPath.matches(".*\\[\\d+\\]\\..*")) {
                return findArrayElementPropertyLine(unitLineStart, propertyPath);
            }

            // Look for the property within the next reasonable range (e.g., 50 lines)
            int searchEnd = Math.min(unitLineStart + 50, lineTracker.getLineCount());

            for (int i = unitLineStart; i < searchEnd; i++) {
                String line = lineTracker.getOriginalLine(i);
                if (containsProperty(line, propertyPath)) {
                    return i;
                }
            }
        }

        return -1;
    }
    
    /**
     * Find the line containing a unit definition
     */
    private int findUnitDefinitionLine(String unitName) {
        for (int i = 0; i < lineTracker.getLineCount(); i++) {
            String line = lineTracker.getOriginalLine(i);

            if (line.contains("export " + unitName + " is ") ||
                line.contains(unitName + " is TEntityDescriptor")) {
                return i;
            }
        }

        return -1;
    }

    /**
     * Find property line for array element patterns like ModulesDescriptors[15].MaxPhysicalDamages
     */
    private int findArrayElementPropertyLine(int unitLineStart, String propertyPath) {
        // Parse the pattern: ModulesDescriptors[15].MaxPhysicalDamages
        String[] parts = propertyPath.split("\\[\\d+\\]\\.");
        if (parts.length != 2) {
            return -1;
        }

        String arrayName = parts[0]; // "ModulesDescriptors"
        String targetProperty = parts[1]; // "MaxPhysicalDamages"

        // Extract the index
        int startBracket = propertyPath.indexOf('[');
        int endBracket = propertyPath.indexOf(']');
        int targetIndex = Integer.parseInt(propertyPath.substring(startBracket + 1, endBracket));

        // Find the array start
        int arrayStartLine = -1;
        int searchEnd = Math.min(unitLineStart + 200, lineTracker.getLineCount());

        for (int i = unitLineStart; i < searchEnd; i++) {
            String line = lineTracker.getOriginalLine(i);
            if (line.trim().startsWith(arrayName + " = [")) {
                arrayStartLine = i;
                break;
            }
        }

        if (arrayStartLine == -1) {
            return -1;
        }

        // Count array elements to find the target index
        int currentIndex = 0;
        int currentLine = arrayStartLine + 1;
        int bracketDepth = 0;
        boolean inElement = false;

        while (currentLine < lineTracker.getLineCount()) {
            String line = lineTracker.getOriginalLine(currentLine);
            String trimmed = line.trim();

            // Skip empty lines and comments
            if (trimmed.isEmpty() || trimmed.startsWith("//")) {
                currentLine++;
                continue;
            }

            // Track bracket depth to handle nested structures
            for (char c : trimmed.toCharArray()) {
                if (c == '(' || c == '[') bracketDepth++;
                if (c == ')' || c == ']') bracketDepth--;
            }

            // End of main array
            if (bracketDepth < 0) {
                break;
            }

            // Detect start of new array element
            if (!inElement && (trimmed.contains("ModuleDescriptor") ||
                              trimmed.startsWith("~/") ||
                              trimmed.startsWith("$/") ||
                              trimmed.contains(" is ") ||
                              (trimmed.endsWith(",") && !trimmed.contains("=")))) {

                if (currentIndex == targetIndex) {
                    // We're in the target element, look for the property
                    int elementEndLine = findElementEndLine(currentLine, arrayStartLine);
                    for (int j = currentLine; j <= elementEndLine; j++) {
                        String propLine = lineTracker.getOriginalLine(j);
                        if (containsProperty(propLine, targetProperty)) {
                            return j;
                        }
                    }
                    return -1; // Property not found in target element
                }

                currentIndex++;
                inElement = true;
            }

            // Reset inElement when we finish an element (back to depth 0 and see a comma)
            if (inElement && bracketDepth == 0 && trimmed.endsWith(",")) {
                inElement = false;
            }

            currentLine++;
        }

        return -1;
    }

    /**
     * Find the end line of an array element
     */
    private int findElementEndLine(int elementStartLine, int arrayStartLine) {
        int bracketDepth = 0;
        int currentLine = elementStartLine;

        while (currentLine < lineTracker.getLineCount()) {
            String line = lineTracker.getOriginalLine(currentLine);
            String trimmed = line.trim();

            // Track bracket depth
            for (char c : trimmed.toCharArray()) {
                if (c == '(' || c == '[') bracketDepth++;
                if (c == ')' || c == ']') bracketDepth--;
            }

            // If we're back to depth 0 and see a comma or closing bracket, this is the end
            if (currentLine > elementStartLine && bracketDepth <= 0 &&
                (trimmed.endsWith(",") || trimmed.equals("]") || trimmed.startsWith("]"))) {
                return currentLine - 1;
            }

            currentLine++;
        }

        return currentLine - 1;
    }
    

    
    /**
     * Extract the final property name from a property path
     */
    private String extractFinalPropertyName(String propertyPath) {
        if (propertyPath.contains(".")) {
            String[] parts = propertyPath.split("\\.");
            return parts[parts.length - 1];
        }
        
        // Handle array access patterns like "PropertyName[*]"
        if (propertyPath.contains("[")) {
            return propertyPath.substring(0, propertyPath.indexOf("["));
        }
        
        return propertyPath;
    }
    
    /**
     * Check if a line contains a specific property assignment
     */
    private boolean containsProperty(String line, String propertyPath) {
        String propertyName = extractFinalPropertyName(propertyPath);
        String trimmedLine = line.trim();

        if (trimmedLine.startsWith(propertyName)) {
            String afterPropertyName = trimmedLine.substring(propertyName.length()).trim();
            if (afterPropertyName.startsWith("=")) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Replace a value in a specific line while preserving formatting
     */
    private boolean replaceValueInLine(int lineNumber, String propertyPath, String oldValue, String newValue) {
        String originalLine = lineTracker.getOriginalLine(lineNumber);
        
        if (originalLine.isEmpty()) {
            return false;
        }
        
        // Clean the values for comparison
        String cleanOldValue = cleanValue(oldValue);
        String cleanNewValue = cleanValue(newValue);
        
        // Try different replacement strategies
        String newLine = originalLine;
        
        // Strategy 1: Direct value replacement
        if (originalLine.contains(cleanOldValue)) {
            newLine = originalLine.replace(cleanOldValue, cleanNewValue);
        }
        // Strategy 2: Quoted value replacement
        else if (originalLine.contains("'" + cleanOldValue + "'")) {
            newLine = originalLine.replace("'" + cleanOldValue + "'", "'" + cleanNewValue + "'");
        }
        else if (originalLine.contains("\"" + cleanOldValue + "\"")) {
            newLine = originalLine.replace("\"" + cleanOldValue + "\"", "\"" + cleanNewValue + "\"");
        }
        
        // Apply the modification if the line changed
        if (!newLine.equals(originalLine)) {
            lineTracker.modifyLine(lineNumber, newLine);
            return true;
        }
        
        return false;
    }
    
    /**
     * Clean a value string for comparison and replacement
     */
    private String cleanValue(String value) {
        if (value == null) {
            return "";
        }

        String cleaned = value.trim();

        // Handle ModificationRecord prefixes (SQ: for single quotes, DQ: for double quotes)
        if (cleaned.startsWith("SQ:")) {
            cleaned = cleaned.substring(3); // Remove "SQ:" prefix
        } else if (cleaned.startsWith("DQ:")) {
            cleaned = cleaned.substring(3); // Remove "DQ:" prefix
        }

        // Remove surrounding quotes if present
        if ((cleaned.startsWith("'") && cleaned.endsWith("'")) ||
            (cleaned.startsWith("\"") && cleaned.endsWith("\""))) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
        }

        return cleaned;
    }
    
    /**
     * Get modification statistics for debugging
     */
    public String getModificationStats() {
        return lineTracker.getModificationSummary();
    }
}
