package com.warnomodmaker.parser;

import com.warnomodmaker.model.NDFValue;
import com.warnomodmaker.model.ModificationTracker;
import com.warnomodmaker.model.ModificationRecord;

import java.io.IOException;
import java.io.Writer;
import java.util.*;

/**
 * LINE-BASED ARCHITECTURE: Simple and reliable writer that replaces only modified lines
 * This solves the git diff problem by preserving all unmodified content exactly
 */
public class LineBasedWriter {
    private final Writer writer;
    private final SourceLineTracker lineTracker;
    private final ModificationTracker modificationTracker;
    
    public LineBasedWriter(Writer writer, String originalContent, ModificationTracker modificationTracker) {
        this.writer = writer;
        this.lineTracker = new SourceLineTracker(originalContent);
        this.modificationTracker = modificationTracker;
    }
    
    /**
     * Write the file with line-based modifications applied
     */
    public void write(List<NDFValue.ObjectValue> objects) throws IOException {
        // Apply all modifications to the line tracker
        applyModifications();
        
        // Write the complete output with modifications
        String output = lineTracker.generateOutput();
        writer.write(output);
        
        // Debug output
        String summary = lineTracker.getModificationSummary();
        System.out.println("  + Line-based writing: " + summary);

        // Additional debug info
        if (modificationTracker != null) {
            System.out.println("  + Modification tracker has " + modificationTracker.getModificationCount() + " modifications");
        }
    }
    
    /**
     * Apply all tracked modifications to the appropriate lines
     */
    private void applyModifications() {
        if (modificationTracker == null) {
            return;
        }
        
        List<ModificationRecord> modifications = modificationTracker.getAllModifications();
        
        for (ModificationRecord mod : modifications) {
            applyModification(mod);
        }
    }
    
    /**
     * Apply a single modification to the appropriate line
     */
    private void applyModification(ModificationRecord mod) {
        String unitName = mod.getUnitName();
        String propertyPath = mod.getPropertyPath();
        String oldValue = mod.getOldValue();
        String newValue = mod.getNewValue();

        System.out.println("    Applying modification: " + unitName + "." + propertyPath + " = " + newValue);

        // Find the line containing this property for this unit
        int lineNumber = findPropertyLine(unitName, propertyPath);

        if (lineNumber >= 0) {
            System.out.println("    Found property at line " + (lineNumber + 1));
            // Apply the modification to this line
            boolean success = replaceValueInLine(lineNumber, propertyPath, oldValue, newValue);

            if (!success) {
                System.out.println("    Primary replacement failed, trying fallback");
                // Fallback: try to find the property without unit context
                lineNumber = findPropertyLineSimple(propertyPath);
                if (lineNumber >= 0) {
                    System.out.println("    Found property at fallback line " + (lineNumber + 1));
                    replaceValueInLine(lineNumber, propertyPath, oldValue, newValue);
                }
            }
        } else {
            System.out.println("    Could not find property line for " + unitName + "." + propertyPath);
        }
    }
    
    /**
     * Find the line number containing a property for a specific unit
     */
    private int findPropertyLine(String unitName, String propertyPath) {
        // Strategy 1: Look for the unit definition first, then find the property within that context
        int unitLineStart = findUnitDefinitionLine(unitName);
        
        if (unitLineStart >= 0) {
            // Look for the property within the next reasonable range (e.g., 50 lines)
            int searchEnd = Math.min(unitLineStart + 50, lineTracker.getLineCount());
            
            for (int i = unitLineStart; i < searchEnd; i++) {
                String line = lineTracker.getOriginalLine(i);
                if (containsProperty(line, propertyPath)) {
                    return i;
                }
            }
        }
        
        return -1;
    }
    
    /**
     * Find the line containing a unit definition
     */
    private int findUnitDefinitionLine(String unitName) {
        for (int i = 0; i < lineTracker.getLineCount(); i++) {
            String line = lineTracker.getOriginalLine(i);

            // Look for patterns like: "export UnitName is TEntityDescriptor"
            if (line.contains("export " + unitName + " is ") ||
                line.contains(unitName + " is TEntityDescriptor")) {
                System.out.println("    Found unit definition at line " + (i + 1) + ": " + line.trim());
                return i;
            }
        }

        return -1;
    }
    
    /**
     * Simple property line finder (fallback)
     */
    private int findPropertyLineSimple(String propertyPath) {
        // Extract the final property name from the path
        String propertyName = extractFinalPropertyName(propertyPath);
        
        return lineTracker.findPropertyLine(propertyName);
    }
    
    /**
     * Extract the final property name from a property path
     */
    private String extractFinalPropertyName(String propertyPath) {
        if (propertyPath.contains(".")) {
            String[] parts = propertyPath.split("\\.");
            return parts[parts.length - 1];
        }
        
        // Handle array access patterns like "PropertyName[*]"
        if (propertyPath.contains("[")) {
            return propertyPath.substring(0, propertyPath.indexOf("["));
        }
        
        return propertyPath;
    }
    
    /**
     * Check if a line contains a specific property assignment
     */
    private boolean containsProperty(String line, String propertyPath) {
        String propertyName = extractFinalPropertyName(propertyPath);
        String trimmedLine = line.trim();

        // Look for property assignment patterns with flexible whitespace
        // NDF files often have multiple spaces for alignment: "PropertyName       = value"
        if (trimmedLine.startsWith(propertyName)) {
            // Check if this line contains an assignment after the property name
            String afterPropertyName = trimmedLine.substring(propertyName.length()).trim();
            if (afterPropertyName.startsWith("=")) {
                System.out.println("    Property match found: " + line.trim());
                return true;
            }
        }

        return false;
    }
    
    /**
     * Replace a value in a specific line while preserving formatting
     */
    private boolean replaceValueInLine(int lineNumber, String propertyPath, String oldValue, String newValue) {
        String originalLine = lineTracker.getOriginalLine(lineNumber);
        
        if (originalLine.isEmpty()) {
            return false;
        }
        
        // Clean the values for comparison
        String cleanOldValue = cleanValue(oldValue);
        String cleanNewValue = cleanValue(newValue);
        
        // Try different replacement strategies
        String newLine = originalLine;
        
        // Strategy 1: Direct value replacement
        if (originalLine.contains(cleanOldValue)) {
            newLine = originalLine.replace(cleanOldValue, cleanNewValue);
        }
        // Strategy 2: Quoted value replacement
        else if (originalLine.contains("'" + cleanOldValue + "'")) {
            newLine = originalLine.replace("'" + cleanOldValue + "'", "'" + cleanNewValue + "'");
        }
        else if (originalLine.contains("\"" + cleanOldValue + "\"")) {
            newLine = originalLine.replace("\"" + cleanOldValue + "\"", "\"" + cleanNewValue + "\"");
        }
        
        // Apply the modification if the line changed
        if (!newLine.equals(originalLine)) {
            lineTracker.modifyLine(lineNumber, newLine);
            return true;
        }
        
        return false;
    }
    
    /**
     * Clean a value string for comparison and replacement
     */
    private String cleanValue(String value) {
        if (value == null) {
            return "";
        }

        String cleaned = value.trim();

        // Handle ModificationRecord prefixes (SQ: for single quotes, DQ: for double quotes)
        if (cleaned.startsWith("SQ:")) {
            cleaned = cleaned.substring(3); // Remove "SQ:" prefix
        } else if (cleaned.startsWith("DQ:")) {
            cleaned = cleaned.substring(3); // Remove "DQ:" prefix
        }

        // Remove surrounding quotes if present
        if ((cleaned.startsWith("'") && cleaned.endsWith("'")) ||
            (cleaned.startsWith("\"") && cleaned.endsWith("\""))) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
        }

        return cleaned;
    }
    
    /**
     * Get modification statistics for debugging
     */
    public String getModificationStats() {
        return lineTracker.getModificationSummary();
    }
}
