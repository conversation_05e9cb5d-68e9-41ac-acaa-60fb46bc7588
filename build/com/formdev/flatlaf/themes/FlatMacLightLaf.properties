#
# Copyright 2022 FormDev Software GmbH
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# base theme (light, dark, intellij or darcula); only used by theme editor
@baseTheme = light


#---- macOS NSColor system colors (in NSColorSpace.deviceRGB) ----
# generated on macOS 12.2 using Xcode and flatlaf-testing/misc/MacOSColorDump.playground

@nsLabelColor = #000000d8
@nsSecondaryLabelColor = #0000007f
@nsTertiaryLabelColor = #00000042
@nsQuaternaryLabelColor = #00000019
@nsSystemRedColor = #ff3b30
@nsSystemGreenColor = #28cd41
@nsSystemBlueColor = #007aff
@nsSystemOrangeColor = #ff9500
@nsSystemYellowColor = #ffcc00
@nsSystemBrownColor = #a2845e
@nsSystemPinkColor = #ff2d55
@nsSystemPurpleColor = #af52de
@nsSystemTealColor = #59adc4
@nsSystemIndigoColor = #5856d6
@nsSystemMintColor = #00c7be
@nsSystemCyanColor = #55bef0
@nsSystemGrayColor = #8e8e93
@nsLinkColor = #0068da
@nsPlaceholderTextColor = #0000003f
@nsWindowFrameTextColor = #000000d8
@nsSelectedMenuItemTextColor = #ffffff
@nsAlternateSelectedControlTextColor = #ffffff
@nsHeaderTextColor = #000000d8
@nsSeparatorColor = #00000019
@nsGridColor = #e6e6e6
@nsTextColor = #000000
@nsTextBackgroundColor = #ffffff
@nsSelectedTextColor = #000000
@nsSelectedTextBackgroundColor = #b3d7ff
@nsUnemphasizedSelectedTextBackgroundColor = #dcdcdc
@nsUnemphasizedSelectedTextColor = #000000
@nsWindowBackgroundColor = #ececec
@nsUnderPageBackgroundColor = #969696e5
@nsControlBackgroundColor = #ffffff
@nsSelectedContentBackgroundColor = #0063e1
@nsUnemphasizedSelectedContentBackgroundColor = #dcdcdc
@nsFindHighlightColor = #ffff00
@nsControlColor = #ffffff
@nsControlTextColor = #000000d8
@nsSelectedControlColor = #b3d7ff
@nsSelectedControlTextColor = #000000d8
@nsDisabledControlTextColor = #0000003f
@nsKeyboardFocusIndicatorColor = #0067f47f
@nsControlAccentColor = #007aff

# accent colors are:
#    @nsSelectedTextBackgroundColor     for @textSelectionBackground
#    @nsSelectedContentBackgroundColor  for @selectionBackground
#    @nsSelectedControlColor            unused
#    @nsKeyboardFocusIndicatorColor     for @accentFocusColor
#    @nsControlAccentColor              for @accentColor


#---- variables ----

# general background and foreground (text color)
@background = #f6f6f6
@foreground = over(@nsControlTextColor,@background)
@disabledForeground = over(@nsSecondaryLabelColor,@background)

# component background
@buttonBackground = @nsControlColor
@componentBackground = @nsControlColor
@disabledComponentBackground = darken(@componentBackground,2%)
@menuBackground = darken(@background,4%)

# selection
@selectionBackground = shade(spin(if(systemColor(accent), systemColor(accent), @accentColor),4),10%)
@selectionForeground = @nsSelectedMenuItemTextColor
@selectionInactiveBackground = @nsUnemphasizedSelectedContentBackgroundColor

# text selection
@textSelectionBackground = systemColor(highlight,tint(if(systemColor(accent), systemColor(accent), @accentColor),70%))
@textSelectionForeground = @foreground

# menu
@menuSelectionBackground = lighten(@accentColor,12%)
@menuCheckBackground = lighten(@menuSelectionBackground,25%,derived noAutoInverse)
@menuItemMargin = 3,11,3,11

# accent colors (blueish)
@accentColor = systemColor(accent,@nsControlAccentColor)
@accentFocusColor = fade(spin(if(systemColor(accent), systemColor(accent), @accentColor),4),50%)


#---- Button ----

Button.arc = 12
Button.disabledBackground = @disabledComponentBackground
Button.focusedBackground = null

Button.default.borderWidth = 1
Button.default.boldText = true
Button.default.background = @accentColor
Button.default.foreground = contrast($Button.default.background, @background, $Button.foreground, 20%)


#---- CheckBox ----

CheckBox.iconTextGap = 6
CheckBox.arc = 7
CheckBox.icon.focusWidth = null
CheckBox.icon.style = filled
CheckBox.icon[filled].borderWidth = 1
CheckBox.icon[filled].selectedBorderWidth = 0
CheckBox.icon[filled].disabledSelectedBorderWidth = 1
CheckBox.icon[filled].background = @nsControlColor
CheckBox.icon[filled].disabledBackground = @disabledComponentBackground
CheckBox.icon[filled].selectedBackground = @accentColor
CheckBox.icon[filled].borderColor = $Component.borderColor
CheckBox.icon[filled].disabledBorderColor = $Component.disabledBorderColor
CheckBox.icon[filled].checkmarkColor = @nsSelectedMenuItemTextColor
CheckBox.icon[filled].disabledCheckmarkColor = darken($CheckBox.icon[filled].checkmarkColor,25%)
CheckBox.icon.focusedBackground = null


#---- ComboBox ----

ComboBox.buttonStyle = mac
ComboBox.disabledBackground = @disabledComponentBackground
ComboBox.buttonBackground = @accentColor
ComboBox.buttonArrowColor = @nsSelectedMenuItemTextColor
ComboBox.buttonHoverArrowColor = darken($ComboBox.buttonArrowColor,15%,derived noAutoInverse)
ComboBox.buttonPressedArrowColor = darken($ComboBox.buttonArrowColor,25%,derived noAutoInverse)
ComboBox.popupBackground = @menuBackground
ComboBox.selectionBackground = @menuSelectionBackground
ComboBox.popupInsets = 5,0,5,0
ComboBox.selectionInsets = 0,5,0,5
ComboBox.selectionArc = 8
ComboBox.borderCornerRadius = 8


#---- Component ----

Component.focusWidth = 2
Component.innerFocusWidth = 0
Component.innerOutlineWidth = 0
Component.arc = 12
Component.borderColor = fadein(@nsSeparatorColor,5%)
Component.disabledBorderColor = @nsSeparatorColor


#---- EditorPane ---

EditorPane.disabledBackground = @disabledComponentBackground
EditorPane.selectionBackground = @textSelectionBackground
EditorPane.selectionForeground = @textSelectionForeground


#---- FormattedTextField ---

FormattedTextField.disabledBackground = @disabledComponentBackground
FormattedTextField.selectionBackground = @textSelectionBackground
FormattedTextField.selectionForeground = @textSelectionForeground


#---- MenuBar ----

MenuBar.selectionInsets = 0,0,0,0
MenuBar.selectionEmbeddedInsets = 3,0,3,0
MenuBar.selectionArc = 8
MenuBar.selectionBackground = darken(@menuBackground,15%,derived)
MenuBar.selectionForeground = @foreground


#---- MenuItem ----

MenuItem.selectionInsets = 0,5,0,5
MenuItem.selectionArc = 8

Menu.selectionBackground = @menuSelectionBackground
MenuItem.selectionBackground = @menuSelectionBackground
CheckBoxMenuItem.selectionBackground = @menuSelectionBackground
RadioButtonMenuItem.selectionBackground = @menuSelectionBackground


#---- PasswordField ---

PasswordField.disabledBackground = @disabledComponentBackground
PasswordField.selectionBackground = @textSelectionBackground
PasswordField.selectionForeground = @textSelectionForeground


#---- PopupMenu ----

PopupMenu.borderInsets = 6,1,6,1
PopupMenu.borderCornerRadius = 8


#---- ProgressBar ----

ProgressBar.background = darken(@background,5%)


#---- RadioButton ----

RadioButton.iconTextGap = 6
RadioButton.icon.style = filled
RadioButton.icon[filled].centerDiameter = 6


#---- ScrollBar ----

ScrollBar.width = 12
ScrollBar.track = darken(@componentBackground,2%)
ScrollBar.thumb = darken(@componentBackground,24%)

# from FlatLaf.properties (when using not on macOS)
ScrollBar.minimumThumbSize = 18,18
ScrollBar.thumbInsets = 2,2,2,2
ScrollBar.thumbArc = 999
ScrollBar.hoverThumbWithTrack = true


#---- Separator ----

Separator.foreground = @nsSeparatorColor


#---- Slider ----

Slider.trackWidth = 3
Slider.thumbSize = 14,14
Slider.trackColor = darken(@background,7%)
Slider.thumbColor = @componentBackground
Slider.thumbBorderColor = $Component.borderColor
Slider.disabledTrackColor = lighten($Slider.trackColor,3%)
Slider.disabledThumbColor = darken($Slider.thumbColor,3%)


#---- Spinner ----

Spinner.buttonStyle = mac
Spinner.disabledBackground = @disabledComponentBackground
Spinner.buttonArrowColor = @foreground
Spinner.buttonHoverArrowColor = lighten($Spinner.buttonArrowColor,20%,derived noAutoInverse)
Spinner.buttonPressedArrowColor = lighten($Spinner.buttonArrowColor,30%,derived noAutoInverse)


#---- TabbedPane ----

TabbedPane.tabArc = $Button.arc
TabbedPane.tabSelectionArc = 999
TabbedPane.cardTabArc = $Button.arc


#---- TextArea ---

TextArea.disabledBackground = @disabledComponentBackground
TextArea.selectionBackground = @textSelectionBackground
TextArea.selectionForeground = @textSelectionForeground


#---- TextField ----

TextField.disabledBackground = @disabledComponentBackground
TextField.selectionBackground = @textSelectionBackground
TextField.selectionForeground = @textSelectionForeground


#---- TextPane ---

TextPane.disabledBackground = @disabledComponentBackground
TextPane.selectionBackground = @textSelectionBackground
TextPane.selectionForeground = @textSelectionForeground


#---- ToggleButton ----

ToggleButton.disabledBackground = $Button.disabledBackground


#---- ToolBar ----

ToolBar.hoverButtonGroupArc = 14
