#
# Copyright 2022 FormDev Software GmbH
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# base theme (light, dark, intellij or darcula); only used by theme editor
@baseTheme = dark


#---- macOS NSColor system colors (in NSColorSpace.deviceRGB) ----
# generated on macOS 12.2 using Xcode and flatlaf-testing/misc/MacOSColorDump.playground

@nsLabelColor = #ffffffd8
@nsSecondaryLabelColor = #ffffff8c
@nsTertiaryLabelColor = #ffffff3f
@nsQuaternaryLabelColor = #ffffff19
@nsSystemRedColor = #ff453a
@nsSystemGreenColor = #32d74b
@nsSystemBlueColor = #0a84ff
@nsSystemOrangeColor = #ff9f0a
@nsSystemYellowColor = #ffd60a
@nsSystemBrownColor = #ac8e68
@nsSystemPinkColor = #ff375f
@nsSystemPurpleColor = #bf5af2
@nsSystemTealColor = #6ac4dc
@nsSystemIndigoColor = #5e5ce6
@nsSystemMintColor = #63e6e2
@nsSystemCyanColor = #5ac8f5
@nsSystemGrayColor = #98989d
@nsLinkColor = #419cff
@nsPlaceholderTextColor = #ffffff3f
@nsWindowFrameTextColor = #ffffffd8
@nsSelectedMenuItemTextColor = #ffffff
@nsAlternateSelectedControlTextColor = #ffffff
@nsHeaderTextColor = #ffffff
@nsSeparatorColor = #ffffff19
@nsGridColor = #1a1a1a
@nsTextColor = #ffffff
@nsTextBackgroundColor = #1e1e1e
@nsSelectedTextColor = #ffffff
@nsSelectedTextBackgroundColor = #3f638b
@nsUnemphasizedSelectedTextBackgroundColor = #464646
@nsUnemphasizedSelectedTextColor = #ffffff
@nsWindowBackgroundColor = #323232
@nsUnderPageBackgroundColor = #282828
@nsControlBackgroundColor = #1e1e1e
@nsSelectedContentBackgroundColor = #0058d0
@nsUnemphasizedSelectedContentBackgroundColor = #464646
@nsFindHighlightColor = #ffff00
@nsControlColor = #ffffff3f
@nsControlTextColor = #ffffffd8
@nsSelectedControlColor = #3f638b
@nsSelectedControlTextColor = #ffffffd8
@nsDisabledControlTextColor = #ffffff3f
@nsKeyboardFocusIndicatorColor = #1aa9ff7f
@nsControlAccentColor = #007aff

# accent colors are:
#    @nsSelectedTextBackgroundColor     for @textSelectionBackground
#    @nsSelectedContentBackgroundColor  for @selectionBackground
#    @nsSelectedControlColor            unused
#    @nsKeyboardFocusIndicatorColor     for @accentFocusColor
#    @nsControlAccentColor              for @accentColor


#---- variables ----

# general background and foreground (text color)
@background = @nsControlBackgroundColor
@foreground = over(@nsControlTextColor,@background)
@disabledForeground = over(@nsSecondaryLabelColor,@background)

# component background
@buttonBackground = over(@nsControlColor,@background)
@componentBackground = darken(over(@nsControlColor,@background),18%)
@disabledComponentBackground = darken(@componentBackground,2%)
@menuBackground = lighten(@background,8%)

# selection
@selectionBackground = shade(spin(if(systemColor(accent), systemColor(accent), @accentColor),4),20%)
@selectionForeground = @nsSelectedMenuItemTextColor
@selectionInactiveBackground = @nsUnemphasizedSelectedContentBackgroundColor

# text selection
@textSelectionBackground = systemColor(highlight,darken(desaturate(if(systemColor(accent), systemColor(accent), @accentColor),60%),10%))
@textSelectionForeground = @nsSelectedTextColor

# menu
@menuSelectionBackground = desaturate(@selectionBackground,20%)
@menuItemMargin = 3,11,3,11

# accent colors (blueish)
@accentColor = systemColor(accent,@nsControlAccentColor)
@accentFocusColor = fade(lighten(spin(if(systemColor(accent), systemColor(accent), @accentColor),-8),5%),50%)


#---- Button ----

Button.arc = 12
Button.borderWidth = 0
Button.disabledBackground = darken($Button.background,10%)

Button.default.borderWidth = 0
Button.default.foreground = contrast($Button.default.background, @background, @selectionForeground, 25%)

Button.toolbar.hoverBackground = #fff1
Button.toolbar.pressedBackground = #fff2
Button.toolbar.selectedBackground = #fff3


#---- CheckBox ----

CheckBox.iconTextGap = 6
CheckBox.arc = 7
CheckBox.icon.focusWidth = null
CheckBox.icon.style = filled
CheckBox.icon[filled].borderWidth = 0
CheckBox.icon[filled].selectedBorderWidth = 0
CheckBox.icon[filled].background = darken(over(@nsControlColor,@background),3%)
CheckBox.icon[filled].disabledBackground = darken($CheckBox.icon[filled].background,10%)
CheckBox.icon[filled].selectedBackground = @accentColor
CheckBox.icon[filled].checkmarkColor = fadeout(@nsSelectedMenuItemTextColor,15%)
CheckBox.icon[filled].disabledCheckmarkColor = darken($CheckBox.icon[filled].checkmarkColor,45%)
CheckBox.icon.focusedBackground = null


#---- ComboBox ----

ComboBox.buttonStyle = mac
ComboBox.background = over(@nsControlColor,@background)
ComboBox.editableBackground = @componentBackground
ComboBox.disabledBackground = @disabledComponentBackground
ComboBox.buttonBackground = @accentColor
ComboBox.buttonArrowColor = @nsSelectedMenuItemTextColor
ComboBox.buttonHoverArrowColor = darken($ComboBox.buttonArrowColor,15%,derived noAutoInverse)
ComboBox.buttonPressedArrowColor = darken($ComboBox.buttonArrowColor,25%,derived noAutoInverse)
ComboBox.popupBackground = @menuBackground
ComboBox.selectionBackground = @menuSelectionBackground
ComboBox.popupInsets = 5,0,5,0
ComboBox.selectionInsets = 0,5,0,5
ComboBox.selectionArc = 8
ComboBox.borderCornerRadius = 8


#---- Component ----

Component.focusWidth = 2
Component.innerFocusWidth = 0
Component.innerOutlineWidth = 0
Component.arc = 12
Component.borderColor = @nsSeparatorColor
Component.disabledBorderColor = fadeout(@nsSeparatorColor,5%)


#---- EditorPane ---

EditorPane.disabledBackground = @disabledComponentBackground
EditorPane.selectionBackground = @textSelectionBackground
EditorPane.selectionForeground = @textSelectionForeground


#---- FormattedTextField ---

FormattedTextField.disabledBackground = @disabledComponentBackground
FormattedTextField.selectionBackground = @textSelectionBackground
FormattedTextField.selectionForeground = @textSelectionForeground


#---- MenuBar ----

MenuBar.selectionInsets = 0,0,0,0
MenuBar.selectionEmbeddedInsets = 3,0,3,0
MenuBar.selectionArc = 8
MenuBar.selectionBackground = lighten(@menuBackground,15%,derived)
MenuBar.selectionForeground = @foreground


#---- MenuItem ----

MenuItem.selectionInsets = 0,5,0,5
MenuItem.selectionArc = 8

Menu.selectionBackground = @menuSelectionBackground
MenuItem.selectionBackground = @menuSelectionBackground
CheckBoxMenuItem.selectionBackground = @menuSelectionBackground
RadioButtonMenuItem.selectionBackground = @menuSelectionBackground


#---- PasswordField ---

PasswordField.disabledBackground = @disabledComponentBackground
PasswordField.selectionBackground = @textSelectionBackground
PasswordField.selectionForeground = @textSelectionForeground


#---- PopupMenu ----

PopupMenu.borderInsets = 6,1,6,1
PopupMenu.borderCornerRadius = 8


#---- ProgressBar ----

ProgressBar.background = lighten(@background,8%)


#---- RadioButton ----

RadioButton.iconTextGap = 6
RadioButton.icon.style = filled
RadioButton.icon[filled].centerDiameter = 6


#---- ScrollBar ----

ScrollBar.width = 12
ScrollBar.track = @componentBackground
ScrollBar.thumb = @buttonBackground

# from FlatLaf.properties (when using not on macOS)
ScrollBar.minimumThumbSize = 18,18
ScrollBar.thumbInsets = 2,2,2,2
ScrollBar.thumbArc = 999
ScrollBar.hoverThumbWithTrack = true


#---- Separator ----

Separator.foreground = @nsSeparatorColor


#---- Slider ----

Slider.trackWidth = 3
Slider.thumbSize = 14,14
Slider.trackColor = lighten(@background,8%)
Slider.thumbColor = lighten($Slider.trackColor,35%)
Slider.disabledTrackColor = darken($Slider.trackColor,4%)
Slider.disabledThumbColor = darken($Slider.thumbColor,32%)
Slider.focusedColor = $Component.focusColor


#---- Spinner ----

Spinner.buttonStyle = mac
Spinner.disabledBackground = @disabledComponentBackground
Spinner.buttonBackground = @buttonBackground
Spinner.buttonArrowColor = @foreground
Spinner.buttonHoverArrowColor = lighten($Spinner.buttonArrowColor,10%,derived noAutoInverse)
Spinner.buttonPressedArrowColor = lighten($Spinner.buttonArrowColor,20%,derived noAutoInverse)
Spinner.buttonSeparatorWidth = 0


#---- TabbedPane ----

TabbedPane.tabArc = $Button.arc
TabbedPane.tabSelectionArc = 999
TabbedPane.cardTabArc = $Button.arc


#---- TextArea ---

TextArea.disabledBackground = @disabledComponentBackground
TextArea.selectionBackground = @textSelectionBackground
TextArea.selectionForeground = @textSelectionForeground


#---- TextField ----

TextField.disabledBackground = @disabledComponentBackground
TextField.selectionBackground = @textSelectionBackground
TextField.selectionForeground = @textSelectionForeground


#---- TextPane ---

TextPane.disabledBackground = @disabledComponentBackground
TextPane.selectionBackground = @textSelectionBackground
TextPane.selectionForeground = @textSelectionForeground


#---- ToggleButton ----

ToggleButton.disabledBackground = $Button.disabledBackground
ToggleButton.selectedBackground = lighten($ToggleButton.background,20%,derived)
ToggleButton.selectedForeground = lighten($ToggleButton.foreground,20%)

ToggleButton.toolbar.selectedBackground = #fff3


#---- ToolBar ----

ToolBar.hoverButtonGroupArc = 14
