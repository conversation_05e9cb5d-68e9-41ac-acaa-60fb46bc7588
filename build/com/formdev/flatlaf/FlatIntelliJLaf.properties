#
# Copyright 2019 FormDev Software GmbH
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#
# This file is loaded for "FlatLaf IntelliJ" theme (that extend class FlatIntelliJLaf)
# and for all light IntelliJ Platform themes.
#
# Documentation:
#  - https://www.formdev.com/flatlaf/properties-files/
#  - https://www.formdev.com/flatlaf/how-to-customize/
#
# NOTE: Avoid copying the whole content of this file to own properties files.
#       This will make upgrading to newer FlatLaf versions complex and error-prone.
#       Instead, copy and modify only those properties that you need to alter.
#

# Colors and style mostly based on IntelliJ theme from IntelliJ IDEA Community Edition,
# which is licensed under the Apache 2.0 license. Copyright 2000-2019 JetBrains s.r.o.
# See: https://github.com/JetBrains/intellij-community/

#---- variables ----

# accent colors (blueish)
@accentFocusColor               = if(@accentColor, lighten(@accentColor,20%), lighten(@accentBaseColor,31%))
@accentButtonDefaultBackground  = if(@accentColor, @accentColor, tint(@accentBaseColor,15%))

#---- Button ----

Button.focusedBackground = null

Button.default.background = @accentButtonDefaultBackground
Button.default.foreground = contrast($Button.default.background, tint($Button.foreground,50%), #fff, 50%)
Button.default.focusedBackground = null
Button.default.borderColor = shade($Button.default.background,15%)
Button.default.hoverBorderColor = tint($Button.default.background,50%)
Button.default.focusedBorderColor = $Button.default.hoverBorderColor
Button.default.boldText = true
Button.default.borderWidth = 1


#---- CheckBox ----

CheckBox.icon.style = filled
CheckBox.icon.focusWidth = null
CheckBox.icon.focusedBackground = null


#---- Component ----

Component.focusWidth = 2
Component.innerFocusWidth = 0
Component.innerOutlineWidth = 0
Component.arrowType = triangle


#---- RadioButton ----

RadioButton.icon.style = filled
