################################################################################
#           JAXP Strict Configuration Template
#
# This file, jaxp-strict.properties.template, provides a template for creating
# custom configuration files. The settings in this file are more restrictive than
# those in the default configuration, jaxp.properties. In particular:
#   - JDKCatalog Resolve is on "strict" setting
#   - Extension Functions are disabled
#   - JAXP Limits are set to smaller numbers
#
# To create a configuration file, copy the template to a new file with
# the .properties extension, that is:
#
#     cp $JAVA_HOME/conf/jaxp-strict.properties.template /path/to/jaxp-strict.properties
#
# The configuration file can then be set up using the system property
# java.xml.config.file to override the default configuration jaxp.properties
# and used to assess the impact of a stricter configuration, for example:
#
#     java -Djava.xml.config.file=/path/to/jaxp-strict.properties
#
# The system property java.xml.config.file is defined in the java.xml module
# description.
#
################################################################################

#
# ---- Implementation Specific Properties ----
# For a complete list of properties, refer to the Implementation Specific Properties
# table in the java.xml/module-summary.
#
# Extension Functions:
#
# This property determines whether XSLT and XPath extension functions are allowed.
# The value type is boolean and the default value is true (allowing
# extension functions). The following entry overrides the default value and
# disallows extension functions:
#
jdk.xml.enableExtensionFunctions=false
#
#
# Overriding the default parser:
#
# This property allows a third party implementation to override the default
# parser provided by the JDK. The value type is boolean and the default value is
# false, disallowing overriding the default parser. The setting below reflects
# the default property setting:
#
jdk.xml.overrideDefaultParser=false
#
# Implementation Specific Properties - jdkcatalog.resolve
#
# This property instructs the JDK default CatalogResolver to act in accordance with
# the setting when unable to resolve an external reference with the built-in Catalog.
# The options are:
#     continue -- indicates that the processing should continue
#     ignore -- indicates that the reference is skipped
#     strict -- indicates that the resolver should throw a CatalogException
#
# The following setting causes the default CatalogResolver to throw a CatalogException
# when external references are not resolved by a user-defined resolver or catalog,
# or the built-in Catalog:
jdk.xml.jdkcatalog.resolve=strict
#
# Implementation Specific Properties - DTD
#
# This property instructs the parsers to deny, ignore or allow DTD processing.
# The following setting causes the parser to reject DTDs by throwing an exception.
# jdk.xml.dtd.support=deny
#
# The following setting permits the processor to continue processing DTDs. Note
# that while DTDs are allowed in this configuration, external references are
# restricted, and limits on DTD entities are tightened:
jdk.xml.dtd.support=allow
#
# Implementation Specific Properties - Limits
#
# Limits have a value type Integer. The values must be positive integers. Zero
# means no limit.
#
# Limits the number of entity expansions
jdk.xml.entityExpansionLimit=2500
#
# Limits the total size of all entities that include general and parameter entities.
# The size is calculated as an aggregation of all entities.
jdk.xml.totalEntitySizeLimit=100000
#
# Limits the maximum size of any general entities.
jdk.xml.maxGeneralEntitySizeLimit=100000
#
# Limits the maximum size of any parameter entities, including the result of
# nesting multiple parameter entities.
jdk.xml.maxParameterEntitySizeLimit=15000
#
# Limits the total number of nodes in all entity references.
jdk.xml.entityReplacementLimit=100000
#
# Limits the number of attributes an element can have. The default value is 200.
jdk.xml.elementAttributeLimit=200
#
# Limits the number of content model nodes that may be created when building a
# grammar for a W3C XML Schema that contains maxOccurs attributes with values
# other than "unbounded". The default value is 5000.
jdk.xml.maxOccurLimit=5000
#
# Limits the maximum element depth. The default value is 100.
jdk.xml.maxElementDepth=100
#
# Limits the maximum size of XML names, including element name, attribute name
# and namespace prefix and URI. The default value is 1000.
jdk.xml.maxXMLNameLimit=1000
#
#
# XPath Limits
#
# Limits the number of groups an XPath expression can contain. The default value is 10.
jdk.xml.xpathExprGrpLimit=10
#
# Limits the number of operators an XPath expression can contain. The default value is 100.
jdk.xml.xpathExprOpLimit=100
#
# Limits the total number of XPath operators in an XSL Stylesheet. The default value is 10000.
jdk.xml.xpathTotalOpLimit=10000
