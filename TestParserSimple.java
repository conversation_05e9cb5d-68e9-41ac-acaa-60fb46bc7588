import com.warnomodmaker.parser.*;
import com.warnomodmaker.model.*;
import java.io.*;

public class TestParserSimple {
    public static void main(String[] args) {
        try {
            System.out.println("Testing NEW file format parsing...");

            // Test parsing the actual NEW file
            java.io.File newFile = new java.io.File("tester files/UniteDescriptorNEW.ndf");
            if (!newFile.exists()) {
                System.err.println("File not found: " + newFile.getAbsolutePath());
                return;
            }

            try (java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(newFile))) {
                NDFParser parser = new NDFParser(reader);
                parser.setFileType(NDFValue.NDFFileType.UNITE_DESCRIPTOR);

                java.util.List<NDFValue.ObjectValue> objects = parser.parse();
                
                System.out.println("SUCCESS: Parsed " + objects.size() + " objects");
                
                if (objects.size() > 0) {
                    NDFValue.ObjectValue obj = objects.get(0);
                    System.out.println("Object: " + obj.getInstanceName());
                    
                    NDFValue modulesValue = obj.getProperty("ModulesDescriptors");
                    if (modulesValue instanceof NDFValue.ArrayValue) {
                        NDFValue.ArrayValue modules = (NDFValue.ArrayValue) modulesValue;
                        System.out.println("Modules count: " + modules.getElements().size());

                        for (int i = 0; i < modules.getElements().size(); i++) {
                            NDFValue module = modules.getElements().get(i);
                            System.out.println("  Module " + i + ": " + module.getClass().getSimpleName());
                            if (module instanceof NDFValue.ObjectValue) {
                                NDFValue.ObjectValue moduleObj = (NDFValue.ObjectValue) module;
                                System.out.println("    Type: " + moduleObj.getTypeName());
                                if (moduleObj.getInstanceName() != null) {
                                    System.out.println("    Name: " + moduleObj.getInstanceName());
                                }
                            } else if (module instanceof NDFValue.ResourceRefValue) {
                                NDFValue.ResourceRefValue resourceRef = (NDFValue.ResourceRefValue) module;
                                System.out.println("    Resource: " + resourceRef.getPath());
                                if (resourceRef.getInstanceName() != null) {
                                    System.out.println("    Name: " + resourceRef.getInstanceName());
                                }
                            }
                        }
                    }
                }
                
            } catch (Exception e) {
                System.err.println("FAILED: " + e.getMessage());
                e.printStackTrace();
            }
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
